using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.Powerflow.Apis.Services.Conversation.Services;
using Sleekflow.Powerflow.Apis.Services.Conversation.Services.Export;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using ExportConversationSnapshotRequest = Sleekflow.Powerflow.Apis.ViewModels.ExportConversationSnapshotRequest;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Internal Cms APIs for conversation.
/// </summary>
[Authorize(Roles = ApplicationUserRole.InternalCmsUser)] // Basic Role Requirement
[Route("/internal/conversation/[action]")]
public class InternalCmsConversationSnapshotController : InternalControllerBase
{
    private readonly IInternalConversationSnapshotService _internalConversationSnapshotService;
    private readonly IInternalConversationSnapshotExportService _internalConversationSnapshotExportService;

    public InternalCmsConversationSnapshotController(
        UserManager<ApplicationUser> userManager,
        IInternalConversationSnapshotService internalConversationSnapshotService,
        IInternalConversationSnapshotExportService internalConversationSnapshotExportService)
        : base(userManager)
    {
        _internalConversationSnapshotService = internalConversationSnapshotService;
        _internalConversationSnapshotExportService = internalConversationSnapshotExportService;
    }

    /// <summary>
    /// Export Company Conversation snapshot and saved to Blob.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<IActionResult> ExportConversationSnapshot(
        [FromBody]
        ExportConversationSnapshotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsUser
                }) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var taskId = Guid.NewGuid().ToString();

        BackgroundJob.Enqueue<IInternalConversationSnapshotService>(
            x => x.EnrollExportConversationSnapshot(
                request,
                taskId));

        return Ok(
            new ResponseViewModel
            {
                message = taskId
            });
    }

    /// <summary>
    /// Get Export Company Conversation snapshot progress.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<IActionResult> GetExportConversationSnapshotProgress(
        [FromBody]
        GetExportConversationSnapshotProgressRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsUser
                }) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var progress = await _internalConversationSnapshotService.GetExportConversationSnapshotProgressAsync(
            request.TaskId);

        if (progress == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Progress not found"
                });
        }

        return Ok(progress);
    }

    /// <summary>
    /// Download Export Company Conversation snapshot.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<IActionResult> DownloadExportConversationSnapshot(
        [FromBody]
        DownloadExportConversationSnapshotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsUser
                }) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var progress = await _internalConversationSnapshotService.GetExportConversationSnapshotProgressAsync(
            request.TaskId);

        if (progress == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Progress not found"
                });
        }

        if (progress.Status != "Completed")
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Export not completed"
                });
        }

        var file = await _internalConversationSnapshotService.DownloadExportConversationSnapshotAsync(
            request.TaskId);

        if (file.FileContents == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "File not found"
                });
        }

        return File(
            file.FileContents,
            file.ContentType,
            progress.FileName);
    }
}