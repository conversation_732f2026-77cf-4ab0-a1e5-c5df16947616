﻿using Pulumi.AzureNative.Web;
using Insights = Pulumi.AzureNative.Insights;

namespace Sleekflow.Core.Infra.Components.Models;

public class AppServiceConfiguration
{
    public string Name { get; set; }
    public WebApp WebApp { get; set; }
    public Insights.Component AppInsight { get; set; }
    public AppServicePlan SkuConfig { get; set; }
    public Insights.AutoscaleSetting? AutoScaleConfig { get; set; }

    public AppServiceConfiguration(
        string name,
        WebApp webApp,
        Insights.Component appInsight,
        AppServicePlan skuConfig,
        Insights.AutoscaleSetting? autoScaleConfig = null)
    {
        Name = name;
        WebApp = webApp;
        AppInsight = appInsight;
        SkuConfig = skuConfig;
        AutoScaleConfig = autoScaleConfig;
    }
}