﻿using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.Utils;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Enums;

namespace Sleekflow.Core.Tests.Analytics;

public class ConversationAnalyticsGetDimensionTests
{
    private readonly Condition _mockCondition = new Condition
    {
        FieldName = "Birthday",
        ConditionOperator = SupportedOperator.HigherThan,
        Values = ["2023-04-24T16:00:00.000Z"],
        NextOperator = SupportedNextOperator.And
    };

    private readonly Filter _mockFilterChannel = new Filter(
        ConversationAnalyticsAdvancedFilterFieldNames.Channel,
        SupportedOperator.ContainsAny,
        ["1"]);

    private readonly Filter _mockFilterUser = new Filter(
        ConversationAnalyticsAdvancedFilterFieldNames.Assignee,
        SupportedOperator.ContainsAny,
        ["1"]);

    [Test]
    public void GetDimension_BasicDimension_ShouldReturnCorrectDimension()
    {
        bool isBusinessHourEnabled;
        List<Condition>? conditions;
        AdvancedFilter? advancedFilter;

        // without business hour
        {
            isBusinessHourEnabled = false;
            conditions = null;
            advancedFilter = null;

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.Company));
            Assert.That(dimensionWrapper.IsContactDimension, Is.False);
        }
        {
            isBusinessHourEnabled = false;
            conditions = new List<Condition>();
            advancedFilter = new AdvancedFilter("AND", []);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.Company));
            Assert.That(dimensionWrapper.IsContactDimension, Is.False);
        }

        // with business hour
        {
            isBusinessHourEnabled = true;
            conditions = new List<Condition>();
            advancedFilter = new AdvancedFilter("AND", []);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyWithBusinessHour));
            Assert.That(dimensionWrapper.IsContactDimension, Is.False);
        }
    }

    [Test]
    public void GetDimension_ChannelDimension_ShouldReturnCorrectDimension()
    {
        bool isBusinessHourEnabled;
        List<Condition>? conditions;
        AdvancedFilter? advancedFilter;

        // without business hour
        {
            isBusinessHourEnabled = false;
            conditions = null;
            advancedFilter = new AdvancedFilter("AND", [_mockFilterChannel]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyChannel));
            Assert.That(dimensionWrapper.IsContactDimension, Is.False);
        }

        // with business hour
        {
            isBusinessHourEnabled = true;
            conditions = new List<Condition>();
            advancedFilter = new AdvancedFilter("AND", [_mockFilterChannel, _mockFilterChannel]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyChannelWithBusinessHour));
            Assert.That(dimensionWrapper.IsContactDimension, Is.False);
        }
    }

    [Test]
    public void GetDimension_UserDimension_ShouldReturnCorrectDimension()
    {
        bool isBusinessHourEnabled;
        List<Condition>? conditions;
        AdvancedFilter? advancedFilter;

        // without business hour
        {
            isBusinessHourEnabled = false;
            conditions = null;
            advancedFilter = new AdvancedFilter("AND", [_mockFilterUser]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyUser));
            Assert.That(dimensionWrapper.IsContactDimension, Is.False);
        }

        // with business hour
        {
            isBusinessHourEnabled = true;
            conditions = new List<Condition>();
            advancedFilter = new AdvancedFilter("AND", [_mockFilterUser, _mockFilterUser]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyUserWithBusinessHour));
            Assert.That(dimensionWrapper.IsContactDimension, Is.False);
        }
    }

    [Test]
    public void GetDimension_UserChannelDimension_ShouldReturnCorrectDimension()
    {
        bool isBusinessHourEnabled;
        List<Condition>? conditions;
        AdvancedFilter? advancedFilter;

        // without business hour
        {
            isBusinessHourEnabled = false;
            conditions = null;
            advancedFilter = new AdvancedFilter("AND", [_mockFilterChannel, _mockFilterUser]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyUserChannel));
            Assert.That(dimensionWrapper.IsContactDimension, Is.False);
        }

        // with business hour
        {
            isBusinessHourEnabled = true;
            conditions = new List<Condition>();
            advancedFilter = new AdvancedFilter("AND", [_mockFilterChannel, _mockFilterUser, _mockFilterChannel]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyUserChannelWithBusinessHour));
            Assert.That(dimensionWrapper.IsContactDimension, Is.False);
        }
    }

    [Test]
    public void GetDimension_ContactDimension_ShouldReturnCorrectDimension()
    {
        bool isBusinessHourEnabled;
        List<Condition>? conditions;
        AdvancedFilter? advancedFilter;

        // without business hour
        {
            isBusinessHourEnabled = false;
            conditions = new List<Condition>
            {
                _mockCondition
            };
            advancedFilter = null;

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyContact));
            Assert.That(dimensionWrapper.IsContactDimension, Is.True);
        }

        // with business hour
        {
            isBusinessHourEnabled = true;
            conditions = new List<Condition>
            {
                _mockCondition
            };
            advancedFilter = new AdvancedFilter("AND", []);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyContactWithBusinessHour));
            Assert.That(dimensionWrapper.IsContactDimension, Is.True);
        }
    }

    [Test]
    public void GetDimension_ContactChannelDimension_ShouldReturnCorrectDimension()
    {
        bool isBusinessHourEnabled;
        List<Condition>? conditions;
        AdvancedFilter? advancedFilter;

        // without business hour
        {
            isBusinessHourEnabled = false;
            conditions = new List<Condition>
            {
                _mockCondition
            };
            advancedFilter = new AdvancedFilter("AND", [_mockFilterChannel]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyContactChannel));
            Assert.That(dimensionWrapper.IsContactDimension, Is.True);
        }

        // with business hour
        {
            isBusinessHourEnabled = true;
            conditions = new List<Condition>
            {
                _mockCondition
            };
            advancedFilter = new AdvancedFilter("AND", [_mockFilterChannel, _mockFilterChannel]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyContactChannelWithBusinessHour));
            Assert.That(dimensionWrapper.IsContactDimension, Is.True);
        }
    }

    [Test]
    public void GetDimension_ContactUserDimension_ShouldReturnCorrectDimension()
    {
        bool isBusinessHourEnabled;
        List<Condition>? conditions;
        AdvancedFilter? advancedFilter;

        // without business hour
        {
            isBusinessHourEnabled = false;
            conditions = new List<Condition>
            {
                _mockCondition
            };
            advancedFilter = new AdvancedFilter("AND", [_mockFilterUser]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyContactUser));
            Assert.That(dimensionWrapper.IsContactDimension, Is.True);
        }

        // with business hour
        {
            isBusinessHourEnabled = true;
            conditions = new List<Condition>
            {
                _mockCondition
            };
            advancedFilter = new AdvancedFilter("AND", [_mockFilterUser, _mockFilterUser]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyContactUserWithBusinessHour));
            Assert.That(dimensionWrapper.IsContactDimension, Is.True);
        }
    }

    [Test]
    public void GetDimension_ContactUserChannelDimension_ShouldReturnCorrectDimension()
    {
        bool isBusinessHourEnabled;
        List<Condition>? conditions;
        AdvancedFilter? advancedFilter;

        // without business hour
        {
            isBusinessHourEnabled = false;
            conditions = new List<Condition>
            {
                _mockCondition
            };
            advancedFilter = new AdvancedFilter("AND", [_mockFilterChannel, _mockFilterUser]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyContactUserChannel));
            Assert.That(dimensionWrapper.IsContactDimension, Is.True);
        }

        // with business hour
        {
            isBusinessHourEnabled = true;
            conditions = new List<Condition>
            {
                _mockCondition
            };
            advancedFilter = new AdvancedFilter("AND", [_mockFilterChannel, _mockFilterUser, _mockFilterChannel]);

            var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
                isBusinessHourEnabled,
                conditions,
                advancedFilter);

            Assert.That(dimensionWrapper.Dimension, Is.EqualTo(ConversationAnalyticsDimensions.CompanyContactUserChannelWithBusinessHour));
            Assert.That(dimensionWrapper.IsContactDimension, Is.True);
        }
    }
}