using Newtonsoft.Json;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.InternalDomain.ViewModels;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class GetWhatsappCloudApiUsageRecordsResponse
{
    [JsonProperty("business_balances")]
    public List<ManagementBusinessBalanceDto> WhatsappCloudApiUsageRecords { get; set; }
}

public class CreateWhatsappCloudApiTransactionLogResponse
{
    [JsonProperty("business_balance_transaction_log")]
    public BusinessBalanceTransactionLog WhatsappCloudApiBusinessBalanceTransactionLog { get; set; }
}

public record GetAllWhatsappCloudApiWabasResponse(
    [JsonProperty("wabas")]
    List<ManagementWabaDto> Wabas
);

public record GetAllWhatsAppCloudApiBalancesResponse
(
    [JsonProperty("business_balances")]
    List<ManagementBusinessBalanceDto> WhatsappCloudApiUsageRecords
);

public class TriggerWhatsAppPhoneNumberOtpResponse
{
    [JsonProperty("code_sent")]
    public bool CodeSent { get; set; }
};

public record GetWhatsappPhoneNumberBusinessProfileResponse(
    [JsonProperty("whatsapp_business_profile")]
    FacebookBusinessProfileResult WhatsappBusinessProfile
);

public record SuccessResponse(
    [JsonProperty("success")]
    bool Success);

public class GetMonthlyWhatsappCloudApiConversationUsageAnalyticsResponse
{
    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("facebook_business_name")]
    public string FacebookBusinessName { get; set; }

    [JsonProperty("monthly_summarized_conversation_usage_analytics")]
    public List<MonthlySummarizedConversationUsageAnalytic> MonthlySummarizedConversationUsageAnalytics
    {
        get;
        set;
    }
}

public class MonthlySummarizedConversationUsageAnalytic
{
    [JsonProperty("date")]
    public string Date { get; set; }

    [JsonProperty("total_used")]
    public Money TotalUsed { get; set; }

    [JsonProperty("total_markup")]
    public Money TotalMarkup { get; set; }

    [JsonProperty("total_transaction_handling_fee")]
    public Money TotalTransactionHandlingFee { get; set; }
}

public class GetAllWhatsappCloudApiConversationUsageAnalyticsV2Response
{
    [JsonProperty("by_business_analytics")]
    public List<InternalByBusinessWhatsappCloudApiDailyConversationUsageAnalyticsDto> ByBusinessAnalytics { get; set; }

    [JsonProperty("by_waba_analytics")]
    public List<InternalByWabaWhatsappCloudApiDailyConversationUsageAnalyticsDto> ByWabaAnalytics { get; set; }
}