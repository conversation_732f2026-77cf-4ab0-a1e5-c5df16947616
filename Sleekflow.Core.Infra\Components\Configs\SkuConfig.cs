using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Components.Configs;

public class SkuConfig
{
    [JsonProperty("sleekflow_core")]
    public SkuConfigDetails SleekflowCore { get; set; }

    [JsonProperty("sleekflow_core_db")]
    public SkuConfigDetails SleekflowCoreDb { get; set; }

    [JsonProperty("sleekflow_analytic_db")]
    public SkuConfigDetails SleekflowAnalyticDb { get; set; }

    [JsonProperty("sleekflow_powerflow")]
    public SkuConfigDetails SleekflowPowerflow { get; set; }

    [JsonProperty("sleekflow_sleek_pay")]
    public SkuConfigDetails SleekflowSleekPay { get; set; }

    [JsonProperty("redis")]
    public Dictionary<string, SkuConfigDetails> Redis { get; set; }

    public SkuConfig(
        SkuConfigDetails sleekflowCore,
        SkuConfigDetails sleekflowCoreDb,
        SkuConfigDetails sleekflowAnalyticDb,
        SkuConfigDetails sleekflowPowerflow,
        SkuConfigDetails sleekflowSleekPay,
        Dictionary<string, SkuConfigDetails> redis)
    {
        Redis = redis;
        SleekflowCore = sleekflowCore;
        SleekflowCoreDb = sleekflowCoreDb;
        SleekflowAnalyticDb = sleekflowAnalyticDb;
        SleekflowPowerflow = sleekflowPowerflow;
        SleekflowSleekPay = sleekflowSleekPay;
    }
}