﻿using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Engines;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;

namespace Sleekflow.Core.Benchmarks;

[SimpleJob(RunStrategy.Monitoring, launchCount: 1, warmupCount: 1, iterationCount: 5, invocationCount: 1)]
public class FacebookPageCommentTriggerBenchmark
{
    private bool _firstCall = true;
    private readonly ApplicationDbContext _applicationDbContext;
    private readonly ILogger<FacebookPageCommentTriggerBenchmark> _logger;
    private string? _companyId;
    private string? _pageId;
    private string? _parentCommentId;

    public FacebookPageCommentTriggerBenchmark()
    {
        var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
        _logger = loggerFactory.CreateLogger<FacebookPageCommentTriggerBenchmark>();

        _applicationDbContext = new ApplicationDbContext(
            new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseSqlServer(new SqlConnection(DbConfig.ReadOnlyConnStr))
                .Options);
    }

    private record FacebookCommentTriggerData
    {
        public string CompanyId { get; set; }
        public string? PageId { get; set; }
        public string? ParentCommentId { get; set; }
    }

    [GlobalSetup]
    public async Task GlobalSetup()
    {
        var data = await _applicationDbContext.CompanyAssignmentRules.Include(x => x.AutomationActions)
            .ThenInclude(x => x.FbIgAutoReply).ThenInclude(x => x.FbIgAutoReplyHistoryRecords).Where(
                x => x.Status == AutomationStatus.Live && x.AutomationType == AutomationType.FacebookPostComment &&
                     x.AutomationActions.Any(
                         y => y.FbIgAutoReply != null && y.FbIgAutoReply.FbIgAutoReplyHistoryRecords.Count > 1000))
            .Select(
                x => new FacebookCommentTriggerData()
                {
                    CompanyId = x.CompanyId,
                    PageId =
                        x.AutomationActions
                            .Where(
                                y => y.FbIgAutoReply != null &&
                                     y.FbIgAutoReply.FbIgAutoReplyHistoryRecords.Count > 5000)
                            .Select(y => y.FbIgAutoReply.PageId).FirstOrDefault(),
                    ParentCommentId = x.AutomationActions
                        .Where(y => y.FbIgAutoReply != null && y.FbIgAutoReply.FbIgAutoReplyHistoryRecords.Count > 5000)
                        .Select(
                            y => y.FbIgAutoReply.FbIgAutoReplyHistoryRecords.Where(z => z.ParentCommentId != null).Select(z => z.ParentCommentId).FirstOrDefault()).FirstOrDefault()
                })
            .FirstOrDefaultAsync();

        if (data != null)
        {
            _companyId = data.CompanyId;
            _pageId = data.PageId;
            _parentCommentId = data.ParentCommentId;
        }
    }

    [Benchmark(Baseline = true)]
    public async Task<bool> FilterFacebookCommentAutoReplyHistoryRecordsInMemory()
    {
        var companyId = _companyId;
        var pageId = _pageId;
        var parentCommentId = _parentCommentId;

        var queryable = _applicationDbContext.CompanyAssignmentRules
            .Include(x => x.AutomationActions)
            .ThenInclude(x => x.FbIgAutoReply)
            .ThenInclude(
                x => x.FbIgAutoReplyHistoryRecords)
            .OrderBy(x => x.Order)
            .Where(
                x =>
                    x.Status == AutomationStatus.Live
                    && x.CompanyId == companyId
                    && x.AutomationType == AutomationType.FacebookPostComment
                    && x.Conditions != null);

        if (_firstCall)
        {
            var queryString = queryable.ToQueryString();

            _logger.LogInformation(nameof(FilterFacebookCommentAutoReplyHistoryRecordsInMemory) + " " + queryString);

            _firstCall = false;
        }

        return (await queryable.ToListAsync()).Any(
            x => x.AutomationActions.Any(
                y => y.FbIgAutoReply is {FbIgAutoReplyHistoryRecords: not null} &&
                     y.FbIgAutoReply.FbIgAutoReplyHistoryRecords.Any(
                         z => z.PageId == pageId && z.ParentCommentId == parentCommentId && z.ErrorMessage != null)));
    }

    [Benchmark]
    public async Task<bool> FilterFacebookCommentAutoReplyHistoryRecordsFromDb()
    {
        var companyId = _companyId;
        var pageId = _pageId;
        var parentCommentId = _parentCommentId;

        var queryable = _applicationDbContext.CompanyAssignmentRules
            .Include(x => x.AutomationActions)
            .ThenInclude(x => x.FbIgAutoReply)
            .ThenInclude(
                x => x.FbIgAutoReplyHistoryRecords.Where(
                    y => y.PageId == pageId && y.ParentCommentId == parentCommentId && y.ErrorMessage == null))
            .OrderBy(x => x.Order)
            .Where(
                x =>
                    x.Status == AutomationStatus.Live
                    && x.CompanyId == companyId
                    && x.AutomationType == AutomationType.FacebookPostComment
                    && x.Conditions != null);

        if (_firstCall)
        {
            var queryString = queryable.ToQueryString();

            _logger.LogInformation(nameof(FilterFacebookCommentAutoReplyHistoryRecordsFromDb) + " " + queryString);

            _firstCall = false;
        }

        return (await queryable.ToListAsync()).Any(x => x.AutomationActions.Any(y => y.FbIgAutoReply is
        {
            FbIgAutoReplyHistoryRecords: not null
        } && y.FbIgAutoReply.FbIgAutoReplyHistoryRecords.Any()));
    }
}