using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Components.Configs.SleekflowCore;

public class FlowBuilderFlowEnrollmentsIncentivesConfig
{
    [JsonProperty("period_start")]
    public string PeriodStart { get; set; }

    [JsonProperty("period_end")]
    public string PeriodEnd { get; set; }

    public FlowBuilderFlowEnrollmentsIncentivesConfig(string periodStart, string periodEnd)
    {
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
    }
}