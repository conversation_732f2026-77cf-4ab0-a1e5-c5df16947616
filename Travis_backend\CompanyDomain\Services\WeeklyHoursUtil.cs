using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Travis_backend.CompanyDomain.Models;

namespace Travis_backend.CompanyDomain.Services;

public static class WeeklyHoursUtil
{
    public static void Validate(this WeeklyHours weeklyHours)
    {
        ValidateVersion(weeklyHours.Version);

        ValidateDailyHours(weeklyHours.Version, weeklyHours.Monday);
        ValidateDailyHours(weeklyHours.Version, weeklyHours.Tuesday);
        ValidateDailyHours(weeklyHours.Version, weeklyHours.Wednesday);
        ValidateDailyHours(weeklyHours.Version, weeklyHours.Thursday);
        ValidateDailyHours(weeklyHours.Version, weeklyHours.Friday);
        ValidateDailyHours(weeklyHours.Version, weeklyHours.Saturday);
        ValidateDailyHours(weeklyHours.Version, weeklyHours.Sunday);
    }

    public static bool IsChanged(WeeklyHours previous, WeeklyHours current)
    {
        throw new NotImplementedException();
    }

    private static void ValidateVersion(string version)
    {
        if (version != "v1")
        {
            throw new ValidationException("Invalid version");
        }
    }

    public static void ValidateDailyHours(string version, List<Period> dailyHours)
    {
        switch (version)
        {
            // v1
            // 1. cross day not allowed
            // 2. multi periods not allowed
            // 3. end > start
            case "v1":
                if (dailyHours.Count == 0)
                {
                    return;
                }

                if (dailyHours.Count > 1)
                {
                    throw new ValidationException("Multiple periods not allowed in v1");
                }

                var period = dailyHours[0];

                if (period.CrossDay)
                {
                    throw new ValidationException("Cross day not allowed in v1");
                }

                if (period.Start >= period.End)
                {
                    throw new ValidationException("Start time must be earlier than End time in v1");
                }

                break;
            default:
                throw new ValidationException("Invalid version");
        }
    }
}