﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using LinqKit;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.Constants;

namespace Travis_backend.AnalyticsDomain.Services.ConversationAnalyticsAdvanceFilters;

public class IsNotContainsAnyFilterStrategy : IFilterStrategy
{
    public Expression<Func<ConversationAnalyticsMetric, bool>> BuildPredicate(string fieldName, List<string> values)
    {
        var predicate = PredicateBuilder.New<ConversationAnalyticsMetric>(true);

        foreach (var value in values)
        {
            switch (fieldName)
            {
                case ConversationAnalyticsAdvancedFilterFieldNames.Assignee:
                    predicate = predicate.And(metric => metric.DimensionData.UserIdentityId != value);

                    break;
                case ConversationAnalyticsAdvancedFilterFieldNames.Channel:
                    {
                        var (channelType, channelIdentityId) = SplitChannelValue(value);

                        predicate = predicate.And(
                            metric =>
                                metric.DimensionData.ChannelType != channelType
                                || metric.DimensionData.ChannelIdentityId != channelIdentityId);
                    }

                    break;
                default:
                    throw new ArgumentException($"Field '{fieldName}' not supported.");
            }
        }

        return predicate;
    }

    private static (string ChannelType, string ChannelIdentityId) SplitChannelValue(string channelValue)
    {
        try
        {
            var colonIndex = channelValue.IndexOf(':');

            return (channelValue[..colonIndex], channelValue[(colonIndex + 1)..]);
        }
        catch
        {
            throw new InvalidOperationException($"Split Channel Value: invalid input [{channelValue}]");
        }
    }
}