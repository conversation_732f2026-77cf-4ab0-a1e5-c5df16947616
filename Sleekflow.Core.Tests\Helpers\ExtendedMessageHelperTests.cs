using NUnit.Framework;
using Travis_backend.Helpers;

namespace Sleekflow.Core.Tests.Helpers
{
    [TestFixture]
    public class ExtendedMessageHelperTests
    {
        [TestCase("https://sleekflow-core-app-eas-production.azurewebsites.net/extendedmessagepayload/file/e04fba89-**************-397cbb56732a/private/video/11.mp4?hashcode=dd8a9f9cf252342443fb2938216734707f23a48ff15ccfaf632e84dc8e91352d&mode=redirect", "e04fba89-**************-397cbb56732a")]
        [TestCase(null, null)]
        [TestCase("", null)]
        [TestCase("https://example.com/some/other/path", null)]
        [TestCase("https://sleekflow-core-app-eas-production.azurewebsites.net/extendedmessagepayload/file/e04fba89-**************-397cbb56732a", null)] // Missing slash after GUID
        public void ExtractFileIdFromUrl_Tests(string? url, string? expectedFileId)
        {
            // Act
            var actualFileId = ExtendedMessageHelper.ExtractFileIdFromUrl(url);

            // Assert
            Assert.AreEqual(expectedFileId, actualFileId);
        }
    }
} 