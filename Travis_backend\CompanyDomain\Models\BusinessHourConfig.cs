using System;
using System.ComponentModel.DataAnnotations;
using Travis_backend.CommonDomain.Models;

namespace Travis_backend.CompanyDomain.Models;

public class BusinessHourConfig : IHasCreationDate, IHasUpdateDate
{
    [Key]
    public string CompanyId { get; set; }

    public bool IsEnabled { get; set; }

    public WeeklyHours WeeklyHours { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime UpdatedAt { get; set; }
}