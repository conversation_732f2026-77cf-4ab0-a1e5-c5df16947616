using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Cache;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.Database;
using Travis_backend.**********************;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;

namespace Travis_backend.AutomationDomain.Services;

public class MetaCommentTriggerHandler : IMetaCommentTriggerHandler
{
    private readonly ILogger<MetaCommentTriggerHandler> _logger;
    private readonly ApplicationDbContext _applicationDbContext;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;
    private readonly IFlowHubService _flowHubService;
    private readonly IEventsApi _eventsApi;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ILockService _lockService;


    public MetaCommentTriggerHandler(
        ILogger<MetaCommentTriggerHandler> logger,
        ApplicationDbContext applicationDbContext,
        IDistributedInvocationContextService distributedInvocationContextService,
        IFlowHubService flowHubService,
        IEventsApi eventsApi,
        ICacheManagerService cacheManagerService,
        ILockService lockService)
    {
        _logger = logger;
        _applicationDbContext = applicationDbContext;
        _distributedInvocationContextService = distributedInvocationContextService;
        _flowHubService = flowHubService;
        _eventsApi = eventsApi;
        _cacheManagerService = cacheManagerService;
        _lockService = lockService;
    }

    public async Task FbIgCommentReceived(Entry entry, Change change, string channel)
    {
        string companyId = string.Empty;

        string conversationId = null;
        UserProfile userProfile = null;
        bool isNewContact = false;
        string contactId = null;
        Dictionary<string, object> userDictionary = null;

        if (ChannelTypes.Facebook.EqualsIgnoreCase(channel))
        {
            (companyId, userProfile, isNewContact, conversationId) = await ResolveInfoWithFacebookComment(entry, change, companyId, userProfile, isNewContact, conversationId);
        }
        else
        {
            (companyId, userProfile, isNewContact, conversationId) = await ResolveInfoWithInstagramComment(entry, change, companyId, userProfile, isNewContact, conversationId);
        }
        if (isNewContact)
        {
            contactId = await GenerateContactId(entry, change, channel);
        }
        else
        {
            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfile!.Id);
            userDictionary = userProfileDict.ToDictionary(e => e.Key, object (e) => e.Value);
            var getRet = userDictionary.TryGetValue("id", out var existedContactId);
            if (!getRet || existedContactId == null)
            {
                _logger.LogError("can't get contact id. userDictionary={UserDictionary}", JsonConvert.SerializeObject(userDictionary));
                return;
            }
            contactId = existedContactId.ToString();
        }

        var eventInput = new OnFbIgPostCommentReceivedEventInput(
            companyId,
            contactId,
            new OnFbIgPostCommentReceivedEventBody(
                new OnFbIgPostCommentReceivedEventBodyMessage(
                    GetCommentId(change, channel),
                    GetPostId(change, channel),
                    new FromFacebookSender(
                        change.Value.From.Id,
                        ChannelTypes.Facebook.Equals(channel) ? change.Value.From.Name : change.Value.From.UserName),
                    GetCommentText(change, channel)),
                entry.Id,
                channel,
                GetCommentId(change, channel),
                isNewContact,
                conversationId,
                contactId,
                userDictionary));

        var contextHeader = _distributedInvocationContextService.GetSerializedContextHeader();
        _logger.LogInformation("input: {Input}", JsonConvert.SerializeObject(eventInput));
        await _eventsApi.EventsOnFbIgPostCommentReceivedEventPostAsync(
            null,
            contextHeader,
            eventInput,
            CancellationToken.None);
    }

    private async Task<string> GenerateContactId(Entry entry, Change change, string channel)
    {
        string contactId;
        // redis key: `facebook:facebookUserId:pageId`
        string redisKey = channel + ":" + change.Value.From.Id + ":" + entry.Id;
        var lockKey = $"getContactId:{redisKey}";
        var redisLock = await _lockService.WaitUntilLockAcquiredAsync(lockKey, TimeSpan.FromSeconds(1));
        try
        {
            var cachedContactId = await _cacheManagerService.GetCacheWithConstantKeyAsync(redisKey);
            if (string.IsNullOrWhiteSpace(cachedContactId))
            {
                contactId = Guid.NewGuid().ToString();
                await _cacheManagerService.SaveCacheWithConstantKeyAsync(redisKey, contactId, TimeSpan.FromMinutes(20));
                _logger.LogInformation(
                    "Contact ID not found in Redis for key {RedisKey}, setting new contact ID: {NewContactId}",
                    redisKey,
                    contactId);
            }
            else
            {
                contactId = cachedContactId;
                _logger.LogInformation(
                    "Retrieved contact ID from Redis cache for key {RedisKey}: {ContactId}",
                    redisKey,
                    contactId);
            }
        } finally {
            if (redisLock != null)
            {
                await _lockService.ReleaseLockAsync(redisLock);
            }
        }
        return contactId;
    }

    private async Task<(string CompanyId, UserProfile UserProfile, bool IsNewContact, string ConversationId)> ResolveInfoWithInstagramComment(Entry entry, Change change, string companyId,
        UserProfile userProfile, bool isNewContact, string conversationId)
    {
        var fbIgConfig =
            await _applicationDbContext.ConfigInstagramConfigs.FirstOrDefaultAsync(
                x => entry.Id.Equals(x.InstagramPageId));
        if (fbIgConfig == null)
        {
            _logger.LogError("instagram config not found. pageId={PageId}", entry.Id);
            return (companyId, userProfile, isNewContact, conversationId);
        }

        companyId = fbIgConfig.CompanyId;
        _logger.LogInformation("begin to organize user data, entry id {EntryId}", entry.Id);
        userProfile = await _applicationDbContext.UserProfiles
            .Include(x => x.InstagramUser)
            .Where(
                x =>
                    x.InstagramUser != null
                    && x.InstagramUser.InstagramId == change.Value.From.Id
                    && x.InstagramUser.InstagramPageId == entry.Id)
            .OrderByDescending(x => x.UpdatedAt)
            .FirstOrDefaultAsync();

        isNewContact = userProfile == null;
        if (isNewContact && (string.IsNullOrWhiteSpace(change.Value.From.Id) || string.IsNullOrWhiteSpace(entry.Id)))
        {
            _logger.LogError(
                "Parameter is error. isNewContact: {IsNewContact}, from.id: {FromId}, entry.id: {EntryId}",
                isNewContact,
                change.Value.From.Id,
                entry.Id);
            return (companyId, userProfile, isNewContact, conversationId);
        }

        if (!isNewContact)
        {
            Conversation existingConversation = await _applicationDbContext.Conversations
                .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);

            if (existingConversation == null)
            {
                existingConversation = new Conversation
                {
                    UserProfileId = userProfile!.Id,
                    InstagramUser = userProfile.InstagramUser,
                    MessageGroupName = userProfile.CompanyId,
                    CompanyId = userProfile.CompanyId,
                    ActiveStatus = ActiveStatus.Active
                };

                _applicationDbContext.Conversations.Add(existingConversation);
                await _applicationDbContext.SaveChangesAsync();
            }
            conversationId = existingConversation.Id;
        }

        return (companyId, userProfile, isNewContact, conversationId);
    }

    private async Task<(string CompanyId, UserProfile UserProfile, bool IsNewContact, string ConversationId)> ResolveInfoWithFacebookComment(Entry entry, Change change, string companyId, UserProfile userProfile,
        bool isNewContact, string conversationId)
    {
        var fbIgConfig =
            await _applicationDbContext.ConfigFacebookConfigs.FirstOrDefaultAsync(x => entry.Id.Equals(x.PageId));
        if (fbIgConfig == null)
        {
            _logger.LogError("facebook config not found. pageId={PageId}", entry.Id);
            return (companyId, userProfile, isNewContact, conversationId);
        }

        companyId = fbIgConfig.CompanyId;

        _logger.LogInformation("begin to organize user data, entry id {EntryId}", entry.Id);
        userProfile = await _applicationDbContext.UserProfiles
            .Include(x => x.FacebookAccount)
            .Where(
                x =>
                    x.FacebookAccount != null
                    && x.FacebookAccount.FacebookId == change.Value.From.Id
                    && x.FacebookAccount.pageId == entry.Id)
            .OrderByDescending(x => x.UpdatedAt)
            .FirstOrDefaultAsync();

        isNewContact = userProfile == null;
        if (isNewContact && (string.IsNullOrWhiteSpace(change.Value.From.Id) || string.IsNullOrWhiteSpace(entry.Id)))
        {
            _logger.LogError(
                "Parameter is error. isNewContact: {IsNewContact}, from.id: {FromId}, entry.id: {EntryId}",
                isNewContact,
                change.Value.From.Id,
                entry.Id);
            return (companyId, userProfile, isNewContact, conversationId);
        }

        if (!isNewContact)
        {
            Conversation existingConversation = await _applicationDbContext.Conversations
                .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);

            if (existingConversation == null)
            {
                existingConversation = new Conversation
                {
                    UserProfileId = userProfile!.Id,
                    facebookUser = userProfile.FacebookAccount,
                    MessageGroupName = userProfile.CompanyId,
                    CompanyId = userProfile.CompanyId,
                    ActiveStatus = ActiveStatus.Active
                };

                _applicationDbContext.Conversations.Add(existingConversation);
                await _applicationDbContext.SaveChangesAsync();
            }
            conversationId = existingConversation.Id;
        }

        return (companyId, userProfile, isNewContact, conversationId);
    }

    private static string GetCommentText(Change change, string channel)
    {
        return "facebook".EqualsIgnoreCase(channel) ? change.Value.Message : change.Value.Text;
    }

    private static string GetPostId(Change change, string channel)
    {
        return "facebook".EqualsIgnoreCase(channel) ? change.Value.PostId : change.Value.Media.Id;
    }

    private static string GetCommentId(Change change, string channel)
    {
        return "facebook".EqualsIgnoreCase(channel) ? change.Value.CommentId : change.Value.Id;
    }
}