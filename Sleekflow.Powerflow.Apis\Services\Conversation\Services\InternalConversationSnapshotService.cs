using Hangfire;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Sleekflow.Powerflow.Apis.Services.Conversation.Repositories;
using Sleekflow.Powerflow.Apis.Services.Conversation.Services.Export;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.Cache;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models.Conversation;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Powerflow.Apis.Services.Conversation.Services;

public interface IInternalConversationSnapshotService
{
    Task EnrollExportConversationSnapshot(ExportConversationSnapshotRequest request, string taskId);

    Task<InternalConversationSnapshotExportProgress> GetExportConversationSnapshotProgressAsync(string taskId);

    Task<(byte[] FileContents, string ContentType)>
        DownloadExportConversationSnapshotAsync(
            string taskId);
}

public class InternalConversationSnapshotService : IInternalConversationSnapshotService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IInternalConversationRepository _internalConversationRepository;
    private readonly IInternalConversationSnapshotExportService _internalConversationSnapshotExportService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly ILogger<InternalConversationSnapshotService> _logger;

    public InternalConversationSnapshotService(
        ApplicationDbContext appDbContext,
        IInternalConversationRepository internalConversationRepository,
        IInternalConversationSnapshotExportService internalConversationSnapshotExportService,
        ICacheManagerService cacheManagerService,
        IAzureBlobStorageService azureBlobStorageService,
        ILogger<InternalConversationSnapshotService> logger)
    {
        _appDbContext = appDbContext;
        _internalConversationRepository = internalConversationRepository;
        _internalConversationSnapshotExportService = internalConversationSnapshotExportService;
        _cacheManagerService = cacheManagerService;
        _azureBlobStorageService = azureBlobStorageService;
        _logger = logger;
    }

    public async Task EnrollExportConversationSnapshot(ExportConversationSnapshotRequest request, string taskId)
    {
        var jsonSerializerSettings = new JsonSerializerSettings
        {
            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
            NullValueHandling = NullValueHandling.Ignore,
            ContractResolver = new CamelCasePropertyNamesContractResolver()
        };

        // Convert to UTC
        DateTimeOffset? startInUtc = request.Start.HasValue
            ? new DateTimeOffset(request.Start.Value.DateTime, TimeSpan.Zero)
            : null;
        DateTimeOffset? endInUtc = request.End.HasValue
            ? new DateTimeOffset(request.End.Value.DateTime, TimeSpan.Zero)
            : null;

        try
        {
            // Save task to storage
            await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                keyName: taskId,
                obj: new InternalConversationSnapshotExportProgress
                {
                    TaskId = taskId,
                    JobType = "Export Conversation Snapshot",
                    Status = "In Progress",
                    CompanyId = request.CompanyId,
                    Start = startInUtc,
                    End = endInUtc,
                    Limit = request.Limit
                },
                customDuration: TimeSpan.FromDays(30),
                jsonSerializerSettings);

            var fileName = request.Start is not null && request.End is not null
                ? $"conversation/snapshot-{request.Start.Value.Date:yyyy-MM-dd} - {request.End.Value.Date:yyyy-MM-dd}"
                : "conversation/snapshot";

            var conversationSnapshot = await FindConversationSnapshotsAsync(
                companyId: request.CompanyId,
                startDate: startInUtc,
                endDate: endInUtc,
                limit: request.Limit);

            var file = await _internalConversationSnapshotExportService.ExportConversationSnapshotAsync(
                conversationSnapshot,
                request.FileFormat,
                fileName);

            // Save file to storage
            await _azureBlobStorageService.UploadFileAsBlob(
                new MemoryStream(file.FileContents),
                $"exportconversation/snapshot-{taskId}",
                "internalsnapshotdata",
                "text/csv");

            // Update task status
            await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                keyName: taskId,
                obj: new InternalConversationSnapshotExportProgress
                {
                    TaskId = taskId,
                    JobType = "Export Conversation Snapshot",
                    Status = "Completed",
                    FileName = file.Filename,
                    CompanyId = request.CompanyId,
                    Start = startInUtc,
                    End = endInUtc,
                    Limit = request.Limit
                },
                customDuration: TimeSpan.FromDays(30),
                jsonSerializerSettings);

            // Delete the file after 30 days
            BackgroundJob.Schedule<IAzureBlobStorageService>(
                x => x.DeleteFromAzureBlob(
                    $"exportconversation/snapshot-{taskId}",
                    "internalsnapshotdata"),
                TimeSpan.FromDays(30));
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Error enrolling export conversation snapshot task {TaskId}",
                taskId);

            await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                keyName: taskId,
                obj: new InternalConversationSnapshotExportProgress
                {
                    TaskId = taskId,
                    JobType = "Export Conversation Snapshot",
                    Status = "Failed",
                    CompanyId = request.CompanyId,
                    Start = startInUtc,
                    End = endInUtc,
                    Limit = request.Limit
                },
                customDuration: TimeSpan.FromDays(30),
                jsonSerializerSettings);
        }
    }

    public async Task<InternalConversationSnapshotExportProgress> GetExportConversationSnapshotProgressAsync(
        string taskId)
    {
        try
        {
            var cacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(
                keyName: taskId);

            return !string.IsNullOrWhiteSpace(cacheData)
                ? JsonConvert.DeserializeObject<InternalConversationSnapshotExportProgress>(cacheData)
                : null;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Error getting export conversation snapshot progress {TaskId}",
                taskId);

            return null;
        }
    }

    public async Task<(byte[] FileContents, string ContentType)>
        DownloadExportConversationSnapshotAsync(
            string taskId)
    {
        var file = await _azureBlobStorageService.DownloadFromAzureBlob(
            $"exportconversation/snapshot-{taskId}",
            "internalsnapshotdata");

        return file == null ? (null, null) : (file.ToArray(), "text/csv");
    }

    private async Task<List<ConversationMessage>> FindConversationSnapshotsAsync(
        string companyId,
        string conversationId = null,
        DateTimeOffset? startDate = null,
        DateTimeOffset? endDate = null,
        int? offset = null,
        int? limit = null)
    {
        offset ??= 0;

        var history = new List<ConversationMessage>();

        var timeZoneId = await _appDbContext.CompanyCompanies
            .Where(x => x.Id == companyId)
            .Select(x => x.TimeZoneInfoId)
            .FirstOrDefaultAsync();

        startDate = startDate?.SetTimeZoneUtcOffset(timeZoneId);
        endDate = endDate?.SetTimeZoneUtcOffset(timeZoneId);

        while (true)
        {
            // Get 500 records each time
            var conversationMessages = await _internalConversationRepository.FindConversationMessagesAsync(
                companyId: companyId,
                conversationId: conversationId,
                startDate: startDate,
                endDate: endDate,
                offset: offset,
                limit: 500,
                enableNoTracking: true,
                sortOrder: "asc");

            history.AddRange(conversationMessages);
            offset += conversationMessages.Count;

            if (conversationMessages.Count == 0)
            {
                break;
            }

            if (!limit.HasValue || history.Count < limit)
            {
                continue;
            }

            history = history.Take(limit.Value).ToList();

            break;
        }

        return history;
    }
}