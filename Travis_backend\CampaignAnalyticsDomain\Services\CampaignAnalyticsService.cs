﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Travis_backend.CampaignAnalyticsDomain.Constants;
using Travis_backend.CampaignAnalyticsDomain.Models;
using Travis_backend.CampaignAnalyticsDomain.Utils;
using Travis_backend.CampaignAnalyticsDomain.ViewModels;

namespace Travis_backend.CampaignAnalyticsDomain.Services;

public interface ICampaignAnalyticsService
{
    Task<CampaignCommonMetrics> GetCampaignCommonMetricsByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default);

    Task<CampaignCommonMetrics> GetCampaignCommonMetricsByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default);

    Task<List<CampaignMessageOverviewViewModel>> GetCampaignMessageOverviewByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        string campaignMessageStatus,
        string orderBy,
        string direction,
        int offset,
        int limit,
        ReplyWindow? replyWindow,
        CancellationToken cancellationToken = default);

    Task<List<CampaignMessageOverviewViewModel>> GetCampaignMessageOverviewByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        string campaignMessageStatus,
        string orderBy,
        string direction,
        int offset,
        int limit,
        ReplyWindow? replyWindow,
        CancellationToken cancellationToken = default);

    Task<List<string>> GetUserProfileIdsByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        string campaignMessageStatus,
        ReplyWindow? replyWindow,
        CancellationToken cancellationToken = default);

    Task<List<string>> GetUserProfileIdsByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        string campaignMessageStatus,
        ReplyWindow? replyWindow,
        CancellationToken cancellationToken = default);
}

public class CampaignAnalyticsService : ICampaignAnalyticsService
{
    private readonly ICampaignAnalyticsRepository _analyticsRepository;

    public CampaignAnalyticsService(
        ICampaignAnalyticsRepository analyticsRepository)
    {
        _analyticsRepository = analyticsRepository;
    }

    public async Task<CampaignCommonMetrics> GetCampaignCommonMetricsByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var metricDataPoints = await _analyticsRepository.GetCountsPerMessageStatusByBroadcastAsync(
            companyId,
            broadcastCampaignId,
            cancellationToken);

        var repliedCount = await _analyticsRepository.GetRepliedCountByBroadcastAsync(
            companyId,
            broadcastCampaignId,
            replyWindow,
            cancellationToken);

        return CampaignCommonMetricsCalculator.Calculate(metricDataPoints, repliedCount);
    }

    public async Task<CampaignCommonMetrics> GetCampaignCommonMetricsByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var metricDataPoints = await _analyticsRepository.GetCountsPerMessageStatusByAnalyticTagAsync(
            companyId,
            analyticTag,
            cancellationToken);

        var repliedCount = await _analyticsRepository.GetRepliedCountByAnalyticTagAsync(
            companyId,
            analyticTag,
            replyWindow,
            cancellationToken);

        return CampaignCommonMetricsCalculator.Calculate(metricDataPoints, repliedCount);
    }

    public async Task<List<CampaignMessageOverviewViewModel>> GetCampaignMessageOverviewByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        string campaignMessageStatus,
        string orderBy,
        string direction,
        int offset,
        int limit,
        ReplyWindow? replyWindow,
        CancellationToken cancellationToken = default)
    {
        var campaignMessageOverviewVMs = campaignMessageStatus == CampaignMessageStatuses.Replied
            ? await GetCampaignMessageOverviewOfRepliedByBroadcastAsync(
                companyId,
                broadcastCampaignId,
                orderBy,
                direction,
                offset,
                limit,
                replyWindow,
                cancellationToken)
            : await GetCampaignMessageOverviewOfCommonStatusesByBroadcastAsync(
                companyId,
                broadcastCampaignId,
                campaignMessageStatus,
                orderBy,
                direction,
                offset,
                limit,
                replyWindow,
                cancellationToken);

        return campaignMessageOverviewVMs;
    }

    public async Task<List<CampaignMessageOverviewViewModel>> GetCampaignMessageOverviewByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        string campaignMessageStatus,
        string orderBy,
        string direction,
        int offset,
        int limit,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var campaignMessageOverviewVMs = campaignMessageStatus == CampaignMessageStatuses.Replied
            ? await GetCampaignMessageOverviewOfRepliedByAnalyticTagAsync(
                companyId,
                analyticTag,
                orderBy,
                direction,
                offset,
                limit,
                replyWindow,
                cancellationToken)
            : await GetCampaignMessageOverviewOfCommonStatusesByAnalyticTagAsync(
                companyId,
                analyticTag,
                campaignMessageStatus,
                orderBy,
                direction,
                offset,
                limit,
                replyWindow,
                cancellationToken);

        return campaignMessageOverviewVMs;
    }

    public async Task<List<string>> GetUserProfileIdsByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        string campaignMessageStatus,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var userProfileIds = campaignMessageStatus == CampaignMessageStatuses.Replied
            ? await _analyticsRepository.GetUserProfileIdsOfRepliedByBroadcastAsync(
                companyId,
                broadcastCampaignId,
                replyWindow,
                cancellationToken)
            : await _analyticsRepository.GetUserProfileIdsOfCommonStatusesByBroadcastAsync(
                companyId,
                broadcastCampaignId,
                campaignMessageStatus,
                cancellationToken);

        return userProfileIds;
    }

    public async Task<List<string>> GetUserProfileIdsByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        string campaignMessageStatus,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var userProfileIds = campaignMessageStatus == CampaignMessageStatuses.Replied
            ? await _analyticsRepository.GetUserProfileIdsOfRepliedByAnalyticTagAsync(
                companyId,
                analyticTag,
                replyWindow,
                cancellationToken)
            : await _analyticsRepository.GetUserProfileIdsOfCommonStatusesByAnalyticTagAsync(
                companyId,
                analyticTag,
                campaignMessageStatus,
                cancellationToken);

        return userProfileIds;
    }

    private async Task<List<CampaignMessageOverviewViewModel>> GetCampaignMessageOverviewOfCommonStatusesByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        string campaignMessageStatus,
        string orderBy,
        string direction,
        int offset,
        int limit,
        ReplyWindow? replyWindow,
        CancellationToken cancellationToken = default)
    {
        var campaignMessageOverviews = await _analyticsRepository.GetMessageOverviewByBroadcastAsync(
            companyId,
            broadcastCampaignId,
            campaignMessageStatus,
            orderBy,
            direction,
            offset,
            limit,
            cancellationToken);

        var campaignMessageOverviewVMs = campaignMessageOverviews
            .Select(x => new CampaignMessageOverviewViewModel(x))
            .ToList();

        if (campaignMessageStatus != CampaignMessageStatuses.Bounced)
        {
            await AssignRepliedStatusAsync(campaignMessageOverviewVMs, replyWindow, companyId, cancellationToken);
        }

        return campaignMessageOverviewVMs;
    }

    private async Task<List<CampaignMessageOverviewViewModel>> GetCampaignMessageOverviewOfCommonStatusesByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        string campaignMessageStatus,
        string orderBy,
        string direction,
        int offset,
        int limit,
        ReplyWindow? replyWindow,
        CancellationToken cancellationToken = default)
    {

        var campaignMessageOverviews = await _analyticsRepository.GetMessageOverviewByAnalyticTagAsync(
            companyId,
            analyticTag,
            campaignMessageStatus,
            orderBy,
            direction,
            offset,
            limit,
            cancellationToken);

        var campaignMessageOverviewVMs = campaignMessageOverviews
            .Select(x => new CampaignMessageOverviewViewModel(x))
            .ToList();

        if (campaignMessageStatus != CampaignMessageStatuses.Bounced)
        {
            await AssignRepliedStatusAsync(campaignMessageOverviewVMs, replyWindow, companyId, cancellationToken);
        }

        return campaignMessageOverviewVMs;
    }

    private async Task<List<CampaignMessageOverviewViewModel>> GetCampaignMessageOverviewOfRepliedByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        string orderBy,
        string direction,
        int offset,
        int limit,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var campaignMessageOverviews = await _analyticsRepository.GetRepliedMessageOverviewByBroadcastAsync(
            companyId,
            broadcastCampaignId,
            replyWindow,
            orderBy,
            direction,
            offset,
            limit,
            cancellationToken);

        // manually assign status to replied
        var campaignMessageOverviewVMs = campaignMessageOverviews.Select(
                x => new CampaignMessageOverviewViewModel(x)
                {
                    Status = CampaignMessageStatuses.Replied
                })
            .ToList();

        return campaignMessageOverviewVMs;
    }

    private async Task<List<CampaignMessageOverviewViewModel>> GetCampaignMessageOverviewOfRepliedByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        string orderBy,
        string direction,
        int offset,
        int limit,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var campaignMessageOverviews = await _analyticsRepository.GetRepliedMessageOverviewByAnalyticTagAsync(
            companyId,
            analyticTag,
            replyWindow,
            orderBy,
            direction,
            offset,
            limit,
            cancellationToken);

        // manually assign status to replied
        var campaignMessageOverviewVMs = campaignMessageOverviews.Select(
                x => new CampaignMessageOverviewViewModel(x)
                {
                    Status = CampaignMessageStatuses.Replied
                })
            .ToList();

        return campaignMessageOverviewVMs;
    }

    private async Task AssignRepliedStatusAsync(
        List<CampaignMessageOverviewViewModel> campaignMessageOverviewVMs,
        ReplyWindow replyWindow,
        string companyId,
        CancellationToken cancellationToken)
    {
        var candidateMessageIds = campaignMessageOverviewVMs
            .Where(x => x.Status != CampaignMessageStatuses.Bounced)
            .Select(x => x.MessageId)
            .ToList();

        if (candidateMessageIds.Count == 0)
        {
            return;
        }

        var repliedMessageIds = await _analyticsRepository.GetObjectsAsync<long>(
            CampaignAnalyticsQueryBuilder.FilterRepliedMessageIdsQueryDef(
                candidateMessageIds,
                companyId,
                replyWindow),
            cancellationToken);

        campaignMessageOverviewVMs.ForEach(x =>
        {
            if (repliedMessageIds.Contains(x.MessageId))
            {
                x.Status = CampaignMessageStatuses.Replied;
            }
        });

        // return
    }
}