﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class ApplicationDurationPlotMetric : IDashboardMetric
{
    private readonly Output<string>? _resourceId;
    private readonly Output<string>? _resourceName;

    public ApplicationDurationPlotMetric(Output<string>? resourceId, Output<string>? resourceName)
    {
        _resourceId = resourceId;
        _resourceName = resourceName;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs =
                    new[]
                    {
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "ComponentId"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "ControlType"
                            },
                            {
                                "value", "FrameControlChart"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "DashboardId"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "Dimensions"
                            },
                            {
                                "value", new Dictionary<string, object>()
                                {
                                    {
                                        "aggregation", "Sum"
                                    },
                                    {
                                        "splitBy", Array.Empty<string>()
                                    },
                                    {
                                        "xAxis", new Dictionary<string, object>()
                                        {
                                            {
                                                "name", "timestamp"
                                            },
                                            {
                                                "type", "datetime"
                                            }
                                        }
                                    },
                                    {
                                        "yAxis", new[]
                                        {
                                            new Dictionary<string, object>()
                                            {
                                                {
                                                    "name", "percentile_duration_95"
                                                },
                                                {
                                                    "type", "real"
                                                }
                                            },
                                            new Dictionary<string, object>()
                                            {
                                                {
                                                    "name", "percentile_duration_99"
                                                },
                                                {
                                                    "type", "real"
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "DraftRequestParameters"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "IsQueryContainTimeRange"
                            },
                            {
                                "value", false
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "LegendOptions"
                            },
                            {
                                "value", new Dictionary<string, object>()
                                {
                                    {
                                        "isEnabled", true
                                    },
                                    {
                                        "position", "Bottom"
                                    }
                                }
                            },
                            {
                                "isOptional", true
                            }
                        },
                        // new Dictionary<string, object>()
                        // {
                        //     {
                        //         "name", "PartId"
                        //     },
                        //     {
                        //         "value", _partId
                        //     },
                        //     {
                        //         "isOptional", true
                        //     }
                        // },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "PartSubTitle"
                            },
                            {
                                "value", _resourceName!
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "PartTitle"
                            },
                            {
                                "value", "ApplicationDurationPlot"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "Query"
                            },
                            {
                                "value",
                                "requests\n| summarize percentile(duration, 95), percentile(duration, 99) by bin(timestamp, 15m)\n| order by timestamp\n\n"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "Scope"
                            },
                            {
                                "value", new Dictionary<string, object>()
                                {
                                    {
                                        "resourceIds", new[]
                                        {
                                            _resourceId!
                                        }
                                    }
                                }
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "SpecificChart"
                            },
                            {
                                "value", "StackedColumn"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "TimeRange"
                            },
                            {
                                "value", "PT12H"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "Version"
                            },
                            {
                                "value", "2.0"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "resourceTypeMode"
                            },
                            {
                                "isOptional", true
                            }
                        }
                    },
                Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                Settings =
                {
                    new Dictionary<string, object>()
                    {
                        {
                            "content", new Dictionary<string, object>()
                            {
                                {
                                    "Query",
                                    "requests\n| where url !contains \"healthcheck\" and url !contains \"__health\"\n| summarize percentile(duration, 95), percentile(duration, 99) by bin(timestamp, 10s)\n| order by timestamp\n\n"
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}