using Travis_backend.MessageDomain.ChannelWebhookProvider.Helpers;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.MessageDomain
{
    [TestFixture]
    public class ConversationStatusValidatorTests
    {
        [TestCase(MessageStatus.Sent, MessageStatus.Sent, false)]
        [TestCase(MessageStatus.Sending, MessageStatus.Sent, true)]
        [TestCase(MessageStatus.Sending, MessageStatus.Received, true)]
        [TestCase(MessageStatus.Sending, MessageStatus.Read, true)]
        [TestCase(MessageStatus.Sending, MessageStatus.Failed, true)]
        [TestCase(MessageStatus.Sending, MessageStatus.Undelivered, true)]
        [TestCase(MessageStatus.Sending, MessageStatus.Deleted, true)]
        [TestCase(MessageStatus.Sending, MessageStatus.OutOfCredit, true)]
        public void ValidateIsCanPropagateNewConversationStatus_ShouldReturnExpectedResult_WhenOldStatusAndNewStatusAreProvided(
            MessageStatus initialStatus,
            MessageStatus newStatus,
            bool expectedResult)
        {
            var result = ConversationStatusValidator.ValidateIsCanPropagateNewConversationStatus(initialStatus, newStatus);
            Assert.AreEqual(expectedResult, result);
        }

        [TestCase(MessageStatus.Sent)]
        [TestCase(MessageStatus.Received)]
        [TestCase(MessageStatus.Read)]
        [TestCase(MessageStatus.Failed)]
        [TestCase(MessageStatus.Undelivered)]
        [TestCase(MessageStatus.Deleted)]
        public void ValidateIsCanPropagateNewConversationStatus_ShouldReturnFalse_WhenOldStatusIsRead(
            MessageStatus newStatus)
        {
            var result = ConversationStatusValidator.ValidateIsCanPropagateNewConversationStatus(MessageStatus.Read, newStatus);
            Assert.IsFalse(result);
        }

        [TestCase(MessageStatus.Sent)]
        [TestCase(MessageStatus.Received)]
        [TestCase(MessageStatus.Read)]
        [TestCase(MessageStatus.Failed)]
        [TestCase(MessageStatus.Deleted)]
        public void ValidateIsCanPropagateNewConversationStatus_ShouldReturnTrue_WhenOldStatusIsOutOfCredit(
            MessageStatus newStatus)
        {
            var result = ConversationStatusValidator.ValidateIsCanPropagateNewConversationStatus(MessageStatus.OutOfCredit, newStatus);
            Assert.IsTrue(result);
        }

        [TestCase(MessageStatus.Sending)]
        [TestCase(MessageStatus.Sent)]
        [TestCase(MessageStatus.Received)]
        [TestCase(MessageStatus.Read)]
        [TestCase(MessageStatus.Failed)]
        [TestCase(MessageStatus.Undelivered)]
        [TestCase(MessageStatus.Deleted)]
        [TestCase(MessageStatus.OutOfCredit)]
        public void ValidateIsCanPropagateNewConversationStatus_ShouldReturnTrue_WhenOldStatusIsScheduled(
            MessageStatus newStatus)
        {
            var result = ConversationStatusValidator.ValidateIsCanPropagateNewConversationStatus(MessageStatus.Scheduled, newStatus);
            Assert.IsTrue(result);
        }

        [TestCase(MessageStatus.Read)]
        [TestCase(MessageStatus.Received)]
        [TestCase(MessageStatus.Deleted)]
        public void ValidateIsCanPropagateNewConversationStatus_ShouldReturnFalse_WhenOldStatusIsFailed(
            MessageStatus newStatus)
        {
            var result = ConversationStatusValidator.ValidateIsCanPropagateNewConversationStatus(MessageStatus.Failed, newStatus);
            Assert.IsFalse(result);
        }

        [TestCase(MessageStatus.PaymentLinkPaid)]
        [TestCase(MessageStatus.PaymentLinkCanceled)]
        public void ValidateIsCanPropagateNewConversationStatus_ShouldReturnTrue_WhenOldStatusIsPaymentLinkPending(
            MessageStatus newStatus)
        {
            var result = ConversationStatusValidator.ValidateIsCanPropagateNewConversationStatus(MessageStatus.PaymentLinkPending, newStatus);
            Assert.IsTrue(result);
        }

        [TestCase(MessageStatus.PaymentLinkPaid, MessageStatus.PaymentLinkPending)]
        [TestCase(MessageStatus.PaymentLinkCanceled, MessageStatus.PaymentLinkPending)]
        public void ValidateIsCanPropagateNewConversationStatus_ShouldReturnTrue_WhenOldStatusIsPaymentLinkPaidOrCanceled(
            MessageStatus initialStatus,
            MessageStatus newStatus)
        {
            var result = ConversationStatusValidator.ValidateIsCanPropagateNewConversationStatus(initialStatus, newStatus);
            Assert.IsTrue(result);
        }
    }
}