using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Database.Services;

namespace Travis_backend.ContactDomain.Services.Cache;

public class ContactCacheService : IContactCacheService
{
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IDbContextService _dbContextService;
    private readonly ILogger<ContactCacheService> _logger;

    public ContactCacheService(
        ICacheManagerService cacheManagerService,
        IDbContextService dbContextService,
        ILogger<ContactCacheService> logger)
    {
        _cacheManagerService = cacheManagerService;
        _dbContextService = dbContextService;
        _logger = logger;
    }

    public async Task<List<CompanyCustomUserProfileField>> GetCompanyCustomUserProfileFieldsCachedAsync(string companyId)
    {
        var cacheKeyPattern = new ContactCustomUserProfileFieldsCacheKeyPattern(companyId);

        var cachedFields = await _cacheManagerService.GetAndSaveCacheAsync(
            cacheKeyPattern,
            async () => await GetCompanyCustomUserProfileFieldsFromDbAsync(companyId));

        return cachedFields;
    }

    public async Task InvalidateCompanyCustomUserProfileFieldsCacheAsync(string companyId)
    {
        var cacheKeyPattern = new ContactCustomUserProfileFieldsCacheKeyPattern(companyId);

        try
        {
            await _cacheManagerService.DeleteCacheAsync(cacheKeyPattern);
            _logger.LogInformation(
                "Successfully invalidated contact custom user profile fields cache for company {CompanyId}",
                companyId);
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex,
                "Failed to invalidate contact custom user profile fields cache for company {CompanyId}",
                companyId);
        }
    }

    private async Task<List<CompanyCustomUserProfileField>> GetCompanyCustomUserProfileFieldsFromDbAsync(string companyId)
    {
        var dbContext = _dbContextService.GetDbContext();

        var fields = await dbContext.CompanyCustomUserProfileFields
            .Where(x => x.CompanyId == companyId)
            .Include(x => x.CustomUserProfileFieldOptions)
            .AsNoTracking()
            .ToListAsync();

        return fields;
    }
}