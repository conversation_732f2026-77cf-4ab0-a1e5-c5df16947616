using System.Collections.Generic;
using System.Linq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;

namespace Travis_backend.CompanyDomain.Utils;

public static class StaffSortingExtensions
{
    /// <summary>
    /// Apply sort to the given <paramref name="staffs"/> by the given <paramref name="sortBy"/> field,
    /// in either ascending or descending order based on <paramref name="isDescending"/>.
    /// </summary>
    /// <param name="staffs">The list of staffs to sort.</param>
    /// <param name="sortBy">The field to sort the staffs by.</param>
    /// <param name="isDescending">Whether to sort the staffs in descending order.</param>
    /// <returns>The sorted list of staffs.</returns>
    public static IEnumerable<StaffOverviewResponse> ApplySort(
        this IEnumerable<StaffOverviewResponse> staffs,
        StaffSortField? sortBy,
        bool isDescending)
    {
        if (!sortBy.HasValue)
        {
            return staffs;
        }

        var orderedStaffs = sortBy.Value switch
        {
            StaffSortField.DisplayName => isDescending
                ? staffs.OrderByDescending(x => x.DisplayName)
                : staffs.OrderBy(x => x.DisplayName),

            StaffSortField.Email => isDescending
                ? staffs.OrderByDescending(x => x.Email)
                : staffs.OrderBy(x => x.Email),

            StaffSortField.Role => isDescending
                ? staffs.OrderByDescending(x => x.Role)
                : staffs.OrderBy(x => x.Role),

            StaffSortField.Status => isDescending
                ? staffs.OrderByDescending(x => x.Status)
                : staffs.OrderBy(x => x.Status),

            StaffSortField.Position => isDescending
                ? staffs.OrderByDescending(x => x.Position)
                : staffs.OrderBy(x => x.Position),

            StaffSortField.CreatedAt => isDescending
                ? staffs.OrderByDescending(x => x.CreatedAt)
                : staffs.OrderBy(x => x.CreatedAt),

            StaffSortField.InvitePending => isDescending
                ? staffs.OrderByDescending(x => x.IsInvitationExpired)
                : staffs.OrderBy(x => x.IsInvitationExpired),

            _ => staffs
        };

        return orderedStaffs;
    }

    /// <summary>
    /// Apply multiple sort fields to the given <paramref name="staffs"/> in order,
    /// in either ascending or descending order based on <paramref name="isDescending"/>.
    /// </summary>
    /// <param name="staffs">The list of staffs to sort.</param>
    /// <param name="sortBy">The list of fields to sort the staffs by.</param>
    /// <param name="isDescending">Whether to sort the staffs in descending order.</param>
    /// <returns>The sorted list of staffs.</returns>
    public static IEnumerable<StaffOverviewResponse> ApplySort(
        this IEnumerable<StaffOverviewResponse> staffs,
        List<StaffSortField> sortBy,
        bool isDescending)
    {
        var orderedStaffs = staffs;

        foreach (var sortField in sortBy)
        {
            orderedStaffs = sortField switch
            {
                StaffSortField.DisplayName => isDescending
                    ? orderedStaffs.OrderByDescending(x => x.DisplayName)
                    : orderedStaffs.OrderBy(x => x.DisplayName),
                StaffSortField.Email => isDescending
                    ? orderedStaffs.OrderByDescending(x => x.Email)
                    : orderedStaffs.OrderBy(x => x.Email),
                StaffSortField.Role => isDescending
                    ? orderedStaffs.OrderByDescending(x => x.Role)
                    : orderedStaffs.OrderBy(x => x.Role),
                StaffSortField.Status => isDescending
                    ? orderedStaffs.OrderByDescending(x => x.Status)
                    : orderedStaffs.OrderBy(x => x.Status),
                StaffSortField.Position => isDescending
                    ? orderedStaffs.OrderByDescending(x => x.Position)
                    : orderedStaffs.OrderBy(x => x.Position),
                StaffSortField.CreatedAt => isDescending
                    ? orderedStaffs.OrderByDescending(x => x.CreatedAt)
                    : orderedStaffs.OrderBy(x => x.CreatedAt),
                StaffSortField.InvitePending => isDescending
                    ? orderedStaffs.OrderByDescending(x => x.IsInvitationPending)
                    : orderedStaffs.OrderBy(x => x.IsInvitationPending),
                StaffSortField.InviteExpired => isDescending
                    ? orderedStaffs.OrderByDescending(x => x.InvitationExpiredAt)
                    : orderedStaffs.OrderBy(x => x.InvitationExpiredAt),
                _ => orderedStaffs
            };
        }

        return orderedStaffs;
    }
}
