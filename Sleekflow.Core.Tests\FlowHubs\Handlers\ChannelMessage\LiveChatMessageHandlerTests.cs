using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.FlowHubs.Handlers.ChannelMessage;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.FlowHubs.Handlers.ChannelMessage;

[TestFixture]
public class LiveChatMessageHandlerTests
{
  private LiveChatMessageHandler _handler;
  private ILogger<LiveChatMessageHandler> _mockLogger;

  [SetUp]
  public void SetUp()
  {
    _mockLogger = Substitute.For<ILogger<LiveChatMessageHandler>>();

    _handler = new LiveChatMessageHandler(
        null,
        _mockLogger);
  }

  [Test]
  public void ChannelType_ShouldReturnLiveChat()
  {
    // Act
    var result = _handler.ChannelType;

    // Assert
    Assert.That(result, Is.EqualTo(ChannelTypes.LiveChat));
  }

  [Test]
  public async Task PrepareConversationMessageAsync_WithTextMessage_ShouldSetCorrectProperties()
  {
    // Arrange
    var companyId = "test-company-id";
    var channelIdentityId = "test-channel-id";
    var messageType = "text";
    var messageText = "Hello, this is a test message";

    var messageBody = new MessageBody
    {
      LiveChatMessage = new LiveChatMessageObject
      {
        Message = messageText
      }
    };

    var conversation = new Conversation
    {
      Id = "test-conversation-id",
      WebClient = new WebClientSender
      {
        WebClientUUID = "test-session-id",
        Name = "Test User"
      }
    };

    var files = new List<IFormFile>();

    // Act
    var result = await _handler.PrepareConversationMessageAsync(
        companyId, channelIdentityId, messageType, messageBody, conversation, files);

    // Assert
    Assert.Multiple(() =>
    {
      Assert.That(result.Channel, Is.EqualTo(ChannelTypes.LiveChat));
      Assert.That(result.ConversationId, Is.EqualTo(conversation.Id));
      Assert.That(result.MessageType, Is.EqualTo(messageType));
      Assert.That(result.MessageContent, Is.EqualTo(messageText));
      Assert.That(result.WebClientReceiver, Is.EqualTo(conversation.WebClient));
      Assert.That(result.DeliveryType, Is.EqualTo(DeliveryType.FlowHubAction));
      Assert.That(result.AnalyticTags, Is.Null);
      Assert.That(result.IsSentFromSleekflow, Is.True);
      Assert.That(files, Is.Empty);
    });
  }

  [Test]
  public void PrepareConversationMessageAsync_WithUnsupportedMessageType_ShouldThrowNotImplementedException()
  {
    // Arrange
    var companyId = "test-company-id";
    var channelIdentityId = "test-channel-id";
    var messageType = "unsupported";
    var messageBody = new MessageBody();
    var conversation = new Conversation { Id = "test-conversation-id" };
    var files = new List<IFormFile>();

    // Act & Assert
    var exception = Assert.ThrowsAsync<NotImplementedException>(async () =>
        await _handler.PrepareConversationMessageAsync(
            companyId, channelIdentityId, messageType, messageBody, conversation, files));

    Assert.That(exception.Message, Is.EqualTo($"Message type {messageType} not implemented for {ChannelTypes.LiveChat}"));
  }
}