using System;
using System.Collections.Generic;

namespace Travis_backend.AnalyticsDomain.ViewModels;

public class GetConversationBroadcastMetricResponse
{
    public string StartDate { get; set; }

    public string EndDate { get; set; }

    public BroadcastMessageMetricViewModel Summary { get; set; }

    public List<BroadcastMessageMetricViewModel> DailyLogs { get; set; }

    public DateTime? LastUpdateTime { get; set; }

    public GetConversationBroadcastMetricResponse(
        DateOnly startDate,
        DateOnly endDate,
        BroadcastMessageMetricViewModel summary,
        List<BroadcastMessageMetricViewModel> dailyLogs,
        DateTime? lastUpdateTime = null)
    {
        StartDate = startDate.ToString("yyyy-MM-dd");
        EndDate = endDate.ToString("yyyy-MM-dd");
        Summary = summary;
        DailyLogs = dailyLogs;
        LastUpdateTime = lastUpdateTime;
    }
}