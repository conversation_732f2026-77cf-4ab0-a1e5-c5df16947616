using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Perf.Components.Configs.SleekflowCore;

public class SqlPerformanceConfig
{
    [JsonProperty("from_raw_sql")]
    public string FromRawSql { get; set; }

    [JsonProperty("is_and_condition_enabled")]
    public string IsAndConditionEnabled { get; set; }

    [JsonProperty("is_or_condition_enabled")]
    public string IsOrConditionEnabled { get; set; }

    [JsonProperty("is_conversation_analytics_condition_enabled")]
    public string? IsConversationAnalyticsConditionEnabled { get; set; }

    [JsonProperty("is_shopify_order_statistics_enabled")]
    public string IsShopifyOrderStatisticsEnabled { get; set; }

    [JsonProperty("is_sales_performance_enabled")]
    public string IsSalesPerformanceEnabled { get; set; }

    [JsonProperty("is_public_api_new_upsert_enabled")]
    public string IsPublicApiNewUpsertEnabled { get; set; }

    [JsonProperty("is_zapier_new_upsert_enabled")]
    public string IsZapierNewUpsertEnabled { get; set; }

    public SqlPerformanceConfig(
        string fromRawSql,
        string isAndConditionEnabled,
        string isOrConditionEnabled,
        string? isConversationAnalyticsConditionEnabled,
        string isShopifyOrderStatisticsEnabled,
        string isSalesPerformanceEnabled,
        string isPublicApiNewUpsertEnabled,
        string isZapierNewUpsertEnabled)
    {
        FromRawSql = fromRawSql;
        IsAndConditionEnabled = isAndConditionEnabled;
        IsOrConditionEnabled = isOrConditionEnabled;
        IsConversationAnalyticsConditionEnabled = isConversationAnalyticsConditionEnabled;
        IsShopifyOrderStatisticsEnabled = isShopifyOrderStatisticsEnabled;
        IsSalesPerformanceEnabled = isSalesPerformanceEnabled;
        IsPublicApiNewUpsertEnabled = isPublicApiNewUpsertEnabled;
        IsZapierNewUpsertEnabled = isZapierNewUpsertEnabled;
    }
}