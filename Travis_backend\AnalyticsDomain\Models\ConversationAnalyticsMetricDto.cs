﻿using System;
using Travis_backend.AnalyticsDomain.ViewModels;

namespace Travis_backend.AnalyticsDomain.Models;

/// <summary>
/// For calculating the metrics data.
/// Act as the middle layer between the raw data and the view model.
/// </summary>
public class ConversationAnalyticsMetricDto
{
    public DateOnly? PeriodStartDate { get; set; }

    public int TotalConversations { get; set; }

    public int TotalActiveConversations { get; set; }

    public int MessagesSent { get; set; }

    public int MessagesReceived { get; set; }

    public int MessagesSendFailed { get; set; }

    public int NewEnquiries { get; set; }

    public int NewContacts { get; set; }

    public int ResolutionTimeInSecondsNumerator { get; set; }

    public int ResolutionTimeInSecondsDenominator { get; set; }

    public int FirstResponseTimeInSecondsNumerator { get; set; }

    public int FirstResponseTimeInSecondsDenominator { get; set; }

    public int ResponseTimeInSecondsNumerator { get; set; }

    public int ResponseTimeInSecondsDenominator { get; set; }

    public ConversationAnalyticsMetricDto(
        DateOnly? periodStartDate,
        int totalConversations,
        int totalActiveConversations,
        int messagesSent,
        int messagesReceived,
        int messagesSendFailed,
        int newEnquiries,
        int newContacts,
        int resolutionTimeInSecondsNumerator,
        int resolutionTimeInSecondsDenominator,
        int firstResponseTimeInSecondsNumerator,
        int firstResponseTimeInSecondsDenominator,
        int responseTimeInSecondsNumerator,
        int responseTimeInSecondsDenominator)
    {
        PeriodStartDate = periodStartDate;
        TotalConversations = totalConversations;
        TotalActiveConversations = totalActiveConversations;
        MessagesSent = messagesSent;
        MessagesReceived = messagesReceived;
        MessagesSendFailed = messagesSendFailed;
        NewEnquiries = newEnquiries;
        NewContacts = newContacts;
        ResolutionTimeInSecondsNumerator = resolutionTimeInSecondsNumerator;
        ResolutionTimeInSecondsDenominator = resolutionTimeInSecondsDenominator;
        FirstResponseTimeInSecondsNumerator = firstResponseTimeInSecondsNumerator;
        FirstResponseTimeInSecondsDenominator = firstResponseTimeInSecondsDenominator;
        ResponseTimeInSecondsNumerator = responseTimeInSecondsNumerator;
        ResponseTimeInSecondsDenominator = responseTimeInSecondsDenominator;
    }

    public ConversationAnalyticsMetricDto(ConversationAnalyticsMetric metric)
    {
        PeriodStartDate = metric.PeriodStartDate;
        TotalConversations = metric.TotalConversations ?? 0;
        TotalActiveConversations = metric.TotalActiveConversations ?? 0;
        MessagesSent = metric.MessagesSent ?? 0;
        MessagesReceived = metric.MessagesReceived ?? 0;
        MessagesSendFailed = metric.MessagesSendFailed ?? 0;
        NewEnquiries = metric.NewEnquiries ?? 0;
        NewContacts = metric.NewContacts ?? 0;
        ResolutionTimeInSecondsNumerator = metric.ResolutionTimeInSecondsNumerator ?? 0;
        ResolutionTimeInSecondsDenominator = metric.ResolutionTimeInSecondsDenominator ?? 0;
        FirstResponseTimeInSecondsNumerator = metric.FirstResponseTimeInSecondsNumerator ?? 0;
        FirstResponseTimeInSecondsDenominator = metric.FirstResponseTimeInSecondsDenominator ?? 0;
        ResponseTimeInSecondsNumerator = metric.ResponseTimeInSecondsNumerator ?? 0;
        ResponseTimeInSecondsDenominator = metric.ResponseTimeInSecondsDenominator ?? 0;
    }

    public static ConversationAnalyticsMetricDto Default(DateOnly? date = null)
    {
        return new ConversationAnalyticsMetricDto(
            date,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0);
    }

    public ConversationCommonMetricViewModel ToConversationCommonMetricViewModel(bool isPartialAnalyticAccessOnly = false)
    {
        var avgResponseTimeInSeconds = Average(ResponseTimeInSecondsNumerator, ResponseTimeInSecondsDenominator);
        var avgFirstResponseTimeInSeconds = Average(FirstResponseTimeInSecondsNumerator, FirstResponseTimeInSecondsDenominator);
        var avgResolutionTimeInSeconds = Average(ResolutionTimeInSecondsNumerator, ResolutionTimeInSecondsDenominator);

        if (isPartialAnalyticAccessOnly)
        {
            return new ConversationCommonMetricViewModel(
                PeriodStartDate,
                TotalConversations,
                TotalActiveConversations,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0);
        }

        return new ConversationCommonMetricViewModel(
            PeriodStartDate,
            TotalConversations,
            TotalActiveConversations,
            MessagesSent,
            MessagesReceived,
            MessagesSendFailed,
            avgResponseTimeInSeconds,
            avgFirstResponseTimeInSeconds,
            avgResolutionTimeInSeconds,
            NewEnquiries,
            NewContacts);
    }

    private static int Average(int numerator, int denominator)
    {
        return denominator <= 0 ? 0 : numerator / denominator;
    }
}