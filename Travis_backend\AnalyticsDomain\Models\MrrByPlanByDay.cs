using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Travis_backend.AnalyticsDomain.Models;

[Table("mrr_by_plan_by_day")]
public class MrrByPlanByDay
{
    [Column("id")]
    public string Id { get; set; }

    [Column("date")]
    public DateOnly Date { get; set; }

    [Column("subscription_plan_id")]
    public string SubscriptionPlanId { get; set; }

    [Column("main_subscription_plan_id")]
    public string MainSubscriptionPlanId { get; set; }

    [Column("monthly_recurring_revenue_usd")]
    public decimal MonthlyRecurringRevenueUsd { get; set; }

    [Column("companies_paid")]
    public int CompaniesPaid { get; set; }

    [Column("plans_paid")]
    public int PlansPaid { get; set; }

    [Column("to_be_deleted")]
    public byte ToBeDeleted { get; set; }
}