using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.Conversations.Rbac.RbacTestData;

public class InboxViewTestData
{
    public static IEnumerable<TestCaseData> GetAllInboxViewConversationTestCases()
    {
        var companyId = "sleekflow";
        var staffId = 1;

        var teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long>
            {
                1, 2, 3
            }
        };

        var inboxView = new InboxView(InboxViewTypes.All);


        var staff = new StaffAccessControlAggregate
        {
            StaffId = staffId,
            CompanyId = companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                teamA
            },
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = companyId,
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe,
                        RbacViewConversationsPermissions.AssignedToMyTeam,
                        RbacViewConversationsPermissions.AllAssignedConversations,
                        RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                        RbacViewConversationsPermissions.AllUnassignedConversations
                    ]
                }
            }
        };

        // convo with assign to me
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToMeConversation", CompanyId = companyId, AssigneeId = staffId
            },
            inboxView,
            true).SetName("Assigned to Me Conversation");

        // convo with assign to user with same team (without team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsContactOwnerConversation", CompanyId = companyId, AssigneeId = 2
            },
            inboxView,
            true).SetName("Team Member As Contact Owner Conversation");

        //  convo with assign to user with different team (without team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToOtherConversation", CompanyId = companyId, AssigneeId = 100
            },
            inboxView,
            true).SetName("Assigned to Non-Associated Team Member Conversation");

        // convo with assign to user with same team (with team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsContactOwnerWithAssociatedTeamConversation",
                CompanyId = companyId,
                AssigneeId = 2,
                AssignedTeamId = teamA.Id
            },
            inboxView,
            true).SetName("Team Member As Contact Owner With Associated Team Conversation");

        //  convo with assign to user with different team (with team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToOtherConversation", CompanyId = companyId, AssigneeId = 100, AssignedTeamId = 100,
            },
            inboxView,
            true).SetName("Assigned to Other With Non-Associated Team Conversation");

        // convo with assign to team with user is in
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToTeamConversation", CompanyId = companyId, AssignedTeamId = teamA.Id
            },
            inboxView,
            true).SetName("Assigned to Associated Team Conversation");

        //  convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToTeamConversation", CompanyId = companyId, AssignedTeamId = 2
            },
            inboxView,
            true).SetName("Assigned to Non-Associated Team Conversation");

        //  unassigned convo
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "unassignedConversation", CompanyId = companyId, AssigneeId = null
            },
            inboxView,
            true).SetName("Unassigned Conversation");

        // convo with user as collaborator
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId,
                        AssigneeId =
                            staffId
                    }
                },
            },
            inboxView,
            true).SetName("Assigned As Collaborator Conversation");

        // convo with team member as collaborator
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            true).SetName("Team Member As Collaborator Conversation");

        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AssignedTeamId = 1,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            true).SetName("Team Member As Collaborator Conversation With Associated Team");

        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AssignedTeamId = 2,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            true).SetName("Team Member As Collaborator Conversation With Non-Associated Team");

        // convo with user is mentioned
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "mentionedConversation",
                CompanyId = companyId,
                ChatHistory =
                [
                    new ConversationMessage
                    {
                        Channel = ChannelTypes.Note,
                        MessageAssigneeId = staffId,
                        UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1)
                    }
                ]
            },
            inboxView,
            true).SetName("Mentioned Conversation");
    }
    public static IEnumerable<TestCaseData> GetAssignedToMeInboxViewConversationTestCases()
    {
        var companyId = "sleekflow";
        var staffId = 1;

        var teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long>
            {
                1, 2, 3
            }
        };

        var inboxView = new InboxView(InboxViewTypes.AssignedToMe);


        var staff = new StaffAccessControlAggregate
        {
            StaffId = staffId,
            CompanyId = companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                teamA
            },
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = companyId,
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe,
                        RbacViewConversationsPermissions.AssignedToMyTeam,
                        RbacViewConversationsPermissions.AllAssignedConversations,
                        RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                        RbacViewConversationsPermissions.AllUnassignedConversations
                    ]
                }
            }
        };

        // convo with assign to me
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToMeConversation", CompanyId = companyId, AssigneeId = staffId
            },
            inboxView,
            true).SetName("Assigned to Me Conversation");

        // convo with assign to user with same team (without team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsContactOwnerConversation", CompanyId = companyId, AssigneeId = 2
            },
            inboxView,
            false).SetName("Team Member As Contact Owner Conversation");

        //  convo with assign to user with different team (without team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToOtherConversation", CompanyId = companyId, AssigneeId = 100
            },
            inboxView,
            false).SetName("Assigned to Non-Associated Team Member Conversation");

        // convo with assign to user with same team (with team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsContactOwnerWithAssociatedTeamConversation",
                CompanyId = companyId,
                AssigneeId = 2,
                AssignedTeamId = teamA.Id
            },
            inboxView,
            false).SetName("Team Member As Contact Owner With Associated Team Conversation");

        //  convo with assign to user with different team (with team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToOtherConversation", CompanyId = companyId, AssigneeId = 100, AssignedTeamId = 100,
            },
            inboxView,
            false).SetName("Assigned to Other With Non-Associated Team Conversation");

        // convo with assign to team with user is in
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToTeamConversation", CompanyId = companyId, AssignedTeamId = teamA.Id
            },
            inboxView,
            false).SetName("Assigned to Associated Team Conversation");

        //  convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToTeamConversation", CompanyId = companyId, AssignedTeamId = 2
            },
            inboxView,
            false).SetName("Assigned to Non-Associated Team Conversation");

        //  unassigned convo
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "unassignedConversation", CompanyId = companyId, AssigneeId = null
            },
            inboxView,
            false).SetName("Unassigned Conversation");

        // convo with user as collaborator
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId,
                        AssigneeId =
                            staffId
                    }
                },
            },
            inboxView,
            false).SetName("Assigned As Collaborator Conversation");

        // convo with team member as collaborator
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            false).SetName("Team Member As Collaborator Conversation");

        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AssignedTeamId = 1,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            false).SetName("Team Member As Collaborator Conversation With Associated Team");

        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AssignedTeamId = 2,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            false).SetName("Team Member As Collaborator Conversation With Non-Associated Team");

        // convo with user is mentioned
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "mentionedConversation",
                CompanyId = companyId,
                ChatHistory =
                [
                    new ConversationMessage
                    {
                        Channel = ChannelTypes.Note,
                        MessageAssigneeId = staffId,
                        UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1)
                    }
                ]
            },
            inboxView,
            false).SetName("Mentioned Conversation");
    }
    public static IEnumerable<TestCaseData> GetCollaborationsInboxViewConversationTestCases()
    {
        var companyId = "sleekflow";
        var staffId = 1;

        var teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long>
            {
                1, 2, 3
            }
        };

        var inboxView = new InboxView(InboxViewTypes.Collaborations);


        var staff = new StaffAccessControlAggregate
        {
            StaffId = staffId,
            CompanyId = companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                teamA
            },
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = companyId,
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe,
                        RbacViewConversationsPermissions.AssignedToMyTeam,
                        RbacViewConversationsPermissions.AllAssignedConversations,
                        RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                        RbacViewConversationsPermissions.AllUnassignedConversations
                    ]
                }
            }
        };

        // convo with assign to me
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToMeConversation", CompanyId = companyId, AssigneeId = staffId
            },
            inboxView,
            false).SetName("Assigned to Me Conversation");

        // convo with assign to user with same team (without team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsContactOwnerConversation", CompanyId = companyId, AssigneeId = 2
            },
            inboxView,
            false).SetName("Team Member As Contact Owner Conversation");

        //  convo with assign to user with different team (without team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToOtherConversation", CompanyId = companyId, AssigneeId = 100
            },
            inboxView,
            false).SetName("Assigned to Non-Associated Team Member Conversation");

        // convo with assign to user with same team (with team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsContactOwnerWithAssociatedTeamConversation",
                CompanyId = companyId,
                AssigneeId = 2,
                AssignedTeamId = teamA.Id
            },
            inboxView,
            false).SetName("Team Member As Contact Owner With Associated Team Conversation");

        //  convo with assign to user with different team (with team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToOtherConversation", CompanyId = companyId, AssigneeId = 100, AssignedTeamId = 100,
            },
            inboxView,
            false).SetName("Assigned to Other With Non-Associated Team Conversation");

        // convo with assign to team with user is in
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToTeamConversation", CompanyId = companyId, AssignedTeamId = teamA.Id
            },
            inboxView,
            false).SetName("Assigned to Associated Team Conversation");

        //  convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToTeamConversation", CompanyId = companyId, AssignedTeamId = 2
            },
            inboxView,
            false).SetName("Assigned to Non-Associated Team Conversation");

        //  unassigned convo
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "unassignedConversation", CompanyId = companyId, AssigneeId = null
            },
            inboxView,
            false).SetName("Unassigned Conversation");

        // convo with user as collaborator
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId,
                        AssigneeId =
                            staffId
                    }
                },
            },
            inboxView,
            true).SetName("Assigned As Collaborator Conversation");

        // convo with team member as collaborator
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            false).SetName("Team Member As Collaborator Conversation");

        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AssignedTeamId = 1,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            false).SetName("Team Member As Collaborator Conversation With Associated Team");

        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AssignedTeamId = 2,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            false).SetName("Team Member As Collaborator Conversation With Non-Associated Team");

        // convo with user is mentioned
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "mentionedConversation",
                CompanyId = companyId,
                ChatHistory =
                [
                    new ConversationMessage
                    {
                        Channel = ChannelTypes.Note,
                        MessageAssigneeId = staffId,
                        UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1)
                    }
                ]
            },
            inboxView,
            false).SetName("Mentioned Conversation");
    }
    public static IEnumerable<TestCaseData> GetMentionsInboxViewConversationTestCases()
    {
        var companyId = "sleekflow";
        var staffId = 1;

        var teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long>
            {
                1, 2, 3
            }
        };

        var inboxView = new InboxView(InboxViewTypes.Mentions);


        var staff = new StaffAccessControlAggregate
        {
            StaffId = staffId,
            CompanyId = companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                teamA
            },
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = companyId,
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe,
                        RbacViewConversationsPermissions.AssignedToMyTeam,
                        RbacViewConversationsPermissions.AllAssignedConversations,
                        RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                        RbacViewConversationsPermissions.AllUnassignedConversations
                    ]
                }
            }
        };

        // convo with assign to me
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToMeConversation", CompanyId = companyId, AssigneeId = staffId
            },
            inboxView,
            false).SetName("Assigned to Me Conversation");

        // convo with assign to user with same team (without team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsContactOwnerConversation", CompanyId = companyId, AssigneeId = 2
            },
            inboxView,
            false).SetName("Team Member As Contact Owner Conversation");

        //  convo with assign to user with different team (without team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToOtherConversation", CompanyId = companyId, AssigneeId = 100
            },
            inboxView,
            false).SetName("Assigned to Non-Associated Team Member Conversation");

        // convo with assign to user with same team (with team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsContactOwnerWithAssociatedTeamConversation",
                CompanyId = companyId,
                AssigneeId = 2,
                AssignedTeamId = teamA.Id
            },
            inboxView,
            false).SetName("Team Member As Contact Owner With Associated Team Conversation");

        //  convo with assign to user with different team (with team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToOtherConversation", CompanyId = companyId, AssigneeId = 100, AssignedTeamId = 100,
            },
            inboxView,
            false).SetName("Assigned to Other With Non-Associated Team Conversation");

        // convo with assign to team with user is in
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToTeamConversation", CompanyId = companyId, AssignedTeamId = teamA.Id
            },
            inboxView,
            false).SetName("Assigned to Associated Team Conversation");

        //  convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToTeamConversation", CompanyId = companyId, AssignedTeamId = 2
            },
            inboxView,
            false).SetName("Assigned to Non-Associated Team Conversation");

        //  unassigned convo
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "unassignedConversation", CompanyId = companyId, AssigneeId = null
            },
            inboxView,
            false).SetName("Unassigned Conversation");

        // convo with user as collaborator
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId,
                        AssigneeId =
                            staffId
                    }
                },
            },
            inboxView,
            false).SetName("Assigned As Collaborator Conversation");

        // convo with team member as collaborator
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            false).SetName("Team Member As Collaborator Conversation");

        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AssignedTeamId = 1,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            false).SetName("Team Member As Collaborator Conversation With Associated Team");

        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AssignedTeamId = 2,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            false).SetName("Team Member As Collaborator Conversation With Non-Associated Team");

        // convo with user is mentioned
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "mentionedConversation",
                CompanyId = companyId,
                Mentions = [
                    new Mention
                    {
                        MentionedStaffId = staffId,
                        CreatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1)
                    }
                ],
            },
            inboxView,
            true).SetName("Mentioned Conversation");
    }
    public static IEnumerable<TestCaseData> GetTeamInboxViewConversationTestCases()
    {
        var companyId = "sleekflow";
        var staffId = 1;

        var teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long>
            {
                1, 2, 3
            }
        };

        var inboxView = new InboxView(InboxViewTypes.TeamInbox, teamA.Id);


        var staff = new StaffAccessControlAggregate
        {
            StaffId = staffId,
            CompanyId = companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                teamA
            },
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = companyId,
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe,
                        RbacViewConversationsPermissions.AssignedToMyTeam,
                        RbacViewConversationsPermissions.AllAssignedConversations,
                        RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                        RbacViewConversationsPermissions.AllUnassignedConversations
                    ]
                }
            }
        };

        // convo with assign to me
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToMeConversation", CompanyId = companyId, AssigneeId = staffId
            },
            inboxView,
            false).SetName("Assigned to Me Conversation");

        // convo with assign to user with same team (without team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsContactOwnerConversation", CompanyId = companyId, AssigneeId = 2
            },
            inboxView,
            false).SetName("Team Member As Contact Owner Conversation");

        //  convo with assign to user with different team (without team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToOtherConversation", CompanyId = companyId, AssigneeId = 100
            },
            inboxView,
            false).SetName("Assigned to Non-Associated Team Member Conversation");

        // convo with assign to user with same team (with team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsContactOwnerWithAssociatedTeamConversation",
                CompanyId = companyId,
                AssigneeId = 2,
                AssignedTeamId = teamA.Id
            },
            inboxView,
            true).SetName("Team Member As Contact Owner With Associated Team Conversation");

        //  convo with assign to user with different team (with team)
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToOtherConversation", CompanyId = companyId, AssigneeId = 100, AssignedTeamId = 100,
            },
            inboxView,
            false).SetName("Assigned to Other With Non-Associated Team Conversation");

        // convo with assign to team with user is in
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToTeamConversation", CompanyId = companyId, AssignedTeamId = teamA.Id
            },
            inboxView,
            true).SetName("Assigned to Associated Team Conversation");

        //  convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "assignedToTeamConversation", CompanyId = companyId, AssignedTeamId = 2
            },
            inboxView,
            false).SetName("Assigned to Non-Associated Team Conversation");

        //  unassigned convo
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "unassignedConversation", CompanyId = companyId, AssigneeId = null
            },
            inboxView,
            false).SetName("Unassigned Conversation");

        // convo with user as collaborator
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId,
                        AssigneeId =
                            staffId
                    }
                },
            },
            inboxView,
            false).SetName("Assigned As Collaborator Conversation");

        // convo with team member as collaborator
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            false).SetName("Team Member As Collaborator Conversation");

        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AssignedTeamId = teamA.Id,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            true).SetName("Team Member As Collaborator Conversation With Associated Team");

        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                AssignedTeamId = 2,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 2
                    }
                },
            },
            inboxView,
            false).SetName("Team Member As Collaborator Conversation With Non-Associated Team");

        // convo with user is mentioned
        yield return new TestCaseData(
            staff,
            new Conversation
            {
                Id = "mentionedConversation",
                CompanyId = companyId,
                ChatHistory =
                [
                    new ConversationMessage
                    {
                        Channel = ChannelTypes.Note,
                        MessageAssigneeId = staffId,
                        UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1)
                    }
                ]
            },
            inboxView,
            false).SetName("Mentioned Conversation");
    }
}