name: dev_sleekflow-core-stress-tests

on:
  schedule:
    # Run daily at 00:00 UTC (8:00 AM HKT)
    - cron: '0 0 * * *'

concurrency: dev_sleekflow-core-stress-tests_manual

env:
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages

jobs:
  update:
    name: dev_sleekflow-core-stress-tests_manual
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up .NET Core
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.303'
          include-prerelease: false

      - uses: actions/cache@v3
        with:
          path: ${{ github.workspace }}/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-

      - name: Restore dependencies
        run: dotnet restore Sleekflow.Core.StressTests/Sleekflow.Core.StressTests.csproj

      - name: Build
        run: dotnet build Sleekflow.Core.StressTests/Sleekflow.Core.StressTests.csproj --configuration Release --no-restore /p:Environment=Development

      - name: Run Stress Tests
        run: dotnet test Sleekflow.Core.StressTests/Sleekflow.Core.StressTests.csproj --configuration Release --verbosity normal --logger "trx;logfilename=test-results/stress-test-results.trx" /p:Environment=Development
        env:
          Environment: Development

      - name: Publish Test Results
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          files: |
            **/*.trx
          check_name: Stress Test Results