using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Commons.Models;

namespace Travis_backend.CompanyDomain.ViewModels;

public class CompanyUsage
{
    public int totalContacts { get; set; }

    public int totalConversations { get; set; }

    public int totalMessages { get; set; }

    public int totalMessagesSentFromSleekFlow { get; set; }

    public int totalChannelAdded { get; set; }

    public int totalAgents { get; set; }

    public int totalAPICalls { get; set; }

    public int BaseMaximumApiCalls { get; set; }

    public int BaseMaximumContacts { get; set; }

    public int BaseMaximumAutomatedMessages { get; set; }

    public int BaseMaximumAgents { get; set; }

    public int BaseMaximumAutomations { get; set; }

    public int BaseMaximumNumberOfWhatsappCloudApiChannels { get; set; }

    public int BaseMaximumNumberOfChannel { get; set; }

    public bool IsExceededWALimit { get; set; }

    public int CurrentNumberOfWhatsappCloudApiChannels { get; set; }

    public int CurrentNumberOfChannels { get; set; }

    public DateTime? LastLogin { get; set; }

    public List<BillingPeriodUsage> billingPeriodUsages { get; set; }

    public CompanyUsageLimitOffsetProfile UsageLimitOffsetProfile { get; set; } = new CompanyUsageLimitOffsetProfile();

    public int MaximumAPICalls
    {
        get
        {
            var result = BaseMaximumApiCalls + (UsageLimitOffsetProfile.IsEnabled ? UsageLimitOffsetProfile.ApiCallLimitOffSet : 0);
            return result < 0 ? 0 : result;
        }
    }

    public int MaximumContacts
    {
        get
        {
            var result = BaseMaximumContacts + (UsageLimitOffsetProfile.IsEnabled ? UsageLimitOffsetProfile.ContactsLimitOffset : 0);
            return result < 0 ? 0 : result;
        }
    }

    public int MaximumAutomatedMessages
    {
        get
        {
            var result = BaseMaximumAutomatedMessages + (UsageLimitOffsetProfile.IsEnabled ? UsageLimitOffsetProfile.AutomatedMessagesLimitOffset : 0);
            return result < 0 ? 0 : result;
        }
    }

    public int MaximumAgents
    {
        get
        {
            var result = BaseMaximumAgents + (UsageLimitOffsetProfile.IsEnabled ? UsageLimitOffsetProfile.AgentsLimitOffset : 0);
            return result < 1 ? 1 : result;
        }
    }

    public int MaximumAutomations
    {
        get
        {
            var result = BaseMaximumAutomations + (UsageLimitOffsetProfile.IsEnabled ? UsageLimitOffsetProfile.AutomationsLimitOffset : 0);
            return result < 1 ? 1 : result;
        }
    }

    public int MaximumNumberOfWhatsappCloudApiChannels
    {
        get
        {
            var result = BaseMaximumNumberOfWhatsappCloudApiChannels + (UsageLimitOffsetProfile.IsEnabled ? UsageLimitOffsetProfile.WhatsappInstanceLimitOffset : 0);
            return result < 0 ? 0 : result;
        }
    }

    public int MaximumNumberOfChannel
    {
        get
        {
            var result = BaseMaximumNumberOfChannel + (UsageLimitOffsetProfile.IsEnabled ? UsageLimitOffsetProfile.ChannelLimitOffset : 0);
            return result < 0 ? 0 : result;
        }
    }

    public DateTimeRange UsageCycleDateTimeRange { get; set; }

    #region FlowHub Related

    public decimal CurrentFlowBuilderFlowEnrolmentUsage { get; set; }

    public long BaseFlowBuilderFlowEnrolmentUsage { get; set; }

    public long MaximumFlowBuilderFlowEnrolmentUsage
    {
        get
        {
            var result = BaseFlowBuilderFlowEnrolmentUsage + (IsFlowHubUsageLimitOffsetEnabled ? FlowHubUsageLimitOffset.MaximumNumOfMonthlyWorkflowExecutionsOffset.GetValueOrDefault(0) : 0);
            return Math.Max(0, result);
        }
    }

    public bool IsFlowHubUsageLimitOffsetEnabled { get; set; }

    public UsageLimitOffset FlowHubUsageLimitOffset { get; set; } = new(0, 0, 0);

    #endregion
}