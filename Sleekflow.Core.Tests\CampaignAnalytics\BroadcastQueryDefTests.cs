﻿using Travis_backend.CampaignAnalyticsDomain.Constants;
using Travis_backend.CampaignAnalyticsDomain.Models;
using Travis_backend.CampaignAnalyticsDomain.Services;

namespace Sleekflow.Core.Tests.CampaignAnalytics;

public class BroadcastQueryDefTests
{
    [Test]
    public void GetCountsPerMessageStatusByBroadcastQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string getCountsPerMessageStatusByBroadcastQ =
            """
            SELECT
                CM.Status AS MessageStatus,
                COUNT(*) AS Count
            FROM
                ConversationMessages CM
                    JOIN BroadcastCompaignHistories BCH
                    ON CM.BroadcastHistoryId = BCH.Id
            WHERE
                BCH.BroadcastCampaignId = @BroadcastCampaignId
                AND CM.CompanyId = @CompanyId
            GROUP BY
                CM.Status
            """;

        // Act
        var queryDef = CampaignAnalyticsQueryBuilder.GetCountsPerMessageStatusByBroadcastQueryDef(
            string.Empty,
            string.Empty);

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(getCountsPerMessageStatusByBroadcastQ));
    }

    [Test]
    public void GetMessageOverviewByBroadcastQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string getMessageOverviewByBroadcastQ =
            """
            SELECT
                CM.Id AS MessageId,
                CM.ConversationId AS ConversationId,
                UP.Id AS UserProfileId,
                UP.FirstName AS FirstName,
                UP.LastName AS LastName,
                UP.FullName AS FullName,
                UP.PhoneNumber AS PhoneNumber,
                CM.Status AS MessageStatus,
                CM.ChannelStatusMessage AS ChannelStatusMessage,
                CM.CreatedAt AS CreatedAt
            FROM
                ConversationMessages CM
                    JOIN BroadcastCompaignHistories BCH
                         ON CM.BroadcastHistoryId = BCH.Id
                    JOIN Conversations C
                         ON CM.ConversationId = C.Id
                    JOIN UserProfiles UP
                         ON C.UserProfileId = UP.Id
            WHERE
                BCH.BroadcastCampaignId = @BroadcastCampaignId
                AND CM.CompanyId = @CompanyId
                AND CM.Status IN (2, 3)
            ORDER BY CM.CreatedAt ASC
            OFFSET @Offset ROWS
                FETCH NEXT @Limit ROWS ONLY;
            """;

        // Act
        var queryDef = CampaignAnalyticsQueryBuilder.GetMessageOverviewByBroadcastQueryDef(
            string.Empty,
            string.Empty,
            MessageStatusGroups.Delivered,
            GetMessageOverviewSortableFields.CreatedAt,
            "asc",
            0,
            0);

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(getMessageOverviewByBroadcastQ));
    }

    [Test]
    public void GetUserProfileIdsByBroadcastQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string getRepliedMessageIdsByBroadcastQ =
            """
            SELECT
                UP.Id as UserProfileId
            FROM
                ConversationMessages CM
                    JOIN BroadcastCompaignHistories BCH
                         ON CM.BroadcastHistoryId = BCH.Id
                    JOIN Conversations C
                         ON CM.ConversationId = C.Id
                    JOIN UserProfiles UP
                         ON C.UserProfileId = UP.Id
            WHERE
                BCH.BroadcastCampaignId = @BroadcastCampaignId
                AND CM.CompanyId = @CompanyId
                AND CM.Status IN  (2, 3);
            """;

        // Act
        var queryDef = CampaignAnalyticsQueryBuilder.GetUserProfileIdsByBroadcastQueryDef(
            string.Empty,
            string.Empty,
            MessageStatusGroups.Delivered);

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(getRepliedMessageIdsByBroadcastQ));
    }

    [Test]
    public void GetRepliedCountByBroadcastQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string getRepliedCountByBroadcastQ =
            """
            WITH
            OutMessages AS (
                SELECT
                CM.Id AS OutMessageId,
                CM.ConversationId,
                CM.CreatedAt AS OutCreatedAt,
                CM.ChannelStatusMessage,
                CM.Status
            FROM
                BroadcastCompaignHistories CMH
            JOIN
                ConversationMessages CM
            ON
                CMH.Id = CM.BroadcastHistoryId
                AND CM.CompanyId = @CompanyId
                AND CM.Status IN (2, 3)
            WHERE
                BroadcastCampaignId = @BroadcastCampaignId
            ),
            RepliedMessages AS (
                SELECT
                O.OutMessageId
            FROM
                OutMessages o
            WHERE
                EXISTS (
                SELECT 1
                FROM
                    ConversationMessages CM_E
                WHERE
                    O.ConversationId = CM_E.ConversationId
                    AND CM_E.CompanyId = @CompanyId
                    AND CM_E.IsSentFromSleekflow = 0
                    AND CM_E.CreatedAt BETWEEN O.OutCreatedAt AND DATEADD(HOUR, @ReplyWindowLengthInHours, O.OutCreatedAt)
                )
            )
            SELECT
                Count(1) AS Count
            FROM
                RepliedMessages;
            """;

        var queryDef = CampaignAnalyticsQueryBuilder.GetRepliedCountByBroadcastQueryDef(
            string.Empty,
            string.Empty,
            new ReplyWindow("day", 1));

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(getRepliedCountByBroadcastQ));
    }

    [Test]
    public void GetRepliedMessageOverviewByBroadcastQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string getRepliedMessageOverviewByBroadcastQ =
            """
            WITH
            OutMessages AS (
                SELECT
                CM.Id AS OutMessageId,
                CM.ConversationId,
                CM.CreatedAt AS OutCreatedAt,
                CM.ChannelStatusMessage,
                CM.Status
            FROM
                BroadcastCompaignHistories CMH
            JOIN
                ConversationMessages CM
            ON
                CMH.Id = CM.BroadcastHistoryId
                AND CM.CompanyId = @CompanyId
                AND CM.Status IN (2, 3)
            WHERE
                BroadcastCampaignId = @BroadcastCampaignId
            ),
            RepliedMessages AS (
                SELECT
                O.OutMessageId,
                C.Id as ConversationId,
                C.UserProfileId,
                U.FirstName,
                U.LastName,
                U.FullName,
                U.PhoneNumber,
                O.ChannelStatusMessage,
                O.Status,
                O.OutCreatedAt
            FROM
                OutMessages O
            JOIN
                Conversations C
            ON
                O.ConversationId = C.Id
            JOIN
                UserProfiles U
            ON
                C.UserProfileId = U.Id
             WHERE
                EXISTS(
                    SELECT
                        1
                    FROM
                        ConversationMessages CM_E
                    WHERE
                        O.ConversationId = CM_E.ConversationId
                        AND CM_E.CompanyId = @CompanyId
                        AND CM_E.IsSentFromSleekflow = 0
                        AND CM_E.CreatedAt BETWEEN O.OutCreatedAt AND DATEADD(HOUR, @ReplyWindowLengthInHours, O.OutCreatedAt)
                )
             ORDER BY
                 O.OutCreatedAt ASC
             OFFSET @Offset ROWS
                 FETCH NEXT @Limit ROWS ONLY
            )
            SELECT
                OutMessageId AS MessageId,
                ConversationId AS ConversationId,
                UserProfileId AS UserProfileId,
                FirstName AS FirstName,
                LastName AS LastName,
                FullName AS FullName,
                PhoneNumber AS PhoneNumber,
                Status AS MessageStatus,
                ChannelStatusMessage AS ChannelStatusMessage,
                OutCreatedAt AS CreatedAt
            FROM
                RepliedMessages;
            """;

        // Act
        var queryDef = CampaignAnalyticsQueryBuilder.GetRepliedMessageOverviewByBroadcastQueryDef(
            string.Empty,
            string.Empty,
            new ReplyWindow("day", 1),
            GetMessageOverviewSortableFields.CreatedAt,
            "asc",
            0,
            0);

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(getRepliedMessageOverviewByBroadcastQ));
    }

    [Test]
    public void GetRepliedMessageUserProfileIdsByBroadcastQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string gtRepliedMessageUserProfileIdsByBroadcastQueryDef =
            """
            WITH
            OutMessages AS (
                SELECT
                CM.Id AS OutMessageId,
                CM.ConversationId,
                CM.CreatedAt AS OutCreatedAt,
                CM.ChannelStatusMessage,
                CM.Status
            FROM
                BroadcastCompaignHistories CMH
            JOIN
                ConversationMessages CM
            ON
                CMH.Id = CM.BroadcastHistoryId
                AND CM.CompanyId = @CompanyId
                AND CM.Status IN (2, 3)
            WHERE
                BroadcastCampaignId = @BroadcastCampaignId
            ),
            RepliedMessages AS (
                SELECT
                C.UserProfileId
            FROM
                OutMessages O
            JOIN
                Conversations C
            ON
                O.ConversationId = C.Id
            WHERE
                EXISTS(
                    SELECT
                        1
                    FROM
                        ConversationMessages CM_E
                    WHERE
                        O.ConversationId = CM_E.ConversationId
                        AND CM_E.CompanyId = @CompanyId
                        AND CM_E.IsSentFromSleekflow = 0
                        AND CM_E.CreatedAt BETWEEN O.OutCreatedAt AND DATEADD(HOUR, @ReplyWindowLengthInHours, O.OutCreatedAt)
                )
            )
            SELECT
                UserProfileId
            FROM
                RepliedMessages;
            """;

        // Act
        var queryDef = CampaignAnalyticsQueryBuilder.GetRepliedMessageUserProfileIdsByBroadcastQueryDef(
            string.Empty,
            string.Empty,
            new ReplyWindow("day", 1));

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(gtRepliedMessageUserProfileIdsByBroadcastQueryDef));
    }

    [Test]
    public void FilterRepliedMessageIdsQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string filterRepliedMessageIdsQueryDef =
            """
            WITH
            OutMessages AS (
                SELECT
                    Id AS OutMessageId,
                    ConversationId,
                    CreatedAt AS OutCreatedAt
                FROM
                    ConversationMessages CM
                WHERE
                    CompanyId = @CompanyId
                    AND Id IN (11, 22, 33)
            ),
            RepliedMessages AS (
                SELECT
                O.OutMessageId
            FROM
                OutMessages o
            WHERE
                EXISTS (
                SELECT 1
                FROM
                    ConversationMessages CM_E
                WHERE
                    O.ConversationId = CM_E.ConversationId
                    AND CM_E.CompanyId = @CompanyId
                    AND CM_E.IsSentFromSleekflow = 0
                    AND CM_E.CreatedAt BETWEEN O.OutCreatedAt AND DATEADD(HOUR, @ReplyWindowLengthInHours, O.OutCreatedAt)
                )
            )
            SELECT
                OutMessageId as MessageId
            FROM
                RepliedMessages;
            """;

        // Act
        var queryDef = CampaignAnalyticsQueryBuilder.FilterRepliedMessageIdsQueryDef(
            new List<long>{11, 22, 33},
            string.Empty,
            new ReplyWindow("day", 1));

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(filterRepliedMessageIdsQueryDef));
    }
}