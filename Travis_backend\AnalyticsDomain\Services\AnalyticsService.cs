#nullable enable
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.Utils;
using Travis_backend.AnalyticsDomain.ViewModels;
using Travis_backend.Cache;
using Travis_backend.Cache.Models;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.AnalyticsDomain.Services
{
    public interface IAnalyticsService
    {
        Task<AnalyticsResult> GetSummaryData(
            string companyId,
            string staffId = null,
            long? teamId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            List<Condition> conditions = null,
            Func<int, ValueTask> onComplete = null);

        Task<List<AnalyticsRecord>> GetDataForCompany(
            string companyId,
            string staffId = null,
            long? teamId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            List<Condition> conditions = null,
            Func<int, ValueTask> onComplete = null);

        Task<List<AnalyticsRecord>> GenerateDataForCompany(
            string companyId,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            List<Condition> conditions = null);

        Task<string> ExportDataForCompany(
            string companyId,
            long staffId,
            string staffUserId,
            DateTime messageFrom,
            DateTime messageTo,
            string status = null,
            string channels = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null,
            List<Condition> conditions = null,
            Func<int, ValueTask> onComplete = null);

        Task<string> ExportDataForCustomField(
            string companyId,
            string analyticsEmailNotificationConfigId,
            DateTime messageFrom,
            DateTime messageTo);

        void DailyExportCustomFieldAnalyticCsv();

        Task HandleCustomFieldAnalyticCsv(
            string companyId,
            string analyticsEmailNotificationConfigId,
            DateTime messageFrom,
            DateTime messageTo);

        Task<(List<UserProfile> ConditionalUserProfiles, List<string> ConditionalUserProfileIds)>
            GetConditionalUserProfileIds(
                long? teamId,
                string staffId,
                string companyId,
                List<Condition> conditions,
                string searchConditionHash);

        /// <summary>
        /// Generate the hash for conditions in the segment
        /// </summary>
        /// <param name="conditions">Conditions.</param>
        /// <returns>Hash string.</returns>
        string GetConditionHash(List<Condition> conditions);

        Task<List<BroadcastMessageMetricViewModel>> GetBroadcastMessageMetricDailyLogsAsync(
            string companyId,
            DateOnly from,
            DateOnly to,
            bool usingCache,
            CancellationToken cancellationToken);

        BroadcastMessageMetricViewModel CalculateSummedLog(
            DateOnly? date,
            IReadOnlyCollection<BroadcastMessageMetricViewModel> metricVMs);
    }

    public class AnalyticsService : IAnalyticsService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IUserProfileService _userProfileService;
        private readonly IEmailNotificationService _emailNotificationService;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly IAnalyticsSqlService _analyticsSqlService;
        private readonly IServiceProvider _serviceProvider;

        private readonly bool _isAnalyticsRawSqlEnabled;
        private readonly bool _isUserProfileRawSqlEnabled;

        public AnalyticsService(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<AnalyticsService> logger,
            IUserProfileService userProfileService,
            IEmailNotificationService emailNotificationService,
            IConversationMessageService conversationMessageService,
            ICacheManagerService cacheManagerService,
            IAnalyticsSqlService analyticsSqlService,
            IServiceProvider serviceProvider)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _logger = logger;
            _userProfileService = userProfileService;
            _emailNotificationService = emailNotificationService;
            _conversationMessageService = conversationMessageService;
            _cacheManagerService = cacheManagerService;
            _analyticsSqlService = analyticsSqlService;
            _serviceProvider = serviceProvider;
            _isAnalyticsRawSqlEnabled = configuration.GetValue<bool>("SqlPerformance:IsConversationAnalyticsConditionEnabled");
            _isUserProfileRawSqlEnabled = configuration.GetValue<bool>("SqlPerformance:FromRawSql");
        }

        public async Task<AnalyticsResult> GetSummaryData(
            string companyId,
            string staffId = null,
            long? teamId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            List<Condition> conditions = null,
            Func<int, ValueTask> onComplete = null)
        {
            var billRecords = _appDbContext.CompanyBillRecords
                .Where(
                    x =>
                        (ValidSubscriptionPlan.EnterpriseTier.Contains(x.SubscriptionPlanId)
                         || ValidSubscriptionPlan.PremiumTier.Contains(x.SubscriptionPlanId))
                        && DateTime.UtcNow < x.PeriodEnd.AddDays(3)
                        && x.CompanyId == companyId);

            if (!billRecords.Any())
            {
                return new AnalyticsResult();
            }

            var company = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == companyId)
                .Select(
                    x =>
                        new
                        {
                            companyId = x.Id, CreatedAt = x.CreatedAt
                        })
                .FirstOrDefaultAsync();

            fromDate = fromDate.Value.Date;
            toDate = toDate.Value.Date;

            if (fromDate < company.CreatedAt)
            {
                fromDate = company.CreatedAt.Date;
            }

            if (toDate >= DateTime.UtcNow.Date.AddDays(-1))
            {
                toDate = DateTime.UtcNow.Date.AddDays(-1);
            }

            if (conditions != null && conditions.Count == 0)
            {
                conditions = null;
            }

            var result = await GetDataForCompany(companyId, staffId, teamId, fromDate, toDate, conditions, onComplete);

            var response = new AnalyticsResult
            {
                CompanyId = companyId,
                StaffId = staffId,
                TeamId = teamId,
                StartDate = fromDate.Value,
                EndDate = toDate.Value,
                Conditions = conditions
            };
            response.DailyLogs = result.Select(x => x.Data).OrderBy(x => x.DateTime).ToList();

            if (response.DailyLogs?.Count > 0)
            {
                var countedResponseTimeForAllMessages = result.Select(x => x.Data.ResponseTimeForAllMessages)
                    .Where(x => x != TimeSpan.Zero).ToList();
                var countedResponseTimeForFirstMessages = result.Select(x => x.Data.ResponseTimeForFirstMessages)
                    .Where(x => x != TimeSpan.Zero).ToList();

                response.Summary = new AnalyticsData
                {
                    NumberOfBroadcastSent = result.Select(x => x.Data.NumberOfBroadcastSent).Sum(),
                    NumberOfAutomatedMessages = result.Select(x => x.Data.NumberOfAutomatedMessages).Sum(),
                    NumberOfContacts = result.Select(x => x.Data.NumberOfContacts).Sum(),
                    NumberOfMessageReceived = result.Select(x => x.Data.NumberOfMessageReceived).Sum(),
                    NumberOfMessagesSent = result.Select(x => x.Data.NumberOfMessagesSent).Sum(),
                    NumberOfNewEnquires = result.Select(x => x.Data.NumberOfNewEnquires).Sum(),
                    NumberOfActiveConversations = result.Select(x => x.Data.NumberOfActiveConversations).Sum(),
                    NumberOfAllConversations = result.Select(x => x.Data.NumberOfAllConversations).Sum(),
                    NumberOfBroadcastReplied = result.Select(x => x.Data.NumberOfBroadcastReplied).Sum(),
                    NumberOfBroadcastDelivered = result.Select(x => x.Data.NumberOfBroadcastDelivered).Sum(),
                    NumberOfBroadcastRead = result.Select(x => x.Data.NumberOfBroadcastRead).Sum(),
                    NumberOfBroadcastBounced = result.Select(x => x.Data.NumberOfBroadcastBounced).Sum(),
                    ResponseTimeForAllMessages =
                        countedResponseTimeForAllMessages.Count > 0
                            ? GetAverage(countedResponseTimeForAllMessages)
                            : TimeSpan.Zero,
                    ResponseTimeForFirstMessages =
                        countedResponseTimeForFirstMessages.Count > 0
                            ? GetAverage(countedResponseTimeForFirstMessages)
                            : TimeSpan.Zero,
                    ActiveAgents = result.Select(x => x.Data.ActiveAgents).Max()
                };

                // foreach (var allConversation in result)
                // {
                //    response.Summary.AllConversationIds.AddRange(allConversation.Data.AllConversationIds);
                //    response.Summary.ActiveConversationIds.AddRange(allConversation.Data.ActiveConversationIds);
                // }

                // response.Summary.AllConversationIds = response.Summary.AllConversationIds.Distinct().ToList();
                // response.Summary.ActiveConversationIds = response.Summary.ActiveConversationIds.Distinct().ToList();
                response.Summary.NumberOfActiveConversationsAverage = response.Summary.ActiveAgents != 0
                    ? (int) Math.Ceiling(
                        (double) response.Summary.NumberOfActiveConversations / response.Summary.ActiveAgents)
                    : response.Summary.NumberOfActiveConversations;
                response.Summary.NumberOfAllConversationsAverage = response.Summary.ActiveAgents != 0
                    ? (int) Math.Ceiling(
                        (double) response.Summary.NumberOfAllConversations / response.Summary.ActiveAgents)
                    : response.Summary.NumberOfAllConversations;
                response.Summary.NumberOfContactsAverage = response.Summary.ActiveAgents != 0
                    ? (int) Math.Ceiling((double) response.Summary.NumberOfContacts / response.Summary.ActiveAgents)
                    : response.Summary.NumberOfContacts;
                response.Summary.NumberOfMessageReceivedAverage = response.Summary.ActiveAgents != 0
                    ? (int) Math.Ceiling(
                        (double) response.Summary.NumberOfMessageReceived / response.Summary.ActiveAgents)
                    : response.Summary.NumberOfMessageReceived;
                response.Summary.NumberOfMessagesSentAverage = response.Summary.ActiveAgents != 0
                    ? (int) Math.Ceiling((double) response.Summary.NumberOfMessagesSent / response.Summary.ActiveAgents)
                    : response.Summary.NumberOfMessagesSent;
                response.Summary.NumberOfNewEnquiresAverage = response.Summary.ActiveAgents != 0
                    ? (int) Math.Ceiling((double) response.Summary.NumberOfNewEnquires / response.Summary.ActiveAgents)
                    : response.Summary.NumberOfNewEnquires;

                // response.Summary.NumberOfUniqueActiveConversationAverage = (int)Math.Ceiling((double)response.Summary.NumberOfUniqueActiveConversations / response.Summary.ActiveAgents);
                // response.Summary.NumberOfUniqueConversationAverage = (int)Math.Ceiling((double)response.Summary.NumberOfUniqueConversations / response.Summary.ActiveAgents);
                response.DailyLogs.ForEach(
                    x => x.NumberOfActiveConversationsAverage = x.ActiveAgents != 0
                        ? (int) Math.Ceiling((double) x.NumberOfActiveConversations / x.ActiveAgents)
                        : x.NumberOfActiveConversations);
                response.DailyLogs.ForEach(
                    x => x.NumberOfAllConversationsAverage = x.ActiveAgents != 0
                        ? (int) Math.Ceiling((double) x.NumberOfAllConversations / x.ActiveAgents)
                        : x.NumberOfAllConversations);
                response.DailyLogs.ForEach(
                    x => x.NumberOfContactsAverage = x.ActiveAgents != 0
                        ? (int) Math.Ceiling((double) x.NumberOfContacts / x.ActiveAgents)
                        : x.NumberOfContacts);
                response.DailyLogs.ForEach(
                    x => x.NumberOfMessageReceivedAverage = x.ActiveAgents != 0
                        ? (int) Math.Ceiling((double) x.NumberOfMessageReceived / x.ActiveAgents)
                        : x.NumberOfMessageReceived);
                response.DailyLogs.ForEach(
                    x => x.NumberOfMessagesSentAverage = x.ActiveAgents != 0
                        ? (int) Math.Ceiling((double) x.NumberOfMessagesSent / x.ActiveAgents)
                        : x.NumberOfMessagesSent);
                response.DailyLogs.ForEach(
                    x => x.NumberOfNewEnquiresAverage = x.ActiveAgents != 0
                        ? (int) Math.Ceiling((double) x.NumberOfNewEnquires / x.ActiveAgents)
                        : x.NumberOfNewEnquires);

                // response.DailyLogs.ForEach(x => x.NumberOfUniqueActiveConversationAverage = (int)Math.Ceiling((double)x.NumberOfUniqueActiveConversations / x.ActiveAgents));
                // response.DailyLogs.ForEach(x => x.NumberOfUniqueConversationAverage = (int)Math.Ceiling((double)x.NumberOfUniqueConversations / x.ActiveAgents));
            }

            return response;
        }

        public async Task<List<AnalyticsRecord>> GetDataForCompany(
            string companyId,
            string staffId = null,
            long? teamId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            List<Condition> conditions = null,
            Func<int, ValueTask> onComplete = null)
        {
            var result = new List<AnalyticsRecord>();

            // Ensure the fromDate and toDate are not null to avoid any issues
            if (fromDate is null || toDate is null)
            {
                return result;
            }

            var team = await _appDbContext.CompanyStaffTeams
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == teamId);

            var timeZoneId = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == companyId)
                .Select(x => x.TimeZoneInfoId)
                .FirstOrDefaultAsync();

            List<Condition> searchCondition = conditions;
            var searchConditionHash = GetConditionHash(conditions);

            // Get User Profile with Condition
            var (conditionalUserProfiles, conditionalUserProfileIds) = await GetConditionalUserProfileIds(
                teamId,
                staffId,
                companyId,
                conditions,
                searchConditionHash);

            if (_isAnalyticsRawSqlEnabled)
            {
                // Get the conditional conversation ids based on the user profile ids
                var conditionalConversationIds =
                    conditionalUserProfileIds is not null && conditionalUserProfileIds.Count > 0
                        ? await _appDbContext.Conversations
                            .Where(c => c.CompanyId == companyId && conditionalUserProfileIds.Contains(c.UserProfileId))
                            .Select(c => c.Id)
                            .ToListAsync()
                        : null;

                return await _analyticsSqlService.GetDataForCompanyAsync(
                    companyId,
                    staffId,
                    teamId,
                    team,
                    fromDate.Value,
                    toDate.Value,
                    timeZoneId,
                    conditions,
                    searchConditionHash,
                    conditionalUserProfiles,
                    conditionalConversationIds,
                    onComplete);
            }

            var completed = 0;
            for (var date = fromDate.Value.Date; date <= toDate.Value.Date; date = date.AddDays(1))
            {
                _logger.LogInformation(
                    "Retrieving analytics data for company: {AnalyticsFromDate} {CompanyId}, staffId {StaffId}, teamId: {TeamId}",
                    fromDate.Value.ToString("d"),
                    companyId,
                    staffId,
                    teamId);

                try
                {
                    var fromWithTimeZone = date.ConvertSpecificTimeZoneDateTimeToUtcDateTime(timeZoneId);
                    var toWithTimeZone = date.AddDays(1).ConvertSpecificTimeZoneDateTimeToUtcDateTime(timeZoneId);

                    var recordKey =
                        $"v5_AnalyticsRecord_{companyId}_{staffId}_{teamId}_{fromWithTimeZone}_{toWithTimeZone}_{searchConditionHash}";

                    var analyticsRecordCacheKeyPattern =
                        new AnalyticsRecordCacheKeyPattern(companyId, staffId, teamId, fromWithTimeZone, toWithTimeZone, searchConditionHash);

                    var recordData = await _cacheManagerService.GetCacheAsync(analyticsRecordCacheKeyPattern);
                    if (!string.IsNullOrEmpty(recordData))
                    {
                        result.Add(JsonConvert.DeserializeObject<AnalyticsRecord>(recordData));
                        continue;
                    }

                    // // Late fetch user profile data when cache not hit
                    // if (staffId != null || teamId != null || conditions != null)
                    // {
                    //     if (conditionalUserProfiles == null && conditionalUserProfileIds == null)
                    //     {
                    //         conditionalUserProfiles = await GetConditionalUserProfiles(
                    //             companyId,
                    //             staffId,
                    //             teamId,
                    //             conditions);
                    //
                    //         conditionalUserProfileIds = conditionalUserProfiles
                    //             .Select(x => x.Id)
                    //             .ToList();
                    //     }
                    // }

                    // Company
                    if (string.IsNullOrEmpty(staffId) && !teamId.HasValue)
                    {
                        var getData = await GetRecords(
                            companyId,
                            fromWithTimeZone,
                            toWithTimeZone,
                            null,
                            conditionalUserProfileIds,
                            conditionalUserProfiles?
                                .Where(
                                    x =>
                                        x.CreatedAt >= fromWithTimeZone
                                        && x.CreatedAt <= toWithTimeZone)
                                .Select(x => x.Id)
                                .ToList());

                        var record = new AnalyticsRecord
                        {
                            CompanyId = companyId, DateTime = date, ConditionsHash = searchConditionHash, Data = getData
                        };

                        if (toWithTimeZone.Date < DateTime.UtcNow.Date)
                        {
                            await _cacheManagerService.SaveCacheAsync(
                                analyticsRecordCacheKeyPattern,
                                record);
                        }

                        result.Add(record);
                    }

                    // Team
                    else if (teamId.HasValue && string.IsNullOrEmpty(staffId))
                    {
                        var getData = await GetRecords(
                            companyId,
                            fromWithTimeZone,
                            toWithTimeZone,
                            null,
                            conditionalUserProfileIds,
                            conditionalUserProfiles?
                                .Where(
                                    x =>
                                        x.CreatedAt >= fromWithTimeZone
                                        && x.CreatedAt <= toWithTimeZone)
                                .Select(x => x.Id)
                                .ToList());

                        var record = new AnalyticsRecord
                        {
                            CompanyId = companyId,
                            DateTime = date,
                            TeamId = team.Id,
                            ConditionsHash = searchConditionHash,
                            Data = getData
                        };

                        if (toWithTimeZone.Date < DateTime.UtcNow.Date)
                        {
                            await _cacheManagerService.SaveCacheAsync(
                                analyticsRecordCacheKeyPattern,
                                record);
                        }

                        result.Add(record);
                    }

                    // Staff
                    else if (!string.IsNullOrEmpty(staffId))
                    {
                        var getData = await GetRecords(
                            companyId,
                            fromWithTimeZone,
                            toWithTimeZone,
                            staffId,
                            conditionalUserProfileIds,
                            conditionalUserProfiles?
                                .Where(
                                    x =>
                                        x.CreatedAt >= fromWithTimeZone
                                        && x.CreatedAt <= toWithTimeZone)
                                .Select(x => x.Id)
                                .ToList());

                        var record = new AnalyticsRecord
                        {
                            CompanyId = companyId,
                            DateTime = date,
                            StaffId = staffId,
                            Data = getData,
                            ConditionsHash = searchConditionHash
                        };

                        if (toWithTimeZone.Date < DateTime.UtcNow.Date)
                        {
                            await _cacheManagerService.SaveCacheAsync(
                                analyticsRecordCacheKeyPattern,
                                record);
                        }

                        result.Add(record);
                    }

                    if (onComplete != null)
                    {
                        completed++;
                        await onComplete(completed);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Analytic {MethodName}] error: {ExceptionMessage}",
                        nameof(GetDataForCompany),
                        ex.Message);

                    return result;
                }
            }

            return result;
        }


        /***
         * Extracting the conditional user profiles logic out of the GetSummaryData method to keep it retrieve once per loop
         * And to avoid the same data being retrieved multiple times with the same conditions
         * Adding a cache layer to store the conditional user profiles data for 30 minutes
         * @param teamId
         * @param staffId
         * @param companyId
         * @param conditions
         * @param searchConditionHash
         */
        public async Task<(List<UserProfile> ConditionalUserProfiles, List<string> ConditionalUserProfileIds)>
            GetConditionalUserProfileIds(
                long? teamId,
                string staffId,
                string companyId,
                List<Condition> conditions,
                string searchConditionHash)
        {
            var cacheKey =
                $"v5_analytics_record_conditional_user_profile_{companyId}_{staffId}_{teamId}_{searchConditionHash}";
            if (staffId == null && teamId == null && conditions == null)
            {
                return (null, null);
            }

            List<UserProfile> conditionalUserProfiles;
            var cachedData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrEmpty(cachedData))
            {
                conditionalUserProfiles = JsonConvert.DeserializeObject<List<UserProfile>>(cachedData);
            }
            else
            {
                conditionalUserProfiles = !_isUserProfileRawSqlEnabled
                    ? await GetConditionalUserProfiles(
                        companyId,
                        staffId,
                        teamId,
                        conditions)
                    : await _analyticsSqlService.GetConditionalUserProfilesAsync(
                        companyId,
                        staffId,
                        teamId,
                        conditions);
                await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                    cacheKey,
                    JsonConvert.SerializeObject(conditionalUserProfiles),
                    TimeSpan.FromMinutes(30));
            }

            return (conditionalUserProfiles, conditionalUserProfiles.Select(u => u.Id).ToList());
        }

        public string GetConditionHash(List<Condition> conditions)
        {
            return SHA256Helper.sha256_hash(JsonConvert.SerializeObject(conditions));
        }

        public async Task<List<BroadcastMessageMetricViewModel>> GetBroadcastMessageMetricDailyLogsAsync(
            string companyId,
            DateOnly from,
            DateOnly to,
            bool usingCache,
            CancellationToken cancellationToken)
        {
            // the segment will not be applied in Broadcast metrics in revamp phase 1
            // so we remove it from input, just keep the function signature for future use
            var conditions = new List<Condition>();

            // prepare
            var userProfileIds = new List<string>();
            var isConditionApplied = false;
            var conditionHash = GetConditionHash(conditions);

            if (conditions is { Count: > 0 })
            {
                isConditionApplied = true;

                (_, userProfileIds) = await GetConditionalUserProfileIds(
                    null,
                    null!,
                    companyId,
                    conditions,
                    conditionHash);
            }

            var timeZoneId = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == companyId)
                .Select(x => x.TimeZoneInfoId)
                .FirstOrDefaultAsync(cancellationToken: cancellationToken);

            // fetch daily data
            var dateRange = new List<DateOnly>();
            for (var date = from; date <= to; date = date.AddDays(1))
            {
                dateRange.Add(date);
            }

            var dailyLogs = new ConcurrentBag<BroadcastMessageMetricViewModel>();

            await Parallel.ForEachAsync(
                dateRange,
                new ParallelOptions { MaxDegreeOfParallelism = 6 },
                async (thisDate, thisCancellationToken) =>
                {
                    var fromWithTimeZone = thisDate.ToDateTime(TimeOnly.MinValue)
                        .ConvertSpecificTimeZoneDateTimeToUtcDateTime(timeZoneId);
                    var toWithTimeZone = thisDate.ToDateTime(TimeOnly.MinValue)
                        .AddDays(1)
                        .ConvertSpecificTimeZoneDateTimeToUtcDateTime(timeZoneId);

                    // todo remove the code block after performance testing
                    if (!usingCache)
                    {
                        dailyLogs.Add(
                            await GetBroadcastMessageMetricDailyLogAsync(
                                companyId,
                                isConditionApplied,
                                thisDate,
                                fromWithTimeZone,
                                toWithTimeZone,
                                userProfileIds,
                                thisCancellationToken));

                        return;
                    }

                    var cacheKeyPattern = new ConversationAnalyticsBroadcastMessageMetricsCacheKeyPattern(
                        companyId,
                        thisDate,
                        conditionHash);

                    var dailyLog = await _cacheManagerService.GetAndSaveCacheAsync(
                        cacheKeyPattern,
                        async () => await GetBroadcastMessageMetricDailyLogAsync(
                                companyId,
                                isConditionApplied,
                                thisDate,
                                fromWithTimeZone,
                                toWithTimeZone,
                                userProfileIds,
                                thisCancellationToken));

                    dailyLogs.Add(dailyLog);
                });

            return dailyLogs.OrderBy(l => l.Date).ToList();
        }

        public BroadcastMessageMetricViewModel CalculateSummedLog(
            DateOnly? date,
            IReadOnlyCollection<BroadcastMessageMetricViewModel> metricVMs)
        {
            var summedLog = BroadcastMessageMetricViewModel.Default(date);

            foreach (var metricVM in metricVMs)
            {
                summedLog.NumberOfBroadcastSent += metricVM.NumberOfBroadcastSent;
                summedLog.NumberOfBroadcastBounced += metricVM.NumberOfBroadcastBounced;
                summedLog.NumberOfBroadcastDelivered += metricVM.NumberOfBroadcastDelivered;
                summedLog.NumberOfBroadcastRead += metricVM.NumberOfBroadcastRead;
                summedLog.NumberOfBroadcastReplied += metricVM.NumberOfBroadcastReplied;
            }

            return summedLog;
        }

        private async Task<BroadcastMessageMetricViewModel> GetBroadcastMessageMetricDailyLogAsync(
            string companyId,
            bool isConditionApplied,
            DateOnly date,
            DateTime fromWithTimeZone,
            DateTime toWithTimeZone,
            ICollection<string> allUserProfileIds,
            CancellationToken cancellationToken)
        {
            // This function is designed to be called in parallel, thus we assign a new scope for each call.
            // Analytic DB is readonly so guaranteed to be thread-safe
            using var scope = _serviceProvider.CreateScope();
            var conversationMessageService = scope.ServiceProvider.GetRequiredService<IConversationMessageService>();
            var applicationReadDbContext = scope.ServiceProvider.GetRequiredService<ApplicationReadDbContext>();

            var conversationMessagesQuery =
                conversationMessageService.GetConversationMessagesHistory(
                    companyId,
                    null,
                    null,
                    null,
                    fromWithTimeZone,
                    toWithTimeZone);

            // fetch from DB then could join with local lists
            var conversationMessagesEnum = await conversationMessagesQuery
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Channel != ChannelTypes.Note
                        && x.MessageType != MessageTypes.System
                        && x.DeliveryType != DeliveryType.AutomatedMessage
                        && x.DeliveryType != DeliveryType.FlowHubAction
                        && x.DeliveryType != DeliveryType.AiAgentAction
                        && x.CreatedAt >= fromWithTimeZone
                        && x.CreatedAt < toWithTimeZone)
                .ToListAsync(cancellationToken: cancellationToken);

            // Apply Condition
            if (isConditionApplied)
            {
                var conversationDtos = await applicationReadDbContext.Conversations
                    .Where(x => x.CompanyId == companyId)
                    .Select(
                        x => new
                        {
                            x.Id, x.UserProfileId
                        })
                    .ToListAsync(cancellationToken: cancellationToken);

                var conditionalConversationIds = conversationDtos
                    .Join(
                        allUserProfileIds,
                        conversation => conversation.UserProfileId,
                        userProfileId => userProfileId,
                        (conversation, userProfileId) => conversation.Id)
                    .ToList();

                conversationMessagesEnum = conversationMessagesEnum
                    .Join(
                        conditionalConversationIds,
                        conversationMessage => conversationMessage.ConversationId,
                        convId => convId,
                        (conversationMessage, convId) => conversationMessage)
                    .ToList();
            }

            return AnalyticsUtils.ConstructBroadcastMessageMetricData(
                conversationMessagesEnum,
                date);
        }

        // public async Task GenerateData()
        // {
        //    var billRecords = _appDbContext.CompanyBillRecords.Where(x => (ValidSubscriptionPlan.EnterpriseTier.Contains(x.SubscriptionPlanId) || ValidSubscriptionPlan.PremiumTier.Contains(x.SubscriptionPlanId)) && DateTime.UtcNow < x.PeriodEnd);
        //    var companies = await _appDbContext.CompanyCompanies.Where(x => billRecords.Select(x => x.CompanyId).Contains(x.Id)).ToListAsync();
        //
        // foreach (var company in companies)
        //    {
        //        var from = DateTime.UtcNow.Date.AddDays(-14);
        //
        // if (from < company.CreatedAt)
        //            from = company.CreatedAt;
        //
        // await GenerateDataForCompany(company.Id, from, DateTime.UtcNow.Date.AddDays(-1));
        //    }
        // }

        public async Task<List<AnalyticsRecord>> GenerateDataForCompany(
            string companyId,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            List<Condition> conditions = null)
        {
            var result = new List<AnalyticsRecord>();

            result.AddRange(
                await GetDataForCompany(
                    companyId,
                    null,
                    teamId: null,
                    fromDate,
                    toDate,
                    conditions));

            // var existingData = await _appDbContext.AnalyticsRecords.Where(x => x.CompanyId == companyId && string.IsNullOrEmpty(x.StaffId) && !x.TeamId.HasValue && x.DateTime.Date == fromDate.Value.Date && x.Conditions == conditions).FirstOrDefaultAsync();
            // if (existingData == null)
            // {
            //    var getData = GetRecords(companyId, null, null, null, fromDate.Value.Date, fromDate.Value.AddDays(1), null, null, null, null, null, conditions);
            //    var record = new AnalyticsRecord { CompanyId = companyId, DateTime = fromDate.Value.Date, Conditions = conditions, Data = getData };
            //    _appDbContext.AnalyticsRecords.AddRange(record);
            //    await _appDbContext.SaveChangesAsync();

            // result.Add(record);
            // }
            // else
            // {
            //    result.Add(existingData);
            // }
            var teamShowed = new List<string>();

            // teamData
            var teams = await _appDbContext.CompanyStaffTeams
                .Where(x => x.CompanyId == companyId)
                .Include(x => x.Members)
                .ThenInclude(x => x.Staff.Identity)
                .ToListAsync();

            foreach (var team in teams)
            {
                result.AddRange(
                    await GetDataForCompany(
                        companyId,
                        null,
                        team.Id,
                        fromDate,
                        toDate,
                        conditions));

                foreach (var staff in team.Members)
                {
                    result.AddRange(
                        await GetDataForCompany(
                            companyId,
                            staff.Staff.IdentityId,
                            team.Id,
                            fromDate,
                            toDate,
                            conditions));
                }

                // var existingteamUnassignedData = await _appDbContext.AnalyticsRecords.Where(x => x.CompanyId == companyId && x.TeamId == team.Id && x.IsTeamUnassigned == true && x.DateTime.Date == fromDate.Value.Date && x.Conditions == conditions).FirstOrDefaultAsync();
                // if (existingteamUnassignedData == null)
                // {
                //    //teamUnassigned
                //    var teamUnassignedRecord = GetRecords(companyId, null, null, null, fromDate.Value.Date, fromDate.Value.AddDays(1), null, null, team.Id, true, null, null);
                //    var teamUnassigned = new AnalyticsRecord { CompanyId = companyId, DateTime = fromDate.Value.Date, TeamId = team.Id, IsTeamUnassigned = true, Data = teamUnassignedRecord, Conditions = conditions };
                //    _appDbContext.AnalyticsRecords.AddRange(teamUnassigned);
                //    await _appDbContext.SaveChangesAsync();
                //    result.Add(teamUnassigned);
                // }
                // else
                // {
                //    result.Add(existingteamUnassignedData);
                // }
            }

            // StaffData
            var staffs = await _appDbContext.UserRoleStaffs
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && !teamShowed.Contains(x.IdentityId)
                        && x.Id != 1)
                .Include(x => x.Identity)
                .ToListAsync();

            foreach (var staff in staffs)
            {
                result.AddRange(
                    await GetDataForCompany(
                        companyId,
                        staff.IdentityId,
                        null,
                        fromDate,
                        toDate,
                        conditions));
            }

            result.AddRange(
                await GetDataForCompany(
                    companyId,
                    "unassigned",
                    null,
                    fromDate,
                    toDate,
                    conditions));

            // var existingUnassignedData = await _appDbContext.AnalyticsRecords.Where(x => x.CompanyId == companyId && x.StaffId == "unassigned" && x.DateTime.Date == fromDate.Value.Date && x.Conditions == conditions).FirstOrDefaultAsync();
            // if (existingUnassignedData == null)
            // {
            //    var unassignedRecord = GetRecords(companyId, "unassigned", null, null, fromDate.Value.Date, fromDate.Value.AddDays(1), null, null, null, null, null, conditions);
            //    var unassignedRecordData = new AnalyticsRecord { CompanyId = companyId, DateTime = fromDate.Value.Date, StaffId = "unassigned", IsTeamUnassigned = true, Data = unassignedRecord, Conditions = conditions };
            //    _appDbContext.AnalyticsRecords.AddRange(unassignedRecordData);
            //    await _appDbContext.SaveChangesAsync();

            // result.Add(unassignedRecordData);
            // }
            // else
            // {
            //    result.Add(existingUnassignedData);
            // }
            return result;
        }

        public async Task<string> ExportDataForCustomField(
            string companyId,
            string analyticsEmailNotificationConfigId,
            DateTime messageFrom,
            DateTime messageTo)
        {
            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == companyId);

            var analyticsEmailNotificationConfig = await _appDbContext.AnalyticsEmailNotificationConfigs
                .FirstOrDefaultAsync(x => x.Id == analyticsEmailNotificationConfigId);

            if (company == null ||
                analyticsEmailNotificationConfig == null)
            {
                return string.Empty;
            }

            var customField = await _appDbContext.CompanyCustomUserProfileFields
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == analyticsEmailNotificationConfig.CompanyCustomUserProfileFieldId);

            if (customField == null)
            {
                return string.Empty;
            }

            var fieldValues = await _appDbContext.UserProfileCustomFields
                .Where(
                    x =>
                        x.CompanyDefinedField.CompanyId == companyId
                        && x.CompanyDefinedField.FieldName == customField.FieldName)
                .GroupBy(x => x.Value)
                .Select(x => x.Key)
                .ToListAsync();

            // Header
            var response = string.Empty;

            var header = "\"\"," +
                         "\"Response Time To New Customers\"," +
                         "\"Response Time To All Messages\"," +
                         "\"Newly Added Contacts\"," +
                         "\"Enquiries from New Customers\"," +
                         "\"All Active conversations\"," +
                         "\"All Conversations\"," +
                         "\"Unique Active conversations\"," +
                         "\"Unique Conversations\"," +
                         "\"Total Messages Sent\"," +
                         "\"Total Messages Received\"," +
                         "\"Total Automated Messages Sent\"," +
                         "\"Total Broadcast Messages\"," +
                         "\"Total Delivered Broadcast Messages\"," +
                         "\"Total Read Broadcast Messages\"," +
                         "\"Total Replied Broadcast Messages\"," +
                         "\"Total Bounced Broadcast Messages\"";

            response += header + Environment.NewLine;

            // response += $"Condition: ";
            // response += Environment.NewLine;
            // if (messageFrom == null)
            // {
            //     messageFrom = DateTime.Now.AddDays(-7);
            // }
            //
            // if (messageTo == null)
            // {
            //     messageTo = DateTime.Now;
            // }

            // Company data
            var companyData = await GetRecords(
                company.CompanyName,
                companyId,
                null,
                null,
                null,
                messageFrom,
                messageTo,
                null,
                null,
                null,
                null,
                null,
                null);

            response += $"{companyData}";

            response += "\n-------------\n";
            foreach (var fieldValue in fieldValues)
            {
                response += await GetRecords(
                    fieldValue,
                    companyId,
                    messageFrom: messageFrom,
                    messageTo: messageTo,
                    conditions: new List<Condition>()
                    {
                        new Condition()
                        {
                            FieldName = customField.FieldName,
                            ConditionOperator = SupportedOperator.Contains,
                            Values = new List<string>()
                            {
                                fieldValue
                            }
                        }
                    }) + Environment.NewLine;
            }

            return response;
        }

        public async Task<string> ExportDataForCompany(
            string companyId,
            long staffId,
            string staffUserId,
            DateTime messageFrom,
            DateTime messageTo,
            string status = null,
            string channels = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null,
            List<Condition> conditions = null,
            Func<int, ValueTask> onComplete = null)
        {
            var companyUser = await _appDbContext.UserRoleStaffs
                .Include(x => x.Company)
                .FirstOrDefaultAsync(x => x.Id == staffId);

            // Header
            var header = "\"\"," +
                         "\"Response Time To New Customers\"," +
                         "\"Response Time To All Messages\"," +
                         "\"Newly Added Contacts\"," +
                         "\"Enquiries from New Customers\"," +
                         "\"All Active conversations\"," +
                         "\"All Conversations\"," +
                         "\"Unique Active conversations\"," +
                         "\"Unique Conversations\"," +
                         "\"Total Messages Sent\"," +
                         "\"Total Messages Received\"," +
                         "\"Total Automated Messages Sent\"," +
                         "\"Total Broadcast Messages\"," +
                         "\"Total Delivered Broadcast Messages\"," +
                         "\"Total Read Broadcast Messages\"," +
                         "\"Total Replied Broadcast Messages\"," +
                         "\"Total Bounced Broadcast Messages\"";

            var exportResult = string.Empty;

            exportResult += $"{header}\n";
            exportResult += $"Condition: ";

            if (!string.IsNullOrEmpty(staffUserId))
            {
                exportResult += $"StaffId: {staffUserId}    ";
            }

            if (!string.IsNullOrEmpty(status))
            {
                exportResult += $"Status: {status}  ";
            }

            if (!string.IsNullOrEmpty(channels))
            {
                exportResult += $"Channels {channels}";

                if (string.IsNullOrEmpty(channelIds))
                {
                    exportResult += $" ChannelIds: {channelIds} ";
                }
            }

            if (!string.IsNullOrEmpty(tags))
            {
                exportResult += $"Labels: {tags}    ";
            }

            if (teamId.HasValue)
            {
                exportResult += $"TeamId: {teamId}    ";
            }

            exportResult += "\n";

            if (string.IsNullOrEmpty(staffUserId)
                && !teamId.HasValue)
            {
                // Company data
                var record = await GetRecords(
                    companyUser.Company.CompanyName,
                    companyUser.CompanyId,
                    staffUserId,
                    status,
                    channels,
                    messageFrom,
                    messageTo,
                    channelIds,
                    tags,
                    teamId,
                    isTeamUnassigned,
                    null,
                    conditions);

                exportResult += $"{record}";

                exportResult += "\n-------------\n";

                var completed = 1;

                if (onComplete != null)
                {
                    await onComplete(completed);
                }

                var staffs = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyUser.CompanyId && x.Id != 1)
                    .Include(x => x.Identity)
                    .ToListAsync();

                foreach (var staff in staffs)
                {
                    var staffRecord = await GetRecords(
                        $"{staff.Identity.UserName} ({staff.Identity.FirstName})",
                        companyUser.CompanyId,
                        staff.IdentityId,
                        status,
                        channels,
                        messageFrom,
                        messageTo,
                        channelIds,
                        tags,
                        null,
                        isTeamUnassigned,
                        null,
                        conditions);

                    completed++;
                    exportResult += $"{staffRecord}\n";

                    if (onComplete != null)
                    {
                        await onComplete(completed);
                    }
                }

                exportResult += "\n-------------\n";
            }
            else if (teamId.HasValue)
            {
                var teamShowed = new List<string>();
                var team = await _appDbContext.CompanyStaffTeams
                    .Include(x => x.Members)
                    .ThenInclude(x => x.Staff.Identity)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.Id == teamId);

                var completed = 1;

                if (onComplete != null)
                {
                    await onComplete(completed);
                }

                exportResult += $"\n";

                var teamRecord = await GetRecords(
                    $"[TEAM]{team.TeamName}",
                    companyUser.CompanyId,
                    staffUserId,
                    status,
                    channels,
                    messageFrom,
                    messageTo,
                    channelIds,
                    tags,
                    teamId,
                    isTeamUnassigned,
                    null,
                    conditions);
                exportResult += $"{teamRecord}\n";

                exportResult += "\n[Members]:\n";

                foreach (var staff in team.Members)
                {
                    teamShowed.Add(staff.Staff.IdentityId);

                    var teamMemberRecord = await GetRecords(
                        $"{staff.Staff.Identity.UserName} ({staff.Staff.Identity.FirstName})",
                        companyUser.CompanyId,
                        staff.Staff.IdentityId,
                        status,
                        channels,
                        messageFrom,
                        messageTo,
                        channelIds,
                        tags,
                        teamId,
                        isTeamUnassigned,
                        null,
                        conditions);

                    completed++;
                    if (onComplete != null)
                    {
                        await onComplete(completed);
                    }

                    exportResult += $"{teamMemberRecord}\n";
                }
            }
            else if (!string.IsNullOrEmpty(staffUserId))
            {
                var staff = await _appDbContext.UserRoleStaffs
                    .Include(x => x.Identity)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.IdentityId == staffUserId);

                var staffRecord = await GetRecords(
                    $"{staff.Identity.UserName} ({staff.Identity.FirstName})",
                    companyUser.CompanyId,
                    staff.IdentityId,
                    status,
                    channels,
                    messageFrom,
                    messageTo,
                    channelIds,
                    tags,
                    teamId,
                    isTeamUnassigned,
                    true,
                    conditions,
                    onComplete);

                exportResult += $"{staffRecord}\n";
            }

            exportResult += "\n-------------\n";

            return exportResult;
        }

        private async Task<string> GetRecords(
            string name,
            string companyId,
            string staffId = null,
            string status = null,
            string channels = null,
            DateTime? messageFrom = null,
            DateTime? messageTo = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null,
            bool? isExportDetails = null,
            List<Condition> conditions = null,
            Func<int, ValueTask> onComplete = null)
        {
            try
            {
                var summaryData = await GetSummaryData(
                    companyId,
                    staffId,
                    teamId,
                    messageFrom,
                    messageTo,
                    conditions,
                    onComplete);

                var firstMessageResult = $"No data";
                var allMessageResult = $"No data";

                if (summaryData.Summary.ResponseTimeForFirstMessages.TotalSeconds > 0)
                {
                    firstMessageResult =
                        $"{summaryData.Summary.ResponseTimeForFirstMessages.Days} days {summaryData.Summary.ResponseTimeForFirstMessages.Hours} hours {summaryData.Summary.ResponseTimeForFirstMessages.Minutes} minutes {summaryData.Summary.ResponseTimeForFirstMessages.Seconds} seconds";
                }

                if (summaryData.Summary.ResponseTimeForAllMessages.TotalSeconds > 0)
                {
                    allMessageResult =
                        $"{summaryData.Summary.ResponseTimeForAllMessages.Days} days {summaryData.Summary.ResponseTimeForAllMessages.Hours} hours {summaryData.Summary.ResponseTimeForAllMessages.Minutes} minutes {summaryData.Summary.ResponseTimeForAllMessages.Seconds} seconds";
                }

                var record = $"\"{name}\"," +
                             $"\"{firstMessageResult}\"," +
                             $"\"{allMessageResult}\"," +
                             $"\"{summaryData.Summary.NumberOfContacts}\"," +
                             $"\"{summaryData.Summary.NumberOfNewEnquires}\"," +
                             $"\"{summaryData.Summary.NumberOfActiveConversations}\"," +
                             $"\"{summaryData.Summary.NumberOfAllConversations}\"," +
                             $"\"{summaryData.Summary.NumberOfUniqueActiveConversations}\"," +
                             $"\"{summaryData.Summary.NumberOfUniqueConversations}\"," +
                             $"\"{summaryData.Summary.NumberOfMessagesSent}\"," +
                             $"\"{summaryData.Summary.NumberOfMessageReceived}\"," +
                             $"\"{summaryData.Summary.NumberOfAutomatedMessages}\"," +
                             $"\"{summaryData.Summary.NumberOfBroadcastSent}\"," +
                             $"\"{summaryData.Summary.NumberOfBroadcastDelivered}\"," +
                             $"\"{summaryData.Summary.NumberOfBroadcastRead}\"," +
                             $"\"{summaryData.Summary.NumberOfBroadcastReplied}\"," +
                             $"\"{summaryData.Summary.NumberOfBroadcastBounced}\"";

                return record;
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        private async Task<List<UserProfile>> GetConditionalUserProfiles(
            string companyId,
            string staffId = null,
            long? teamId = null,
            List<Condition> conditions = null,
            Func<int, ValueTask> onComplete = null)
        {
            var channelList = new List<string>();
            var channelIdList = new List<string>();

            if (conditions?.Count > 0)
            {
                var condLastChannel = conditions
                    .FirstOrDefault(
                        x =>
                            !string.IsNullOrEmpty(x.FieldName)
                            && x.FieldName.ToLower() == "lastchannel"
                            && x.ConditionOperator == SupportedOperator.Contains);

                if (condLastChannel != null)
                {
                    foreach (var value in condLastChannel.Values)
                    {
                        var set = value.Split(":");

                        if (set.Count() > 1)
                        {
                            if (!channelList.Contains(set[0]))
                            {
                                channelList.Add(set[0]);
                            }

                            channelIdList.Add(value.Substring(value.IndexOf(":") + 1));
                        }
                    }
                }
            }

            long? staffIdLong = null;

            if (string.IsNullOrEmpty(staffId))
            {
                staffIdLong = _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyId && x.IdentityId == staffId).Select(
                        x => new
                        {
                            x.Id
                        }).FirstOrDefault()?.Id;
            }

            List<UserProfile> allContacts = new List<UserProfile>();
            List<UserProfile> userProfiles = new List<UserProfile>();

            if (conditions != null)
            {
                var userProfileResult =
                    await _userProfileService.GetUserProfilesIdsByFields(
                        companyId,
                        conditions,
                        null,
                        null,
                        "createdat",
                        "desc",
                        staffIdLong,
                        channelList,
                        channelIdList,
                        StaffUserRole.Admin);

                // allContacts = await _appDbContext.UserProfiles.Where(x => userProfileResult.UserProfileIds.Contains(x.Id)).ToListAsync(); // Used Get Ids for reducing the unnecessary data
                for (var i = 0; i < userProfileResult.TotalResult; i += 10000)
                {
                    var contacts = await _appDbContext.UserProfiles
                        .Where(
                            x => userProfileResult.UserProfileIds
                                .Skip(i)
                                .Take(10000)
                                .ToList()
                                .Contains(x.Id))
                        .ToListAsync();

                    if (contacts.Count > 0)
                    {
                        allContacts.AddRange(contacts);
                    }
                }

                if (teamId.HasValue
                    && string.IsNullOrEmpty(staffId))
                {
                    var results = _appDbContext.CompanyTeamMembers
                        .Where(y => y.CompanyTeamId == teamId.Value)
                        .Select(y => y.StaffId);

                    var userProfileIdsAssignedToTeam = await _appDbContext.Conversations
                        .Where(
                            conversation =>
                                conversation.CompanyId == companyId
                                && (conversation.AssignedTeamId == teamId.Value
                                    || results.Contains(conversation.AssigneeId.Value)))
                        .Select(conversation => conversation.UserProfileId)
                        .ToListAsync();

                    allContacts = allContacts
                        .Where(profile => userProfileIdsAssignedToTeam.Contains(profile.Id))
                        .ToList();
                }

                if (!string.IsNullOrEmpty(staffId))
                {
                    if (staffId == "unassigned")
                    {
                        var companyConversationDtos = await _appDbContext.Conversations
                            .Where(conversation => conversation.CompanyId == companyId)
                            .Select(
                                conversation => new
                                {
                                    conversation.UserProfileId, conversation.AssigneeId
                                })
                            .ToListAsync();

                        allContacts = allContacts
                            .Where(
                                profile =>
                                    companyConversationDtos
                                        .Where(conversation => !conversation.AssigneeId.HasValue)
                                        .Select(conversation => conversation!.UserProfileId)
                                        .Contains(profile.Id) ||
                                    companyConversationDtos
                                        .All(conversation => !conversation.UserProfileId.Equals(profile.Id)))
                            .ToList();
                    }
                    else
                    {
                        var userProfileIdsAssignedOrCollaborateToStaff = await _appDbContext.Conversations
                            .Where(
                                conversation =>
                                    conversation.CompanyId == companyId
                                    && (conversation.Assignee.IdentityId == staffId
                                        || conversation.AdditionalAssignees.Any(
                                            collaborator =>
                                                collaborator.Assignee.IdentityId == staffId &&
                                                collaborator.CompanyId == companyId)))
                            .Select(conversation => conversation.UserProfileId)
                            .ToListAsync();

                        allContacts = allContacts
                            .Where(
                                profile =>
                                    userProfileIdsAssignedOrCollaborateToStaff.Contains(profile.Id))
                            .ToList();
                    }
                }

                userProfiles = allContacts;
            }
            else
            {
                var userProfilesQ = _appDbContext.UserProfiles
                    .Where(
                        profile =>
                            profile.CompanyId == companyId
                            && profile.ActiveStatus == ActiveStatus.Active);

                if (teamId.HasValue && string.IsNullOrEmpty(staffId))
                {
                    var teamMemberStaffIdsQueryable = _appDbContext.CompanyTeamMembers
                        .Where(teamMember => teamMember.CompanyTeamId == teamId.Value)
                        .Select(teamMember => teamMember.StaffId);

                    userProfilesQ = userProfilesQ
                        .Where(
                            profile =>
                                profile.Conversation.AssignedTeamId == teamId.Value ||
                                teamMemberStaffIdsQueryable.Contains(profile.Conversation.AssigneeId.Value));
                }

                if (!string.IsNullOrEmpty(staffId))
                {
                    if (staffId == "unassigned")
                    {
                        userProfilesQ = userProfilesQ
                            .Where(
                                profile =>
                                    !profile.Conversation.AssigneeId.HasValue ||
                                    !_appDbContext.Conversations.Any(
                                        conversation =>
                                            conversation.CompanyId == companyId
                                            && conversation.UserProfileId.Equals(profile.Id)));
                    }
                    else
                    {
                        userProfilesQ = userProfilesQ
                            .Where(
                                profile =>
                                    profile.Conversation.Assignee.IdentityId == staffId ||
                                    profile.Conversation.AdditionalAssignees
                                        .Any(
                                            collaborator =>
                                                collaborator.Assignee.IdentityId == staffId));
                    }
                }

                userProfiles = await userProfilesQ.ToListAsync();
            }

            return userProfiles;
        }

        private async Task<AnalyticsData> GetRecords(
            string companyId,
            DateTime fromWithTimeZone,
            DateTime toWithTimeZone,
            string staffUserId = null,
            List<string> allUserProfileIds = null,
            List<string> newUserProfileIds = null)
        {
            var timeZoneId = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == companyId)
                .Select(x => x.TimeZoneInfoId)
                .FirstOrDefaultAsync();

            var conversationMessagesQuery =
                _conversationMessageService.GetConversationMessagesHistory(
                    companyId,
                    staffUserId,
                    null,
                    null,
                    fromWithTimeZone,
                    toWithTimeZone);

            // fetch from DB then could join with local lists
            var conversationMessagesEnum = await conversationMessagesQuery
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Channel != ChannelTypes.Note
                        && x.MessageType != MessageTypes.System
                        && x.DeliveryType != DeliveryType.AutomatedMessage
                        && x.DeliveryType != DeliveryType.FlowHubAction
                        && x.DeliveryType != DeliveryType.AiAgentAction
                        && x.CreatedAt >= fromWithTimeZone
                        && x.CreatedAt < toWithTimeZone)
                .ToListAsync();

            var newCustomersConversationsEnum = await _appDbContext.Conversations
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.CreatedAt >= fromWithTimeZone
                        && x.CreatedAt < toWithTimeZone
                        && x.ActiveStatus == ActiveStatus.Active
                        && x.ChatHistory.Any(m => !m.IsSentFromSleekflow))
                .ToListAsync();

            var newUserProfileIdsEnum = await _appDbContext.UserProfiles
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.ActiveStatus == ActiveStatus.Active
                        && x.CreatedAt >= fromWithTimeZone
                        && x.CreatedAt < toWithTimeZone)
                .ToListAsync();

            var conversationMessagesRespTimeEnum = await conversationMessagesQuery
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Channel != ChannelTypes.Note
                        && x.MessageType != MessageTypes.System
                        && x.DeliveryType != DeliveryType.AutomatedMessage
                        && x.DeliveryType != DeliveryType.FlowHubAction
                        && x.DeliveryType != DeliveryType.AiAgentAction
                        && x.CreatedAt >= fromWithTimeZone
                        && x.CreatedAt < toWithTimeZone)
                .ToListAsync();

            // Apply Condition
            if (allUserProfileIds != null)
            {
                var conversationDtos = await _appDbContext.Conversations
                    .Where(x => x.CompanyId == companyId)
                    .Select(
                        x => new
                        {
                            x.Id, x.UserProfileId
                        })
                    .ToListAsync();

                var conditionalConversationIds = conversationDtos
                    .Join(
                        allUserProfileIds,
                        conversation => conversation.UserProfileId,
                        userProfileId => userProfileId,
                        (conversation, userProfileId) => conversation.Id)
                    .ToList();

                conversationMessagesEnum = conversationMessagesEnum
                    .Join(
                        conditionalConversationIds,
                        conversationMessage => conversationMessage.ConversationId,
                        convId => convId,
                        (conversationMessage, convId) => conversationMessage)
                    .ToList();

                newCustomersConversationsEnum = newCustomersConversationsEnum
                    .Join(
                        conditionalConversationIds,
                        conversation => conversation.Id,
                        convId => convId,
                        (conversation, convId) => conversation)
                    .ToList();

                conversationMessagesRespTimeEnum = conversationMessagesRespTimeEnum
                    .Join(
                        conditionalConversationIds,
                        conversationMessage => conversationMessage.ConversationId,
                        convId => convId,
                        (conversationMessage, convId) => conversationMessage)
                    .ToList();
            }

            if (newUserProfileIds != null)
            {
                newUserProfileIdsEnum = newUserProfileIdsEnum
                    .Join(
                        newUserProfileIds,
                        userProfile => userProfile.Id,
                        userProfileId => userProfileId,
                        (userProfile, userProfileId) => userProfile)
                    .ToList();
            }

            return AnalyticsUtils.ConstructingAnalyticsData(
                conversationMessagesEnum,
                newCustomersConversationsEnum,
                newUserProfileIdsEnum,
                conversationMessagesRespTimeEnum,
                fromWithTimeZone,
                timeZoneId);
        }

        public void DailyExportCustomFieldAnalyticCsv()
        {
            var companyConfigs = _appDbContext.AnalyticsEmailNotificationConfigs;

            // TODO: a for loop here to trigger the background tasks
            DateTime now = DateTime.UtcNow;
            DateTime messageTo = now;
            DateTime messageFrom = messageTo.AddDays(-1);

            foreach (var companyConfig in companyConfigs)
            {
                BackgroundJob.Enqueue<IAnalyticsService>(
                    x =>
                        x.HandleCustomFieldAnalyticCsv(
                            companyConfig.CompanyId,
                            companyConfig.Id,
                            messageFrom,
                            messageTo));
            }
        }

        public async Task HandleCustomFieldAnalyticCsv(
            string companyId,
            string analyticsEmailNotificationConfigId,
            DateTime messageFrom,
            DateTime messageTo)
        {
            var csv = await ExportDataForCustomField(
                companyId,
                analyticsEmailNotificationConfigId,
                messageFrom,
                messageTo);

            var ms = new MemoryStream();
            var sw = new StreamWriter(ms, Encoding.UTF8);
            await sw.WriteAsync(csv);
            await sw.FlushAsync();
            ms.Seek(0, SeekOrigin.Begin);

            var attachmentBytes = ms.ToArray();
            var fileName = $"Conversation Report - {messageTo:yyyy-MM-dd}.csv";

            var attachments = new Dictionary<string, byte[]>
            {
                {
                    fileName, attachmentBytes
                }
            };

            await _emailNotificationService.SendCustomFieldAnalytics(
                companyId,
                analyticsEmailNotificationConfigId,
                messageTo,
                attachments);
        }

        private static TimeSpan GetAverage(List<TimeSpan> timeSpans)
        {
            double responseTimeForAllMessagesAverage = timeSpans.Average(timeSpan => timeSpan.Ticks);
            long longAverageTicks = Convert.ToInt64(responseTimeForAllMessagesAverage);

            return new TimeSpan(longAverageTicks);
        }
    }
}