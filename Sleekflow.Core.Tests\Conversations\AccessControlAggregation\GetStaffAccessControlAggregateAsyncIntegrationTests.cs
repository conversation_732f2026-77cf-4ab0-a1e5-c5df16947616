using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Sleekflow.Core.Tests.TestCommon;
using TestContainers.Container.Abstractions.Hosting;
using TestContainers.Container.Database.Hosting;
using TestContainers.Container.Database.MsSql;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationQueryables;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.TenantHubDomain.Services;

namespace Sleekflow.Core.Tests.Conversations.AccessControlAggregation;

[TestFixture]
public class GetStaffAccessControlAggregateAsyncIntegrationTests
{
    private MsSqlContainer _azureSqlContainer;
    private IDbContextService _dbContextService;
    private IAccessControlAggregationService _accessControlAggregationService;
    private Mock<IRbacService> _rbacServiceMock;
    private IServiceProvider _serviceProvider;
    private IMentionQueryableResolver _mentionQueryableResolver;


    [OneTimeSetUp]
    public async Task OneTimeSetUp()
    {
        // Set up the containerized test database
        _azureSqlContainer = new ContainerBuilder<MsSqlContainer>()
            .ConfigureDatabaseConfiguration("testdb", "SAERsdf123456!!", "YOURpassword123")
            .ConfigureDockerImageName("mcr.microsoft.com/azure-sql-edge:latest")
            .Build();

        try
        {
            await _azureSqlContainer.StartAsync(); // Use 'await' instead of .Wait()
            Console.WriteLine("Azure SQL TestContainer started successfully.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error starting Azure SQL TestContainer: {ex}");
            throw;
        }

        _dbContextService = DatabaseHelper.GetDbContextService(_azureSqlContainer.GetConnectionString());

        var context = _dbContextService.GetDbContext();
        await context.Database.EnsureCreatedAsync(); // Creates schema without migrations

        _rbacServiceMock = new Mock<IRbacService>();

        // Setup mock service provider
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton(_rbacServiceMock.Object);
        _serviceProvider = serviceCollection.BuildServiceProvider();

        _mentionQueryableResolver = new MentionQueryableResolver();

        _accessControlAggregationService = new AccessControlAggregationService(
            _dbContextService,
            _serviceProvider,
            NullLogger<AccessControlAggregationService>.Instance,
            _mentionQueryableResolver);
    }

    [OneTimeTearDown]
    public async Task OneTimeTearDown()
    {
        await _azureSqlContainer.StopAsync(); // Ensures proper container cleanup
    }

    [Test]
    public async Task can_get_correct_staff_access_control_aggregate()
    {
        var dbContext = _dbContextService.GetDbContext();
        var companyId = "TestCompanyId";

        // Arrange
        var company = new Company
        {
            Id = companyId,
        };

        // Act
        dbContext.CompanyCompanies.Add(company);
        await dbContext.SaveChangesAsync();

        var teamA = new CompanyTeam
        {
            CompanyId = companyId,
            DefaultChannels = new List<TargetedChannelModel>()
            {
                new ()
                {
                    channel = "whatsappcloudapi",
                    ids = new List<string> { "1" }
                }
            }
        };

        // Act
        dbContext.CompanyStaffTeams.Add(teamA);

        var staffA = new Staff
        {
            CompanyId = companyId,
            RoleType = StaffUserRole.Admin
        };

        var staffB = new Staff
        {
            CompanyId = companyId,
            RoleType = StaffUserRole.Admin
        };

        var staffC = new Staff
        {
            CompanyId = companyId, RoleType = StaffUserRole.Admin
        };

        var bulkInsertStaffList = new List<Staff>
        {
            staffA, staffB, staffC
        };

        dbContext.UserRoleStaffs.AddRange(bulkInsertStaffList);
        await dbContext.SaveChangesAsync();

        var teamMemberA = new TeamMember
        {
            CompanyTeamId = teamA.Id,
            StaffId = staffA.Id
        };

        var teamMemberB = new TeamMember
        {
            CompanyTeamId = teamA.Id,
            StaffId = staffB.Id
        };

        // var teamMemberC = new TeamMember()
        // {
        //     CompanyTeamId = teamA.Id, StaffId = staffC.Id
        // };

        var teamAMemberList = new List<TeamMember>
        {
            teamMemberA, teamMemberB
        };

        dbContext.CompanyTeamMembers.AddRange(teamAMemberList);
        await dbContext.SaveChangesAsync();

        var adminRolePermission = new RolePermission
        {
            CompanyId =  companyId,
            StoredPermission = new Permission
            {
                IsShowDefaultChannelMessagesOnly = true
            },
            StaffUserRole = StaffUserRole.Admin
        };

        dbContext.CompanyRolePermissions.AddRange(adminRolePermission);

        await dbContext.SaveChangesAsync();


        // Assert

        var retrievedStaffA = await dbContext.UserRoleStaffs.FindAsync(staffA.Id);
        Assert.That(retrievedStaffA, Is.Not.Null);

        var retrievedStaffB = await dbContext.UserRoleStaffs.FindAsync(staffB.Id);
        Assert.That(retrievedStaffB, Is.Not.Null);

        var retrievedTeamA = await dbContext.CompanyStaffTeams.FindAsync(teamA.Id);
        Assert.That(retrievedTeamA, Is.Not.Null);

        var actualStaff =
            await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(staffA.Id, "TestCompanyId");

        var expectTeamAccessControlAggregate = new TeamAccessControlAggregate
        {
            Id = teamA.Id,
            TeamDefaultChannels = new TeamDefaultChannels
            {
                ChannelTypeToChannelIdentityIds = new Dictionary<string, List<string>>()
                {
                    {
                        "whatsappcloudapi", ["1"]
                    }
                }
            },
            TeamMemberStaffIds = [staffA.Id, staffB.Id]
        };

        var expectedStaff = new StaffAccessControlAggregate
        {
            StaffId = staffA.Id,
            RoleType = StaffUserRole.Admin,
            CompanyId = companyId,
            AssociatedTeams = [expectTeamAccessControlAggregate]
        };

        // Assert
        Assert.IsNotNull(actualStaff);
        Assert.That(actualStaff.StaffId, Is.EqualTo(expectedStaff.StaffId));
        Assert.That(actualStaff.CompanyId, Is.EqualTo(expectedStaff.CompanyId));
        Assert.That(actualStaff.RoleType, Is.EqualTo(expectedStaff.RoleType));
        Assert.That(actualStaff.AssociatedTeams.Count, Is.EqualTo(expectedStaff.AssociatedTeams.Count));

        var expectedTeamsById = expectedStaff.AssociatedTeams.ToDictionary(team => team.Id);

        foreach (var actualTeam in actualStaff.AssociatedTeams)
        {
            if (expectedTeamsById.TryGetValue(actualTeam.Id, out var expectedTeam))
            {
                Assert.Multiple(() =>
                {
                    Assert.That(actualTeam.Id, Is.EqualTo(expectedTeam.Id));
                    Assert.That(
                        actualTeam.TeamDefaultChannels.ChannelTypeToChannelIdentityIds.Count,
                        Is.EqualTo(expectedTeam.TeamDefaultChannels.ChannelTypeToChannelIdentityIds.Count));
                    Assert.That(actualTeam.TeamMemberStaffIds, Is.EquivalentTo(expectedTeam.TeamMemberStaffIds));
                });
            }
            else
            {
                Assert.Fail($"Unexpected team ID: {actualTeam.Id}");
            }
        }

    }
}