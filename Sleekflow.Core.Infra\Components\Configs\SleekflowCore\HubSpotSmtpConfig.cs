using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Components.Configs.SleekflowCore;

public class HubSpotSmtpConfig
{
    [JsonProperty("username")]
    public string Username { get; set; }

    [JsonProperty("password")]
    public string Password { get; set; }

    [JsonProperty("send_execution_usage_reached_threshold_email")]
    public SendExecutionUsageReachedThresholdEmailConfig SendExecutionUsageReachedThresholdEmail { get; set; }

    public HubSpotSmtpConfig(string username, string password,
        SendExecutionUsageReachedThresholdEmailConfig sendExecutionUsageReachedThresholdEmail)
    {
        Username = username;
        Password = password;
        SendExecutionUsageReachedThresholdEmail = sendExecutionUsageReachedThresholdEmail;
    }
}

public class SendExecutionUsageReachedThresholdEmailConfig
{
    [<PERSON>son<PERSON>roper<PERSON>("username")]
    public string Username { get; set; }

    [JsonProperty("password")]
    public string Password { get; set; }

    public SendExecutionUsageReachedThresholdEmailConfig(string username, string password)
    {
        Username = username;
        Password = password;
    }

}