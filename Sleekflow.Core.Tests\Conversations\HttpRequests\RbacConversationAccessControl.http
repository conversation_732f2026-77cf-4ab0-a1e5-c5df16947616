POST https://localhost:5000/auth0/account/getusertoken
content-type: application/json-patch+json

{
  "username": "",
  "password": ""
}

> {% client.global.set("token", response.body.accessToken); %}

### Rbac Conversation Accessible
POST https://{{host}}/conversation/accessible
content-type: application/json
Authorization: Bearer {{token}}

{
  //"conversationId": "0012e3cc-0a2c-401c-bec6-d99a574bf65d"
  "conversationId": "950cea09-a13a-4e3b-820b-4be04490e5a1"
}
