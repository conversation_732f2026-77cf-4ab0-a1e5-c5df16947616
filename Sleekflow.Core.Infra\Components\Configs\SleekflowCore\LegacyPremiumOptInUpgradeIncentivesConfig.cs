using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Components.Configs.SleekflowCore;

public class LegacyPremiumOptInUpgradeIncentivesConfig
{
    [JsonProperty("period_start")]
    public string PeriodStart { get; set; }

    [JsonProperty("period_end")]
    public string PeriodEnd { get; set; }

    public LegacyPremiumOptInUpgradeIncentivesConfig(string periodStart, string periodEnd)
    {
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
    }
}