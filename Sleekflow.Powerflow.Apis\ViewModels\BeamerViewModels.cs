using Newtonsoft.Json;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class BeamerNPSResponse
{
    [JsonProperty("date")]
    public string Date { get; set; }

    [JsonProperty("score")]
    public int Score { get; set; }

    [JsonProperty("userEmail")]
    public string UserEmail { get; set; }
}

public class BeamerNPSCountResponse
{
    [JsonProperty("count")]
    public int Count { get; set; }
}

public class SyncBeamerNPSWithoutHubSpotRequest
{
    [JsonProperty("startPage")]
    public int? StartPage { get; set; }

    [JsonProperty("endPage")]
    public int? EndPage { get; set; }
}