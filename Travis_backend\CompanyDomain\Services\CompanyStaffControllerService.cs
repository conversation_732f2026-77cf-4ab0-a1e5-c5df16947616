using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Azure.ResourceManager.Media.Models;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.TenantHub.Api;
using Sleekflow.Apis.TenantHub.Model;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.CompanyDomain.Utils;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.SignalR;
using Travis_backend.TenantHubDomain.Services;
using StaffWithoutCompanyResponse = Travis_backend.ConversationDomain.ViewModels.StaffWithoutCompanyResponse;

namespace Travis_backend.CompanyDomain.Services;

/// <inheritdoc />
public class CompanyStaffControllerService : ICompanyStaffControllerService
{
    /// <summary>
    /// UserRoleStaffRepository.
    /// </summary>
    private readonly IUserRoleStaffRepository _userRoleStaffRepository;

    private readonly IManagementRbacApi _managementRbacApi;

    private readonly IManagementRolesApi _managementRolesApi;

    private readonly IRbacService _rbacService;

    private readonly IMapper _mapper;

    private readonly IStaffHooks _staffHooks;

    private readonly ISignalRService _signalRService;

    private readonly ILogger<CompanyStaffControllerService> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="CompanyStaffControllerService"/> class.
    /// </summary>
    /// <param name="userRoleStaffRepository">UserRoleStaffRepository.</param>
    public CompanyStaffControllerService(
        IUserRoleStaffRepository userRoleStaffRepository,
        IManagementRbacApi managementRbacApi,
        IManagementRolesApi managementRolesApi,
        IRbacService rbacService,
        IMapper mapper,
        IStaffHooks staffHooks,
        ISignalRService signalRService,
        ILogger<CompanyStaffControllerService> logger)
    {
        _userRoleStaffRepository = userRoleStaffRepository;
        _managementRbacApi = managementRbacApi;
        _managementRolesApi = managementRolesApi;
        _rbacService = rbacService;
        _mapper = mapper;
        _staffHooks = staffHooks;
        _signalRService = signalRService;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task<(IEnumerable<StaffOverviewResponse> Results, int Count)> GetCompanyStaffAsync(
        string companyId,
        CompanyType companyType,
        GetCompanyStaffRequestQuery requestQuery,
        int? offset,
        int? limit)
    {
        Staff companyOwnerStaff = null;
        IEnumerable<GetCompanyStaffsQueryResult> queryResult;
        IEnumerable<StaffUserRole> roles = null;
        List<string> searchRoles = new List<string>();
        List<string> excludeRoles = new List<string>();

        var rbacEnabled = _rbacService.IsRbacEnabled();
        if (rbacEnabled)
        {
            searchRoles = requestQuery.SearchRoles?.FirstOrDefault()?.Split(',').ToList() ?? new List<string>();
            excludeRoles = requestQuery.ExcludeRoles?.FirstOrDefault()?.Split(',').ToList() ?? new List<string>();
        }
        else
        {
            roles = requestQuery.SearchRoles?.Select(
                x => (StaffUserRole) Enum.Parse(typeof(StaffUserRole), x, true));
        }

        if (companyType != CompanyType.ResellerClient)
        {
            var result = await _userRoleStaffRepository.GetCompanyStaffAsync(
                companyId,
                requestQuery.SearchString,
                roles,
                requestQuery.Teams,
                requestQuery.JoinDateTime?.From,
                requestQuery.JoinDateTime?.To,
                null,
                null);

            companyOwnerStaff = await _userRoleStaffRepository.GetCompanyOwnerAsync(companyId);
            queryResult = result.Staffs;
        }
        else
        {
            var result = await _userRoleStaffRepository.GetResellerCompanyStaffAsync(
                companyId,
                requestQuery.SearchString,
                roles,
                requestQuery.Teams,
                requestQuery.JoinDateTime?.From,
                requestQuery.JoinDateTime?.To,
                null,
                null);

            companyOwnerStaff = await _userRoleStaffRepository.GetResellerClientCompanyOwnerAsync(companyId);
            queryResult = result.Staffs;
        }

        var queryResultList = queryResult?.ToList();

        // Add null/empty check
        if (queryResultList == null || queryResultList.Any() != true)
        {
            return (new List<StaffOverviewResponse>(), 0);
        }

        // Make a tenant hub call to fetch user roles information.
        var staffIds = queryResultList.Select(
            x => x.Staff.Id.ToString()).ToList();
        var rbacRoles =
            await _rbacService.GetRbacUserRoles(companyId, staffIds)
            ?? new List<RbacUserRoles>();

        var foundStaffs = queryResultList.Select(
            x => new StaffOverviewResponse
            {
                Id = x.Staff.Id,
                StaffIdentityId = x.Staff?.IdentityId,
                Username = x.Staff?.Identity?.UserName,
                DisplayName = x.Staff?.Identity?.DisplayName,
                FirstName = x.Staff?.Identity?.FirstName,
                LastName = x.Staff?.Identity?.LastName,
                Email = x.Staff?.Identity?.Email,
                Role = x.Staff?.RoleType.ToString(),
                ProfilePicture = x.Staff?.ProfilePicture,
                AssociatedTeamIds = x.Teams?.Select(team => team.CompanyTeamId).ToList(),
                IsCompanyOwner = companyOwnerStaff != null && companyOwnerStaff.Id == x.Staff?.Id,
                Position = x.Staff?.Position,
                Status = x.Staff?.Status.ToString(),
                CreatedAt = x.Staff?.Identity?.CreatedAt,
                RbacRoles = rbacRoles?.Count > 0
                    ? rbacRoles.Find(r => r.SleekflowUserId == x.Staff.IdentityId)?.Roles
                    : Enumerable.Empty<RbacRoleItem>().ToList(),
                QRCodeChannel = x.Staff?.QRCodeChannel,
                InvitationExpiredAt = x.Staff?.Identity?.InviteTokenExpireAt,
                IsInvitationPending =
                    x.Staff?.Identity?.UserName is not null && x.Staff.Identity.UserName.StartsWith("invite."),
            }).ToList();

        if (requestQuery.IsInvitePending != null)
        {
            foundStaffs = foundStaffs.Where(
                x => x.IsInvitationPending == requestQuery.IsInvitePending)
                .ToList();
            // requestQuery.SortBy = "invite_expired,invite_pending";
            // requestQuery.IsDesc = true;
        }

        // New version of code to Filter the staffs based on the rbacRoles from the tenant hub.
        var matchedStaffs = await ApplyRbacFiltering(
            foundStaffs,
            companyId,
            searchRoles,
            excludeRoles);

        var matchedStaffsList = matchedStaffs.ToList();

        // Apply sorting
        var overviewResponses = requestQuery.SortBy is not null
            ? matchedStaffsList.ApplySort(requestQuery.GetSortFields(), requestQuery.IsDesc).ToList()
            : matchedStaffsList.ToList();

        var totalCount = overviewResponses.Count;

        // Apply pagination
        var staffs =
            ApplyPagination(overviewResponses, offset ?? 0, limit ?? -1);

        return (staffs, totalCount);
    }

    /// <inheritdoc />
    public async Task<int> CountCompanyStaffAsync(string companyId, CompanyType companyType, GetCompanyStaffRequestQuery requestQuery)
    {
        var (_, count) =
            await GetCompanyStaffAsync(companyId, companyType, requestQuery, 0, int.MaxValue);

        return count;
    }

    /// <inheritdoc />
    public async Task<bool> IsCompanyOwnerAsync(string companyId, string identityId)
    {
        var companyOwnerStaff = await _userRoleStaffRepository.GetCompanyOwnerAsync(companyId);
        return companyOwnerStaff.IdentityId.Equals(identityId, StringComparison.OrdinalIgnoreCase);
    }

    public async Task<Result<StaffUserRole, string>> UpdateStaffRoleAsync(
        string companyId,
        string staffId,
        string targetRole)
    {
        try
        {
            // Convert role string to enum
            var roleResult = RoleTypeConverter.ConvertToEnum(targetRole);
            if (roleResult.IsFailure)
            {
                return Result<StaffUserRole, string>.Failure(roleResult.Error);
            }

            // Get target staff
            var targetStaff = await _userRoleStaffRepository.GetUserRoleStaffByStaffIdAsync(companyId, staffId);
            if (targetStaff == null)
            {
                return Result<StaffUserRole, string>.Failure("Staff not found");
            }

            var oldRole = targetStaff.RoleType;

            // Update role
            targetStaff.RoleType = roleResult.Value;

            // Save changes
            await _userRoleStaffRepository.UpdateUserRoleStaffAsync(targetStaff);

            _logger.LogInformation(
                "Staff role updated successfully. StaffId: {StaffId}, CompanyId: {CompanyId}, OldRole: {OldRole}, NewRole: {NewRole}",
                staffId,
                companyId,
                oldRole,
                roleResult.Value);

            var staffsVm = _mapper.Map<StaffWithoutCompanyResponse>(targetStaff);
            await _staffHooks.OnStaffRoleUpdatedAsync(
                targetStaff.CompanyId,
                targetStaff.Id,
                targetStaff.RoleType);
            await _signalRService.SignalROnStaffUpdated(targetStaff.IdentityId, staffsVm);

            return Result<StaffUserRole, string>.Success(roleResult.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to update staff role. CompanyId: {CompanyId}, StaffId: {StaffId}, TargetRole: {TargetRole}",
                companyId,
                staffId,
                targetRole);
            return Result<StaffUserRole, string>.Failure("Internal server error");
        }
    }

    private async Task<IEnumerable<StaffOverviewResponse>> ApplyRbacFiltering(
        IEnumerable<StaffOverviewResponse> staffs,
        string companyId,
        List<string> searchRoles,
        List<string> excludeRoles)
    {
        // If both searchRoles and excludeRoles are empty, return the full list
        if ((searchRoles == null || !searchRoles.Any()) && (excludeRoles == null || !excludeRoles.Any()))
        {
            return staffs;
        }

        // Materialize the staffs enumerable into a list
        var staffsList = staffs.ToList();

        var staffIds = staffsList.Select(x => x.Id.ToString()).ToList();
        var rbacRoles =
            await _rbacService.GetRbacUserRoles(companyId, staffIds)
            ?? new List<RbacUserRoles>();

        return staffsList.Where(s =>
        {
            var userRoles =
                rbacRoles.Find(r => r.SleekflowUserId == s.StaffIdentityId)?.Roles
                ?? new List<RbacRoleItem>();

            bool includeStaff =
                searchRoles?.Count == 0
                || userRoles.Exists(r => searchRoles != null
                    && searchRoles.Contains(r.RoleName, StringComparer.Ordinal));
            bool excludeStaff =
                excludeRoles?.Exists(role => userRoles.Exists(r => role.Equals(r.RoleName, StringComparison.OrdinalIgnoreCase))) ?? false;

            return includeStaff && !excludeStaff;
        });
    }

    private IEnumerable<StaffOverviewResponse> ApplyPagination(
        IEnumerable<StaffOverviewResponse> staffs,
        int offset,
        int limit)
    {
        // or you could use (limit == int.MaxValue)
        if (limit == -1)
        {
            return staffs.Skip(offset);  // Skip offset but don't limit the results
        }

        return staffs.Skip(offset).Take(limit);
    }
}