using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Travis_backend.CompanyDomain.Models;

public class WeeklyHours
{
    [JsonProperty("version")]
    public string Version { get; set; }

    [JsonProperty("1")]
    public List<Period> Monday { get; set; }

    [JsonProperty("2")]
    public List<Period> Tuesday { get; set; }

    [JsonProperty("3")]
    public List<Period> Wednesday { get; set; }

    [JsonProperty("4")]
    public List<Period> Thursday { get; set; }

    [JsonProperty("5")]
    public List<Period> Friday { get; set; }

    [JsonProperty("6")]
    public List<Period> Saturday { get; set; }

    [JsonProperty("7")]
    public List<Period> Sunday { get; set; }

    [JsonConstructor]
    public WeeklyHours(
        string version,
        List<Period> monday,
        List<Period> tuesday,
        List<Period> wednesday,
        List<Period> thursday,
        List<Period> friday,
        List<Period> saturday,
        List<Period> sunday)
    {
        Version = version;
        Monday = monday;
        Tuesday = tuesday;
        Wednesday = wednesday;
        Thursday = thursday;
        Friday = friday;
        Saturday = saturday;
        Sunday = sunday;
    }
}

public class Period
{
    [JsonProperty("start")]
    public TimeOnly Start { get; set; }

    [JsonProperty("end")]
    public TimeOnly End { get; set; }

    [JsonProperty("cross_day")]
    public bool CrossDay { get; set; }

    [JsonConstructor]
    public Period(TimeOnly start, TimeOnly end, bool crossDay)
    {
        Start = start;
        End = end;
        CrossDay = crossDay;
    }
}