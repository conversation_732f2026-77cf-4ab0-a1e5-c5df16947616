﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class RequestsForSleekFlowMetric : IDashboardMetric
{
    private readonly Output<string>? _sleekflowCoreName;
    private readonly Output<string>? _sleekflowCoreResourceId;
    private readonly Output<string>? _sleekflowCoreWorkerName;
    private readonly Output<string>? _sleekflowCoreWorkerResourceId;

    public RequestsForSleekFlowMetric(
        Output<string>? sleekflowCoreName,
        Output<string>? sleekflowCoreResourceId,
        Output<string>? sleekflowCoreWorkerName,
        Output<string>? sleekflowCoreWorkerResourceId)
    {
        _sleekflowCoreName = sleekflowCoreName;
        _sleekflowCoreResourceId = sleekflowCoreResourceId;
        _sleekflowCoreWorkerName = sleekflowCoreWorkerName;
        _sleekflowCoreWorkerResourceId = sleekflowCoreWorkerResourceId;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        var metrics = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                {
                    "aggregationType", 1
                },
                {
                    "metricVisualization", new Dictionary<string, object>
                    {
                        {
                            "displayName", "Requests"
                        },
                        {
                            "resourceDisplayName", _sleekflowCoreName!
                        }
                    }
                },
                {
                    "name", "Requests"
                },
                {
                    "namespace", "microsoft.web/sites"
                },
                {
                    "resourceMetadata", new Dictionary<string, object>
                    {
                        {
                            "id", _sleekflowCoreResourceId!
                        }
                    }
                }
            }
        };

        if (_sleekflowCoreWorkerName != null && _sleekflowCoreWorkerResourceId != null)
        {
            metrics.Add(
                new Dictionary<string, object>
                {
                    {
                        "aggregationType", 1
                    },
                    {
                        "metricVisualization", new Dictionary<string, object>
                        {
                            {
                                "displayName", "Requests"
                            },
                            {
                                "resourceDisplayName", _sleekflowCoreWorkerName!
                            }
                        }
                    },
                    {
                        "name", "Requests"
                    },
                    {
                        "namespace", "microsoft.web/sites"
                    },
                    {
                        "resourceMetadata", new Dictionary<string, object>
                        {
                            {
                                "id", _sleekflowCoreWorkerResourceId!
                            }
                        }
                    }
                });
        }

        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs = new[]
                {
                    new Dictionary<string, object>
                    {
                        {
                            "name", "options"
                        },
                        {
                            "isOptional", true
                        }
                    },
                    new Dictionary<string, object>
                    {
                        {
                            "name", "sharedTimeRange"
                        },
                        {
                            "isOptional", true
                        }
                    }
                },
                Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                Settings = new Dictionary<string, object>
                {
                    {
                        "content", new Dictionary<string, object>
                        {
                            {
                                "options", new Dictionary<string, object>
                                {
                                    {
                                        "chart", new Dictionary<string, object>
                                        {
                                            {
                                                "metrics", metrics.ToArray()
                                            },
                                            {
                                                "title", "Requests for SleekFlow"
                                            },
                                            {
                                                "titleKind", 2
                                            },
                                            {
                                                "visualization", new Dictionary<string, object>
                                                {
                                                    {
                                                        "axisVisualization", new Dictionary<string, object>
                                                        {
                                                            {
                                                                "x", new Dictionary<string, object>
                                                                {
                                                                    {
                                                                        "axisType", 2
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "y", new Dictionary<string, object>
                                                                {
                                                                    {
                                                                        "axisType", 1
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "chartType", 2
                                                    },
                                                    {
                                                        "disablePinning", true
                                                    },
                                                    {
                                                        "legendVisualization", new Dictionary<string, object>
                                                        {
                                                            {
                                                                "hideHoverCard", false
                                                            },
                                                            {
                                                                "hideLabelNames", true
                                                            },
                                                            {
                                                                "hideSubtitle", false
                                                            },
                                                            {
                                                                "isVisible", true
                                                            },
                                                            {
                                                                "position", 2
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        };
    }
}