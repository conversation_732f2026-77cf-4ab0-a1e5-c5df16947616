﻿using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using StackExchange.Redis;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CompanyDomain.ViewModels;

namespace Sleekflow.Core.Tests.Cache;

[TestFixture]
public class CacheManagerServiceTest
{
    private ICacheManagerService _cacheManagerService;
    private const string RedisConnectionString = "sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False";

    private readonly JsonSerializerSettings _jsonSerializerSettings = new ()
    {
        DateTimeZoneHandling = DateTimeZoneHandling.Utc,
        NullValueHandling = NullValueHandling.Ignore,
        ContractResolver = new CamelCasePropertyNamesContractResolver()
    };

    [SetUp]
    public void Setup()
    {
        var connectionMultiplexer = ConnectionMultiplexer.Connect(
            RedisConnectionString);

        _cacheManagerService = new CacheManagerService(
            connectionMultiplexer,
            new Mock<ILogger<CacheManagerService>>().Object);
    }

    private bool IsSameObject<T>(T obj1, T obj2)
    {
        var serializeObject1 = JsonConvert.SerializeObject(obj1, _jsonSerializerSettings);
        var serializeObject2 = JsonConvert.SerializeObject(obj2, _jsonSerializerSettings);
        return serializeObject1 == serializeObject2;
    }

    [Test]
    public async Task TestCompanyUsage()
    {
        var companyUsage = new CompanyUsage
        {
            totalContacts = 100,
            CurrentNumberOfChannels = 300,
            totalChannelAdded = 500,
            CurrentNumberOfWhatsappCloudApiChannels = 600,
            BaseMaximumNumberOfWhatsappCloudApiChannels = 250,
            BaseMaximumContacts = 180,
            BaseMaximumAutomatedMessages = 380,
            BaseMaximumApiCalls = 30
        };

        async Task<CompanyUsage> CompanyUsageFunc()
        {
            return companyUsage;
        }

        const string companyIdForTest = "companyId-Test-abcd-0123-efg";
        var getCompanyUsageCacheKeyPattern = new GetCompanyUsageCacheKeyPattern(companyIdForTest);

        var usageFromGetAndSave = await _cacheManagerService.GetAndSaveCacheAsync(
            getCompanyUsageCacheKeyPattern,
            CompanyUsageFunc);

        Assert.That(usageFromGetAndSave, !Is.Null);

        Assert.That(IsSameObject(usageFromGetAndSave, companyUsage), Is.EqualTo(true));

        await _cacheManagerService.SaveCacheAsync(getCompanyUsageCacheKeyPattern, companyUsage);
        var usageFromGet = await _cacheManagerService.GetCacheAsync(getCompanyUsageCacheKeyPattern);
        var deserializedUsageFromGet = JsonConvert.DeserializeObject<CompanyUsage>(usageFromGet);

        Assert.That(usageFromGet, !Is.Null);

        Assert.That(IsSameObject(deserializedUsageFromGet, companyUsage), Is.EqualTo(true));

        await _cacheManagerService.DeleteCacheAsync(getCompanyUsageCacheKeyPattern);

        var usageFromGetAfterDelete = await _cacheManagerService.GetCacheAsync(getCompanyUsageCacheKeyPattern);
        Assert.That(usageFromGetAfterDelete, Is.Null);
    }

    [Test]
    public async Task TestAnalyticsRecord()
    {
        var analyticsRecordCacheKeyPattern = new AnalyticsRecordCacheKeyPattern(
            "companyId--Test",
            "staffId--Test",
            1234567,
            new DateTime(2000, 01, 01),
            new DateTime(2020, 01, 01),
            "searchConditionHash--Test");

        var analyticsData = new AnalyticsData
        {
            ActiveAgents = 2,
            DateTime = new DateTime(2021, 1, 1),
            NumberOfContacts = 50,
            NumberOfAllConversations = 20,
            NumberOfAutomatedMessages = 30,
            NumberOfBroadcastBounced = 130
        };

        var analyticsRecord = new AnalyticsRecord
        {
            CompanyId = "companyId--Test",
            DateTime = new DateTime(2023, 1, 1),
            ConditionsHash = "searchConditionHash",
            Data = analyticsData
        };

        async Task<AnalyticsRecord> AnalyticsRecordFunc()
        {
            return analyticsRecord;
        }

        var usageFromGetAndSave = await _cacheManagerService.GetAndSaveCacheAsync(
            analyticsRecordCacheKeyPattern,
            AnalyticsRecordFunc);

        Assert.That(usageFromGetAndSave, !Is.Null);

        Assert.That(IsSameObject(usageFromGetAndSave, analyticsRecord), Is.EqualTo(true));

        await _cacheManagerService.SaveCacheAsync(analyticsRecordCacheKeyPattern, analyticsRecord);
        var usageFromGet = await _cacheManagerService.GetCacheAsync(analyticsRecordCacheKeyPattern);
        var deserializedUsageFromGet = JsonConvert.DeserializeObject<AnalyticsRecord>(usageFromGet);

        Assert.That(usageFromGet, !Is.Null);

        Assert.That(IsSameObject(deserializedUsageFromGet, analyticsRecord), Is.EqualTo(true));

        await _cacheManagerService.DeleteCacheAsync(analyticsRecordCacheKeyPattern);
        var usageFromGetAfterDelete = await _cacheManagerService.GetCacheAsync(analyticsRecordCacheKeyPattern);
        Assert.That(usageFromGetAfterDelete, Is.Null);
    }

    [Test]
    public async Task TestConstantKeyString()
    {
        var content = "string content";
        var key = "keyName";

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(key,content, TimeSpan.FromMinutes(5));
        var cached = await _cacheManagerService.GetCacheWithConstantKeyAsync(key);
        Assert.That(cached, Is.EqualTo(content));

        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(key);
        var cachedAfterDelete = await _cacheManagerService.GetCacheWithConstantKeyAsync(key);
        Assert.That(cachedAfterDelete, Is.Null);

        //==== test for boolean
        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            "ip-middleware1",
            true.ToString().ToLower(),
            TimeSpan.FromMinutes(15));

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            "ip-middleware2",
            false.ToString().ToLower(),
            TimeSpan.FromMinutes(15));

        var cacheDataStr1 = await _cacheManagerService.GetCacheWithConstantKeyAsync("ip-middleware1");
        var cacheData1 = bool.Parse(cacheDataStr1);
        Assert.That(cacheData1, Is.EqualTo(true));

        var cacheDataStr2 = await _cacheManagerService.GetCacheWithConstantKeyAsync("ip-middleware2");
        var cacheData2 = bool.Parse(cacheDataStr2);
        Assert.That(cacheData2, Is.EqualTo(false));
    }
}