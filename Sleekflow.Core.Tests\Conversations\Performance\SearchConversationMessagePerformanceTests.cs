using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sleekflow.Core.Tests.Constants;
using Sleekflow.Core.Tests.TestCommon;
using Travis_backend.ConversationDomain.ConversationInboxFilterConstants;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationQueryables;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;


namespace Sleekflow.Core.Tests.Conversations.Performance;

public class SearchConversationMessagePerformanceTests
{
    private ApplicationDbContext _appDbContext;
    private IMentionQueryableResolver _mentionQueryableResolver;
    private bool _firstCall = true;
    private readonly ILogger<SearchConversationMessagePerformanceTests> _logger;
    private string _companyId;
    private string _assignedTo = InboxAssignedTos.All;
    private string _status = "open";
    private RbacRole _adminRole;
    private RbacRole _teamAdminRole;
    private StaffAccessControlAggregate _staff;
    private int _limit = 20;
    private int _offset = 0;
    private string _channelList = "whatsappcloudapi";


    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseSqlServer(DbConnectionStrings.Dev)
            .Options;
        _appDbContext = new ApplicationDbContext(options);

        _companyId = TestDbConfigs.Env switch
        {
            TestDbConfigs.DevEnv => "b6d7e442-38ae-4b9a-b100-2951729768bc",
            TestDbConfigs.ProdEnv => "471a6289-b9b7-43c3-b6ad-395a1992baea",
            _ => throw new Exception()
        };

        var dbContextService = new DbContextService(
            new PersistenceContext(),
            new ApplicationDbContext(DatabaseHelper.GetDbContextOptions<ApplicationDbContext>(DbConnectionStrings.Dev)),
            new ApplicationReadDbContext(DatabaseHelper.GetDbContextOptions<ApplicationReadDbContext>(DbConnectionStrings.Dev)),
            new AnalyticDbContext(DatabaseHelper.GetDbContextOptions<AnalyticDbContext>(DbConnectionStrings.Dev)));

         _mentionQueryableResolver = new MentionQueryableResolver();

        _adminRole = new RbacRole
        {
            SleekflowRoleName = "Admin",
            SleekflowCompanyId = _companyId,
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllAssignedConversations,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations,
                RbacAssignConversationsPermissions.AssignToAnyUser
            ]
        };

        _teamAdminRole = new RbacRole
        {
            SleekflowRoleName = "TeamAdmin",
            SleekflowCompanyId = _companyId,
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations
            ]
        };

        // _staff = new StaffAccessControlAggregate
        // {
        //     CompanyId = _companyId,
        //     StaffId = 996, //DEV: 996, PROD: 15201
        //     RbacRoles = [_teamAdminRole],
        //     RoleType = StaffUserRole.TeamAdmin
        // };

        _staff = new StaffAccessControlAggregate
        {
            CompanyId = _companyId,
            StaffId = 996, //DEV: 996, PROD: 15201
            RbacRoles = [_adminRole],
            RoleType = StaffUserRole.Admin
        };

    }

    [Test]
    public async Task Performance_Measure_SearchConversationMessageQueryable()
    {
        IQueryable<ConversationMessage> newQueryable =
            new SearchConversationMessageQueryableBuilder(_appDbContext, _mentionQueryableResolver)
                .Build(
                    _companyId,
                    _assignedTo,
                    "Hello",
                    _status,
                    null,
                    "1",
                    _channelList.Split(',').ToList(),
                    _staff,
                    null);

        var stopwatch = new Stopwatch();
        stopwatch.Start();

        var conversationMessages = await newQueryable
            .OrderByDescending(x => x.CreatedAt)
            .Select(cm => cm.Id)
            .Skip(_offset)
            .Take(_limit)
            .ToListAsync();

        stopwatch.Stop();
        Console.WriteLine($"Time taken: {stopwatch.ElapsedMilliseconds} ms for SearchConversationMessageQueryableBuilder new queryable for {conversationMessages.Count} messages");
        stopwatch.Reset();
    }
}