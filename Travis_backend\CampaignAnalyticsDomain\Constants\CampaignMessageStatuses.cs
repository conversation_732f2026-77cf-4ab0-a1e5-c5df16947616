﻿using System.Collections.Immutable;

namespace Travis_backend.CampaignAnalyticsDomain.Constants;

public static class CampaignMessageStatuses
{
    public const string Sent = "sent";
    public const string Delivered = "delivered";
    public const string Read = "read";
    public const string Replied = "replied";
    public const string Bounced = "bounced";

    public static ImmutableList<string> All = ImmutableList.Create(
        Sen<PERSON>,
        Delivered,
        Read,
        Replied,
        Bounced);
}