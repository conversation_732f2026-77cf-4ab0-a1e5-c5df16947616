using System.Linq.Expressions;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationSpecifications.RbacConversationSpecifications.SpecificationBuilders;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.Conversations.Rbac.ViewConversations;

[TestFixture]
public class ConversationSpecificationTests
{
    private StaffAccessControlAggregate _adminA;
    private StaffAccessControlAggregate _teamAdminA;
    private StaffAccessControlAggregate _staffA;
    private StaffAccessControlAggregate _adminB;
    private StaffAccessControlAggregate _teamAdminB;
    private StaffAccessControlAggregate _staffB;
    private StaffAccessControlAggregate _adminC;
    private StaffAccessControlAggregate _adminInAnotherCompany;
    private StaffAccessControlAggregate _staffC;
    private StaffAccessControlAggregate _staffD;
    private TeamAccessControlAggregate _teamA;
    private TeamAccessControlAggregate _teamB;
    private RbacRole _admin;
    private RbacRole _teamAdmin;
    private RbacRole _staff;
    private string _companyId;
    private string _anotherCompanyId;

    private DbContextOptions<ConversationDbContext> _options;

    [SetUp]
    public void Setup()
    {
        _options = new DbContextOptionsBuilder<ConversationDbContext>()
            .UseInMemoryDatabase("TestConversationDb")
            .Options;

        _companyId = "sleekflow";
        _anotherCompanyId = "another_companyId";

        _admin = new RbacRole
        {
            SleekflowRoleName = "Admin",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllAssignedConversations,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations
            ]
        };

        // Conversation access rules for a team admin:
        // - Assigned to Associated Teams: Must be linked to at least one team in `associatedTeamIds`.
        // - Team Member as Contact Owner: Must have a contact owner from `teamMemberStaffIds`.
        // - Team Member as Collaborator: Must include a collaborator from `teamMemberStaffIds`.
        _teamAdmin = new RbacRole
        {
            SleekflowRoleName = "TeamAdmin",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations
            ]
        };

        _staff = new RbacRole
        {
            SleekflowRoleName = "Staff",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations
            ]
        };

        // Teams
        _teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long>
            {
                1, 2, 3
            }
        };
        _teamB = new TeamAccessControlAggregate
        {
            Id = 2,
            TeamMemberStaffIds = new List<long>
            {
                4, 5, 6
            }
        };

        // Team A members
        _adminA = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RoleType = StaffUserRole.Admin,
            RbacRoles = [_admin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };
        _teamAdminA = new StaffAccessControlAggregate
        {
            StaffId = 2,
            RoleType = StaffUserRole.TeamAdmin,
            RbacRoles = [_teamAdmin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };
        _staffA = new StaffAccessControlAggregate
        {
            StaffId = 3,
            RoleType = StaffUserRole.Staff,
            RbacRoles = [_staff],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };

        // Team B members
        _adminB = new StaffAccessControlAggregate
        {
            StaffId = 4,
            RoleType = StaffUserRole.Admin,
            RbacRoles = [_admin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _teamAdminB = new StaffAccessControlAggregate
        {
            StaffId = 5,
            RoleType = StaffUserRole.TeamAdmin,
            RbacRoles = [_teamAdmin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _staffB = new StaffAccessControlAggregate
        {
            StaffId = 6,
            RoleType = StaffUserRole.Staff,
            RbacRoles = [_staff],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };

        // Others
        _adminC = new StaffAccessControlAggregate
        {
            StaffId = 7, RoleType = StaffUserRole.Admin, RbacRoles = [_admin], CompanyId = _companyId
        };

        _staffC = new StaffAccessControlAggregate
        {
            StaffId = 8, RoleType = StaffUserRole.Staff, RbacRoles = [_staff], CompanyId = _companyId
        };

        _adminInAnotherCompany = new StaffAccessControlAggregate
        {
            StaffId = 9, RoleType = StaffUserRole.Admin, RbacRoles = [_admin], CompanyId = _anotherCompanyId
        };

        _staffD = new StaffAccessControlAggregate
        {
            StaffId = 10, RoleType = StaffUserRole.Staff, RbacRoles = [_staff], CompanyId = _companyId
        };
    }

    [TearDown]
    public void TearDown()
    {
        using (var context = new ConversationDbContext(_options))
        {
            context.Database.EnsureDeleted();
        }
    }

    [Test]
    public void HealthCheck_CanConnectToDatabase()
    {
        using (var context = new ConversationDbContext(_options))
        {
            // Ensure the database can be connected to and queried
            Assert.DoesNotThrow(() => context.Conversations.ToList());
        }

        // Team Admin A As the collaborator
        var collaborator = new AdditionalAssignee
        {
            Id = 2, CompanyId = _companyId, AssigneeId = _teamAdminA.StaffId
        };

        var teamAdminAAsCollaboratorAssignedTeamBAndNoContactOwnerConversation = new Conversation
        {
            Id = "teamAdminAAsCollaboratorAssignedTeamBAndNoContactOwnerConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = null,
            AdditionalAssignees = [collaborator],
            AssignedTeamId = _teamB.Id
        };

        var specification = new RbacViewConversationsSpecificationBuilder(_teamAdminA).Build();

        var specResult =
            specification.IsSatisfiedBy(teamAdminAAsCollaboratorAssignedTeamBAndNoContactOwnerConversation);

        Assert.That(specResult, Is.EqualTo(true));
    }

    [TestCaseSource(nameof(GetTeamAdminSuccessTestCases))]
    public void SuccessTestCases(Conversation conversation, Mention? mention)
    {
        var specification = new RbacViewConversationsSpecificationBuilder(_teamAdminA).Build();

        using (var context = new ConversationDbContext(_options))
        {
            if (mention is not null)
            {
                context.Mentions.Add(mention);

            }

            context.Conversations.Add(conversation);
            context.SaveChanges();
        }

        using (var context = new ConversationDbContext(_options))
        {
            var mentionResult = context.Mentions
                .Where(m => m.MentionedStaffId == _teamAdminA.StaffId)
                .Select(m => m.ConversationId)
                .ToList();

            var specExpression = specification.ToExpression();
            Expression<Func<Conversation, bool>> mentionExpression = c => mentionResult.Contains(c.Id);

            var combinedExpression = specExpression.Or(mentionExpression);

            var result = context.Conversations
                .Where(combinedExpression)
                .ToList();

            Assert.That(
                result.Count,
                Is.EqualTo(1),
                $"Expected 1 conversation to match the specification for conversation Id: {conversation.Id}");
        }
    }

    [TestCaseSource(nameof(GetTeamAdminFailureTestCases))]
    public void FailureTestCases(Conversation conversation, Mention? mention)
    {
        var specification = new RbacViewConversationsSpecificationBuilder(_teamAdminA).Build();

        using (var context = new ConversationDbContext(_options))
        {
            if (mention is not null)
            {
                context.Mentions.Add(mention);

            }
            context.Conversations.Add(conversation);
            context.SaveChanges();
        }

        using (var context = new ConversationDbContext(_options))
        {
            var mentionResult = context.Mentions
                .Where(m => m.MentionedStaffId == _teamAdminA.StaffId)
                .Select(m => m.ConversationId)
                .ToList();

            var specExpression = specification.ToExpression();
            Expression<Func<Conversation, bool>> mentionExpression = c => mentionResult.Contains(c.Id);

            var combinedExpression = specExpression.Or(mentionExpression);

            var result = context.Conversations
                .Where(combinedExpression)
                .ToList();

            Assert.That(
                result.Count,
                Is.EqualTo(0),
                $"Expected 0 conversation to match the specification for conversation Id: {conversation.Id}");
        }
    }

    private static IEnumerable<TestCaseData> GetTeamAdminSuccessTestCases()
    {
        var companyId = "sleekflow";
        var teamA = new TeamAccessControlAggregate
        {
            Id = 1
        };
        var teamB = new TeamAccessControlAggregate
        {
            Id = 2
        };
        var teamAdminA = new StaffAccessControlAggregate
        {
            StaffId = 2, CompanyId = companyId
        };

        yield return new TestCaseData(
            new Conversation
            {
                Id = "unassignedConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssignedTeamId = null
            },
            null).SetName("Unassigned Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "assignedToManagedTeamConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssignedTeamId = teamA.Id
            },
            null
            ).SetName("Assigned to Managed Team Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "assignedAsContactOwnerConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssigneeId = teamAdminA.StaffId,
                AssignedTeamId = null
            },
            null
            ).SetName("Assigned as Contact Owner Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "assignedAsContactOwnerButDifferentTeamConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssigneeId = teamAdminA.StaffId,
                AssignedTeamId = teamB.Id
            },
            null).SetName("Assigned as Contact Owner but Different Team Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "assignedAsCollaboratorConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = teamAdminA.StaffId
                    }
                },
                AssignedTeamId = null
            },
            null).SetName("Assigned as Collaborator Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "assignedAsCollaboratorButContactOwnerIsOtherTeamConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssigneeId = 5,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = teamAdminA.StaffId
                    }
                },
                AssignedTeamId = null
            },
            null).SetName("Assigned as Collaborator but Contact Owner is Other Team Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "assignedAsCollaboratorButAssignedTeamIsOtherTeamConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = teamAdminA.StaffId
                    }
                },
                AssignedTeamId = teamB.Id
            },
            null).SetName("Assigned as Collaborator but Assigned Team is Other Team Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "teamMemberAsCollaboratorConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 3
                    }
                },
                AssignedTeamId = null
            },
            null).SetName("Team Member as Collaborator Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "teamMemberAsCollaboratorButContactOwnerIsOtherTeamConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssigneeId = 5,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 3
                    }
                },
                AssignedTeamId = teamB.Id
            },
            null).SetName("Team Member as Collaborator but Contact Owner is Other Team Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "teamMemberAsCollaboratorButAssignedTeamIsOtherTeamConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AdditionalAssignees = new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = companyId, AssigneeId = 3
                    }
                },
                AssignedTeamId = teamB.Id
            },
            null).SetName("Team Member as Collaborator but Assigned Team is Other Team Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "teamMemberAsContactOwnerConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssigneeId = 3,
                AssignedTeamId = null
            },
            null).SetName("Team Member as Contact Owner Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "teamMemberAsContactOwnerButAssignedTeamIsOtherTeamConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssigneeId = 3,
                AssignedTeamId = teamB.Id
            },
            null).SetName("Team Member as Contact Owner but Assigned Team is Other Team Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "mentionedStaffConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssigneeId = 5,
                AssignedTeamId = null
            },
            new Mention
            {
                Id = Guid.NewGuid().ToString(),
                ConversationId = "mentionedStaffConversation",
                CompanyId = companyId,
                CreatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1),
                UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1),
                MentionedStaffId = teamAdminA.StaffId
            }
            ).SetName("Mentioned Staff Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "mentionedStaffButAssignedTeamIsOtherTeamConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssignedTeamId = teamB.Id
            },
            new Mention
            {
                Id = Guid.NewGuid().ToString(),
                ConversationId = "mentionedStaffButAssignedTeamIsOtherTeamConversation",
                CompanyId = companyId,
                CreatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1),
                UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1),
                MentionedStaffId = teamAdminA.StaffId
            }
            ).SetName("Mentioned Staff but Assigned Team is Other Team Conversation");
    }

    private static IEnumerable<TestCaseData> GetTeamAdminFailureTestCases()
    {
        var companyId = "sleekflow";

        var teamB = new TeamAccessControlAggregate
        {
            Id = 2
        };

        yield return new TestCaseData(
            new Conversation
            {
                Id = "otherTeamConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssignedTeamId = teamB.Id
            },
            null).SetName("Other Team Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "assignedOtherTeamStaffAsContactOwnerConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                AssigneeId = 5,
                AssignedTeamId = null
            },
            null).SetName("Assigned Other Team Staff as Contact Owner Conversation");

        yield return new TestCaseData(
            new Conversation
            {
                Id = "mentionedOtherTeamStaffConversation",
                CompanyId = companyId,
                ActiveStatus = ActiveStatus.Active,
                ChatHistory = new List<ConversationMessage>
                {
                    new ConversationMessage
                    {
                        Channel = ChannelTypes.Note,
                        MessageAssigneeId = 5,
                        UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1)
                    }
                },
                AssignedTeamId = teamB.Id
            },
            new Mention
            {
                Id = Guid.NewGuid().ToString(),
                ConversationId = "mentionedOtherTeamStaffConversation",
                CompanyId = companyId,
                CreatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1),
                UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1),
                MentionedStaffId = 5
            }
            ).SetName("Mentioned Other Team Staff Conversation");
    }
}