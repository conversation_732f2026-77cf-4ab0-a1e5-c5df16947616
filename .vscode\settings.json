{
    "azureFunctions.deploySubpath": "Sleekflow.SleekPay/bin/Release/net8.0/publish",
    "azureFunctions.projectLanguage": "C#",
    "azureFunctions.projectRuntime": "~4",
    "debug.internalConsoleOptions": "neverOpen",
    "azureFunctions.preDeployTask": "publish (functions)",
    "dotnet.server.useOmnisharp": true,
    "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true,
    "dotnet.inlayHints.enableInlayHintsForParameters": false,
    "dotnet.inlayHints.enableInlayHintsForLiteralParameters": false,
    "dotnet.inlayHints.enableInlayHintsForIndexerParameters": false,
    "dotnet.inlayHints.enableInlayHintsForObjectCreationParameters": false,
    "dotnet.inlayHints.enableInlayHintsForOtherParameters": false,
    "dotnet.inlayHints.suppressInlayHintsForParametersThatDifferOnlyBySuffix": false,
    "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchMethodIntent": false,
    "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchArgumentName": false,
    "csharp.format.enable": true,
    "csharp.semanticHighlighting.enabled": true,
    "editor.formatOnSave": true,
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit",
        "source.fixAll": "explicit"
    },
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": true,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": false,
    "files.trimFinalNewlines": true,
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "git.autofetch": true,
    "git.enableStatusBarSync": true,
    "scm.defaultViewMode": "tree",
    "files.associations": {
        "*.cs": "csharp",
        "*.csproj": "xml",
        "*.props": "xml",
        "*.targets": "xml",
        "*.resx": "xml",
        "*.config": "xml",
        "*.json": "jsonc",
        "*.yml": "yaml",
        "*.yaml": "yaml"
    },
    "files.exclude": {
        "**/bin": true,
        "**/obj": true,
        "**/.vs": true,
        "**/.vscode": false,
        "**/.idea": true,
        "**/packages": true,
        "**/*.user": true,
        "**/.DS_Store": true,
        "**/node_modules": true,
        "**/dist": true,
        "**/build": true
    },
    "search.exclude": {
        "**/bin": true,
        "**/obj": true,
        "**/.vs": true,
        "**/.idea": true,
        "**/packages": true,
        "**/*.user": true,
        "**/node_modules": true,
        "**/dist": true,
        "**/build": true
    },
    "editor.suggestSelection": "first",
    "editor.quickSuggestions": {
        "other": true,
        "comments": false,
        "strings": false
    },
    "editor.parameterHints.enabled": true,
    "editor.hover.enabled": true,
    "editor.lineNumbers": "on",
    "editor.cursorBlinking": "blink",
    "editor.renderWhitespace": "boundary",
    "editor.guides.indentation": true,
    "editor.guides.bracketPairs": true,
    "problems.decorations.enabled": true,
    "problems.showCurrentInStatus": true,
    "editor.semanticTokenColorCustomizations": {
        "enabled": true,
    },
    "dotnet.unitTests.runSettingsPath": "",
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/**": true,
        "**/bin/**": true,
        "**/obj/**": true,
        "**/.vs/**": true,
        "**/.idea/**": true
    },
    "cSpell.words": [
        "Enablements",
        "Sleekflow",
        "Vtex",
        "Powerflow",
        "SleekPay",
        "Auth0",
        "Hangfire",
        "Resharper",
        "Omnisharp",
        "Migrator",
        "Infra",
        "Perf",
        "Exporter",
        "Benchmarks",
        "Toolchain",
        "Nuget",
        "Autofac",
        "Serilog",
        "Polly",
        "Automapper",
        "Mediatr",
        "Newtonsoft",
        "AspNetCore",
        "SignalR",
        "Swagger",
        "OpenAPI",
        "Kubernetes",
        "Azure",
        "Pulumi",
        "Dockerfile",
        "Containerization",
        "Microservices",
        "Redis",
        "MongoDB",
        "PostgreSQL",
        "EntityFramework",
        "LINQ",
        "Async",
        "Await",
        "Middleware",
        "Startup",
        "Appsettings",
        "Validators",
        "Repositories",
        "Controllers",
        "Services",
        "Handlers",
        "Filters",
        "Attributes",
        "Enums",
        "Extensions",
        "Helpers",
        "Utilities",
        "Constants",
        "Configurations",
        "Mappings",
        "ViewModels",
        "DTOs",
        "Entities",
        "Models",
        "Exceptions",
        "Middlewares",
        "Hubs",
        "Telemetries",
        "Infrastructures",
        "Domains",
        "Binaries",
        "Serializations",
        "Migrations",
        "Rbac",
        "Webhook",
        "Shopify",
        "Hubspot",
        "Vtex",
        "Whatsapp",
        "Instagram",
        "Facebook",
        "Stripe",
        "Partnerstack",
        "Shopline",
        "Crm",
        "Commerce",
        "Ticketing",
        "Broadcasting",
        "Automation",
        "Analytics",
        "Campaigns",
        "Conversations",
        "Messaging",
        "Notifications",
        "Channels",
        "Integrations",
        "Tenants",
        "Subscriptions",
        "Resellers",
        "Demos",
        "Stress",
        "Benchmarking",
        "Profiling",
        "Debugging",
        "Testing",
        "Mocking",
        "Stubbing",
        "Faker",
        "Moq",
        "Xunit",
        "Nunit",
        "FluentAssertions",
        "Bogus",
        "Testcontainers",
        "Docker",
        "Compose",
        "Kubernetes",
        "Helm",
        "Terraform",
        "Pulumi",
        "Ansible",
        "Jenkins",
        "GitHub",
        "Actions",
        "Workflows",
        "Pipelines",
        "Artifacts",
        "Deployments",
        "Rollouts",
        "Rollbacks",
        "Monitoring",
        "Alerting",
        "Logging",
        "Tracing",
        "Metrics",
        "Observability",
        "Elasticsearch",
        "Kibana",
        "Grafana",
        "Prometheus",
        "Jaeger",
        "Zipkin",
        "OpenTelemetry",
        "ApplicationInsights",
        "Datadog",
        "Sentry",
        "Splunk",
        "Fluentd",
        "Logstash",
        "Filebeat",
        "Metricbeat",
        "Heartbeat",
        "Winlogbeat",
        "Packetbeat",
        "Auditbeat",
        "Functionbeat",
        "Journalbeat"
    ],
    "debug.console.fontFamily": "Menlo, Monaco, 'Courier New', monospace",
    "debug.console.wordWrap": true,
    "debug.openDebug": "openOnDebugBreak",
    "debug.showBreakpointsInOverviewRuler": true,
    "debug.showInlineBreakpointCandidates": true,
    "extensions.ignoreRecommendations": false,
    "emmet.includeLanguages": {
        "razor": "html",
        "cshtml": "html"
    },
    "debug.console.acceptSuggestionOnEnter": "on",
    "dotnet.navigation.navigateToDecompiledSources": true,
    "terminal.integrated.enableMultiLinePasteWarning": false,
    "terminal.integrated.allowChords": false,
    "rest-client.environmentVariables": {
        "dev": {
            "baseUrl": "http://localhost:5000",
            "auth0BaseUrl": "http://localhost:5002",
            "powerflowBaseUrl": "http://localhost:5003"
        }
    },
    "csharp.inlayHints.enableInlayHintsForTypes": false,
    "csharp.inlayHints.enableInlayHintsForImplicitVariableTypes": false,
    "csharp.inlayHints.enableInlayHintsForLambdaParameterTypes": false,
    "csharp.inlayHints.enableInlayHintsForImplicitObjectCreation": false,
    "dotnet.codeLens.enableReferencesCodeLens": true,
    "dotnet.codeLens.enableTestsCodeLens": true
}