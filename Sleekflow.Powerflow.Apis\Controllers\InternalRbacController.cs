using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.Powerflow.Apis.Services;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Configuration;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Route("/internal/rbac/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsUser)]
public class InternalRbacController : InternalControllerBase
{
    private readonly IInternalRbacService _internalRbacService;

    public InternalRbacController(
        UserManager<ApplicationUser> userManager,
        IInternalRbacService internalRbacService)
        : base(userManager)
    {
        _internalRbacService = internalRbacService;
    }

    [HttpPost]
    public async Task<ActionResult>
        InitiateRbacInboxSettingMigration(
            [FromBody]
            InitiateRbacInboxSettingMigrationRequest initiateRbacInboxSettingMigrationRequest)
    {

        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        await _internalRbacService.InitiateRbacInboxSettingMigration(initiateRbacInboxSettingMigrationRequest.CompanyIds);

        return Ok();
    }
}