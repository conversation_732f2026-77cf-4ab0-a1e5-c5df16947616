using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.FlowHubs;

namespace Travis_backend.CompanyDomain.Services;

public interface IBusinessHourConfigService
{
    Task<bool> IsEnabledAsync(string companyId);

    Task<BusinessHourConfig?> GetAsync(string companyId);

    Task<BusinessHourConfig> CreateAndGetConfigAsync(string companyId, bool isEnabled, WeeklyHours weeklyHours);

    Task<BusinessHourConfig?> UpdateAndGetAsync(string companyId, bool isEnabled, WeeklyHours weeklyHours);
}

public class BusinessHourConfigService : IBusinessHourConfigService
{
    private readonly IBusinessHourConfigRepository _businessHourConfigrepository;
    private readonly IAnalyticsHooks _analyticsHooks;

    public BusinessHourConfigService(
        IBusinessHourConfigRepository businessHourConfigrepository,
        IAnalyticsHooks analyticsHooks)
    {
        _businessHourConfigrepository = businessHourConfigrepository;
        _analyticsHooks = analyticsHooks;
    }

    public async Task<bool> IsEnabledAsync(string companyId)
    {
        var businessHourConfig = await GetAsync(companyId);

        return businessHourConfig?.IsEnabled ?? false;
    }

    public async Task<BusinessHourConfig?> GetAsync(string companyId)
    {
        return await _businessHourConfigrepository.GetAsync(companyId);
    }

    public async Task<BusinessHourConfig> CreateAndGetConfigAsync(string companyId, bool isEnabled, WeeklyHours weeklyHours)
    {
        weeklyHours.Validate();

        var businessHourConfig = await _businessHourConfigrepository.CreateAndGetAsync(companyId, isEnabled, weeklyHours);

        await _analyticsHooks.OnBusinessHourConfigUpdatedAsync(businessHourConfig);

        return businessHourConfig;
    }

    public async Task<BusinessHourConfig?> UpdateAndGetAsync(string companyId, bool isEnabled, WeeklyHours weeklyHours)
    {
        weeklyHours.Validate();
        BusinessHourConfig businessHourConfig = null;

        var updatedCount = await _businessHourConfigrepository.UpdateAsync(companyId, isEnabled, weeklyHours);
        if (updatedCount != 0)
        {
            businessHourConfig = await GetAsync(companyId);
            await _analyticsHooks.OnBusinessHourConfigUpdatedAsync(businessHourConfig);
        }

        return businessHourConfig;
    }
}