namespace Sleekflow.Core.Benchmarks;

public class DbConfig
{
    public const string Env = DevEnv;
    public const string ReadWriteConnStr =
        "Server=tcp:sleekflow-core-sql-server-eas-dev928ea268.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=s81604a6f;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=150;";
    public const string ReadOnlyConnStr =
        "Server=tcp:sleekflow-core-sql-server-eas-dev928ea268.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=s81604a6f;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=150;Connection Lifetime=60;ApplicationIntent=ReadOnly;";
    public const string AnalyticDbConnStr =
        "Server=tcp:sleekflow-core-sql-server-eas-dev928ea268.database.windows.net,1433;Initial Catalog=sleekflow-analytic-sql-db-eas-dev;Persist Security Info=False;User ID=s81604a6f;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=100;ApplicationIntent=ReadOnly;";
    // public const string Env = StagingEnv;
    // public const string ConnStr =
    //     "...";

    // public const string Env = ProdEnv;
    // public const string ConnStr =
    //     "...";

    public const string ProdEnv = "prod";
    public const string StagingEnv = "staging";
    public const string DevEnv = "dev";
}