﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Travis_backend.AnalyticsDomain.Models;

[Table("topic_analytics_metrics")]
public class TopicAnalyticsMetric
{
    [Column("id")]
    public string Id { get; set; }

    [Column("date")]
    public DateOnly? Date { get; set; }

    [Column("sleekflow_company_id")]
    public string CompanyId { get; set; }

    [Column("topic_id")]
    public string TopicId { get; set; }

    [Column("total_conversations")]
    public int? TotalConversations { get; set; }

    [Column("to_be_deleted")]
    public byte? ToBeDeleted { get; set; }

    public static TopicAnalyticsMetric Empty()
    {
        return new TopicAnalyticsMetric
        {
            Date = null,
            CompanyId = string.Empty,
            TopicId = string.Empty,
            TotalConversations = 0,
            ToBeDeleted = 0
        };
    }
}