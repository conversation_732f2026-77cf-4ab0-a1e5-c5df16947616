using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RestSharp;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Configuration;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.Services;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Route("internal/beamer/NPS/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
public class InternalBeamerController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly IInternalHubspotRepository _internalHubspotRepository;
    private readonly ILogger<InternalBeamerWebhookController> _logger;

    private bool CheckIsHubSpotEnable()
    {
        return !string.IsNullOrWhiteSpace(_configuration["HubSpot:InternalHubSpotApiKey"]);
    }

    public InternalBeamerController(
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        IInternalHubspotRepository internalHubspotRepository,
        ILogger<InternalBeamerWebhookController> logger,
        UserManager<ApplicationUser> userManager
    )
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _configuration = configuration;
        _internalHubspotRepository = internalHubspotRepository;
        _logger = logger;
    }

    [HttpPost]
    public async Task<IActionResult> SyncAllBeamerNPS()
    {
        var beamerApiKey = _configuration["Beamer:APIKey"];
        var beamerApiUrl = _configuration["Beamer:APIUrl"];
        if (string.IsNullOrWhiteSpace(beamerApiKey) || string.IsNullOrWhiteSpace(beamerApiUrl))
        {
            _logger.LogError("Beamer Api Key or URL Not Found in Server Config");
            return Ok("Beamer Api Key or URL Not Found in Server Config");
        }

        var request = new RestRequest(Method.GET);
        request.AddHeader("Beamer-Api-Key", beamerApiKey);

        var countClient = new RestClient($"{beamerApiUrl}/count");
        var countResponse = await countClient.ExecuteAsync<BeamerNPSCountResponse>(request);

        if (countResponse.Data.Count == 0)
        {
            return Ok("No data get from Beamer NPS");
        }

        var total = countResponse.Data.Count;
        _logger.LogInformation("Total NPS get from Beamer: {Total}", total);

        const int maxResults = 100;
        var client = new RestClient(beamerApiUrl);
        request.AddQueryParameter("maxResults", $"{maxResults}");
        request.AddQueryParameter("page", "0");

        // Loop from oldest to latest
        for (var i = total / maxResults; i >= 0; i--)
        {
            var start = ((total / maxResults) - i) * maxResults;
            _logger.LogInformation(
                "Processing {Start} - {End} out of {Total}",
                start,
                start + 100,
                total);
            request.Parameters[2].Value = $"{i}";
            var contacts = new List<InternalHubSpotContact>();

            try
            {
                var response = await client.ExecuteAsync<List<BeamerNPSResponse>>(request);
                foreach (var beamerNpsResponse in response.Data.Where(
                             beamerNpsResponse => !beamerNpsResponse.UserEmail.IsNullOrEmpty()))
                {
                    // Sync to DB Company
                    var staff = await _appDbContext.UserRoleStaffs.Include(staff => staff.Company).FirstOrDefaultAsync(
                        x => x.Identity.Email == beamerNpsResponse.UserEmail);
                    if (staff != null)
                    {
                        staff.Company.NPSScore = beamerNpsResponse.Score;
                        _logger.LogInformation(
                            "DB Update: {CompanyName}'s NPS will set to {Score}",
                            staff.Company.CompanyName,
                            beamerNpsResponse.Score);
                    }
                    else
                    {
                        _logger.LogInformation(
                            "Staff {UserEmail} not found in DB!",
                            beamerNpsResponse.UserEmail);
                    }

                    // Get contact object from hubspot & Add into list for Bulk Sync to HubSpot
                    var contactObject =
                        await _internalHubspotRepository.GetContactObjectByEmailAsync(beamerNpsResponse.UserEmail);
                    if (contactObject == null)
                    {
                        continue;
                    }

                    contactObject.NPSScore = beamerNpsResponse.Score;
                    contactObject.NPSDate = beamerNpsResponse.Date;
                    contacts.Add(contactObject);
                    _logger.LogInformation(
                        "HubSpot Bulk Update List Added: {ContactName}'s NPS Score = {Score}",
                        $"{contactObject.FirstName} {contactObject.LastName}",
                        beamerNpsResponse.Score);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error on getting data: {Message}", e.Message);
            }

            await _appDbContext.SaveChangesAsync();
            _logger.LogInformation("DB Updated");

            // Bulk Sync to HubSpot
            if (!CheckIsHubSpotEnable())
            {
                _logger.LogError("HubSpot API key not found");
                continue;
            }

            if (contacts.Count == 0)
            {
                continue;
            }

            try
            {
                _logger.LogInformation(
                    "HubSpot Bulk Update Processing: {Count} update will be process",
                    contacts.Count);
                await _internalHubspotRepository.BulkUpdateContactObjectAsync(contacts.ToArray());
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error bulk update contact objects to HubSpot: {Message}", e.Message);
            }
        }

        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> SyncBeamerNPSWithoutHubSpot(
        [FromBody]
        SyncBeamerNPSWithoutHubSpotRequest req)
    {
        var beamerApiKey = _configuration["Beamer:APIKey"];
        var beamerApiUrl = _configuration["Beamer:APIUrl"];
        if (string.IsNullOrWhiteSpace(beamerApiKey) || string.IsNullOrWhiteSpace(beamerApiUrl))
        {
            _logger.LogError("Beamer Api Key or URL Not Found in Server Config");
            return Ok("Beamer Api Key or URL Not Found in Server Config");
        }

        var request = new RestRequest(Method.GET);
        request.AddHeader("Beamer-Api-Key", beamerApiKey);

        var countClient = new RestClient($"{beamerApiUrl}/count");
        var countResponse = await countClient.ExecuteAsync<BeamerNPSCountResponse>(request);

        if (countResponse.Data.Count == 0)
        {
            return Ok("No data get from Beamer NPS");
        }

        var total = countResponse.Data.Count;
        _logger.LogInformation("Total NPS get from Beamer: {Total}", total);

        const int maxResults = 100;
        var client = new RestClient(beamerApiUrl);
        request.AddQueryParameter("maxResults", $"{maxResults}");
        request.AddQueryParameter("page", "0");

        // Loop from oldest to latest
        var totalPage = req.EndPage ?? total / maxResults;
        for (var i = totalPage; i >= (req.StartPage ?? 0); i--)
        {
            var start = (totalPage - i) * maxResults;
            _logger.LogInformation(
                "Processing {Start} - {End} out of total {Total} Beamer NPS Count, Beamer Page {Page}",
                start,
                start + maxResults,
                total,
                i);
            request.Parameters[2].Value = $"{i}";

            try
            {
                var response = await client.ExecuteAsync<List<BeamerNPSResponse>>(request);
                foreach (var beamerNpsResponse in response.Data.Where(
                             beamerNpsResponse => !beamerNpsResponse.UserEmail.IsNullOrEmpty()))
                {
                    // Sync to DB Company
                    var staff = await _appDbContext.UserRoleStaffs.Include(staff => staff.Company).FirstOrDefaultAsync(
                        x => x.Identity.Email == beamerNpsResponse.UserEmail);
                    if (staff != null)
                    {
                        staff.Company.NPSScore = beamerNpsResponse.Score;
                        _logger.LogInformation(
                            "DB Update: {CompanyName}'s NPS will set to {Score}",
                            staff.Company.CompanyName,
                            beamerNpsResponse.Score);
                    }
                    else
                    {
                        _logger.LogInformation(
                            "Staff {UserEmail} not found in DB!",
                            beamerNpsResponse.UserEmail);
                    }
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error on getting data: {Message}", e.Message);
            }

            await _appDbContext.SaveChangesAsync();
            _logger.LogInformation("DB Updated");
        }

        _logger.LogInformation("Sync Completed");
        return Ok();
    }
}