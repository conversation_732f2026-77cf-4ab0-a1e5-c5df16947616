using Pulumi;
using Pulumi.AzureNative.Authorization;
using Pulumi.AzureNative.Resources;
using Pulumi.AzureNative.Web;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Constants;
using Cache = Pulumi.AzureNative.Cache;
using Network = Pulumi.AzureNative.Network;
using Storage = Pulumi.AzureNative.Storage;
using SignalRService = Pulumi.AzureNative.SignalRService;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Insights = Pulumi.AzureNative.Insights;

namespace Sleekflow.Core.Infra.Components.Models;

public class EnvGroup
{
    public string LocationName { get; set; }

    public Dictionary<string, Cache.Redis> Redis { get; set; }

    public SqlServerProperties? SqlServerProperties { get; set; }

    public SqlServerProperties? AnalyticSqlServerProperties { get; set; }

    public ResourceGroup ResourceGroup { get; set; }

    public SignalRService.SignalR SignalR { get; set; }

    public Dictionary<string, AppServiceConfiguration> WebApps { get; set; }

    public Dictionary<string, AppServiceConfiguration> WorkerWebApps { get; set; }

    public Storage.StorageAccount BlobStorage { get; set; }

    public Network.PrivateZone PrivateZone { get; set; }

    public Network.VirtualNetwork VirtualNetwork { get; set; }

    public OperationalInsights.Workspace LogAnalyticsWorkspace { get; set; }

    public Insights.ActionGroup CriticalActionGroup { get; set; }

    public Insights.ActionGroup PreventionActionGroup { get; set; }

    public Output<GetClientConfigResult> ClientConfig { get; set; }


    public EnvGroup(
        string locationName,
        Dictionary<string, Cache.Redis> redis,
        ResourceGroup resourceGroup,
        SignalRService.SignalR signalR,
        Dictionary<string, AppServiceConfiguration> webApps,
        Dictionary<string, AppServiceConfiguration> workerWebApps,
        Storage.StorageAccount blobStorage,
        Network.PrivateZone privateZone,
        Network.VirtualNetwork virtualNetwork,
        SqlServerProperties? sqlServerProperties,
        SqlServerProperties? analyticSqlServerProperties,
        OperationalInsights.Workspace logAnalyticsWorkspace,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup,
        Output<GetClientConfigResult> clientConfig)
    {
        Redis = redis;
        SignalR = signalR;
        WebApps = webApps;
        WorkerWebApps = workerWebApps;
        LocationName = locationName;
        ResourceGroup = resourceGroup;
        BlobStorage = blobStorage;
        PrivateZone = privateZone;
        VirtualNetwork = virtualNetwork;
        SqlServerProperties = sqlServerProperties;
        AnalyticSqlServerProperties = analyticSqlServerProperties;
        LogAnalyticsWorkspace = logAnalyticsWorkspace;
        CriticalActionGroup = criticalActionGroup;
        PreventionActionGroup = preventionActionGroup;
        ClientConfig = clientConfig;
    }

    public virtual string FormatAppName(string appName)
    {
        var containerAppName = $"sleekflow-{appName}-app";
        return $"{containerAppName}-{LocationNames.GetShortName(LocationName)}";
    }

    public Output<string> FormatSfEnvironment()
    {
        return FormatSfEnvironment(Output.Create(LocationName));
    }

    protected virtual Output<string> FormatSfEnvironment(Output<string> locationName)
    {
        return locationName;
    }
}