using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.IntelligentHubDomain.Services;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Route("/internal/intelligent-hub/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
public class InternalIntelligentHubController : InternalControllerBase
{
    private readonly IIntelligentHubService _intelligentHubService;

    public InternalIntelligentHubController(
        UserManager<ApplicationUser> userManager,
        IIntelligentHubService intelligentHubService)
        : base(userManager)
    {
        _intelligentHubService = intelligentHubService;
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> GetInternalIntelligentHubData(
        [FromBody]
        GetInternalIntelligentHubDataRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var response = await _intelligentHubService.GetInternalIntelligentHubDataAsync(request.CompanyId);

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdateIntelligentHubConfigUsageLimitOffsets(
        [FromBody]
        UpdateIntelligentHubConfigUsageLimitOffsetsRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            await _intelligentHubService.UpdateIntelligentHubConfigUsageLimitOffsetsAsync(
                request.CompanyId,
                request.UsageLimitOffsets);
        }
        catch (Exception e)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = e.Message
                });
        }

        return Ok("Update IntelligentHubConfig UsageLimitOffsets Successfully");
    }
}