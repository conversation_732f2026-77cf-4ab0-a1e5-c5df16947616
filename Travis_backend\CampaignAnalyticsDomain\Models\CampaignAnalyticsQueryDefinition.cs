﻿using System.Collections.Generic;

namespace Travis_backend.CampaignAnalyticsDomain.Models;

using Microsoft.Data.SqlClient;

public class CampaignAnalyticsQueryDefinition
{
    public string Query { get; set; }

    public List<SqlParameter> Parameters { get; set; }

    public CampaignAnalyticsQueryDefinition(string query, List<SqlParameter> parameters)
    {
        Query = query;
        Parameters = parameters;
    }
}