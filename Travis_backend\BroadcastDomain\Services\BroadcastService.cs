using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Force.DeepCloner;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RestSharp;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.BroadcastDomain.Constants;
using Travis_backend.BroadcastDomain.Models;
using Travis_backend.BroadcastDomain.ViewModels;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FileDomain.Models;
using Travis_backend.FileDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.PiiMasking.Models;
using Travis_backend.PiiMaskingDomain.Services;
using Travis_backend.SignalR;
using Travis_backend.StripeIntegrationDomain.Models;
using Travis_backend.StripeIntegrationDomain.Services;
using Travis_backend.TenantHubDomain.Services;
using WABA360Dialog.ApiClient.Payloads.Models.MessageObjects.InteractiveObjects;
using MessageStatus = Travis_backend.MessageDomain.Models.MessageStatus;
using ParameterType = WABA360Dialog.ApiClient.Payloads.Enums.ParameterType;

namespace Travis_backend.BroadcastDomain.Services
{
    public interface IBroadcastService
    {
        Task BroadcastMessageAsync(
            long senderId,
            BroadcastViewModel broadcastsViewModel,
            bool isTesting,
            CancellationToken cancellationToken);

        Task<string> ExportBroadcastStatus(
            string companyId,
            string broadcastTemplateId,
            long staffId,
            Func<int, ValueTask> onComplete = null);

        Task<List<CompanyMessageTemplate>> DuplicatedMessageTemplates(
            Staff companyUser,
            List<string> broadcastTemplateIds);

        Task<StatisticsData> GetBroadcastStatus(Staff companyUser, string broadcastTemplateId, string broadcastStatus);

        Task<bool> BroadcastExists(string companyId, string broadcastTemplateId);

        // BroadcastByUserProfileBatch Jobs
        [HangfireMethodConcurrencyLimit("broadcast_batch:{0}:{1}", 10, 30)]
        public Task BroadcastByUserProfileBatchJobAsync(
            string companyId,
            string broadcastTemplateId,
            long staffId,
            List<string> userProfileIds,
            bool isTesting,
            int batchNumber,
            int totalBatchSize,
            CancellationToken cancellationToken = default);

        [HangfireMethodConcurrencyLimit("broadcast_batch_high_concurrency:{0}:{1}", 50, 30)]
        public Task BroadcastByUserProfileHighConcurrencyBatchJobAsync(
            string companyId,
            string broadcastTemplateId,
            long staffId,
            List<string> userProfileIds,
            bool isTesting,
            int batchNumber,
            int totalBatchSize,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// To get the broadcast contacts count with in 24 hrs, future and past 24 hrs, to have a rough count of daily message usage
        /// </summary>
        /// <param name="companyId">Guid.</param>
        /// <param name="queryDate">DateTime default current.</param>
        /// <returns>sum of broadcast contacts.</returns>
        public Task<long> GetBroadcastContactsCountAsync(string companyId, DateTime queryDate);
    }

    public class BroadcastStatusResponseViewModel
    {
        public int Sent { get; set; }

        public int Delivered { get; set; }

        public int Read { get; set; }

        public int Replied { get; set; }

        public int Failed { get; set; }
    }

    public class BroadcastService : IBroadcastService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly ILogger _logger;
        private readonly ISignalRService _signalRService;
        private readonly IUserProfileService _userProfileService;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly ILockService _lockService;
        private readonly ISleekPayService _sleekPayService;
        private readonly IUserProfileSqlService _userProfileSqlService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IUploadService _uploadService;
        private readonly IBroadcastChannelService _broadcastChannelService;
        private readonly IPiiMaskingService _piiMaskingService;
        private readonly IServiceProvider _serviceProvider;


        public BroadcastService(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<BroadcastService> logger,
            ISignalRService signalRService,
            IUserProfileService userProfileService,
            IConversationMessageService conversationMessageService,
            ICompanyUsageService companyUsageService,
            IAzureBlobStorageService azureBlobStorageService,
            ILockService lockService,
            ISleekPayService sleekPayService,
            IUserProfileSqlService userProfileSqlService,
            IHttpClientFactory httpClientFactory,
            IUploadService uploadService,
            IBroadcastChannelService broadcastChannelService,
            IPiiMaskingService piiMaskingService,
            IServiceProvider serviceProvider)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _signalRService = signalRService;
            _userProfileService = userProfileService;
            _conversationMessageService = conversationMessageService;
            _companyUsageService = companyUsageService;
            _azureBlobStorageService = azureBlobStorageService;
            _lockService = lockService;
            _sleekPayService = sleekPayService;
            _userProfileSqlService = userProfileSqlService;
            _httpClientFactory = httpClientFactory;
            _uploadService = uploadService;
            _broadcastChannelService = broadcastChannelService;
            _piiMaskingService = piiMaskingService;
            _serviceProvider = serviceProvider;
        }

        public async Task BroadcastMessageAsync(
            long senderId,
            BroadcastViewModel broadcastsViewModel,
            bool isTesting,
            CancellationToken cancellationToken)
        {
            _appDbContext.Database.SetCommandTimeout(60);
            var companyUser = await _appDbContext.UserRoleStaffs
                .FirstOrDefaultAsync(x => x.Id == senderId, cancellationToken: cancellationToken);

            var messageTemplate = await _appDbContext.CompanyMessageTemplates
                .Include(x => x.CampaignChannelMessages)
                .ThenInclude(x => x.UploadedFiles)
                .FirstOrDefaultAsync(x => x.Id == broadcastsViewModel.TempleteId, cancellationToken: cancellationToken);

            if (messageTemplate is null)
            {
                return;
            }

            try
            {
                if (!isTesting)
                {
                    IQueryable<UserProfile> filteredUserProfilesQueryable;
                    var rbacService = _serviceProvider.GetRequiredService<IRbacService>();
                    if (await rbacService.IsRbacEnabled(companyUser.CompanyId))
                    {
                        filteredUserProfilesQueryable = await _userProfileService.GetRbacFilteredBroadcastContacts(
                            companyUser.CompanyId,
                            messageTemplate.Conditions);
                    }
                    else
                    {
                        filteredUserProfilesQueryable = await _userProfileService.GetIQueryableUserProfilesWithFilter(
                            companyUser.CompanyId,
                            messageTemplate.Conditions,
                            staffUserRole: companyUser.RoleType,
                            assigneeId: companyUser.Id);
                    }

                    var topicId = messageTemplate.CampaignChannelMessages
                        .FirstOrDefault(x => x.FacebookOTNTopicId != null)?
                        .FacebookOTNTopicId;

                    if (!string.IsNullOrEmpty(topicId))
                    {
                        // Filter userProfiles from redeemed tokens
                        var userProfileIds = await _appDbContext.UserProfiles
                            .AsNoTracking()
                            .AsSplitQuery()
                            .Where(
                                x => x.FacebookAccountId.HasValue &&
                                     _appDbContext.FacebookUserOneTimeTokens
                                         .Where(
                                             y => y.FacebookOTNTopicId == topicId && y.IsTokenRedeemed &&
                                                  y.ExpiryDate > DateTime.UtcNow).Select(y => y.FacebookUserId)
                                         .Contains((long) x.FacebookAccountId))
                            .Select(x => x.Id)
                            .ToListAsync(cancellationToken: cancellationToken);

                        broadcastsViewModel.UserProfileIds = userProfileIds;
                    }
                    else
                    {
                        broadcastsViewModel.UserProfileIds = await filteredUserProfilesQueryable
                            .Select(x => x.Id)
                            .AsNoTracking()
                            .ToListAsync(cancellationToken: cancellationToken);
                    }

                    // Populate Whatsapp Media Id
                    if (messageTemplate.CampaignChannelMessages?.Any(
                            x => x.TargetedChannel.ChannelType == ChannelTypes.WhatsappCloudApi) ?? false)
                    {
                        var isChangePopulated = await _broadcastChannelService.PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync(messageTemplate);
                        if (isChangePopulated)
                        {
                            messageTemplate.CampaignChannelMessages.ForEach(c=>
                            {
                                _appDbContext.Entry(c).Property(x => x.ExtendedMessagePayloadDetail).IsModified = true;
                            });
                        }
                    }

                    messageTemplate.Status = BroadcastStatus.Sending;
                    await _signalRService.SignalROnBroadcastCampaignSending(messageTemplate);
                    await _appDbContext.SaveChangesAsync(cancellationToken);

                    messageTemplate.SentAt = DateTime.UtcNow;
                }

                messageTemplate.LastSentById = companyUser.Id;
                await _appDbContext.SaveChangesAsync(cancellationToken);

                var response = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);
                var postBroadcastUsage =
                    (response.billingPeriodUsages.FirstOrDefault()?.TotalMessagesSentFromSleekflow ?? 0) +
                    (broadcastsViewModel.UserProfileIds?.Count ?? 0);

                if (postBroadcastUsage > response.MaximumAutomatedMessages)
                {
                    if (!isTesting)
                    {
                        messageTemplate.Status = BroadcastStatus.ExceededQuota;
                        messageTemplate.SentAt = DateTime.UtcNow;
                    }

                    await _appDbContext.SaveChangesAsync(cancellationToken);

                    await _lockService.ReleaseLockAsync(broadcastsViewModel.TempleteId);

                    return;
                }

                #region WeChat Broadcast

                if (messageTemplate.TargetedChannel.ChannelType == ChannelTypes.Wechat)
                {
                    var broadcastWeChatConversationIds = await _appDbContext.Conversations
                        .Where(
                            x =>
                                x.CompanyId == messageTemplate.CompanyId
                                && x.WeChatUserId.HasValue
                                && broadcastsViewModel.UserProfileIds.Contains(x.UserProfileId))
                        .Select(x => x.Id)
                        .ToListAsync(cancellationToken: cancellationToken);

                    // WeChat broadcast
                    if (broadcastWeChatConversationIds.Count > 0)
                    {
                        await BroadcastWeChatAsync(
                            messageTemplate,
                            companyUser,
                            broadcastWeChatConversationIds,
                            isTesting);
                    }

                    return;
                }

                #endregion

                var broadcastUserProfileIds = broadcastsViewModel.UserProfileIds ?? new List<string>();

                #region Broadcast with UserProfileId

                if (broadcastUserProfileIds.Count > 0)
                {
                    var broadcastUserProfileIdsChunks = broadcastUserProfileIds.Chunk(100).ToArray();
                    for (var i = 0; i < broadcastUserProfileIdsChunks.Length; i++)
                    {
                        var userProfileIdsChunk = broadcastUserProfileIdsChunks[i];

                        var batchNumber = i + 1;
                        // L'Occitane and prudential request faster broadcast
                        if (companyUser.CompanyId is
                            "dc4fac25-b55d-4d12-9f4d-1b25322e3055" or // L'Occitane Saudi Arabia
                            "8494ce7f-f3be-400f-8167-9dab6b28dbaf" or // L'Occitane UAE
                            "9b51debc-8cd9-4549-9ab7-40963a9a2f20" or // L'Occitane Oman
                            "e676b366-de6f-46bf-bc38-81d076349cf2" or // L'Occitane Bahrain
                            "7fcc040f-f714-4f14-9ca3-91255c187d10" or // L'Occitane Jordan
                            "f7930fb0-1e2b-4034-a8cb-9fc702c3ce41" or // L'Occitane Qatar
                            "a9fd2541-fb6c-424f-999a-0f1628b2f714" or // L'Occitane Kuwait
                            "24cd1e6d-0405-4aeb-8015-ab4c83f28862"    // Prudential
                           )
                        {
                            BackgroundJob.Enqueue(
                                () => BroadcastByUserProfileHighConcurrencyBatchJobAsync(
                                    messageTemplate.CompanyId,
                                    messageTemplate.Id,
                                    companyUser.Id,
                                    userProfileIdsChunk.ToList(),
                                    isTesting,
                                    batchNumber,
                                    broadcastUserProfileIdsChunks.Length,
                                    CancellationToken.None));
                        }
                        else
                        {
                            BackgroundJob.Enqueue(
                                () => BroadcastByUserProfileBatchJobAsync(
                                    messageTemplate.CompanyId,
                                    messageTemplate.Id,
                                    companyUser.Id,
                                    userProfileIdsChunk.ToList(),
                                    isTesting,
                                    batchNumber,
                                    broadcastUserProfileIdsChunks.Length,
                                    CancellationToken.None));
                        }
                    }
                }
                else
                {
                    messageTemplate.Status = BroadcastStatus.Sent;
                    messageTemplate.SentAt = DateTime.UtcNow;

                    await _appDbContext.SaveChangesAsync(cancellationToken);
                }

                #endregion

            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Broadcast {BroadcastId} error: {ExceptionString}",
                    broadcastsViewModel.TempleteId,
                    ex.ToString());

                messageTemplate.Status = ex.Message;
                await _appDbContext.SaveChangesAsync(cancellationToken);

                throw;
            }

            await _lockService.ReleaseLockAsync(broadcastsViewModel.TempleteId);
        }

        public async Task<string> ExportBroadcastStatus(
            string companyId,
            string broadcastTemplateId,
            long staffId,
            Func<int, ValueTask> onComplete = null)
        {
            var messageTemplates = await _appDbContext.CompanyMessageTemplates
                .AsNoTracking()
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == broadcastTemplateId);

            if (messageTemplates == null)
            {
                return null;
            }

            var maskingRole = MaskingRoles.Staff;

            var staff = await _appDbContext.UserRoleStaffs
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == staffId);

            if (staff != null)
            {
                Enum.TryParse(staff.RoleType.ToString(), out maskingRole);
            }

            var hasPiiMaskingConfig = await _piiMaskingService.IsConfiguredAsync(companyId);

            var exportResult = new StringBuilder();

            var header =
                "\"ContactId\",\"FirstName\",\"LastName\",\"PhoneNumber\",\"Status\",\"Channels\",\"Datetime (UTC)\",\"Replied At (UTC)\",\"Replied Message\",\"Status Reason\"";
            // var propertyType = "ContactId,Default contact property,Default contact property";
            exportResult.AppendLine(header);

            var broadcastHistories = await _appDbContext.BroadcastCompaignHistories
                .AsNoTracking()
                .AsSplitQuery()
                .Where(x =>
                    x.BroadcastCampaignId == messageTemplates.Id
                    && x.Conversation.UserProfile.ActiveStatus == ActiveStatus.Active)
                .Include(x => x.Conversation)
                .ThenInclude(x => x.UserProfile)
                .Include(x => x.ConversationMessages)
                .Include(x => x.BroadcastSentBy.Identity)
                .Include(x => x.BroadcastCampaign)
                .ToListAsync();

            var completed = 0;

            foreach (var history in broadcastHistories)
            {
                try
                {
                    var firstName = history.Conversation?.UserProfile?.FirstName;
                    var lastName = history.Conversation?.UserProfile?.LastName;

                    var phoneNumberCustomField = await _userProfileService.GetCustomFieldByFieldName(
                        history.Conversation.UserProfileId,
                        "PhoneNumber");

                    var phoneNumber = phoneNumberCustomField?.Value;

                    if (hasPiiMaskingConfig)
                    {
                        if (!string.IsNullOrEmpty(firstName))
                        {
                            firstName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                                companyId,
                                firstName,
                                MaskingLocations.Contact,
                                maskingRole);
                        }

                        if (!string.IsNullOrEmpty(lastName))
                        {
                            lastName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                                companyId,
                                lastName,
                                MaskingLocations.Contact,
                                maskingRole);
                        }

                        if (!string.IsNullOrEmpty(phoneNumber))
                        {
                            phoneNumber = await _piiMaskingService.MaskPiiIfEnabledAsync(
                                companyId,
                                phoneNumber,
                                MaskingLocations.Contact,
                                maskingRole);
                        }
                    }

                    if (history.ConversationMessages?.Count > 0)
                    {
                        var status = history.ConversationMessages.First().Status switch
                                     {
                                         MessageStatus.Sending => MessageStatus.Sending.ToString(),
                                         MessageStatus.Sent => MessageStatus.Sent.ToString(),
                                         MessageStatus.Received => "Delivered",
                                         MessageStatus.Read => MessageStatus.Read.ToString(),
                                         MessageStatus.Failed => MessageStatus.Failed.ToString(),
                                         MessageStatus.Undelivered => MessageStatus.Undelivered.ToString(),
                                         MessageStatus.Deleted => MessageStatus.Deleted.ToString(),
                                         MessageStatus.OutOfCredit => MessageStatus.OutOfCredit.ToString(),
                                         MessageStatus.Scheduled => MessageStatus.Scheduled.ToString(),
                                         _ => null,
                                     }
                                     ?? null;

                        var repliedMessageContent = string.Empty;
                        var repliedAt = string.Empty;

                        var channelName = history.BroadcastCampaign.TargetedChannel.ChannelType;

                        if (channelName == ChannelTypes.WhatsappTwilio)
                        {
                            var sender = await _appDbContext.SenderWhatsappSenders
                                .FirstOrDefaultAsync(x => x.Id == history.Conversation.WhatsappUserId);

                            if (sender != null)
                            {
                                var twilio = await _appDbContext.ConfigWhatsAppConfigs
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.TwilioAccountId == sender.InstanceId
                                            && x.WhatsAppSender == sender.InstaneSender);

                                if (twilio == null)
                                {
                                    twilio = await _appDbContext.ConfigWhatsAppConfigs
                                        .FirstOrDefaultAsync(x => x.TwilioAccountId == sender.InstanceId);
                                }

                                if (twilio != null
                                    && !string.IsNullOrEmpty(twilio.Name))
                                {
                                    channelName = twilio.Name;
                                }
                            }
                        }

                        var repliedMessage = await _appDbContext.ConversationMessages
                            .Where(
                                x =>
                                    x.ConversationId == history.ConversationId
                                    && x.DeliveryType == DeliveryType.Normal
                                    && x.CreatedAt > history.CreatedAt
                                    && x.CreatedAt <= history.CreatedAt.AddDays(3)
                                    && !x.IsSentFromSleekflow)
                            .FirstOrDefaultAsync();

                        if (repliedMessage != null)
                        {
                            repliedMessageContent = hasPiiMaskingConfig && !string.IsNullOrEmpty(repliedMessage.MessageContent)
                                ? await _piiMaskingService.MaskPiiIfEnabledAsync(
                                    companyId,
                                    repliedMessage.MessageContent,
                                    MaskingLocations.IncomingMessage,
                                    maskingRole)
                                : repliedMessage.MessageContent;

                            repliedMessageContent = repliedMessageContent?.Replace(Environment.NewLine, " ");
                            repliedAt = repliedMessage.CreatedAt.ToString("o");
                        }

                        exportResult.AppendLine(
                            $"\"{history.Conversation.UserProfileId}\",\"{firstName}\",\"{lastName}\",\"{phoneNumber}\",\"{status}\",\"{channelName}\",\"{history.ConversationMessages.FirstOrDefault()?.CreatedAt.ToString("u")}\",\"{repliedAt}\",\"{repliedMessageContent}\",\"{history.ConversationMessages.FirstOrDefault().ChannelStatusMessage}\"");
                    }
                    else
                    {
                        exportResult.AppendLine(
                            $"\"{history.Conversation.UserProfileId}\",\"{firstName}\",\"{lastName}\",\"{phoneNumber}\",\"{history.Status}\",\"\",\"\",\"\",\"\",\"\"");
                    }

                    if (onComplete != null)
                    {
                        completed++;
                        await onComplete(completed);
                    }
                }
                catch (Exception ex)
                {
                    exportResult.AppendLine($"\"{history.Conversation.UserProfileId}\",\"{ex.Message}\"");
                }
            }

            return exportResult.ToString();
        }

        private async Task BroadcastWeChatAsync(
            CompanyMessageTemplate messageTemplate,
            Staff companyUser,
            List<string> conversationIds,
            bool isTesting)
        {
            var client = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            _logger.LogInformation(
                "[{MethodName}] Company {CompanyId} WeChat broadcast {BroadcastId} by user {UserId}, IsTesting: {IsTesting}, broadcast content: {BroadcastContent}",
                nameof(BroadcastWeChatAsync),
                messageTemplate.CompanyId,
                messageTemplate.Id,
                companyUser.Id,
                isTesting,
                messageTemplate.TemplateContent);

            var tagUserModel = new TagUserModel
            {
                OpenidList = new List<string>(),
                Tagid = 0
            };

            var createTags = new TagModel();
            Company weChatConfig = null;

            try
            {
                var conversations = await _appDbContext.Conversations
                    .Where(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && conversationIds.Contains(x.Id)
                            && x.UserProfile.ActiveStatus == ActiveStatus.Active)
                    .Include(x => x.WeChatUser)
                    .Include(x => x.UserProfile)
                    .ToListAsync();

                if (!isTesting)
                {
                    var conversationIdsAlreadyBroadcast = await _appDbContext.BroadcastCompaignHistories
                        .Where(history => history.BroadcastCampaignId == messageTemplate.Id)
                        .Select(history => history.ConversationId)
                        .ToListAsync();

                    conversations = conversations
                        .Where(x => !conversationIdsAlreadyBroadcast.Contains(x.Id))
                        .ToList();
                }

                var broadcastWeChatUserOpenIds = conversations
                    .Select(x => x.WeChatUser.openid)
                    .ToList();

                if (broadcastWeChatUserOpenIds.Count == 0)
                {
                    return;
                }

                weChatConfig = await _appDbContext.CompanyCompanies
                    .Include(x => x.WeChatConfig)
                    .FirstOrDefaultAsync(x => x.Id == messageTemplate.CompanyId);

                if (!weChatConfig.WeChatConfigId.HasValue)
                {
                    return;
                }

                if (string.IsNullOrEmpty(weChatConfig.WeChatConfig.AccessToken) ||
                    DateTime.UtcNow > weChatConfig.WeChatConfig.TokenExpireAt)
                {
                    var getAccessTokenResponse = await client.GetStringAsync(
                        $"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={weChatConfig.WeChatConfig.AppId}&secret={weChatConfig.WeChatConfig.AppSecret}");
                    var accessToken = JsonConvert.DeserializeObject<AccessTokenResponse>(getAccessTokenResponse);
                    weChatConfig.WeChatConfig.AccessToken = accessToken.access_token;
                    weChatConfig.WeChatConfig.TokenExpireAt = DateTime.UtcNow.AddSeconds(accessToken.expires_in - 30);
                    await _appDbContext.SaveChangesAsync();

                    _logger.LogInformation(
                        "[{MethodName}] Company {CompanyId} refresh WeChat token while processing broadcast {BroadcastId}, IsTesting: {IsTesting}",
                        nameof(BroadcastWeChatAsync),
                        messageTemplate.CompanyId,
                        messageTemplate.Id,
                        isTesting);
                }

                // create tag
                var tagRequest = new TagModel
                {
                    Tag = new Tag
                    {
                        Name = $"{DateTime.UtcNow.Millisecond}{messageTemplate.TemplateName}"
                    }
                };

                if (tagRequest.Tag.Name.Length > 30)
                {
                    tagRequest.Tag.Name = tagRequest.Tag.Name.Substring(0, 29);
                }

                var createTagResponse = await PostJsonAsync(
                    client,
                    $"https://api.weixin.qq.com/cgi-bin/tags/create?access_token={weChatConfig.WeChatConfig.AccessToken}",
                    tagRequest);
                var createTagResponseString = await createTagResponse.Content.ReadAsStringAsync();

                _logger.LogInformation(
                    "[{MethodName}] Company {CompanyId} broadcast {BroadcastId} create tag {WeChatTagName} response: {CreateTagResponse}",
                    nameof(BroadcastWeChatAsync),
                    messageTemplate.CompanyId,
                    messageTemplate.Id,
                    tagRequest.Tag.Name,
                    createTagResponseString);

                createTags = JsonConvert.DeserializeObject<TagModel>(createTagResponseString);

                tagUserModel = new TagUserModel
                {
                    OpenidList = broadcastWeChatUserOpenIds,
                    Tagid = createTags.Tag.Id
                };


                var tagUserResponse = await PostJsonAsync(
                    client,
                    $"https://api.weixin.qq.com/cgi-bin/tags/members/batchtagging?access_token={weChatConfig.WeChatConfig.AccessToken}",
                    tagUserModel);
                var tagUserResponseString = await tagUserResponse.Content.ReadAsStringAsync();

                _logger.LogInformation(
                    "[{MethodName}] Company {CompanyId} broadcast {BroadcastId} batch tag {WeChatTagName} response: {BatchTagResponse}",
                    nameof(BroadcastWeChatAsync),
                    messageTemplate.CompanyId,
                    messageTemplate.Id,
                    tagRequest.Tag.Name,
                    tagUserResponseString);

                var tagUserResult = JsonConvert.DeserializeObject<WeChatAPIResponse>(tagUserResponseString);

                if (tagUserResult.errcode > 0)
                {
                    return;
                }

                var uploadedFile = messageTemplate.UploadedFiles;

                var channelMessage = messageTemplate.CampaignChannelMessages
                    .First(x => x.TargetedChannel.ChannelType == ChannelTypes.Wechat);

                var messageContent = channelMessage.TemplateContent;

                if (channelMessage.UploadedFiles.Count > 0)
                {
                    uploadedFile = channelMessage.UploadedFiles;
                }

                // Upload image
                if (uploadedFile.Count == 0)
                {
                    return;
                }

                var stream = await _azureBlobStorageService.DownloadFromAzureBlob(
                    uploadedFile.First().Filename,
                    uploadedFile.First().BlobContainer);

                var tempFile = Path.GetTempPath() + Path.GetFileName(uploadedFile.First().Filename);
                await File.WriteAllBytesAsync(tempFile, stream.ToArray());

                var restClient = new RestClient(
                    $"https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={weChatConfig.WeChatConfig.AccessToken}");
                var request = new RestRequest(Method.POST);
                request.AddFile("media", tempFile);

                var response = await restClient.ExecuteAsync(request);
                var uploadFileResult = JsonConvert.DeserializeObject<WeChatUploadedFileResponse>(response.Content);

                _logger.LogInformation(
                    "[{MethodName}] Company {CompanyId} WeChat broadcast {BroadcastId} upload media response: {UploadFileResult}, IsTesting: {IsTesting}",
                    nameof(BroadcastWeChatAsync),
                    messageTemplate.CompanyId,
                    messageTemplate.Id,
                    response.Content,
                    isTesting);

                TempFileHelper.DeleteFileWithRetry(tempFile);

                var setupBroadcast = new SetUpBroadcastRequest
                {
                    Articles = new List<Article>
                    {
                        new Article
                        {
                            Content = messageContent,
                            Title = messageTemplate.TemplateName,
                            ShowCoverPic = 0,
                            ThumbMediaId = uploadFileResult.MediaId,
                            ContentSourceUrl = uploadFileResult.Url
                        }
                    }
                };

                // Change to the update api, refer to https://mp.weixin.qq.com/cgi-bin/announce?action=getannouncement&announce_id=11644831863qFQSh&version=&lang=zh_CN
                var setupResponse = await PostJsonAsync(
                    client,
                    $"https://api.weixin.qq.com/cgi-bin/draft/add?access_token={weChatConfig.WeChatConfig.AccessToken}",
                    setupBroadcast);
                var setupResponseString = await setupResponse.Content.ReadAsStringAsync();
                var setUpResult = JsonConvert.DeserializeObject<SetUpBroadcastResponse>(setupResponseString);

                _logger.LogInformation(
                    "[{MethodName}] Company {CompanyId} WeChat broadcast {BroadcastId} setup draft response: {SetupDraftResponse}, IsTesting: {IsTesting}",
                    nameof(BroadcastWeChatAsync),
                    messageTemplate.CompanyId,
                    messageTemplate.Id,
                    setupResponseString,
                    isTesting);

                var sendBroadcast = new WeChatBroadcastSent
                {
                    Filter = new Filter
                    {
                        IsToAll = false,
                        TagId = createTags.Tag.Id
                    },
                    Mpnews = new Mpnews
                    {
                        MediaId = setUpResult.MediaId
                    },
                    Msgtype = "mpnews",
                    SendIgnoreReprint = 0
                };

                var sendResponse = await PostJsonAsync(
                    client,
                    $"https://api.weixin.qq.com/cgi-bin/message/mass/sendall?access_token={weChatConfig.WeChatConfig.AccessToken}",
                    sendBroadcast);
                var sendResponseString = await sendResponse.Content.ReadAsStringAsync();
                var sendResult = JsonConvert.DeserializeObject<SendBroadcastResponse>(sendResponseString);

                _logger.LogInformation(
                    "[{MethodName}] Company {CompanyId} WeChat broadcast {BroadcastId} mass sending response: {SendResult}, IsTesting: {IsTesting}",
                    nameof(BroadcastWeChatAsync),
                    messageTemplate.CompanyId,
                    messageTemplate.Id,
                    sendResponseString,
                    isTesting);

                if (!isTesting)
                {
                    messageTemplate.Status = sendResult.Errcode == 0 ? BroadcastStatus.Sent : BroadcastStatus.Error;
                    await _appDbContext.SaveChangesAsync();
                }

                foreach (var conversation in conversations)
                {
                    ConversationMessage message = null;

                    if (sendResult.Errcode == 0)
                    {
                        message = new ConversationMessage
                        {
                            ConversationId = conversation.Id,
                            CompanyId = conversation.CompanyId,
                            Channel = ChannelTypes.Wechat,
                            MessageType = "file",
                            MessageContent = messageContent,
                            WeChatReceiverId = conversation.WeChatUserId,
                            IsSentFromSleekflow = true,
                            DeliveryType = DeliveryType.Broadcast,
                            Status = MessageStatus.Sent,
                            UploadedFiles = new List<UploadedFile>
                            {
                                new UploadedFile
                                {
                                    Filename = uploadedFile.FirstOrDefault().Filename,
                                    BlobContainer = uploadedFile.FirstOrDefault().BlobContainer,
                                    Channel = ChannelTypes.Wechat,
                                    MIMEType = uploadedFile.FirstOrDefault().MIMEType,
                                    Url = uploadedFile.FirstOrDefault().Url,
                                }
                            }
                        };

                        _appDbContext.ConversationMessages.Add(message);
                        await _appDbContext.SaveChangesAsync();

                        await _conversationMessageService.RemoveCache(conversation.CompanyId, conversation.Id);

                        conversation.LastMessageChannel = ChannelTypes.Wechat;
                        conversation.LastMessageId = message.Id;
                        conversation.UpdatedTime = message.CreatedAt;
                        conversation.IsUnreplied = false;
                    }

                    if (!isTesting)
                    {
                        var broadcastHistory = new BroadcastHistory
                        {
                            BroadcastCampaignId = messageTemplate.Id,
                            ConversationId = conversation.Id,
                            ConversationMessages = message is not null
                                ? new List<ConversationMessage> { message }
                                : new List<ConversationMessage>(),
                            Status = message is not null
                                ? message.Status.ToString()
                                : MessageStatus.Failed.ToString(),
                            BroadcastSentById = companyUser.Id,
                        };

                        _appDbContext.BroadcastCompaignHistories.Add(broadcastHistory);
                        await _appDbContext.SaveChangesAsync();
                    }

                    // Trigger campaign automation
                    try
                    {
                        if (messageTemplate.CampaignAutomationActions?.Count > 0)
                        {
                            var automationActions =
                                _mapper.Map<List<AutomationAction>>(messageTemplate.CampaignAutomationActions);

                            BackgroundJob.Enqueue<IAutomationService>(
                                x => x.ExecuteAutomationActions(
                                    conversation.UserProfileId,
                                    $"Campaign {messageTemplate.TemplateName}",
                                    automationActions,
                                    message.Id));

                            _logger.LogInformation(
                                "[{MethodName}] Post-actions for user profile {UserProfileId} in broadcast {BroadcastId} enqueued",
                                nameof(BroadcastWeChatAsync),
                                conversation?.UserProfileId,
                                messageTemplate?.Id);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Unable to enqueue post-actions for user profile {UserProfileId} in broadcast {BroadcastId}: {ExceptionMessage}",
                            nameof(BroadcastWeChatAsync),
                            conversation?.UserProfileId,
                            messageTemplate?.Id,
                            ex.Message);
                    }
                }

                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} broadcast WeChat {BroadcastId} error: {ExceptionString}",
                    nameof(BroadcastWeChatAsync),
                    messageTemplate.CompanyId,
                    messageTemplate.Id,
                    ex.ToString());

                if (!isTesting)
                {
                    messageTemplate.Status = BroadcastStatus.Error;
                }

                await _appDbContext.SaveChangesAsync();
            }

            BackgroundJob.Schedule(
                () => PostWeChatBroadcastCleanupAsync(
                    messageTemplate,
                    weChatConfig,
                    tagUserModel,
                    createTags),
                TimeSpan.FromMinutes(30));
        }

        public async Task PostWeChatBroadcastCleanupAsync(
            CompanyMessageTemplate messageTemplate,
            Company weChatConfig,
            TagUserModel tagUserModel,
            TagModel tagModel)
        {
            if (tagModel is not { Tag.Id: not 0 })
            {
                return;
            }

            // Untag the WeChat user no matter it's success or not, as no consequence if untagged the wrong guy.
            var client = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            try
            {
                if (weChatConfig is not null)
                {
                    var unTagUserResponse = await PostJsonAsync(
                        client,
                        $"https://api.weixin.qq.com/cgi-bin/tags/members/batchuntagging?access_token={weChatConfig.WeChatConfig.AccessToken}",
                        tagUserModel);
                    var unTagUserResponseString = await unTagUserResponse.Content.ReadAsStringAsync();

                    _logger.LogInformation(
                        "[{MethodName}] Company {CompanyId} broadcast {BroadcastId} batch untag {WeChatTagName}, result: {ResultString}",
                        nameof(PostWeChatBroadcastCleanupAsync),
                        messageTemplate.CompanyId,
                        messageTemplate.Id,
                        tagModel.Tag.Name,
                        unTagUserResponseString);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} broadcast {BroadcastId} batch untag error: {ExceptionString}",
                    nameof(PostWeChatBroadcastCleanupAsync),
                    messageTemplate.CompanyId,
                    messageTemplate.Id,
                    ex.ToString());
            }

            try
            {
                if (weChatConfig is not null)
                {
                    var removeTagUserResponse = await PostJsonAsync(
                        client,
                        $"https://api.weixin.qq.com/cgi-bin/tags/delete?access_token={weChatConfig.WeChatConfig.AccessToken}",
                        tagModel);
                    var removeTagUserResponseString = await removeTagUserResponse.Content.ReadAsStringAsync();

                    _logger.LogInformation(
                        "[{MethodName}] Company {CompanyId} broadcast {BroadcastId} delete WeChat tag {WeChatTagName}, result: {ResultString}",
                        nameof(PostWeChatBroadcastCleanupAsync),
                        messageTemplate.CompanyId,
                        messageTemplate.Id,
                        tagModel.Tag.Name,
                        removeTagUserResponseString);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} broadcast {BroadcastId} delete WeChat tag {WeChatTagName} error: {ExceptionString}",
                    nameof(PostWeChatBroadcastCleanupAsync),
                    messageTemplate.CompanyId,
                    messageTemplate.Id,
                    tagModel.Tag.Name,
                    ex.ToString());
            }
        }

        public async Task<StatisticsData> GetBroadcastStatus(
            Staff companyUser,
            string broadcastTemplateId,
            string broadcastStatus)
        {
            var rbacService = _serviceProvider.GetRequiredService<IRbacService>();
            var isRbacEnabled = await rbacService.IsRbacEnabled(companyUser.CompanyId);
            if (isRbacEnabled)
            {
                return await RbacGetBroadcastStatus(companyUser, broadcastTemplateId, broadcastStatus);
            }

            return await DefaultGetBroadcastStatus(companyUser, broadcastTemplateId, broadcastStatus);
        }

        private async Task<StatisticsData> DefaultGetBroadcastStatus(Staff companyUser, string broadcastTemplateId, string broadcastStatus)
        {
            var requireStatisticsStatus = new List<string>()
            {
                "sent", "sending", "paused", "failed"
            };

            var conditionDict = new Dictionary<BroadcastMessageStatus, Condition>()
            {
                {
                    BroadcastMessageStatus.Sent, new Condition()
                    {
                        BroadcastMessageStatus = BroadcastMessageStatus.Sent,
                        CompanyMessageTemplateId = broadcastTemplateId
                    }
                },
                {
                    BroadcastMessageStatus.Delivered, new Condition()
                    {
                        BroadcastMessageStatus = BroadcastMessageStatus.Delivered,
                        CompanyMessageTemplateId = broadcastTemplateId
                    }
                },
                {
                    BroadcastMessageStatus.Read, new Condition()
                    {
                        BroadcastMessageStatus = BroadcastMessageStatus.Read,
                        CompanyMessageTemplateId = broadcastTemplateId
                    }
                },
                {
                    BroadcastMessageStatus.Replied, new Condition()
                    {
                        BroadcastMessageStatus = BroadcastMessageStatus.Replied,
                        CompanyMessageTemplateId = broadcastTemplateId
                    }
                },
                {
                    BroadcastMessageStatus.Failed, new Condition()
                    {
                        BroadcastMessageStatus = BroadcastMessageStatus.Failed,
                        CompanyMessageTemplateId = broadcastTemplateId
                    }
                }
            };

            var countDict = new Dictionary<BroadcastMessageStatus, int>()
            {
                {
                    BroadcastMessageStatus.Sent, 0
                },
                {
                    BroadcastMessageStatus.Delivered, 0
                },
                {
                    BroadcastMessageStatus.Read, 0
                },
                {
                    BroadcastMessageStatus.Replied, 0
                },
                {
                    BroadcastMessageStatus.Failed, 0
                },
            };

            if (requireStatisticsStatus.Contains(broadcastStatus.ToLower()))
            {
                var isFromRawSql = _configuration.GetValue<bool>("SqlPerformance:FromRawSql");

                foreach (var kvp in conditionDict)
                {
                    try
                    {
                        var condition = new List<Condition>()
                        {
                            kvp.Value
                        };

                        if (isFromRawSql)
                        {
                            var (_, count, _) = await _userProfileSqlService.GetUserProfilesAsync(
                                companyUser.CompanyId,
                                condition,
                                0,
                                10,
                                "createdat",
                                "desc",
                                ((companyUser.RoleType != StaffUserRole.Admin) ? (long?) companyUser.Id : null),
                                null,
                                null,
                                companyUser.RoleType,
                                getCountOnly: true);

                            countDict[kvp.Key] = count;
                        }
                        else
                        {
                            var getUserProfilesIdsByFields =
                                await _userProfileService.GetIQueryableUserProfilesWithFilter(
                                    companyId: companyUser.CompanyId,
                                    conditions: condition,
                                    offset: null,
                                    limit: null,
                                    sortBy: "createdat",
                                    order: "desc",
                                    assigneeId: companyUser.RoleType != StaffUserRole.Admin ? companyUser.Id : null,
                                    channels: null,
                                    channelIds: null,
                                    staffUserRole: companyUser.RoleType);

                            countDict[kvp.Key] = await getUserProfilesIdsByFields.CountAsync();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "GetBroadcastStatus error. {BroadcastTemplateId} {Status}",
                            broadcastTemplateId,
                            kvp.Key);
                    }
                }
            }

            var result = new StatisticsData()
            {
                Sent = countDict[BroadcastMessageStatus.Sent],
                Delivered = countDict[BroadcastMessageStatus.Delivered],
                Read = countDict[BroadcastMessageStatus.Read],
                Replied = countDict[BroadcastMessageStatus.Replied],
                Failed = countDict[BroadcastMessageStatus.Failed],
                UpdatedAt = DateTime.UtcNow
            };

            return result;
        }

        private async Task<StatisticsData> RbacGetBroadcastStatus(Staff companyUser, string broadcastTemplateId, string broadcastStatus)
        {
            var requireStatisticsStatus = new List<string>()
            {
                "sent", "sending", "paused", "failed"
            };

            var conditionDict = new Dictionary<BroadcastMessageStatus, Condition>()
            {
                {
                    BroadcastMessageStatus.Sent, new Condition()
                    {
                        BroadcastMessageStatus = BroadcastMessageStatus.Sent,
                        CompanyMessageTemplateId = broadcastTemplateId
                    }
                },
                {
                    BroadcastMessageStatus.Delivered, new Condition()
                    {
                        BroadcastMessageStatus = BroadcastMessageStatus.Delivered,
                        CompanyMessageTemplateId = broadcastTemplateId
                    }
                },
                {
                    BroadcastMessageStatus.Read, new Condition()
                    {
                        BroadcastMessageStatus = BroadcastMessageStatus.Read,
                        CompanyMessageTemplateId = broadcastTemplateId
                    }
                },
                {
                    BroadcastMessageStatus.Replied, new Condition()
                    {
                        BroadcastMessageStatus = BroadcastMessageStatus.Replied,
                        CompanyMessageTemplateId = broadcastTemplateId
                    }
                },
                {
                    BroadcastMessageStatus.Failed, new Condition()
                    {
                        BroadcastMessageStatus = BroadcastMessageStatus.Failed,
                        CompanyMessageTemplateId = broadcastTemplateId
                    }
                }
            };

            var countDict = new Dictionary<BroadcastMessageStatus, int>()
            {
                {
                    BroadcastMessageStatus.Sent, 0
                },
                {
                    BroadcastMessageStatus.Delivered, 0
                },
                {
                    BroadcastMessageStatus.Read, 0
                },
                {
                    BroadcastMessageStatus.Replied, 0
                },
                {
                    BroadcastMessageStatus.Failed, 0
                },
            };

            if (requireStatisticsStatus.Contains(broadcastStatus.ToLower()))
            {
                var isFromRawSql = _configuration.GetValue<bool>("SqlPerformance:FromRawSql");

                foreach (var kvp in conditionDict)
                {
                    try
                    {
                        var condition = new List<Condition>()
                        {
                            kvp.Value
                        };

                        if (isFromRawSql)
                        {
                            var (_, count, _) = await _userProfileSqlService.GetUserProfilesAsync(
                                companyId: companyUser.CompanyId,
                                conditions: condition,
                                offset: 0,
                                limit:10,
                                sortBy: "createdat",
                                order: "desc",
                                assigneeId: null,
                                channels: null,
                                channelIds: null,
                                getCountOnly: true);

                            countDict[kvp.Key] = count;
                        }
                        else
                        {
                            var getUserProfilesIdsByFields =
                                await _userProfileService.GetIQueryableUserProfilesWithFilter(
                                    companyId: companyUser.CompanyId,
                                    conditions: condition,
                                    offset: null,
                                    limit: null,
                                    sortBy: "createdat",
                                    order: "desc",
                                    assigneeId: null,
                                    channels: null,
                                    channelIds: null,
                                    staffUserRole: StaffUserRole.Admin);

                            countDict[kvp.Key] = await getUserProfilesIdsByFields.CountAsync();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            "GetBroadcastStatus error. {BroadcastTemplateId} {Status}",
                            broadcastTemplateId,
                            kvp.Key);
                    }
                }
            }

            var result = new StatisticsData()
            {
                Sent = countDict[BroadcastMessageStatus.Sent],
                Delivered = countDict[BroadcastMessageStatus.Delivered],
                Read = countDict[BroadcastMessageStatus.Read],
                Replied = countDict[BroadcastMessageStatus.Replied],
                Failed = countDict[BroadcastMessageStatus.Failed],
                UpdatedAt = DateTime.UtcNow
            };

            return result;
        }


        public Task<bool> BroadcastExists(string companyId, string broadcastTemplateId)
            => _appDbContext.CompanyMessageTemplates
                .AnyAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == broadcastTemplateId);

        [HangfireMethodConcurrencyLimit("broadcast_batch:{0}:{1}", 10, 30)]
        public async Task BroadcastByUserProfileBatchJobAsync(
            string companyId,
            string broadcastTemplateId,
            long staffId,
            List<string> userProfileIds,
            bool isTesting,
            int batchNumber,
            int totalBatchSize,
            CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                await BroadcastByUserProfileBatchAsync(broadcastTemplateId, staffId, userProfileIds, isTesting);

                // If this is the last batch, trigger the broadcast completed event
                if (batchNumber == totalBatchSize)
                {
                    await OnBroadcastCompletedAsync(companyId, broadcastTemplateId, isTesting);
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("[{MethodName}] Job was cancelled due to shutdown.", nameof(BroadcastByUserProfileBatchJobAsync));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[{MethodName}] Job was failed.", nameof(BroadcastByUserProfileBatchJobAsync));
                throw;
            }
        }

        [HangfireMethodConcurrencyLimit("broadcast_batch_high_concurrency:{0}:{1}", 50, 30)]
        public async Task BroadcastByUserProfileHighConcurrencyBatchJobAsync(
            string companyId,
            string broadcastTemplateId,
            long staffId,
            List<string> userProfileIds,
            bool isTesting,
            int batchNumber,
            int totalBatchSize,
            CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                await BroadcastByUserProfileBatchAsync(broadcastTemplateId, staffId, userProfileIds, isTesting);

                // If this is the last batch, trigger the broadcast completed event
                if (batchNumber == totalBatchSize)
                {
                    await OnBroadcastCompletedAsync(companyId, broadcastTemplateId, isTesting);
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("[{MethodName}] Job was cancelled due to shutdown.", nameof(BroadcastByUserProfileHighConcurrencyBatchJobAsync));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[{MethodName}] Job was failed.", nameof(BroadcastByUserProfileHighConcurrencyBatchJobAsync));
                throw;
            }
        }

        public async Task BroadcastByUserProfileBatchAsync(
            string broadcastTemplateId,
            long staffId,
            List<string> userProfileIds,
            bool isTesting)
        {
            var companyUser = await _appDbContext.UserRoleStaffs
                .Include(x => x.Company.EmailConfig)
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.Id == staffId);

            var messageTemplate = await _appDbContext.CompanyMessageTemplates
                .Include(x => x.UploadedFiles)
                .Include(x => x.LastSentBy)
                .Include(x => x.CampaignChannelMessages)
                .ThenInclude(x => x.UploadedFiles)
                .Include(x => x.CampaignAutomationActions)
                .ThenInclude(x => x.UploadedFiles)
                .FirstOrDefaultAsync(x => x.Id == broadcastTemplateId);

            if (messageTemplate is null)
            {
                return;
            }

            if (!messageTemplate.IsBroadcastOn
                && !string.Equals(messageTemplate.Status, BroadcastStatus.Paused, StringComparison.OrdinalIgnoreCase))
            {
                await UpdateCompanyMessageTemplateStatusAsync(
                    messageTemplate.CompanyId,
                    messageTemplate.Id,
                    BroadcastStatus.Paused);

                return;
            }

            foreach (var userProfileId in userProfileIds)
            {
                var conversationId = string.Empty;

                try
                {
                    var conversation = await _appDbContext.Conversations
                        .Where(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && x.UserProfileId == userProfileId)
                        .FirstOrDefaultAsync();

                    if (conversation is null)
                    {
                        var newConversation = await _userProfileService.GetConversationByUserProfileId(
                            companyUser.CompanyId,
                            userProfileId,
                            isNotify: false);

                        conversationId = newConversation.Id;
                    }
                    else
                    {
                        conversationId = conversation.Id;
                    }

                    await BroadcastWithConversationIdAsync(
                        messageTemplate,
                        companyUser,
                        conversationId,
                        isTesting);

                    // Clear the change tracker to prevent memory leak after each iteration
                    _appDbContext.ChangeTracker.Clear();
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception exception)
                {
                    _logger.LogError(
                        exception,
                        "[{ServiceName} method {MethodName}] Company {CompanyId} broadcasting template {TemplateId} in conversation {ConversationId} general exception. {ExceptionMessage}",
                        nameof(BroadcastService),
                        nameof(BroadcastByUserProfileBatchAsync),
                        companyUser.CompanyId,
                        messageTemplate.Id,
                        conversationId,
                        exception.Message);
                }
            }
        }

        private async Task OnBroadcastCompletedAsync(string companyId, string broadcastTemplateId, bool isTesting)
        {
            var messageTemplateStatus = await _appDbContext.CompanyMessageTemplates
                .Where(x => x.Id == broadcastTemplateId)
                .Select(x => x.Status)
                .FirstOrDefaultAsync();

            if (!isTesting
                && !string.Equals(messageTemplateStatus, BroadcastStatus.Sent, StringComparison.OrdinalIgnoreCase))
            {
                await UpdateCompanyMessageTemplateStatusAsync(
                    companyId,
                    broadcastTemplateId,
                    BroadcastStatus.Sent);

                var messageTemplateSent = await _appDbContext.CompanyMessageTemplates
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.LastSentBy)
                    .Include(x => x.CampaignChannelMessages)
                    .ThenInclude(x => x.UploadedFiles)
                    .Include(x => x.CampaignAutomationActions)
                    .ThenInclude(x => x.UploadedFiles)
                    .FirstOrDefaultAsync(x => x.Id == broadcastTemplateId);

                await _signalRService.SignalROnBroadcastCampaignSent(
                    messageTemplateSent,
                    new List<BroadcastHistory>());

                await _appDbContext.SaveChangesAsync();
            }
        }

        private async Task UpdateCompanyMessageTemplateStatusAsync(
            string companyId,
            string companyMessageTemplateId,
            string status)
        {
            var messageTemplate = await _appDbContext.CompanyMessageTemplates.FirstOrDefaultAsync(
                x => x.Id == companyMessageTemplateId && x.CompanyId == companyId);
            messageTemplate.Status = status;
            messageTemplate.SentAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();
        }

        private async Task BroadcastWithConversationIdAsync(
            CompanyMessageTemplate messageTemplate,
            Staff companyUser,
            string conversationId,
            bool isTesting)
        {
            if (await _appDbContext.CompanyMessageTemplates
                    .AnyAsync(
                        x =>
                            x.Id == messageTemplate.Id
                            && !x.IsBroadcastOn))
            {
                await UpdateCompanyMessageTemplateStatusAsync(
                    messageTemplate.CompanyId,
                    messageTemplate.Id,
                    BroadcastStatus.Paused);

                return;
            }

            var conversation = await _appDbContext.Conversations
                .Include(x => x.NaiveUser)
                .Include(x => x.facebookUser)
                .Include(x => x.EmailAddress)
                .Include(x => x.WhatsappUser)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(x => x.ViberUser)
                .Include(x => x.TelegramUser)
                .Include(x => x.InstagramUser)
                .Include(x => x.LineUser)
                .Include(x => x.SMSUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyUser.CompanyId);

            if (conversation is null)
            {
                return;
            }

            var hasBroadcastHistory = await _appDbContext.BroadcastCompaignHistories
                .AnyAsync(
                    x =>
                        x.BroadcastCampaignId == messageTemplate.Id
                        && x.ConversationId == conversation.Id);

            if (!isTesting
                && hasBroadcastHistory)
            {
                return;
            }

            if (!isTesting)
            {
                var subscriberField = await _userProfileService.GetCustomFieldByFieldName(
                    conversation.UserProfileId,
                    "Subscriber");

                var skipBroadcast = subscriberField is not null &&
                                    bool.TryParse(subscriberField.Value, out var isSubscribed) && !isSubscribed;

                if (skipBroadcast)
                {
                    var broadcastHistory = new BroadcastHistory
                    {
                        BroadcastCampaignId = messageTemplate.Id,
                        ConversationId = conversation.Id,
                        Status = "Unsubscribed",
                        BroadcastSentById = companyUser.Id,
                    };

                    _appDbContext.BroadcastCompaignHistories.Add(broadcastHistory);
                    await _appDbContext.SaveChangesAsync();

                    return;
                }
            }

            var userProfile = await _appDbContext.UserProfiles
                .Include(x => x.WhatsAppAccount)
                .Include(x => x.WeChatUser)
                .Include(x => x.WebClient)
                .Include(x => x.UserDevice)
                .Include(x => x.EmailAddress)
                .Include(x => x.FacebookAccount)
                .Include(x => x.LineUser)
                .Include(x => x.ViberUser)
                .Include(x => x.TelegramUser)
                .Include(x => x.InstagramUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .Include(x => x.SMSUser)
                .FirstOrDefaultAsync(x => x.Id == conversation.UserProfileId);

            messageTemplate.TemplateParams ??= new List<string>();

            ConnectStripeResponse paymentLinkResult = null;

            if (messageTemplate.StripePaymentRequestOption != null)
            {
                var stripePaymentRequest = new GenerateStripPaymentRequest
                {
                    ShopifyId = messageTemplate.StripePaymentRequestOption.ShopifyId,
                    LineItems = messageTemplate.StripePaymentRequestOption.LineItems,
                    IsReserveInventory = messageTemplate.StripePaymentRequestOption.IsReserveInventory,
                    ExpiredAt = messageTemplate.StripePaymentRequestOption.ExpiredAt,
                    UserProfileId = conversation.UserProfileId,
                    SharedType = StripePaymentLinkSharedType.Campaign,
                    PlatformCountry = messageTemplate.StripePaymentRequestOption.PlatformCountry
                };

                paymentLinkResult = await _sleekPayService.GenerateSleekPayLink(
                    messageTemplate.CompanyId,
                    null,
                    stripePaymentRequest);
            }

            var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

            if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
            {
                _logger.LogWarning(
                    "[{MethodName}] User profile {UserProfileId} not active for broadcast {BroadcastId} in company {CompanyId}",
                    nameof(BroadcastWithConversationIdAsync),
                    userProfile.Id,
                    messageTemplate.Id,
                    companyUser.CompanyId);

                return;
            }

            if (messageTemplate.BroadcastAsNote)
            {
                var noteMessage = new ConversationMessage
                {
                    Channel = ChannelTypes.Note,
                    MessageType = "text",
                    DeliveryType = DeliveryType.Broadcast,
                    IsSentFromSleekflow = true,
                    SleekPayRecordId = paymentLinkResult?.StripePaymentRecordId
                };

                var channelMessage = messageTemplate.CampaignChannelMessages
                    .First(x => x.TargetedChannel.ChannelType == ChannelTypes.Note);

                var templates = await _conversationMessageService.FormatParamsWithPaymentUrl(
                    userProfile,
                    channelMessage.TemplateParams,
                    paymentLinkResult?.TrackingUrl);

                noteMessage.MessageContent = string.Format(
                    channelMessage.TemplateContent,
                    templates
                        .Select(x => x.ToString())
                        .ToArray());

                if (channelMessage.UploadedFiles.Count > 0)
                {
                    noteMessage.MessageType = "file";

                    noteMessage.UploadedFiles =
                        _mapper.Map<List<UploadedFile>>(channelMessage.UploadedFiles.OrderBy(x => x.Id));
                }

                var sentMessageNote = await _conversationMessageService.SendConversationNote(
                    companyUser.CompanyId,
                    conversationId,
                    null,
                    noteMessage,
                    null);

                if (sentMessageNote is not null)
                {
                    try
                    {
                        var broadcastHistory = new BroadcastHistory
                        {
                            BroadcastCampaignId = messageTemplate.Id,
                            ConversationId = conversation.Id,
                            ConversationMessages = new List<ConversationMessage>
                            {
                                sentMessageNote
                            },
                            Status = noteMessage.Status.ToString(),
                            BroadcastSentById = companyUser.Id,
                        };

                        _appDbContext.BroadcastCompaignHistories.Add(broadcastHistory);

                        await _appDbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Error adding broadcast note history for conversation {ConversationId} in broadcast {BroadcastId}: {ExceptionMessage}",
                            conversationId,
                            messageTemplate.Id,
                            ex.Message);
                    }

                    _logger.LogInformation(
                        "Broadcast to: {ConversationId}, Message: {NoteMessageContent} by {CompanyUserDisplayName}",
                        conversationId,
                        noteMessage.MessageContent,
                        companyUser.Identity?.DisplayName);
                }
            }
            else
            {
                var conversationMessage = new ConversationMessage
                {
                    ConversationId = conversationId,
                    DeliveryType = DeliveryType.Broadcast,
                    Channel = messageTemplate.TargetedChannel.ChannelType,
                    ChannelIdentityId = messageTemplate.TargetedChannel.ChannelIdentityId,
                    CompanyId = companyUser.CompanyId
                };

                try
                {
                    switch (conversationMessage.Channel)
                    {
                        case ChannelTypes.WhatsappTwilio:
                        case "twilio_whatsapp":
                            if (userProfile.WhatsAppAccount is null)
                            {
                                var broadcastHistory = new BroadcastHistory
                                {
                                    BroadcastCampaignId = messageTemplate.Id,
                                    ConversationId = conversation.Id,
                                    Status = "No phone number found",
                                    BroadcastSentById = companyUser.Id,
                                };

                                _appDbContext.BroadcastCompaignHistories.Add(broadcastHistory);
                                await _appDbContext.SaveChangesAsync();

                                break;
                            }

                            if (conversationMessage.ChannelIdentityId != userProfile.WhatsAppAccount.ChannelIdentityId)
                            {
                                var whatsAppConfig = await _appDbContext.ConfigWhatsAppConfigs
                                    .AsNoTracking()
                                    .FirstAsync(
                                        x =>
                                            x.CompanyId == conversationMessage.CompanyId
                                            && x.ChannelIdentityId == conversationMessage.ChannelIdentityId);

                                await _userProfileService.SwitchWhatsappChannel(
                                    userProfile.Id,
                                    new ChangeChatAPIInstance
                                    {
                                        InstanceId = $"{whatsAppConfig.TwilioAccountId};{whatsAppConfig.WhatsAppSender}"
                                    });

                                userProfile = await _appDbContext.UserProfiles
                                    .Include(x => x.WhatsAppAccount)
                                    .Include(x => x.WeChatUser)
                                    .Include(x => x.WebClient)
                                    .Include(x => x.UserDevice)
                                    .Include(x => x.EmailAddress)
                                    .Include(x => x.FacebookAccount)
                                    .Include(x => x.LineUser)
                                    .Include(x => x.SMSUser)
                                    .FirstOrDefaultAsync(x => x.Id == userProfile.Id);

                                conversation = await _appDbContext.Conversations
                                    .Include(x => x.NaiveUser)
                                    .Include(x => x.facebookUser)
                                    .Include(x => x.InstagramUser)
                                    .Include(x => x.EmailAddress)
                                    .Include(x => x.WhatsappUser)
                                    .Include(x => x.WebClient)
                                    .Include(x => x.WeChatUser)
                                    .Include(x => x.LineUser)
                                    .Include(x => x.SMSUser)
                                    .Include(x => x.ViberUser)
                                    .Include(x => x.TelegramUser)
                                    .Include(x => x.WhatsApp360DialogUser)
                                    .Include(x => x.WhatsappCloudApiUser)
                                    .FirstOrDefaultAsync(x => x.Id == conversation.Id);
                            }

                            break;

                        case ChannelTypes.Whatsapp360Dialog:
                            if (userProfile.WhatsApp360DialogUser is null)
                            {
                                var broadcastHistory = new BroadcastHistory
                                {
                                    BroadcastCampaignId = messageTemplate.Id,
                                    ConversationId = conversation.Id,
                                    Status = "No phone number found",
                                    BroadcastSentById = companyUser.Id,
                                };

                                _appDbContext.BroadcastCompaignHistories.Add(broadcastHistory);
                                await _appDbContext.SaveChangesAsync();

                                break;
                            }

                            if (conversationMessage.ChannelIdentityId != userProfile.WhatsApp360DialogUser.ChannelWhatsAppPhoneNumber)
                            {
                                var whatsapp360DialogConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                                    .AsNoTracking()
                                    .FirstAsync(x =>
                                        x.CompanyId == conversationMessage.CompanyId
                                        && x.ChannelIdentityId == conversationMessage.ChannelIdentityId);

                                await _userProfileService.SwitchWhatsapp360DialogChannel(
                                    userProfile.CompanyId,
                                    userProfile.Id,
                                    whatsapp360DialogConfig.Id);

                                userProfile = await _appDbContext.UserProfiles
                                    .Include(x => x.WhatsAppAccount)
                                    .Include(x => x.WeChatUser)
                                    .Include(x => x.WebClient)
                                    .Include(x => x.UserDevice)
                                    .Include(x => x.EmailAddress)
                                    .Include(x => x.FacebookAccount)
                                    .Include(x => x.LineUser)
                                    .Include(x => x.SMSUser)
                                    .FirstOrDefaultAsync(x => x.Id == userProfile.Id);

                                conversation = await _appDbContext.Conversations
                                    .Include(x => x.NaiveUser)
                                    .Include(x => x.facebookUser)
                                    .Include(x => x.InstagramUser)
                                    .Include(x => x.EmailAddress)
                                    .Include(x => x.WhatsappUser)
                                    .Include(x => x.WebClient)
                                    .Include(x => x.WeChatUser)
                                    .Include(x => x.LineUser)
                                    .Include(x => x.SMSUser)
                                    .Include(x => x.ViberUser)
                                    .Include(x => x.TelegramUser)
                                    .Include(x => x.WhatsApp360DialogUser)
                                    .Include(x => x.WhatsappCloudApiUser)
                                    .FirstOrDefaultAsync(x => x.Id == conversation.Id);
                            }

                            break;

                        case ChannelTypes.WhatsappCloudApi:
                            if (userProfile.WhatsappCloudApiUser is null)
                            {
                                var broadcastHistory = new BroadcastHistory
                                {
                                    BroadcastCampaignId = messageTemplate.Id,
                                    ConversationId = conversation.Id,
                                    Status = "No phone number found",
                                    BroadcastSentById = companyUser.Id,
                                };

                                _appDbContext.BroadcastCompaignHistories.Add(broadcastHistory);
                                await _appDbContext.SaveChangesAsync();

                                break;
                            }

                            if (conversation.WhatsappCloudApiUser is null)
                            {
                                conversation.WhatsappCloudApiUser = userProfile.WhatsappCloudApiUser;
                                await _appDbContext.SaveChangesAsync();
                            }

                            if (conversationMessage.ChannelIdentityId != conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber)
                            {
                                await _userProfileService.SwitchWhatsappCloudApiChannel(
                                    conversation.CompanyId,
                                    conversation.UserProfileId,
                                    conversationMessage.ChannelIdentityId);

                                conversation = await _appDbContext.Conversations
                                    .Include(x => x.NaiveUser)
                                    .Include(x => x.facebookUser)
                                    .Include(x => x.InstagramUser)
                                    .Include(x => x.EmailAddress)
                                    .Include(x => x.WhatsappUser)
                                    .Include(x => x.WebClient)
                                    .Include(x => x.WeChatUser)
                                    .Include(x => x.LineUser)
                                    .Include(x => x.SMSUser)
                                    .Include(x => x.ViberUser)
                                    .Include(x => x.TelegramUser)
                                    .Include(x => x.WhatsApp360DialogUser)
                                    .Include(x => x.WhatsappCloudApiUser)
                                    .FirstOrDefaultAsync(x => x.Id == conversation.Id);
                            }

                            break;

                        case ChannelTypes.Facebook:
                            if (userProfile.FacebookAccount == null)
                            {
                                var broadcastHistory = new BroadcastHistory
                                {
                                    BroadcastCampaignId = messageTemplate.Id,
                                    ConversationId = conversation.Id,
                                    Status = "No facebook account found",
                                    BroadcastSentById = companyUser.Id,
                                };

                                _appDbContext.BroadcastCompaignHistories.Add(broadcastHistory);
                                await _appDbContext.SaveChangesAsync();

                                break;
                            }

                            var sender = await _appDbContext.SenderFacebookSenders
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.FacebookId == _appDbContext.ConfigFacebookConfigs
                                            .Where(y =>
                                                y.CompanyId == conversationMessage.CompanyId
                                                && y.PageId == conversationMessage.ChannelIdentityId)
                                            .Select(y => y.PageId)
                                            .FirstOrDefault()
                                        && x.pageId == x.FacebookId);

                            conversationMessage.facebookSender = sender;
                            conversationMessage.facebookSenderId = sender.Id;
                            conversationMessage.facebookSender = userProfile.FacebookAccount;
                            conversationMessage.facebookSenderId = userProfile.FacebookAccountId;


                            break;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Broadcast {BroadcastId} error when processing TargetedChannelWithIds for conversation {ConversationId}: {ExceptionMessage}",
                        nameof(BroadcastWithConversationIdAsync),
                        messageTemplate.Id,
                        conversationId,
                        ex.Message);
                }

                // Split one-to-many query
                conversation.conversationHashtags = await _appDbContext.ConversationHashtags
                    .Where(x => x.ConversationId == conversation.Id)
                    .ToListAsync();

                var broadcastMessage = new ConversationMessage
                {
                    Channel = conversationMessage.Channel,
                    ChannelIdentityId = conversationMessage.ChannelIdentityId,
                    MessageType = "text",
                    DeliveryType = DeliveryType.Broadcast,
                    IsSentFromSleekflow = true,
                    SleekPayRecordId = paymentLinkResult?.StripePaymentRecordId,
                    SenderId = companyUser.IdentityId
                };

                switch (conversationMessage.Channel)
                {
                    case ChannelTypes.Facebook:
                        broadcastMessage.facebookReceiver = conversation.facebookUser;

                        break;
                    case "whatsapp":
                        if (conversation.WhatsappUserId != userProfile.WhatsAppAccountId)
                        {
                            conversation.WhatsappUserId = userProfile.WhatsAppAccountId;
                            conversation.WhatsappUser = userProfile.WhatsAppAccount;
                        }

                        broadcastMessage.whatsappReceiver = userProfile.WhatsAppAccount;

                        if (broadcastMessage.whatsappReceiver == null)
                        {
                            conversation.WhatsappUser = userProfile.WhatsAppAccount;
                            broadcastMessage.whatsappReceiver = userProfile.WhatsAppAccount;

                            await _appDbContext.SaveChangesAsync();
                        }

                        if (broadcastMessage.whatsappReceiver == null)
                        {
                            return;
                        }

                        break;
                    case ChannelTypes.Line:
                        broadcastMessage.LineReceiver = conversation.LineUser;

                        break;
                    case ChannelTypes.Viber:
                        broadcastMessage.ViberReceiver = conversation.ViberUser;

                        break;
                    case ChannelTypes.Telegram:
                        broadcastMessage.TelegramReceiver = conversation.TelegramUser;

                        break;
                    case ChannelTypes.Instagram:
                        broadcastMessage.InstagramReceiver = conversation.InstagramUser;

                        break;
                    case ChannelTypes.Whatsapp360Dialog:
                        broadcastMessage.Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser;

                        break;
                    case ChannelTypes.WhatsappCloudApi:
                        broadcastMessage.WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser;

                        break;
                    case ChannelTypes.Sms:
                        if (!conversation.SMSUserId.HasValue)
                        {
                            conversation.SMSUser = userProfile.SMSUser;
                            conversation.SMSUserId = userProfile.SMSUserId;
                            await _appDbContext.SaveChangesAsync();
                        }

                        broadcastMessage.SMSReceiver = conversation.SMSUser;

                        break;
                }

                var channelMessage = messageTemplate.CampaignChannelMessages.First();

                var templates = await _conversationMessageService.FormatParamsWithPaymentUrl(
                    userProfile,
                    channelMessage.TemplateParams,
                    paymentLinkResult?.TrackingUrl);

                broadcastMessage.MessageContent = channelMessage.TemplateContent
                    .SafeFormat(templates
                        .Select(x => x.ToString())
                        .ToArray());

                if (channelMessage.UploadedFiles.Count > 0)
                {
                    broadcastMessage.MessageType = "file";

                    broadcastMessage.UploadedFiles = _mapper.Map<List<UploadedFile>>(
                        channelMessage.UploadedFiles.OrderBy(x => x.Id));
                }

                if (conversationMessage.Channel == ChannelTypes.WhatsappTwilio)
                {
                    if (channelMessage.ExtendedMessageType == ExtendedMessageType.TwilioContentApi)
                    {
                        var contentVariables = new Dictionary<string, object>();

                        var i = 1;

                        foreach (var variable in channelMessage.OfficialTemplateParams)
                        {
                            var variableString = variable;
                            var variableInVariables = new List<string>();

                            var pattern = @"\{\{(.+?)\}\}";
                            var matches = Regex.Matches(variable, pattern);
                            var index = 0;

                            foreach (Match match in matches)
                            {
                                var fieldName = match.Groups[1].Value;
                                variableInVariables.Add($"@{fieldName}");

                                var replacePattern = @"\{" + fieldName + @"\}";
                                variableString = Regex.Replace(variable, replacePattern, index.ToString());
                                index++;
                            }

                            if (variableInVariables.Any())
                            {
                                var formattedVariables =
                                    await _conversationMessageService.FormatParamsWithPaymentUrl(
                                        userProfile,
                                        variableInVariables,
                                        paymentLinkResult?.TrackingUrl);

                                variableString = variableString.SafeFormat(
                                    formattedVariables
                                        .Select(x => x.ToString())
                                        .ToArray());
                            }

                            contentVariables.Add($"{i}", variableString);
                            i++;
                        }

                        broadcastMessage.ExtendedMessagePayload = new ExtendedMessagePayload
                        {
                            ExtendedMessagePayloadDetail = channelMessage.ExtendedMessagePayloadDetail
                        };

                        broadcastMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                            .WhatsappTwilioContentApiObject.ContentVariables = contentVariables;

                        broadcastMessage.ExtendedMessagePayload.ExtendedMessageType =
                            channelMessage.ExtendedMessageType.Value;
                    }
                }
                else if (conversationMessage.Channel == ChannelTypes.Whatsapp360Dialog)
                {
                    if (channelMessage.WhatsApp360DialogExtendedCampaignMessage != null
                        && !string.IsNullOrEmpty(channelMessage.WhatsApp360DialogExtendedCampaignMessage.MessageType))
                    {
                        switch (channelMessage.WhatsApp360DialogExtendedCampaignMessage.MessageType)
                        {
                            case "template":
                                broadcastMessage.MessageType = channelMessage
                                    .WhatsApp360DialogExtendedCampaignMessage
                                    .MessageType;

                                broadcastMessage.Whatsapp360DialogExtendedMessagePayload =
                                    new Whatsapp360DialogExtendedMessagePayload()
                                    {
                                        Whatsapp360DialogTemplateMessage =
                                            JsonConvert.DeserializeObject<Whatsapp360DialogTemplateMessageViewModel>(
                                                    JsonConvert.SerializeObject(
                                                        channelMessage.WhatsApp360DialogExtendedCampaignMessage
                                                            .Whatsapp360DialogTemplateMessage)),
                                    };

                                broadcastMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogTemplateMessage.Components =
                                    broadcastMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogTemplateMessage.Components.Format(templates);

                                broadcastMessage.MessageContent =
                                    channelMessage.TemplateContent.FormatParamToBodyText(
                                        broadcastMessage.Whatsapp360DialogExtendedMessagePayload
                                            .Whatsapp360DialogTemplateMessage.Components);

                                break;

                            case "interactive":
                                broadcastMessage.MessageType = channelMessage
                                    .WhatsApp360DialogExtendedCampaignMessage.MessageType;

                                broadcastMessage.Whatsapp360DialogExtendedMessagePayload =
                                    new Whatsapp360DialogExtendedMessagePayload()
                                    {
                                        Whatsapp360DialogInteractiveObject =
                                            JsonConvert.DeserializeObject<InteractiveObject>(
                                                JsonConvert.SerializeObject(
                                                    channelMessage.WhatsApp360DialogExtendedCampaignMessage
                                                        .Whatsapp360DialogInteractiveObject)),
                                    };

                                broadcastMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogInteractiveObject =
                                    broadcastMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogInteractiveObject.Format(templates);

                                break;
                        }
                    }
                }
                else if (conversationMessage.Channel == ChannelTypes.WhatsappCloudApi)
                {
                    if (channelMessage.ExtendedMessageType.HasValue
                        && channelMessage.ExtendedMessagePayloadDetail != null)
                    {
                        broadcastMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                        {
                            Channel = ChannelTypes.WhatsappCloudApi
                        };

                        broadcastMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                            channelMessage.ExtendedMessagePayloadDetail.DeepClone());

                        switch (channelMessage.ExtendedMessageType)
                        {
                            case ExtendedMessageType.WhatsappCloudApiTemplateMessage:
                                broadcastMessage.MessageType = "template";

                                broadcastMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiTemplateMessageObject.Components =
                                    broadcastMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiTemplateMessageObject.Components.Format(templates);

                                broadcastMessage.MessageContent =
                                    channelMessage.TemplateContent
                                        .FormatWhatsappCloudApiTemplateParamToBodyText(
                                            broadcastMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                                .WhatsappCloudApiTemplateMessageObject.Components);

                                break;
                            case ExtendedMessageType.WhatsappCloudApiInteractiveMessage:
                                broadcastMessage.MessageType = "interactive";

                                broadcastMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiInteractiveObject =
                                    broadcastMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiInteractiveObject.Format(templates);

                                if (string.IsNullOrEmpty(broadcastMessage.MessageContent))
                                {
                                    broadcastMessage.MessageContent = broadcastMessage.ExtendedMessagePayload
                                        .ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject.Body
                                        .Text;
                                }

                                break;
                        }
                    }
                }
                else if (conversationMessage.Channel == ChannelTypes.Facebook)
                {
                    broadcastMessage.MessageTag = channelMessage.MessageTag;

                    if (channelMessage.ExtendedMessagePayloadDetail != null
                        && channelMessage.ExtendedMessageType.HasValue)
                    {
                        switch (channelMessage.ExtendedMessageType.Value)
                        {
                            case ExtendedMessageType.FacebookOTNText:
                                broadcastMessage.MessageType = "text";

                                broadcastMessage.MessageContent = channelMessage.TemplateContent
                                    .FormatParamToBodyText(
                                        null,
                                        templates);

                                break;
                            case ExtendedMessageType.FacebookOTNFile:
                                broadcastMessage.MessageType = "file";

                                break;
                            case ExtendedMessageType.FacebookOTNInteractive:
                                broadcastMessage.MessageType = "interactive";

                                break;
                        }

                        broadcastMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                        {
                            Channel = ChannelTypes.Facebook,
                            ExtendedMessageType = channelMessage.ExtendedMessageType.Value,
                            FacebookOTNTopicId = channelMessage.FacebookOTNTopicId,
                            ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                            {
                                FacebookMessagePayload = channelMessage.ExtendedMessagePayloadDetail
                                    .FacebookMessagePayload
                            }
                        };

                        broadcastMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail =
                            broadcastMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail.Format(
                                templates);
                    }
                }


                if (!isTesting)
                {
                    if (await _appDbContext.BroadcastCompaignHistories.AnyAsync(
                            x =>
                                x.BroadcastCampaignId == messageTemplate.Id
                                && x.ConversationId == conversation.Id))
                    {
                        return;
                    }

                    try
                    {
                        // add record to avoid duplication
                        var broadcastHistory = new BroadcastHistory
                        {
                            BroadcastCampaignId = messageTemplate.Id,
                            ConversationId = conversation.Id,
                            BroadcastSentById = companyUser.Id,
                        };

                        _appDbContext.BroadcastCompaignHistories.Add(broadcastHistory);
                        await _appDbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Error when adding broadcast history for conversation {ConversationId} in broadcast {BroadcastId}: {ExceptionMessage}",
                            nameof(BroadcastWithConversationIdAsync),
                            conversationId,
                            messageTemplate?.Id,
                            ex.Message);
                    }
                }

                if (conversation.CompanyId == "9406af18-7f9a-41e4-aa25-d638e8bf7162")
                {
                    conversation.Status = "closed";
                    await _appDbContext.SaveChangesAsync();
                }

                IList<ConversationMessage> sentResult = null;

                try
                {
                    sentResult = await _conversationMessageService.SendMessage(
                        conversation,
                        broadcastMessage,
                        false,
                        false,
                        false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}][Send Message Error when Broadcast Message] Broadcast {BroadcastId} Conversation {ConversationId}, IsTestMessage: {IsTestMessage}, Error: {ExceptionMessage}",
                        nameof(BroadcastWithConversationIdAsync),
                        messageTemplate?.Id,
                        conversation?.Id,
                        isTesting,
                        ex.Message);

                    var pendingHistory = await _appDbContext.BroadcastCompaignHistories
                        .FirstOrDefaultAsync(
                            x =>
                                x.BroadcastCampaignId == messageTemplate.Id
                                && x.ConversationId == conversation.Id);

                    if (pendingHistory is not null)
                    {
                        pendingHistory.ConversationMessages = sentResult?.ToList();

                        pendingHistory.Status = sentResult is { Count: > 0 }
                            ? sentResult.First().Status.ToString()
                            : "Failed";
                    }
                    else
                    {
                        var broadcastHistory = new BroadcastHistory
                        {
                            BroadcastCampaignId = messageTemplate.Id,
                            ConversationId = conversation.Id,
                            ConversationMessages = sentResult?.ToList(),
                            Status = sentResult is { Count: > 0 } ? sentResult.First().Status.ToString() : "Failed",
                            BroadcastSentById = companyUser.Id,
                        };

                        _appDbContext.BroadcastCompaignHistories.Add(broadcastHistory);
                    }

                    await _appDbContext.SaveChangesAsync();

                    return;
                }

                if (!isTesting
                    && sentResult.Count > 0)
                {
                    try
                    {
                        var pendingHistory = await _appDbContext.BroadcastCompaignHistories
                            .FirstOrDefaultAsync(
                                x =>
                                    x.BroadcastCampaignId == messageTemplate.Id
                                    && x.ConversationId == conversation.Id);

                        if (pendingHistory != null)
                        {
                            pendingHistory.ConversationMessages = sentResult.ToList();
                            pendingHistory.Status = sentResult.First().Status.ToString();
                        }
                        else
                        {
                            var broadcastHistory = new BroadcastHistory
                            {
                                BroadcastCampaignId = messageTemplate.Id,
                                ConversationId = conversation.Id,
                                ConversationMessages = sentResult.ToList(),
                                Status = sentResult.First().Status.ToString(),
                                BroadcastSentById = companyUser.Id,
                            };

                            _appDbContext.BroadcastCompaignHistories.Add(broadcastHistory);
                        }

                        await _appDbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Error adding/updating broadcast history for conversation {ConversationId} in broadcast {BroadcastId}: {ExceptionMessage}",
                            nameof(BroadcastWithConversationIdAsync),
                            conversation?.Id,
                            messageTemplate?.Id,
                            ex.Message);
                    }
                }

                _logger.LogInformation(
                    "[{MethodName}] Broadcast {BroadcastId} to: {ConversationId}, IsTestMessage: {IsTestMessage}, Message: {BroadcastMessageContent} by {CompanyUserDisplayName}",
                    nameof(BroadcastWithConversationIdAsync),
                    messageTemplate.Id,
                    conversation.Id,
                    isTesting,
                    broadcastMessage.MessageContent,
                    companyUser.Identity.DisplayName);

                // Execute automation action
                try
                {
                    if (messageTemplate.CampaignAutomationActions?.Count > 0 && sentResult.Any())
                    {
                        var automationActions =
                            _mapper.Map<List<AutomationAction>>(messageTemplate.CampaignAutomationActions);

                        BackgroundJob.Enqueue<IAutomationService>(
                            x => x.ExecuteAutomationActions(
                                conversation.UserProfileId,
                                $"Campaign {messageTemplate.TemplateName}",
                                automationActions,
                                sentResult.FirstOrDefault().Id));

                        _logger.LogInformation(
                            "[{MethodName}] Broadcast id {BroadcastId} post-actions enqueued for {UserProfileId}",
                            nameof(BroadcastWithConversationIdAsync),
                            messageTemplate.Id,
                            conversation.UserProfileId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Broadcast id {BroadcastId} post-action enqueue error for {UserProfileId}",
                        nameof(BroadcastWithConversationIdAsync),
                        messageTemplate.Id,
                        conversation.UserProfileId);
                }

                try
                {
                    if (sentResult.FirstOrDefault()?.Channel == "whatsapp"
                        && (bool) sentResult.FirstOrDefault()?.whatsappSender?.whatsAppId.Contains("+1"))
                    {
                        await Task.Delay(1000);
                    }
                    else if (sentResult.FirstOrDefault()?.Channel == ChannelTypes.Facebook)
                    {
                        await Task.Delay(300);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Exception when executing delay: {ExceptionMessage}",
                        nameof(BroadcastWithConversationIdAsync),
                        ex.Message);
                }
            }
        }

        public async Task<List<CompanyMessageTemplate>> DuplicatedMessageTemplates(
            Staff companyUser,
            List<string> broadcastTemplateIds)
        {
            var response = new List<CompanyMessageTemplate>();

            foreach (var broadcastTemplateId in broadcastTemplateIds)
            {
                var messageTemplate = await _appDbContext.CompanyMessageTemplates
                    .AsNoTracking()
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.CampaignChannelMessages)
                    .ThenInclude(x => x.UploadedFiles)
                    .Include(x => x.SavedBy)
                    .Include(x => x.LastSentBy)
                    .Include(x => x.SavedBy.Identity)
                    .Include(x => x.LastSentBy.Identity)
                    .Include(x => x.CampaignAutomationActions)
                    .ThenInclude(x => x.UploadedFiles)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.Id == broadcastTemplateId);

                var duplicatedMessageTemplate = new CompanyMessageTemplate
                {
                    TargetedChannel = messageTemplate.TargetedChannel,
                    TargetedChannels = messageTemplate.TargetedChannels,
                    TemplateName = $"{messageTemplate.TemplateName} - Copy".Trim(),
                    Conditions = messageTemplate.Conditions,
                    CompanyId = messageTemplate.CompanyId,
                    Status = BroadcastStatus.Draft,
                    SavedById = companyUser.Id,
                    TargetedChannelWithIds = messageTemplate.TargetedChannelWithIds,
                    TemplateContent = messageTemplate.TemplateContent,
                    TemplateParams = messageTemplate.TemplateParams,
                    BroadcastAsNote = messageTemplate.BroadcastAsNote,
                };

                foreach (var uploadedFile in messageTemplate.UploadedFiles
                             .Where(x => !x.CampaignChannelMessageId.HasValue))
                {
                    var fileName = BlobUploadPathNameBuilder.GetBroadcastCampaignUploadedFilePath(
                        duplicatedMessageTemplate.Id,
                        Path.GetFileName(uploadedFile.Filename));

                    var fileStream = await _azureBlobStorageService.DownloadFromAzureBlob(
                        uploadedFile.Filename,
                        uploadedFile.BlobContainer);

                    var uploadFileResult = await _uploadService.UploadFileBySteam(
                        uploadedFile.BlobContainer,
                        fileName,
                        fileStream,
                        uploadedFile.MIMEType);

                    var newUploadedFile = new CampaignUploadedFile
                    {
                        BlobContainer = uploadedFile.BlobContainer,
                        Filename = fileName,
                        Url = uploadFileResult.Url,
                        MIMEType = uploadedFile.MIMEType
                    };

                    duplicatedMessageTemplate.UploadedFiles.Add(newUploadedFile);
                }

                foreach (var channelMessage in messageTemplate.CampaignChannelMessages)
                {
                    var newChannelMessage = new CampaignChannelMessage
                    {
                        TargetedChannels = channelMessage.TargetedChannels,
                        TargetedChannel = channelMessage.TargetedChannel,
                        TemplateContent = channelMessage.TemplateContent,
                        TemplateParams = channelMessage.TemplateParams,
                        TemplateName = channelMessage.TemplateName,
                        OfficialTemplateParams = channelMessage.OfficialTemplateParams,
                        WhatsApp360DialogExtendedCampaignMessage =
                            channelMessage.WhatsApp360DialogExtendedCampaignMessage,
                        ExtendedMessageType = channelMessage.ExtendedMessageType,
                        ExtendedMessagePayloadDetail = channelMessage.ExtendedMessagePayloadDetail,
                        MessageTag = channelMessage.MessageTag,
                    };

                    await CloneCampaignChannelMessageAttachmentsAsync(
                        companyUser,
                        newChannelMessage);

                    foreach (var uploadedFile in channelMessage.UploadedFiles)
                    {
                        var fileName = BlobUploadPathNameBuilder.GetBroadcastCampaignUploadedFilePath(
                            duplicatedMessageTemplate.Id,
                            Path.GetFileName(uploadedFile.Filename));

                        var fileStream = await _azureBlobStorageService.DownloadFromAzureBlob(
                            uploadedFile.Filename,
                            uploadedFile.BlobContainer);

                        var uploadFileResult = await _uploadService.UploadFileBySteam(
                            uploadedFile.BlobContainer,
                            fileName,
                            fileStream,
                            uploadedFile.MIMEType);

                        var newUploadedFile = new CampaignUploadedFile
                        {
                            BlobContainer = uploadedFile.BlobContainer,
                            Filename = fileName,
                            Url = uploadFileResult.Url,
                            MIMEType = uploadedFile.MIMEType,
                        };

                        newChannelMessage.UploadedFiles.Add(newUploadedFile);
                    }

                    duplicatedMessageTemplate.CampaignChannelMessages.Add(newChannelMessage);
                }

                foreach (var automation in messageTemplate.CampaignAutomationActions)
                {
                    var automationAction = new CampaignAutomationAction()
                    {
                        CompanyId = messageTemplate.CompanyId,
                        AssignmentType = automation.AssignmentType,
                        AutomatedTriggerType = automation.AutomatedTriggerType,
                        MessageContent = automation.MessageContent,
                        MessageParams = automation.MessageParams,
                        ActionAddConversationHashtags = automation.ActionAddConversationHashtags,
                        ActionAddConversationRemarks = automation.ActionAddConversationRemarks,
                        ActionUpdateCustomFields = automation.ActionUpdateCustomFields,
                        ActionAddedToGroupIds = automation.ActionAddedToGroupIds,
                        ActionRemoveFromGroupIds = automation.ActionRemoveFromGroupIds,
                        ActionWait = automation.ActionWait,
                        ActionWaitDays = automation.ActionWaitDays,
                        Order = automation.Order,
                        TeamAssignmentType = automation.TeamAssignmentType,
                        AssignedStaffId = automation.AssignedStaffId,
                        AssignedTeamId = automation.AssignedTeamId,
                        ChangeConversationStatus = automation.ChangeConversationStatus,
                        TargetedChannelWithIds = automation.TargetedChannelWithIds,
                        AddAdditionalAssigneeIds = automation.AddAdditionalAssigneeIds,
                        WebhookURL = automation.WebhookURL
                    };

                    duplicatedMessageTemplate.CampaignAutomationActions ??= new List<CampaignAutomationAction>();
                    duplicatedMessageTemplate.CampaignAutomationActions.Add(automationAction);

                    if (automation.UploadedFiles == null)
                    {
                        continue;
                    }

                    foreach (var uploadedFile in automation.UploadedFiles)
                    {
                        var newUploadedFile = new CampaignAutomationUploadedFile()
                        {
                            BlobContainer = uploadedFile.BlobContainer,
                            Filename = uploadedFile.Filename,
                            Url = uploadedFile.Url,
                            MIMEType = uploadedFile.MIMEType,
                        };

                        automationAction.UploadedFiles.Add(newUploadedFile);
                    }
                }

                if (duplicatedMessageTemplate.TargetedChannelWithIds is { Count: > 0 }
                    && duplicatedMessageTemplate.TargetedChannel is null)
                {
                    await _broadcastChannelService.MapChannelIdentityIdAsync(duplicatedMessageTemplate);
                }

                response.Add(duplicatedMessageTemplate);

                _appDbContext.CompanyMessageTemplates.Add(duplicatedMessageTemplate);
            }

            await _appDbContext.SaveChangesAsync();

            return response;
        }

        private async Task CloneCampaignChannelMessageAttachmentsAsync(
            Staff companyUser,
            CampaignChannelMessage channelMessage)
        {
            if (channelMessage.WhatsApp360DialogExtendedCampaignMessage is null
                && channelMessage.ExtendedMessagePayloadDetail is null)
            {
                return;
            }

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.DefaultAllowRedirect);
            var containerName = await _appDbContext.ConfigStorageConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .Select(x => x.ContainerName)
                .FirstOrDefaultAsync() ?? companyUser.CompanyId;

            if (channelMessage.WhatsApp360DialogExtendedCampaignMessage is { Whatsapp360DialogTemplateMessage.Components.Count: > 0 })
            {
                var parameters = channelMessage.WhatsApp360DialogExtendedCampaignMessage
                    .Whatsapp360DialogTemplateMessage
                    .Components
                    .Where(c => c.Parameters is not null)
                    .SelectMany(c => c.Parameters)
                    .Where(p => p.Type is ParameterType.document or ParameterType.image or ParameterType.video)
                    .ToList();

                long? whatsApp360DialogConfigId = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .Where(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.ChannelIdentityId == channelMessage.TargetedChannel.ChannelIdentityId)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();

                if (whatsApp360DialogConfigId.HasValue)
                {
                    foreach (var parameter in parameters)
                    {
                        var mediaObject = parameter.Type switch
                        {
                            ParameterType.document => parameter.Document,
                            ParameterType.image => parameter.Image,
                            ParameterType.video => parameter.Video
                        };

                        var filePath = BlobUploadPathNameBuilder.GetWhatsApp360DialogMediaFilePath(
                            whatsApp360DialogConfigId.Value.ToString(),
                            mediaObject.Filename);

                        var response = await httpClient.GetAsync(mediaObject.Link);
                        var responseStream = await response.Content.ReadAsStreamAsync();

                        var uploadFileResult = await _uploadService.UploadFileBySteam(
                            containerName,
                            filePath,
                            responseStream,
                            response.Content.Headers.ContentType?.ToString());

                        var file = new WhatsApp360DialogMediaFile
                        {
                            CompanyId = companyUser.CompanyId,
                            BlobContainer = containerName,
                            BlobFilePath = filePath,
                            Filename = mediaObject.Filename,
                            MIMEType = response.Content.Headers.ContentType?.ToString(),
                            FileSize = uploadFileResult.FileSizeInByte,
                            DisplayName = mediaObject.Filename,
                            WhatsAppMediaType = parameter.Type.ToString(),
                            WhatsApp360DialogConfigId = whatsApp360DialogConfigId.Value
                        };

                        var domain = _configuration["Values:DomainName"];

                        file.Url = BlobPublicUrlBuilder.GetWhatsApp360DialogMediaFileUrl(
                            domain,
                            whatsApp360DialogConfigId.ToString(),
                            file.Id,
                            file.Filename,
                            file.WhatsAppMediaType);

                        mediaObject.Link = file.Url;
                        _appDbContext.WhatsApp360DialogMediaFiles.Add(file);
                    }
                }
            }

            if (channelMessage.ExtendedMessagePayloadDetail is { WhatsappCloudApiTemplateMessageObject.Components.Count: > 0 })
            {
                var parameters = channelMessage.ExtendedMessagePayloadDetail
                    .WhatsappCloudApiTemplateMessageObject
                    .Components
                    .Where(c => c.Parameters is not null)
                    .SelectMany(c => c.Parameters)
                    .Where(p => p.Type.ToLower() is "document" or "image" or "video")
                    .ToList();

                foreach (var parameter in parameters)
                {
                    var parameterObject = parameter.Type.ToLower() switch
                    {
                        "document" => parameter.Document,
                        "image" => parameter.Image,
                        "video" => parameter.Video
                    };

                    var fileName = Path.GetFileName(new Uri(parameterObject.Link).AbsolutePath);
                    var filePath = BlobUploadPathNameBuilder.GetExtendedMessagePayloadFilePath(fileName);

                    var response = await httpClient.GetAsync(parameterObject.Link);
                    var responseStream = await response.Content.ReadAsStreamAsync();

                    var uploadFile = await _uploadService.UploadFileBySteam(
                        containerName,
                        filePath,
                        responseStream,
                        response.Content.Headers.ContentType?.ToString());

                    var file = new ExtendedMessagePayloadFile()
                    {
                        CompanyId = companyUser.CompanyId,
                        Channel = ChannelTypes.WhatsappCloudApi,
                        ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiTemplateMessage,
                        BlobContainer = containerName,
                        BlobFilePath = filePath,
                        Filename = fileName,
                        MIMEType = response.Content.Headers.ContentType?.ToString(),
                        FileSize = uploadFile.FileSizeInByte,
                        DisplayName = fileName,
                        MediaType = parameter.Type
                    };

                    var domain = _configuration["Values:DomainName"];

                    file.Url = BlobPublicUrlBuilder.GetExtendedMessagePayloadFileUrl(
                        domain,
                        file.Id,
                        file.Filename,
                        file.MediaType,
                        isRedirectMode: true);

                    parameterObject.Link = file.Url;
                    _appDbContext.ExtendedMessagePayloadFiles.Add(file);
                }
            }
        }

        private static async Task<HttpResponseMessage> PostJsonAsync<T>(
            HttpClient client,
            string requestUri,
            T value)
        {
            var data = JsonConvert.SerializeObject(value);

            var content = new StringContent(
                data,
                System.Text.Encoding.UTF8,
                "application/json");

            return await client.PostAsync(requestUri, content).ConfigureAwait(false);
        }

        private async Task<(string UserProfileId, ActiveStatus? ActiveStatus)> GetUserProfileIdStatusByIdAsync(
            string companyId,
            string userProfileId)
        {
            var userProfile = await _appDbContext.UserProfiles
                .Where(x => x.CompanyId == companyId && x.Id == userProfileId)
                .Select(x
                    => new
                    {
                        x.Id,
                        x.ActiveStatus
                    })
                .FirstOrDefaultAsync();

            return (userProfile?.Id, userProfile?.ActiveStatus);
        }

        public async Task<long> GetBroadcastContactsCountAsync(string companyId, DateTime queryDate)
        {
            var scheduledBroadcasts = await _appDbContext.CompanyMessageTemplates
                .Where(x => x.CompanyId == companyId)
                .Where(x => x.Status == BroadcastStatus.Scheduled)
                .Where(x => x.ScheduledAt <= queryDate.AddHours(24))
                .Where(x => x.ScheduledAt >= queryDate.AddHours(-24))
                .ToListAsync();

            var totalCount = 0;
            foreach (var broadcast in scheduledBroadcasts)
            {
                var userProfileResult = await _userProfileService.GetUserProfilesByFields(
                    companyId,
                    broadcast.Conditions,
                    assigneeId: broadcast.SavedById ?? null,
                    enableNoTracking: true);
                totalCount += userProfileResult.TotalResult;
            }

            return totalCount;
        }
    }
}