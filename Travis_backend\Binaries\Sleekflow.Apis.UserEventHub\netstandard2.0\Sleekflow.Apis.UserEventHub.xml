<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sleekflow.Apis.UserEventHub</name>
    </assembly>
    <members>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiSync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiSync.AssociationsAreGroupsConnectedPost(System.String,Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areGroupsConnectedInput"> (optional)</param>
            <returns>AreGroupsConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiSync.AssociationsAreGroupsConnectedPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areGroupsConnectedInput"> (optional)</param>
            <returns>ApiResponse of AreGroupsConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiSync.AssociationsAreUsersConnectedPost(System.String,Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areUsersConnectedInput"> (optional)</param>
            <returns>AreUsersConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiSync.AssociationsAreUsersConnectedPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areUsersConnectedInput"> (optional)</param>
            <returns>ApiResponse of AreUsersConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiSync.AssociationsIsGroupConnectedPost(System.String,Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isGroupConnectedInput"> (optional)</param>
            <returns>IsGroupConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiSync.AssociationsIsGroupConnectedPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isGroupConnectedInput"> (optional)</param>
            <returns>ApiResponse of IsGroupConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiSync.AssociationsIsUserConnectedPost(System.String,Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isUserConnectedInput"> (optional)</param>
            <returns>IsUserConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiSync.AssociationsIsUserConnectedPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isUserConnectedInput"> (optional)</param>
            <returns>ApiResponse of IsUserConnectedOutputOutput</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiAsync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiAsync.AssociationsAreGroupsConnectedPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areGroupsConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of AreGroupsConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiAsync.AssociationsAreGroupsConnectedPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areGroupsConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (AreGroupsConnectedOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiAsync.AssociationsAreUsersConnectedPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areUsersConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of AreUsersConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiAsync.AssociationsAreUsersConnectedPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areUsersConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (AreUsersConnectedOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiAsync.AssociationsIsGroupConnectedPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isGroupConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of IsGroupConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiAsync.AssociationsIsGroupConnectedPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isGroupConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (IsGroupConnectedOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiAsync.AssociationsIsUserConnectedPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isUserConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of IsUserConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAssociationsApiAsync.AssociationsIsUserConnectedPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isUserConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (IsUserConnectedOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.IAssociationsApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.AssociationsApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AssociationsApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AssociationsApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="basePath">The target service's base path in URL format.</param>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.#ctor(Sleekflow.Apis.UserEventHub.Client.Configuration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AssociationsApi"/> class using Configuration object.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="configuration">An instance of Configuration.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.#ctor(System.Net.Http.HttpClient,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AssociationsApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.#ctor(System.Net.Http.HttpClient,System.String,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AssociationsApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="basePath">The target service's base path in URL format.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.#ctor(System.Net.Http.HttpClient,Sleekflow.Apis.UserEventHub.Client.Configuration,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AssociationsApi"/> class using Configuration object.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="configuration">An instance of Configuration.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.#ctor(Sleekflow.Apis.UserEventHub.Client.ISynchronousClient,Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AssociationsApi"/> class
            using a Configuration object and client instance.
            </summary>
            <param name="client">The client interface for synchronous API access.</param>
            <param name="asyncClient">The client interface for asynchronous API access.</param>
            <param name="configuration">The configuration object.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.Dispose">
            <summary>
            Disposes resources if they were created by us
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.ApiClient">
            <summary>
            Holds the ApiClient if created
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AsynchronousClient">
            <summary>
            The client for accessing this underlying API asynchronously.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.Client">
            <summary>
            The client for accessing this underlying API synchronously.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.GetBasePath">
            <summary>
            Gets the base path of the API client.
            </summary>
            <value>The base path</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.Configuration">
            <summary>
            Gets or sets the configuration object
            </summary>
            <value>An instance of the Configuration</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.ExceptionFactory">
            <summary>
            Provides a factory method hook for the creation of exceptions.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsAreGroupsConnectedPost(System.String,Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areGroupsConnectedInput"> (optional)</param>
            <returns>AreGroupsConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsAreGroupsConnectedPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areGroupsConnectedInput"> (optional)</param>
            <returns>ApiResponse of AreGroupsConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsAreGroupsConnectedPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areGroupsConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of AreGroupsConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsAreGroupsConnectedPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areGroupsConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (AreGroupsConnectedOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsAreUsersConnectedPost(System.String,Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areUsersConnectedInput"> (optional)</param>
            <returns>AreUsersConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsAreUsersConnectedPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areUsersConnectedInput"> (optional)</param>
            <returns>ApiResponse of AreUsersConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsAreUsersConnectedPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areUsersConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of AreUsersConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsAreUsersConnectedPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="areUsersConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (AreUsersConnectedOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsIsGroupConnectedPost(System.String,Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isGroupConnectedInput"> (optional)</param>
            <returns>IsGroupConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsIsGroupConnectedPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isGroupConnectedInput"> (optional)</param>
            <returns>ApiResponse of IsGroupConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsIsGroupConnectedPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isGroupConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of IsGroupConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsIsGroupConnectedPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isGroupConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (IsGroupConnectedOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsIsUserConnectedPost(System.String,Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isUserConnectedInput"> (optional)</param>
            <returns>IsUserConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsIsUserConnectedPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isUserConnectedInput"> (optional)</param>
            <returns>ApiResponse of IsUserConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsIsUserConnectedPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isUserConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of IsUserConnectedOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AssociationsApi.AssociationsIsUserConnectedPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="isUserConnectedInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (IsUserConnectedOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiSync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiSync.AuthorizedNotificationsDisableMobilePushNotificationPost(System.String,System.Object)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <returns>DisableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiSync.AuthorizedNotificationsDisableMobilePushNotificationPostWithHttpInfo(System.String,System.Object)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <returns>ApiResponse of DisableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiSync.AuthorizedNotificationsEnableMobilePushNotificationPost(System.String,Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="enableMobilePushNotificationInput"> (optional)</param>
            <returns>EnableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiSync.AuthorizedNotificationsEnableMobilePushNotificationPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="enableMobilePushNotificationInput"> (optional)</param>
            <returns>ApiResponse of EnableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiSync.AuthorizedNotificationsGetNotificationSettingsPost(System.String,System.Object)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <returns>GetNotificationSettingsOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiSync.AuthorizedNotificationsGetNotificationSettingsPostWithHttpInfo(System.String,System.Object)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <returns>ApiResponse of GetNotificationSettingsOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiSync.AuthorizedNotificationsUpdateNotificationSettingsByPlatformPost(System.String,Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="updateNotificationSettingsByPlatformInput"> (optional)</param>
            <returns>UpdateNotificationSettingsByPlatformOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiSync.AuthorizedNotificationsUpdateNotificationSettingsByPlatformPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="updateNotificationSettingsByPlatformInput"> (optional)</param>
            <returns>ApiResponse of UpdateNotificationSettingsByPlatformOutputOutput</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiAsync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiAsync.AuthorizedNotificationsDisableMobilePushNotificationPostAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of DisableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiAsync.AuthorizedNotificationsDisableMobilePushNotificationPostWithHttpInfoAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (DisableMobilePushNotificationOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiAsync.AuthorizedNotificationsEnableMobilePushNotificationPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="enableMobilePushNotificationInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of EnableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiAsync.AuthorizedNotificationsEnableMobilePushNotificationPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="enableMobilePushNotificationInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (EnableMobilePushNotificationOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiAsync.AuthorizedNotificationsGetNotificationSettingsPostAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of GetNotificationSettingsOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiAsync.AuthorizedNotificationsGetNotificationSettingsPostWithHttpInfoAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (GetNotificationSettingsOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiAsync.AuthorizedNotificationsUpdateNotificationSettingsByPlatformPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="updateNotificationSettingsByPlatformInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of UpdateNotificationSettingsByPlatformOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApiAsync.AuthorizedNotificationsUpdateNotificationSettingsByPlatformPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="updateNotificationSettingsByPlatformInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (UpdateNotificationSettingsByPlatformOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.IAuthorizedNotificationsApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="basePath">The target service's base path in URL format.</param>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.#ctor(Sleekflow.Apis.UserEventHub.Client.Configuration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi"/> class using Configuration object.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="configuration">An instance of Configuration.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.#ctor(System.Net.Http.HttpClient,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.#ctor(System.Net.Http.HttpClient,System.String,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="basePath">The target service's base path in URL format.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.#ctor(System.Net.Http.HttpClient,Sleekflow.Apis.UserEventHub.Client.Configuration,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi"/> class using Configuration object.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="configuration">An instance of Configuration.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.#ctor(Sleekflow.Apis.UserEventHub.Client.ISynchronousClient,Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi"/> class
            using a Configuration object and client instance.
            </summary>
            <param name="client">The client interface for synchronous API access.</param>
            <param name="asyncClient">The client interface for asynchronous API access.</param>
            <param name="configuration">The configuration object.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.Dispose">
            <summary>
            Disposes resources if they were created by us
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.ApiClient">
            <summary>
            Holds the ApiClient if created
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AsynchronousClient">
            <summary>
            The client for accessing this underlying API asynchronously.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.Client">
            <summary>
            The client for accessing this underlying API synchronously.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.GetBasePath">
            <summary>
            Gets the base path of the API client.
            </summary>
            <value>The base path</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.Configuration">
            <summary>
            Gets or sets the configuration object
            </summary>
            <value>An instance of the Configuration</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.ExceptionFactory">
            <summary>
            Provides a factory method hook for the creation of exceptions.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsDisableMobilePushNotificationPost(System.String,System.Object)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <returns>DisableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsDisableMobilePushNotificationPostWithHttpInfo(System.String,System.Object)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <returns>ApiResponse of DisableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsDisableMobilePushNotificationPostAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of DisableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsDisableMobilePushNotificationPostWithHttpInfoAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (DisableMobilePushNotificationOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsEnableMobilePushNotificationPost(System.String,Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="enableMobilePushNotificationInput"> (optional)</param>
            <returns>EnableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsEnableMobilePushNotificationPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="enableMobilePushNotificationInput"> (optional)</param>
            <returns>ApiResponse of EnableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsEnableMobilePushNotificationPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="enableMobilePushNotificationInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of EnableMobilePushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsEnableMobilePushNotificationPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="enableMobilePushNotificationInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (EnableMobilePushNotificationOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsGetNotificationSettingsPost(System.String,System.Object)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <returns>GetNotificationSettingsOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsGetNotificationSettingsPostWithHttpInfo(System.String,System.Object)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <returns>ApiResponse of GetNotificationSettingsOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsGetNotificationSettingsPostAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of GetNotificationSettingsOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsGetNotificationSettingsPostWithHttpInfoAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="body"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (GetNotificationSettingsOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsUpdateNotificationSettingsByPlatformPost(System.String,Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="updateNotificationSettingsByPlatformInput"> (optional)</param>
            <returns>UpdateNotificationSettingsByPlatformOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsUpdateNotificationSettingsByPlatformPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="updateNotificationSettingsByPlatformInput"> (optional)</param>
            <returns>ApiResponse of UpdateNotificationSettingsByPlatformOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsUpdateNotificationSettingsByPlatformPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="updateNotificationSettingsByPlatformInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of UpdateNotificationSettingsByPlatformOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.AuthorizedNotificationsApi.AuthorizedNotificationsUpdateNotificationSettingsByPlatformPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="updateNotificationSettingsByPlatformInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (UpdateNotificationSettingsByPlatformOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.IMessagesApiSync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiSync.MessagesRedeliverSessionUnackedMessagesPost(System.String,Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="redeliverSessionUnackedMessagesInput"> (optional)</param>
            <returns>RedeliverSessionUnackedMessagesOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiSync.MessagesRedeliverSessionUnackedMessagesPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="redeliverSessionUnackedMessagesInput"> (optional)</param>
            <returns>ApiResponse of RedeliverSessionUnackedMessagesOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiSync.MessagesSendMessageToGroupPost(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToGroupInput"> (optional)</param>
            <returns>SendMessageToGroupOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiSync.MessagesSendMessageToGroupPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToGroupInput"> (optional)</param>
            <returns>ApiResponse of SendMessageToGroupOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiSync.MessagesSendMessageToUserPost(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUserInput"> (optional)</param>
            <returns>SendMessageToUserOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiSync.MessagesSendMessageToUserPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUserInput"> (optional)</param>
            <returns>ApiResponse of SendMessageToUserOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiSync.MessagesSendMessageToUsersPost(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUsersInput"> (optional)</param>
            <returns>SendMessageToUsersOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiSync.MessagesSendMessageToUsersPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUsersInput"> (optional)</param>
            <returns>ApiResponse of SendMessageToUsersOutputOutput</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.IMessagesApiAsync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiAsync.MessagesRedeliverSessionUnackedMessagesPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="redeliverSessionUnackedMessagesInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of RedeliverSessionUnackedMessagesOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiAsync.MessagesRedeliverSessionUnackedMessagesPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="redeliverSessionUnackedMessagesInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (RedeliverSessionUnackedMessagesOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiAsync.MessagesSendMessageToGroupPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToGroupInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of SendMessageToGroupOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiAsync.MessagesSendMessageToGroupPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToGroupInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (SendMessageToGroupOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiAsync.MessagesSendMessageToUserPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUserInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of SendMessageToUserOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiAsync.MessagesSendMessageToUserPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUserInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (SendMessageToUserOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiAsync.MessagesSendMessageToUsersPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUsersInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of SendMessageToUsersOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.IMessagesApiAsync.MessagesSendMessageToUsersPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUsersInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (SendMessageToUsersOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.IMessagesApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.MessagesApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.MessagesApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.MessagesApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="basePath">The target service's base path in URL format.</param>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.#ctor(Sleekflow.Apis.UserEventHub.Client.Configuration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.MessagesApi"/> class using Configuration object.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="configuration">An instance of Configuration.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.#ctor(System.Net.Http.HttpClient,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.MessagesApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.#ctor(System.Net.Http.HttpClient,System.String,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.MessagesApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="basePath">The target service's base path in URL format.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.#ctor(System.Net.Http.HttpClient,Sleekflow.Apis.UserEventHub.Client.Configuration,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.MessagesApi"/> class using Configuration object.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="configuration">An instance of Configuration.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.#ctor(Sleekflow.Apis.UserEventHub.Client.ISynchronousClient,Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.MessagesApi"/> class
            using a Configuration object and client instance.
            </summary>
            <param name="client">The client interface for synchronous API access.</param>
            <param name="asyncClient">The client interface for asynchronous API access.</param>
            <param name="configuration">The configuration object.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.Dispose">
            <summary>
            Disposes resources if they were created by us
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.MessagesApi.ApiClient">
            <summary>
            Holds the ApiClient if created
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.MessagesApi.AsynchronousClient">
            <summary>
            The client for accessing this underlying API asynchronously.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.MessagesApi.Client">
            <summary>
            The client for accessing this underlying API synchronously.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.GetBasePath">
            <summary>
            Gets the base path of the API client.
            </summary>
            <value>The base path</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.MessagesApi.Configuration">
            <summary>
            Gets or sets the configuration object
            </summary>
            <value>An instance of the Configuration</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.MessagesApi.ExceptionFactory">
            <summary>
            Provides a factory method hook for the creation of exceptions.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesRedeliverSessionUnackedMessagesPost(System.String,Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="redeliverSessionUnackedMessagesInput"> (optional)</param>
            <returns>RedeliverSessionUnackedMessagesOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesRedeliverSessionUnackedMessagesPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="redeliverSessionUnackedMessagesInput"> (optional)</param>
            <returns>ApiResponse of RedeliverSessionUnackedMessagesOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesRedeliverSessionUnackedMessagesPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="redeliverSessionUnackedMessagesInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of RedeliverSessionUnackedMessagesOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesRedeliverSessionUnackedMessagesPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="redeliverSessionUnackedMessagesInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (RedeliverSessionUnackedMessagesOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToGroupPost(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToGroupInput"> (optional)</param>
            <returns>SendMessageToGroupOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToGroupPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToGroupInput"> (optional)</param>
            <returns>ApiResponse of SendMessageToGroupOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToGroupPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToGroupInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of SendMessageToGroupOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToGroupPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToGroupInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (SendMessageToGroupOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToUserPost(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUserInput"> (optional)</param>
            <returns>SendMessageToUserOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToUserPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUserInput"> (optional)</param>
            <returns>ApiResponse of SendMessageToUserOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToUserPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUserInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of SendMessageToUserOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToUserPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUserInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (SendMessageToUserOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToUsersPost(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUsersInput"> (optional)</param>
            <returns>SendMessageToUsersOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToUsersPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUsersInput"> (optional)</param>
            <returns>ApiResponse of SendMessageToUsersOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToUsersPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUsersInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of SendMessageToUsersOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.MessagesApi.MessagesSendMessageToUsersPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendMessageToUsersInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (SendMessageToUsersOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.INotificationsApiSync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.INotificationsApiSync.NotificationsSendPushNotificationPost(System.String,Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendPushNotificationInput"> (optional)</param>
            <returns>SendPushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.INotificationsApiSync.NotificationsSendPushNotificationPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendPushNotificationInput"> (optional)</param>
            <returns>ApiResponse of SendPushNotificationOutputOutput</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.INotificationsApiAsync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.INotificationsApiAsync.NotificationsSendPushNotificationPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendPushNotificationInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of SendPushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.INotificationsApiAsync.NotificationsSendPushNotificationPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendPushNotificationInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (SendPushNotificationOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.INotificationsApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.NotificationsApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.NotificationsApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.NotificationsApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="basePath">The target service's base path in URL format.</param>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.#ctor(Sleekflow.Apis.UserEventHub.Client.Configuration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.NotificationsApi"/> class using Configuration object.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="configuration">An instance of Configuration.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.#ctor(System.Net.Http.HttpClient,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.NotificationsApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.#ctor(System.Net.Http.HttpClient,System.String,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.NotificationsApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="basePath">The target service's base path in URL format.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.#ctor(System.Net.Http.HttpClient,Sleekflow.Apis.UserEventHub.Client.Configuration,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.NotificationsApi"/> class using Configuration object.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="configuration">An instance of Configuration.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.#ctor(Sleekflow.Apis.UserEventHub.Client.ISynchronousClient,Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.NotificationsApi"/> class
            using a Configuration object and client instance.
            </summary>
            <param name="client">The client interface for synchronous API access.</param>
            <param name="asyncClient">The client interface for asynchronous API access.</param>
            <param name="configuration">The configuration object.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.Dispose">
            <summary>
            Disposes resources if they were created by us
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.ApiClient">
            <summary>
            Holds the ApiClient if created
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.AsynchronousClient">
            <summary>
            The client for accessing this underlying API asynchronously.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.Client">
            <summary>
            The client for accessing this underlying API synchronously.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.GetBasePath">
            <summary>
            Gets the base path of the API client.
            </summary>
            <value>The base path</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.Configuration">
            <summary>
            Gets or sets the configuration object
            </summary>
            <value>An instance of the Configuration</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.ExceptionFactory">
            <summary>
            Provides a factory method hook for the creation of exceptions.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.NotificationsSendPushNotificationPost(System.String,Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendPushNotificationInput"> (optional)</param>
            <returns>SendPushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.NotificationsSendPushNotificationPostWithHttpInfo(System.String,Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendPushNotificationInput"> (optional)</param>
            <returns>ApiResponse of SendPushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.NotificationsSendPushNotificationPostAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendPushNotificationInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of SendPushNotificationOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.NotificationsApi.NotificationsSendPushNotificationPostWithHttpInfoAsync(System.String,Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="sendPushNotificationInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (SendPushNotificationOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.ISignalRNegotiateApiSync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.ISignalRNegotiateApiSync.ReliableMessageNegotiatePost(System.String,System.String)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="data"> (optional)</param>
            <param name="X_API_VERSION"> (optional)</param>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.ISignalRNegotiateApiSync.ReliableMessageNegotiatePostWithHttpInfo(System.String,System.String)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="data"> (optional)</param>
            <param name="X_API_VERSION"> (optional)</param>
            <returns>ApiResponse of Object(void)</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.ISignalRNegotiateApiAsync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.ISignalRNegotiateApiAsync.ReliableMessageNegotiatePostAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="data"> (optional)</param>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of void</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.ISignalRNegotiateApiAsync.ReliableMessageNegotiatePostWithHttpInfoAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="data"> (optional)</param>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.ISignalRNegotiateApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="basePath">The target service's base path in URL format.</param>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.#ctor(Sleekflow.Apis.UserEventHub.Client.Configuration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi"/> class using Configuration object.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="configuration">An instance of Configuration.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.#ctor(System.Net.Http.HttpClient,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.#ctor(System.Net.Http.HttpClient,System.String,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="basePath">The target service's base path in URL format.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.#ctor(System.Net.Http.HttpClient,Sleekflow.Apis.UserEventHub.Client.Configuration,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi"/> class using Configuration object.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="configuration">An instance of Configuration.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.#ctor(Sleekflow.Apis.UserEventHub.Client.ISynchronousClient,Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi"/> class
            using a Configuration object and client instance.
            </summary>
            <param name="client">The client interface for synchronous API access.</param>
            <param name="asyncClient">The client interface for asynchronous API access.</param>
            <param name="configuration">The configuration object.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.Dispose">
            <summary>
            Disposes resources if they were created by us
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.ApiClient">
            <summary>
            Holds the ApiClient if created
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.AsynchronousClient">
            <summary>
            The client for accessing this underlying API asynchronously.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.Client">
            <summary>
            The client for accessing this underlying API synchronously.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.GetBasePath">
            <summary>
            Gets the base path of the API client.
            </summary>
            <value>The base path</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.Configuration">
            <summary>
            Gets or sets the configuration object
            </summary>
            <value>An instance of the Configuration</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.ExceptionFactory">
            <summary>
            Provides a factory method hook for the creation of exceptions.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.ReliableMessageNegotiatePost(System.String,System.String)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="data"> (optional)</param>
            <param name="X_API_VERSION"> (optional)</param>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.ReliableMessageNegotiatePostWithHttpInfo(System.String,System.String)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="data"> (optional)</param>
            <param name="X_API_VERSION"> (optional)</param>
            <returns>ApiResponse of Object(void)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.ReliableMessageNegotiatePostAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="data"> (optional)</param>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of void</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRNegotiateApi.ReliableMessageNegotiatePostWithHttpInfoAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="data"> (optional)</param>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.ISignalRWebhookApiSync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.ISignalRWebhookApiSync.SignalRWebhookPost(System.String)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.ISignalRWebhookApiSync.SignalRWebhookPostWithHttpInfo(System.String)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <returns>ApiResponse of Object(void)</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.ISignalRWebhookApiAsync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.ISignalRWebhookApiAsync.SignalRWebhookPostAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of void</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.ISignalRWebhookApiAsync.SignalRWebhookPostWithHttpInfoAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.ISignalRWebhookApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="basePath">The target service's base path in URL format.</param>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.#ctor(Sleekflow.Apis.UserEventHub.Client.Configuration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi"/> class using Configuration object.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="configuration">An instance of Configuration.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.#ctor(System.Net.Http.HttpClient,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.#ctor(System.Net.Http.HttpClient,System.String,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="basePath">The target service's base path in URL format.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.#ctor(System.Net.Http.HttpClient,Sleekflow.Apis.UserEventHub.Client.Configuration,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi"/> class using Configuration object.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="configuration">An instance of Configuration.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.#ctor(Sleekflow.Apis.UserEventHub.Client.ISynchronousClient,Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi"/> class
            using a Configuration object and client instance.
            </summary>
            <param name="client">The client interface for synchronous API access.</param>
            <param name="asyncClient">The client interface for asynchronous API access.</param>
            <param name="configuration">The configuration object.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.Dispose">
            <summary>
            Disposes resources if they were created by us
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.ApiClient">
            <summary>
            Holds the ApiClient if created
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.AsynchronousClient">
            <summary>
            The client for accessing this underlying API asynchronously.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.Client">
            <summary>
            The client for accessing this underlying API synchronously.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.GetBasePath">
            <summary>
            Gets the base path of the API client.
            </summary>
            <value>The base path</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.Configuration">
            <summary>
            Gets or sets the configuration object
            </summary>
            <value>An instance of the Configuration</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.ExceptionFactory">
            <summary>
            Provides a factory method hook for the creation of exceptions.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.SignalRWebhookPost(System.String)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.SignalRWebhookPostWithHttpInfo(System.String)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <returns>ApiResponse of Object(void)</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.SignalRWebhookPostAsync(System.String,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of void</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Api.SignalRWebhookApi.SignalRWebhookPostWithHttpInfoAsync(System.String,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.CustomJsonCodec">
            <summary>
            To Serialize/Deserialize JSON using our custom logic, but only when ContentType is JSON.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.CustomJsonCodec.Serialize(System.Object)">
            <summary>
            Serialize the object into a JSON string.
            </summary>
            <param name="obj">Object to be serialized.</param>
            <returns>A JSON string.</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.CustomJsonCodec.Deserialize(System.Net.Http.HttpResponseMessage,System.Type)">
            <summary>
            Deserialize the JSON string into a proper object.
            </summary>
            <param name="response">The HTTP response.</param>
            <param name="type">Object type.</param>
            <returns>Object representation of the JSON string.</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.ApiClient">
            <summary>
            Provides a default implementation of an Api client (both synchronous and asynchronous implementations),
            encapsulating general REST accessor use cases.
            </summary>
            <remarks>
            The Dispose method will manage the HttpClient lifecycle when not passed by constructor.
            </remarks>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiClient.SerializerSettings">
            <summary>
            Specifies the settings on a <see cref="T:Newtonsoft.Json.JsonSerializer" /> object.
            These settings can be adjusted to accommodate custom serialization rules.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiClient" />, defaulting to the global configurations' base url.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiClient" />.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="basePath">The target service's base path in URL format.</param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.#ctor(System.Net.Http.HttpClient,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiClient" />, defaulting to the global configurations' base url.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.#ctor(System.Net.Http.HttpClient,System.String,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiClient" />.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="basePath">The target service's base path in URL format.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.Dispose">
            <summary>
            Disposes resources if they were created by us
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.PrepareMultipartFormDataContent(Sleekflow.Apis.UserEventHub.Client.RequestOptions)">
            Prepares multipart/form-data content
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.NewRequest(System.Net.Http.HttpMethod,System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Provides all logic for constructing a new HttpRequestMessage.
            At this point, all information for querying the service is known. Here, it is simply
            mapped into the a HttpRequestMessage.
            </summary>
            <param name="method">The http verb.</param>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>[private] A new HttpRequestMessage instance.</returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.GetAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP GET request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.PostAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP POST request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.PutAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP PUT request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.DeleteAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP DELETE request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.HeadAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP HEAD request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.OptionsAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP OPTION request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.PatchAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP PATCH request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.Get``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP GET request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.Post``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP POST request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.Put``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP PUT request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.Delete``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP DELETE request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.Head``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP HEAD request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.Options``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP OPTION request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiClient.Patch``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP PATCH request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.ApiException">
            <summary>
            API Exception
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiException.ErrorCode">
            <summary>
            Gets or sets the error code (HTTP status code)
            </summary>
            <value>The error code (HTTP status code).</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiException.ErrorContent">
            <summary>
            Gets or sets the error content (body json object)
            </summary>
            <value>The error content (Http response body).</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiException.Headers">
            <summary>
            Gets or sets the HTTP headers
            </summary>
            <value>HTTP headers</value>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException"/> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiException.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException"/> class.
            </summary>
            <param name="errorCode">HTTP status code.</param>
            <param name="message">Error message.</param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiException.#ctor(System.Int32,System.String,System.Object,Sleekflow.Apis.UserEventHub.Client.Multimap{System.String,System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiException"/> class.
            </summary>
            <param name="errorCode">HTTP status code.</param>
            <param name="message">Error message.</param>
            <param name="errorContent">Error content.</param>
            <param name="headers">HTTP Headers.</param>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.IApiResponse">
            <summary>
            Provides a non-generic contract for the ApiResponse wrapper.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IApiResponse.ResponseType">
            <summary>
            The data type of <see cref="P:Sleekflow.Apis.UserEventHub.Client.IApiResponse.Content"/>
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IApiResponse.Content">
            <summary>
            The content of this response
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IApiResponse.StatusCode">
            <summary>
            Gets or sets the status code (HTTP status code)
            </summary>
            <value>The status code.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IApiResponse.Headers">
            <summary>
            Gets or sets the HTTP headers
            </summary>
            <value>HTTP headers</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IApiResponse.ErrorText">
            <summary>
            Gets or sets any error text defined by the calling client.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IApiResponse.Cookies">
            <summary>
            Gets or sets any cookies passed along on the response.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IApiResponse.RawContent">
            <summary>
            The raw content of this response
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1">
            <summary>
            API Response
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.StatusCode">
            <summary>
            Gets or sets the status code (HTTP status code)
            </summary>
            <value>The status code.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.Headers">
            <summary>
            Gets or sets the HTTP headers
            </summary>
            <value>HTTP headers</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.Data">
            <summary>
            Gets or sets the data (parsed HTTP body)
            </summary>
            <value>The data.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.ErrorText">
            <summary>
            Gets or sets any error text defined by the calling client.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.Cookies">
            <summary>
            Gets or sets any cookies passed along on the response.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.ResponseType">
            <summary>
            The content of this response
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.Content">
            <summary>
            The data type of <see cref="P:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.Content"/>
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.RawContent">
            <summary>
            The raw content
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.#ctor(System.Net.HttpStatusCode,Sleekflow.Apis.UserEventHub.Client.Multimap{System.String,System.String},`0,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1" /> class.
            </summary>
            <param name="statusCode">HTTP status code.</param>
            <param name="headers">HTTP headers.</param>
            <param name="data">Data (parsed HTTP body)</param>
            <param name="rawContent">Raw content.</param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.#ctor(System.Net.HttpStatusCode,Sleekflow.Apis.UserEventHub.Client.Multimap{System.String,System.String},`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1" /> class.
            </summary>
            <param name="statusCode">HTTP status code.</param>
            <param name="headers">HTTP headers.</param>
            <param name="data">Data (parsed HTTP body)</param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.#ctor(System.Net.HttpStatusCode,`0,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1" /> class.
            </summary>
            <param name="statusCode">HTTP status code.</param>
            <param name="data">Data (parsed HTTP body)</param>
            <param name="rawContent">Raw content.</param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1.#ctor(System.Net.HttpStatusCode,`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1" /> class.
            </summary>
            <param name="statusCode">HTTP status code.</param>
            <param name="data">Data (parsed HTTP body)</param>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.ClientUtils">
            <summary>
            Utility functions providing some benefit to API client consumers.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ClientUtils.SanitizeFilename(System.String)">
            <summary>
            Sanitize filename by removing the path
            </summary>
            <param name="filename">Filename</param>
            <returns>Filename</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ClientUtils.ParameterToMultiMap(System.String,System.String,System.Object)">
            <summary>
            Convert params to key/value pairs.
            Use collectionFormat to properly format lists and collections.
            </summary>
            <param name="collectionFormat">The swagger-supported collection format, one of: csv, tsv, ssv, pipes, multi</param>
            <param name="name">Key name.</param>
            <param name="value">Value object.</param>
            <returns>A multimap of keys with 1..n associated values.</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ClientUtils.ParameterToString(System.Object,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            If parameter is DateTime, output in a formatted string (default ISO 8601), customizable with Configuration.DateTime.
            If parameter is a list, join the list with ",".
            Otherwise just return the string.
            </summary>
            <param name="obj">The parameter (header, path, query, form).</param>
            <param name="configuration">An optional configuration instance, providing formatting options used in processing.</param>
            <returns>Formatted string.</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ClientUtils.Base64Encode(System.String)">
            <summary>
            Encode string in base64 format.
            </summary>
            <param name="text">string to be encoded.</param>
            <returns>Encoded string.</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ClientUtils.ReadAsBytes(System.IO.Stream)">
            <summary>
            Convert stream to byte array
            </summary>
            <param name="inputStream">Input stream to be converted</param>
            <returns>Byte array</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ClientUtils.SelectHeaderContentType(System.String[])">
            <summary>
            Select the Content-Type header's value from the given content-type array:
            if JSON type exists in the given array, use it;
            otherwise use the first one defined in 'consumes'
            </summary>
            <param name="contentTypes">The Content-Type array to select from.</param>
            <returns>The Content-Type header to use.</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ClientUtils.SelectHeaderAccept(System.String[])">
            <summary>
            Select the Accept header's value from the given accepts array:
            if JSON exists in the given array, use it;
            otherwise use all of them (joining into a string)
            </summary>
            <param name="accepts">The accepts array to select from.</param>
            <returns>The Accept header to use.</returns>
        </member>
        <member name="F:Sleekflow.Apis.UserEventHub.Client.ClientUtils.JsonRegex">
            <summary>
            Provides a case-insensitive check that a provided content type is a known JSON-like content type.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ClientUtils.IsJsonMime(System.String)">
            <summary>
            Check if the given MIME is a JSON MIME.
            JSON MIME examples:
               application/json
               application/json; charset=UTF8
               APPLICATION/JSON
               application/vnd.company+json
            </summary>
            <param name="mime">MIME</param>
            <returns>Returns True if MIME type is json.</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.Configuration">
            <summary>
            Represents a set of configuration settings
            </summary>
        </member>
        <member name="F:Sleekflow.Apis.UserEventHub.Client.Configuration.Version">
            <summary>
            Version of the package.
            </summary>
            <value>Version of the package.</value>
        </member>
        <member name="F:Sleekflow.Apis.UserEventHub.Client.Configuration.ISO8601_DATETIME_FORMAT">
            <summary>
            Identifier for ISO 8601 DateTime Format
            </summary>
            <remarks>See https://msdn.microsoft.com/en-us/library/az4se3k1(v=vs.110).aspx#Anchor_8 for more information.</remarks>
        </member>
        <member name="F:Sleekflow.Apis.UserEventHub.Client.Configuration.DefaultExceptionFactory">
            <summary>
            Default creation of exceptions for a given method name and response object
            </summary>
        </member>
        <member name="F:Sleekflow.Apis.UserEventHub.Client.Configuration._basePath">
            <summary>
            Defines the base path of the target API server.
            Example: http://localhost:3000/v1/
            </summary>
        </member>
        <member name="F:Sleekflow.Apis.UserEventHub.Client.Configuration._apiKey">
            <summary>
            Gets or sets the API key based on the authentication name.
            This is the key and value comprising the "secret" for accessing an API.
            </summary>
            <value>The API key.</value>
        </member>
        <member name="F:Sleekflow.Apis.UserEventHub.Client.Configuration._apiKeyPrefix">
            <summary>
            Gets or sets the prefix (e.g. Token) of the API key based on the authentication name.
            </summary>
            <value>The prefix of the API key.</value>
        </member>
        <member name="F:Sleekflow.Apis.UserEventHub.Client.Configuration._servers">
            <summary>
            Gets or sets the servers defined in the OpenAPI spec.
            </summary>
            <value>The servers</value>
        </member>
        <member name="F:Sleekflow.Apis.UserEventHub.Client.Configuration._operationServers">
            <summary>
            Gets or sets the operation servers defined in the OpenAPI spec.
            </summary>
            <value>The operation servers</value>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.Configuration" /> class
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.#ctor(System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.Configuration" /> class
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.BasePath">
            <summary>
            Gets or sets the base path for API access.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.DefaultHeader">
            <summary>
            Gets or sets the default header.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.DefaultHeaders">
            <summary>
            Gets or sets the default headers.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.Timeout">
            <summary>
            Gets or sets the HTTP timeout (milliseconds) of ApiClient. Default to 100000 milliseconds.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.Proxy">
            <summary>
            Gets or sets the proxy
            </summary>
            <value>Proxy.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.UserAgent">
            <summary>
            Gets or sets the HTTP user agent.
            </summary>
            <value>Http user agent.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.Username">
            <summary>
            Gets or sets the username (HTTP basic authentication).
            </summary>
            <value>The username.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.Password">
            <summary>
            Gets or sets the password (HTTP basic authentication).
            </summary>
            <value>The password.</value>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.GetApiKeyWithPrefix(System.String)">
            <summary>
            Gets the API key with prefix.
            </summary>
            <param name="apiKeyIdentifier">API key identifier (authentication scheme).</param>
            <returns>API key with prefix.</returns>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.ClientCertificates">
            <summary>
            Gets or sets certificate collection to be sent with requests.
            </summary>
            <value>X509 Certificate collection.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.AccessToken">
             <summary>
             Gets or sets the access token for OAuth2 authentication.
            
             This helper property simplifies code generation.
             </summary>
             <value>The access token.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.TempFolderPath">
            <summary>
            Gets or sets the temporary folder path to store the files downloaded from the server.
            </summary>
            <value>Folder path.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.DateTimeFormat">
            <summary>
            Gets or sets the date time format used when serializing in the ApiClient
            By default, it's set to ISO 8601 - "o", for others see:
            https://msdn.microsoft.com/en-us/library/az4se3k1(v=vs.110).aspx
            and https://msdn.microsoft.com/en-us/library/8kb3ddd4(v=vs.110).aspx
            No validation is done to ensure that the string you're providing is valid
            </summary>
            <value>The DateTimeFormat string</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.ApiKeyPrefix">
             <summary>
             Gets or sets the prefix (e.g. Token) of the API key based on the authentication name.
            
             Whatever you set here will be prepended to the value defined in AddApiKey.
            
             An example invocation here might be:
             <example>
             ApiKeyPrefix["Authorization"] = "Bearer";
             </example>
             … where ApiKey["Authorization"] would then be used to set the value of your bearer token.
            
             <remarks>
             OAuth2 workflows should set tokens via AccessToken.
             </remarks>
             </summary>
             <value>The prefix of the API key.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.ApiKey">
            <summary>
            Gets or sets the API key based on the authentication name.
            </summary>
            <value>The API key.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.Servers">
            <summary>
            Gets or sets the servers.
            </summary>
            <value>The servers.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Configuration.OperationServers">
            <summary>
            Gets or sets the operation servers.
            </summary>
            <value>The operation servers.</value>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.GetServerUrl(System.Int32)">
            <summary>
            Returns URL based on server settings without providing values
            for the variables
            </summary>
            <param name="index">Array index of the server settings.</param>
            <return>The server URL.</return>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.GetServerUrl(System.Int32,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Returns URL based on server settings.
            </summary>
            <param name="index">Array index of the server settings.</param>
            <param name="inputVariables">Dictionary of the variables and the corresponding values.</param>
            <return>The server URL.</return>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.GetOperationServerUrl(System.String,System.Int32)">
            <summary>
            Returns URL based on operation server settings.
            </summary>
            <param name="operation">Operation associated with the request path.</param>
            <param name="index">Array index of the server settings.</param>
            <return>The operation server URL.</return>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.GetOperationServerUrl(System.String,System.Int32,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Returns URL based on operation server settings.
            </summary>
            <param name="operation">Operation associated with the request path.</param>
            <param name="index">Array index of the server settings.</param>
            <param name="inputVariables">Dictionary of the variables and the corresponding values.</param>
            <return>The operation server URL.</return>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.GetServerUrl(System.Collections.Generic.IList{System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object}},System.Int32,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Returns URL based on server settings.
            </summary>
            <param name="servers">Dictionary of server settings.</param>
            <param name="index">Array index of the server settings.</param>
            <param name="inputVariables">Dictionary of the variables and the corresponding values.</param>
            <return>The server URL.</return>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.ToDebugReport">
            <summary>
            Returns a string with essential information for debugging.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.AddApiKey(System.String,System.String)">
            <summary>
            Add Api Key Header.
            </summary>
            <param name="key">Api Key name.</param>
            <param name="value">Api Key value.</param>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.AddApiKeyPrefix(System.String,System.String)">
            <summary>
            Sets the API key prefix.
            </summary>
            <param name="key">Api Key name.</param>
            <param name="value">Api Key value.</param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Configuration.MergeConfigurations(Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Merge configurations.
            </summary>
            <param name="first">First configuration.</param>
            <param name="second">Second configuration.</param>
            <return>Merged configuration.</return>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.ExceptionFactory">
            <summary>
            A delegate to ExceptionFactory method
            </summary>
            <param name="methodName">Method name</param>
            <param name="response">Response</param>
            <returns>Exceptions</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.FileParameter">
            <summary>
            Represents a File passed to the API as a Parameter, allows using different backends for files
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.FileParameter.Name">
            <summary>
            The filename
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.FileParameter.ContentType">
            <summary>
            The content type of the file
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.FileParameter.Content">
            <summary>
            The content of the file
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.FileParameter.#ctor(System.IO.Stream)">
            <summary>
            Construct a FileParameter just from the contents, will extract the filename from a filestream
            </summary>
            <param name="content"> The file content </param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.FileParameter.#ctor(System.String,System.IO.Stream)">
            <summary>
            Construct a FileParameter from name and content
            </summary>
            <param name="filename">The filename</param>
            <param name="content">The file content</param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.FileParameter.#ctor(System.String,System.String,System.IO.Stream)">
            <summary>
            Construct a FileParameter from name and content
            </summary>
            <param name="filename">The filename</param>
            <param name="contentType">The content type of the file</param>
            <param name="content">The file content</param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.FileParameter.op_Implicit(System.IO.Stream)~Sleekflow.Apis.UserEventHub.Client.FileParameter">
            <summary>
            Implicit conversion of stream to file parameter. Useful for backwards compatibility.
            </summary>
            <param name="s">Stream to convert</param>
            <returns>FileParameter</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.GlobalConfiguration">
            <summary>
            <see cref="T:Sleekflow.Apis.UserEventHub.Client.GlobalConfiguration"/> provides a compile-time extension point for globally configuring
            API Clients.
            </summary>
            <remarks>
            A customized implementation via partial class may reside in another file and may
            be excluded from automatic generation via a .openapi-generator-ignore file.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.GlobalConfiguration.#ctor">
            <inheritdoc />
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.GlobalConfiguration.#ctor(System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String},System.String)">
            <inheritdoc />
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.GlobalConfiguration.Instance">
            <summary>
            Gets or sets the default Configuration.
            </summary>
            <value>Configuration.</value>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.IApiAccessor">
            <summary>
            Represents configuration aspects required to interact with the API endpoints.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IApiAccessor.Configuration">
            <summary>
            Gets or sets the configuration object
            </summary>
            <value>An instance of the Configuration</value>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.IApiAccessor.GetBasePath">
            <summary>
            Gets the base path of the API client.
            </summary>
            <value>The base path</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IApiAccessor.ExceptionFactory">
            <summary>
            Provides a factory method hook for the creation of exceptions.
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient">
             <summary>
             Contract for Asynchronous RESTful API interactions.
            
             This interface allows consumers to provide a custom API accessor client.
             </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient.GetAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the GET http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient.PostAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the POST http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient.PutAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the PUT http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient.DeleteAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the DELETE http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient.HeadAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the HEAD http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient.OptionsAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the OPTIONS http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.IAsynchronousClient.PatchAsync``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the PATCH http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration">
            <summary>
            Represents a readable-only configuration contract.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.AccessToken">
            <summary>
            Gets the access token.
            </summary>
            <value>Access token.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.ApiKey">
            <summary>
            Gets the API key.
            </summary>
            <value>API key.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.ApiKeyPrefix">
            <summary>
            Gets the API key prefix.
            </summary>
            <value>API key prefix.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.BasePath">
            <summary>
            Gets the base path.
            </summary>
            <value>Base path.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.DateTimeFormat">
            <summary>
            Gets the date time format.
            </summary>
            <value>Date time format.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.DefaultHeader">
            <summary>
            Gets the default header.
            </summary>
            <value>Default header.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.DefaultHeaders">
            <summary>
            Gets the default headers.
            </summary>
            <value>Default headers.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.TempFolderPath">
            <summary>
            Gets the temp folder path.
            </summary>
            <value>Temp folder path.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.Timeout">
            <summary>
            Gets the HTTP connection timeout (in milliseconds)
            </summary>
            <value>HTTP connection timeout.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.Proxy">
            <summary>
            Gets the proxy.
            </summary>
            <value>Proxy.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.UserAgent">
            <summary>
            Gets the user agent.
            </summary>
            <value>User agent.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.Username">
            <summary>
            Gets the username.
            </summary>
            <value>Username.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.Password">
            <summary>
            Gets the password.
            </summary>
            <value>Password.</value>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.OperationServers">
            <summary>
            Get the servers associated with the operation.
            </summary>
            <value>Operation servers.</value>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.GetApiKeyWithPrefix(System.String)">
            <summary>
            Gets the API key with prefix.
            </summary>
            <param name="apiKeyIdentifier">API key identifier (authentication scheme).</param>
            <returns>API key with prefix.</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.GetOperationServerUrl(System.String,System.Int32)">
            <summary>
            Gets the Operation server url at the provided index.
            </summary>
            <param name="operation">Operation server name.</param>
            <param name="index">Index of the operation server settings.</param>
            <returns></returns>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration.ClientCertificates">
            <summary>
            Gets certificate collection to be sent with requests.
            </summary>
            <value>X509 Certificate collection.</value>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.ISynchronousClient">
             <summary>
             Contract for Synchronous RESTful API interactions.
            
             This interface allows consumers to provide a custom API accessor client.
             </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ISynchronousClient.Get``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the GET http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ISynchronousClient.Post``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the POST http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ISynchronousClient.Put``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the PUT http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ISynchronousClient.Delete``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the DELETE http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ISynchronousClient.Head``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the HEAD http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ISynchronousClient.Options``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the OPTIONS http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.ISynchronousClient.Patch``1(System.String,Sleekflow.Apis.UserEventHub.Client.RequestOptions,Sleekflow.Apis.UserEventHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the PATCH http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.UserEventHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.Multimap`2">
            <summary>
            A dictionary in which one key has many associated values.
            </summary>
            <typeparam name="TKey">The type of the key</typeparam>
            <typeparam name="TValue">The type of the value associated with the key.</typeparam>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.#ctor">
            <summary>
            Empty Constructor.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
            <summary>
            Constructor with comparer.
            </summary>
            <param name="comparer"></param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.GetEnumerator">
            <summary>
            To get the enumerator.
            </summary>
            <returns>Enumerator</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            To get the enumerator.
            </summary>
            <returns>Enumerator</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Add(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}})">
            <summary>
            Add values to Multimap
            </summary>
            <param name="item">Key value pair</param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Add(Sleekflow.Apis.UserEventHub.Client.Multimap{`0,`1})">
            <summary>
            Add Multimap to Multimap
            </summary>
            <param name="multimap">Multimap</param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Clear">
            <summary>
            Clear Multimap
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Contains(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}})">
            <summary>
            Determines whether Multimap contains the specified item.
            </summary>
            <param name="item">Key value pair</param>
            <exception cref="T:System.NotImplementedException">Method needs to be implemented</exception>
            <returns>true if the Multimap contains the item; otherwise, false.</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}}[],System.Int32)">
            <summary>
             Copy items of the Multimap to an array,
                starting at a particular array index.
            </summary>
            <param name="array">The array that is the destination of the items copied
                from Multimap. The array must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
            <exception cref="T:System.NotImplementedException">Method needs to be implemented</exception>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Remove(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}})">
            <summary>
            Removes the specified item from the Multimap.
            </summary>
            <param name="item">Key value pair</param>
            <returns>true if the item is successfully removed; otherwise, false.</returns>
            <exception cref="T:System.NotImplementedException">Method needs to be implemented</exception>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Count">
            <summary>
            Gets the number of items contained in the Multimap.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Multimap`2.IsReadOnly">
            <summary>
            Gets a value indicating whether the Multimap is read-only.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Add(`0,System.Collections.Generic.IList{`1})">
            <summary>
            Adds an item with the provided key and value to the Multimap.
            </summary>
            <param name="key">The object to use as the key of the item to add.</param>
            <param name="value">The object to use as the value of the item to add.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when couldn't add the value to Multimap.</exception>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.ContainsKey(`0)">
            <summary>
            Determines whether the Multimap contains an item with the specified key.
            </summary>
            <param name="key">The key to locate in the Multimap.</param>
            <returns>true if the Multimap contains an item with
                the key; otherwise, false.</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Remove(`0)">
            <summary>
            Removes item with the specified key from the Multimap.
            </summary>
            <param name="key">The key to locate in the Multimap.</param>
            <returns>true if the item is successfully removed; otherwise, false.</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.TryGetValue(`0,System.Collections.Generic.IList{`1}@)">
            <summary>
            Gets the value associated with the specified key.
            </summary>
            <param name="key">The key whose value to get.</param>
            <param name="value">When this method returns, the value associated with the specified key, if the
                key is found; otherwise, the default value for the type of the value parameter.
                This parameter is passed uninitialized.</param>
            <returns> true if the object that implements Multimap contains
                an item with the specified key; otherwise, false.</returns>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Item(`0)">
            <summary>
            Gets or sets the item with the specified key.
            </summary>
            <param name="key">The key of the item to get or set.</param>
            <returns>The value of the specified key.</returns>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Keys">
            <summary>
            Gets a System.Collections.Generic.ICollection containing the keys of the Multimap.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Values">
            <summary>
            Gets a System.Collections.Generic.ICollection containing the values of the Multimap.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.CopyTo(System.Array,System.Int32)">
            <summary>
             Copy the items of the Multimap to an System.Array,
                starting at a particular System.Array index.
            </summary>
            <param name="array">The one-dimensional System.Array that is the destination of the items copied
                from Multimap. The System.Array must have zero-based indexing.</param>
            <param name="index">The zero-based index in array at which copying begins.</param>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.Add(`0,`1)">
            <summary>
            Adds an item with the provided key and value to the Multimap.
            </summary>
            <param name="key">The object to use as the key of the item to add.</param>
            <param name="value">The object to use as the value of the item to add.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when couldn't add value to Multimap.</exception>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.TryRemove(`0,System.Collections.Generic.IList{`1}@)">
            Helper method to encapsulate generator differences between dictionary types.
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.Multimap`2.TryAdd(`0,System.Collections.Generic.IList{`1})">
            Helper method to encapsulate generator differences between dictionary types.
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.OpenAPIDateConverter">
            <summary>
            Formatter for 'date' openapi formats ss defined by full-date - RFC3339
            see https://github.com/OAI/OpenAPI-Specification/blob/master/versions/3.0.0.md#data-types
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.OpenAPIDateConverter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Client.OpenAPIDateConverter" /> class.
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.RequestOptions">
            <summary>
            A container for generalized request inputs. This type allows consumers to extend the request functionality
            by abstracting away from the default (built-in) request framework (e.g. RestSharp).
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.RequestOptions.PathParameters">
            <summary>
            Parameters to be bound to path parts of the Request's URL
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.RequestOptions.QueryParameters">
            <summary>
            Query parameters to be applied to the request.
            Keys may have 1 or more values associated.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.RequestOptions.HeaderParameters">
            <summary>
            Header parameters to be applied to to the request.
            Keys may have 1 or more values associated.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.RequestOptions.FormParameters">
            <summary>
            Form parameters to be sent along with the request.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.RequestOptions.FileParameters">
            <summary>
            File parameters to be sent along with the request.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.RequestOptions.Cookies">
            <summary>
            Cookies to be sent along with the request.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.RequestOptions.Data">
            <summary>
            Any data associated with a request body.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Client.RequestOptions.#ctor">
            <summary>
            Constructs a new instance of <see cref="T:Sleekflow.Apis.UserEventHub.Client.RequestOptions"/>
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.RetryConfiguration">
            <summary>
            Configuration class to set the polly retry policies to be applied to the requests.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.RetryConfiguration.RetryPolicy">
            <summary>
            Retry policy
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Client.RetryConfiguration.AsyncRetryPolicy">
            <summary>
            Async retry policy
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Client.WebRequestPathBuilder">
            <summary>
            A URI builder
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.AbstractOpenAPISchema">
            <summary>
             Abstract base class for oneOf, anyOf schemas in the OpenAPI specification
            </summary>
        </member>
        <member name="F:Sleekflow.Apis.UserEventHub.Model.AbstractOpenAPISchema.SerializerSettings">
            <summary>
             Custom JSON serializer
            </summary>
        </member>
        <member name="F:Sleekflow.Apis.UserEventHub.Model.AbstractOpenAPISchema.AdditionalPropertiesSerializerSettings">
            <summary>
             Custom JSON serializer for objects with additional properties
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AbstractOpenAPISchema.ActualInstance">
            <summary>
            Gets or Sets the actual instance
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AbstractOpenAPISchema.IsNullable">
            <summary>
            Gets or Sets IsNullable to indicate whether the instance is nullable
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AbstractOpenAPISchema.SchemaType">
            <summary>
            Gets or Sets the schema type, which can be either `oneOf` or `anyOf`
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AbstractOpenAPISchema.ToJson">
            <summary>
            Converts the instance into JSON string.
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput">
            <summary>
            AreGroupsConnectedInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput.#ctor(System.Collections.Generic.List{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput" /> class.
            </summary>
            <param name="userIds">userIds (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput.UserIds">
            <summary>
            Gets or Sets UserIds
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput.Equals(Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput)">
            <summary>
            Returns true if AreGroupsConnectedInput instances are equal
            </summary>
            <param name="input">Instance of AreGroupsConnectedInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput">
            <summary>
            AreGroupsConnectedOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput.#ctor(System.Collections.Generic.Dictionary{System.String,Sleekflow.Apis.UserEventHub.Model.IsConnectedObject})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput" /> class.
            </summary>
            <param name="groupIdToIsConnectedObjectDict">groupIdToIsConnectedObjectDict.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput.GroupIdToIsConnectedObjectDict">
            <summary>
            Gets or Sets GroupIdToIsConnectedObjectDict
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput.Equals(Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput)">
            <summary>
            Returns true if AreGroupsConnectedOutput instances are equal
            </summary>
            <param name="input">Instance of AreGroupsConnectedOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput">
            <summary>
            AreGroupsConnectedOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.#ctor(System.Boolean,Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutput,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput)">
            <summary>
            Returns true if AreGroupsConnectedOutputOutput instances are equal
            </summary>
            <param name="input">Instance of AreGroupsConnectedOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreGroupsConnectedOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput">
            <summary>
            AreUsersConnectedInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput.#ctor(System.Collections.Generic.List{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput" /> class.
            </summary>
            <param name="userIds">userIds (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput.UserIds">
            <summary>
            Gets or Sets UserIds
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput.Equals(Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput)">
            <summary>
            Returns true if AreUsersConnectedInput instances are equal
            </summary>
            <param name="input">Instance of AreUsersConnectedInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput">
            <summary>
            AreUsersConnectedOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput.#ctor(System.Collections.Generic.Dictionary{System.String,Sleekflow.Apis.UserEventHub.Model.IsConnectedObject})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput" /> class.
            </summary>
            <param name="userIdToIsConnectedObjectDict">userIdToIsConnectedObjectDict.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput.UserIdToIsConnectedObjectDict">
            <summary>
            Gets or Sets UserIdToIsConnectedObjectDict
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput.Equals(Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput)">
            <summary>
            Returns true if AreUsersConnectedOutput instances are equal
            </summary>
            <param name="input">Instance of AreUsersConnectedOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput">
            <summary>
            AreUsersConnectedOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.#ctor(System.Boolean,Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutput,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput)">
            <summary>
            Returns true if AreUsersConnectedOutputOutput instances are equal
            </summary>
            <param name="input">Instance of AreUsersConnectedOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.AreUsersConnectedOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput">
            <summary>
            DisableMobilePushNotificationOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput" /> class.
            </summary>
            <param name="result">result.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput.Result">
            <summary>
            Gets or Sets Result
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput.Equals(Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput)">
            <summary>
            Returns true if DisableMobilePushNotificationOutput instances are equal
            </summary>
            <param name="input">Instance of DisableMobilePushNotificationOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput">
            <summary>
            DisableMobilePushNotificationOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.#ctor(System.Boolean,Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutput,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput)">
            <summary>
            Returns true if DisableMobilePushNotificationOutputOutput instances are equal
            </summary>
            <param name="input">Instance of DisableMobilePushNotificationOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.DisableMobilePushNotificationOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput">
            <summary>
            EnableMobilePushNotificationInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput.#ctor(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput" /> class.
            </summary>
            <param name="handle">handle (required).</param>
            <param name="tags">tags (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput.Handle">
            <summary>
            Gets or Sets Handle
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput.Tags">
            <summary>
            Gets or Sets Tags
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput.Equals(Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput)">
            <summary>
            Returns true if EnableMobilePushNotificationInput instances are equal
            </summary>
            <param name="input">Instance of EnableMobilePushNotificationInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput">
            <summary>
            EnableMobilePushNotificationOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput.#ctor(Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput" /> class.
            </summary>
            <param name="notificationSettings">notificationSettings.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput.NotificationSettings">
            <summary>
            Gets or Sets NotificationSettings
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput.Equals(Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput)">
            <summary>
            Returns true if EnableMobilePushNotificationOutput instances are equal
            </summary>
            <param name="input">Instance of EnableMobilePushNotificationOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput">
            <summary>
            EnableMobilePushNotificationOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.#ctor(System.Boolean,Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutput,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput)">
            <summary>
            Returns true if EnableMobilePushNotificationOutputOutput instances are equal
            </summary>
            <param name="input">Instance of EnableMobilePushNotificationOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.EnableMobilePushNotificationOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput">
            <summary>
            GetNotificationSettingsOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput.#ctor(Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput" /> class.
            </summary>
            <param name="notificationSettings">notificationSettings.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput.NotificationSettings">
            <summary>
            Gets or Sets NotificationSettings
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput.Equals(Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput)">
            <summary>
            Returns true if GetNotificationSettingsOutput instances are equal
            </summary>
            <param name="input">Instance of GetNotificationSettingsOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput">
            <summary>
            GetNotificationSettingsOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.#ctor(System.Boolean,Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutput,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput)">
            <summary>
            Returns true if GetNotificationSettingsOutputOutput instances are equal
            </summary>
            <param name="input">Instance of GetNotificationSettingsOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.GetNotificationSettingsOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.IsConnectedObject">
            <summary>
            IsConnectedObject
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsConnectedObject.#ctor(System.Boolean,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.IsConnectedObject" /> class.
            </summary>
            <param name="isConnected">isConnected.</param>
            <param name="numOfSessions">numOfSessions.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsConnectedObject.IsConnected">
            <summary>
            Gets or Sets IsConnected
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsConnectedObject.NumOfSessions">
            <summary>
            Gets or Sets NumOfSessions
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsConnectedObject.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsConnectedObject.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsConnectedObject.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsConnectedObject.Equals(Sleekflow.Apis.UserEventHub.Model.IsConnectedObject)">
            <summary>
            Returns true if IsConnectedObject instances are equal
            </summary>
            <param name="input">Instance of IsConnectedObject to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsConnectedObject.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsConnectedObject.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput">
            <summary>
            IsGroupConnectedInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput" /> class.
            </summary>
            <param name="groupId">groupId (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput.GroupId">
            <summary>
            Gets or Sets GroupId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput.Equals(Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput)">
            <summary>
            Returns true if IsGroupConnectedInput instances are equal
            </summary>
            <param name="input">Instance of IsGroupConnectedInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput">
            <summary>
            IsGroupConnectedOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput.#ctor(System.Boolean,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput" /> class.
            </summary>
            <param name="isConnected">isConnected.</param>
            <param name="numOfSessions">numOfSessions.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput.IsConnected">
            <summary>
            Gets or Sets IsConnected
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput.NumOfSessions">
            <summary>
            Gets or Sets NumOfSessions
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput.Equals(Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput)">
            <summary>
            Returns true if IsGroupConnectedOutput instances are equal
            </summary>
            <param name="input">Instance of IsGroupConnectedOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput">
            <summary>
            IsGroupConnectedOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.#ctor(System.Boolean,Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutput,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput)">
            <summary>
            Returns true if IsGroupConnectedOutputOutput instances are equal
            </summary>
            <param name="input">Instance of IsGroupConnectedOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsGroupConnectedOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput">
            <summary>
            IsUserConnectedInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput" /> class.
            </summary>
            <param name="userId">userId (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput.UserId">
            <summary>
            Gets or Sets UserId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput.Equals(Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput)">
            <summary>
            Returns true if IsUserConnectedInput instances are equal
            </summary>
            <param name="input">Instance of IsUserConnectedInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput">
            <summary>
            IsUserConnectedOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput.#ctor(System.Boolean,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput" /> class.
            </summary>
            <param name="isConnected">isConnected.</param>
            <param name="numOfSessions">numOfSessions.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput.IsConnected">
            <summary>
            Gets or Sets IsConnected
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput.NumOfSessions">
            <summary>
            Gets or Sets NumOfSessions
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput.Equals(Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput)">
            <summary>
            Returns true if IsUserConnectedOutput instances are equal
            </summary>
            <param name="input">Instance of IsUserConnectedOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput">
            <summary>
            IsUserConnectedOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.#ctor(System.Boolean,Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutput,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput)">
            <summary>
            Returns true if IsUserConnectedOutputOutput instances are equal
            </summary>
            <param name="input">Instance of IsUserConnectedOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.IsUserConnectedOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto">
            <summary>
            MobileNotificationSettingsDto
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.#ctor(System.Boolean,System.String,System.String,System.Boolean,System.Boolean,System.Collections.Generic.List{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto" /> class.
            </summary>
            <param name="banner">banner.</param>
            <param name="iosSound">iosSound.</param>
            <param name="androidSound">androidSound.</param>
            <param name="badge">badge.</param>
            <param name="isNotificationEnabled">isNotificationEnabled.</param>
            <param name="enabledNotificationEvents">enabledNotificationEvents.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.Banner">
            <summary>
            Gets or Sets Banner
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.IosSound">
            <summary>
            Gets or Sets IosSound
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.AndroidSound">
            <summary>
            Gets or Sets AndroidSound
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.Badge">
            <summary>
            Gets or Sets Badge
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.IsNotificationEnabled">
            <summary>
            Gets or Sets IsNotificationEnabled
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.EnabledNotificationEvents">
            <summary>
            Gets or Sets EnabledNotificationEvents
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.Equals(Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto)">
            <summary>
            Returns true if MobileNotificationSettingsDto instances are equal
            </summary>
            <param name="input">Instance of MobileNotificationSettingsDto to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto">
            <summary>
            NotificationSettingsDto
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto.#ctor(System.String,Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto,Sleekflow.Apis.UserEventHub.Model.MobileNotificationSettingsDto,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto" /> class.
            </summary>
            <param name="sleekflowStaffId">sleekflowStaffId.</param>
            <param name="web">web.</param>
            <param name="mobile">mobile.</param>
            <param name="id">id.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto.SleekflowStaffId">
            <summary>
            Gets or Sets SleekflowStaffId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto.Web">
            <summary>
            Gets or Sets Web
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto.Mobile">
            <summary>
            Gets or Sets Mobile
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto.Id">
            <summary>
            Gets or Sets Id
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto.Equals(Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto)">
            <summary>
            Returns true if NotificationSettingsDto instances are equal
            </summary>
            <param name="input">Instance of NotificationSettingsDto to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto">
            <summary>
            PlatformSpecificNotificationSettingsDto
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto.#ctor(System.Boolean,System.Collections.Generic.List{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto" /> class.
            </summary>
            <param name="isNotificationEnabled">isNotificationEnabled.</param>
            <param name="enabledNotificationEvents">enabledNotificationEvents.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto.IsNotificationEnabled">
            <summary>
            Gets or Sets IsNotificationEnabled
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto.EnabledNotificationEvents">
            <summary>
            Gets or Sets EnabledNotificationEvents
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto.Equals(Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto)">
            <summary>
            Returns true if PlatformSpecificNotificationSettingsDto instances are equal
            </summary>
            <param name="input">Instance of PlatformSpecificNotificationSettingsDto to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.PlatformSpecificNotificationSettingsDto.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput">
            <summary>
            RedeliverSessionUnackedMessagesInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput" /> class.
            </summary>
            <param name="sessionId">sessionId (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput.SessionId">
            <summary>
            Gets or Sets SessionId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput.Equals(Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput)">
            <summary>
            Returns true if RedeliverSessionUnackedMessagesInput instances are equal
            </summary>
            <param name="input">Instance of RedeliverSessionUnackedMessagesInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput">
            <summary>
            RedeliverSessionUnackedMessagesOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.#ctor(System.Boolean,System.Object,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput)">
            <summary>
            Returns true if RedeliverSessionUnackedMessagesOutputOutput instances are equal
            </summary>
            <param name="input">Instance of RedeliverSessionUnackedMessagesOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.RedeliverSessionUnackedMessagesOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput">
            <summary>
            SendMessageToGroupInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput.#ctor(System.String,System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput" /> class.
            </summary>
            <param name="groupId">groupId (required).</param>
            <param name="messageType">messageType (required).</param>
            <param name="messageObject">messageObject (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput.GroupId">
            <summary>
            Gets or Sets GroupId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput.MessageType">
            <summary>
            Gets or Sets MessageType
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput.MessageObject">
            <summary>
            Gets or Sets MessageObject
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput.Equals(Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput)">
            <summary>
            Returns true if SendMessageToGroupInput instances are equal
            </summary>
            <param name="input">Instance of SendMessageToGroupInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput">
            <summary>
            SendMessageToGroupOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.#ctor(System.Boolean,System.Object,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput)">
            <summary>
            Returns true if SendMessageToGroupOutputOutput instances are equal
            </summary>
            <param name="input">Instance of SendMessageToGroupOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToGroupOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput">
            <summary>
            SendMessageToUserInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput.#ctor(System.String,System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput" /> class.
            </summary>
            <param name="userId">userId (required).</param>
            <param name="messageType">messageType (required).</param>
            <param name="messageObject">messageObject (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput.UserId">
            <summary>
            Gets or Sets UserId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput.MessageType">
            <summary>
            Gets or Sets MessageType
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput.MessageObject">
            <summary>
            Gets or Sets MessageObject
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput.Equals(Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput)">
            <summary>
            Returns true if SendMessageToUserInput instances are equal
            </summary>
            <param name="input">Instance of SendMessageToUserInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput">
            <summary>
            SendMessageToUserOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.#ctor(System.Boolean,System.Object,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput)">
            <summary>
            Returns true if SendMessageToUserOutputOutput instances are equal
            </summary>
            <param name="input">Instance of SendMessageToUserOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUserOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput">
            <summary>
            SendMessageToUsersInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput.#ctor(System.Collections.Generic.List{System.String},System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput" /> class.
            </summary>
            <param name="userIds">userIds (required).</param>
            <param name="messageType">messageType (required).</param>
            <param name="messageObject">messageObject (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput.UserIds">
            <summary>
            Gets or Sets UserIds
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput.MessageType">
            <summary>
            Gets or Sets MessageType
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput.MessageObject">
            <summary>
            Gets or Sets MessageObject
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput.Equals(Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput)">
            <summary>
            Returns true if SendMessageToUsersInput instances are equal
            </summary>
            <param name="input">Instance of SendMessageToUsersInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput">
            <summary>
            SendMessageToUsersOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.#ctor(System.Boolean,System.Object,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput)">
            <summary>
            Returns true if SendMessageToUsersOutputOutput instances are equal
            </summary>
            <param name="input">Instance of SendMessageToUsersOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendMessageToUsersOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput">
            <summary>
            SendPushNotificationInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.#ctor(System.String,System.Collections.Generic.List{System.String},System.String,System.String,System.Nullable{System.Int32},System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput" /> class.
            </summary>
            <param name="notificationEvent">notificationEvent (required).</param>
            <param name="tags">tags (required).</param>
            <param name="title">title.</param>
            <param name="body">body.</param>
            <param name="badge">badge.</param>
            <param name="conversationId">conversationId.</param>
            <param name="companyId">companyId.</param>
            <param name="userId">userId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.NotificationEvent">
            <summary>
            Gets or Sets NotificationEvent
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.Tags">
            <summary>
            Gets or Sets Tags
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.Title">
            <summary>
            Gets or Sets Title
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.Body">
            <summary>
            Gets or Sets Body
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.Badge">
            <summary>
            Gets or Sets Badge
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.ConversationId">
            <summary>
            Gets or Sets ConversationId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.CompanyId">
            <summary>
            Gets or Sets CompanyId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.UserId">
            <summary>
            Gets or Sets UserId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.Equals(Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput)">
            <summary>
            Returns true if SendPushNotificationInput instances are equal
            </summary>
            <param name="input">Instance of SendPushNotificationInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput">
            <summary>
            SendPushNotificationOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.#ctor(System.Boolean,System.Object,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput)">
            <summary>
            Returns true if SendPushNotificationOutputOutput instances are equal
            </summary>
            <param name="input">Instance of SendPushNotificationOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.SendPushNotificationOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput">
            <summary>
            UpdateNotificationSettingsByPlatformInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.#ctor(System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.String,System.String,System.Nullable{System.Boolean},System.Collections.Generic.List{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput" /> class.
            </summary>
            <param name="platform">platform (required).</param>
            <param name="isNotificationEnabled">isNotificationEnabled.</param>
            <param name="banner">banner.</param>
            <param name="androidSound">androidSound.</param>
            <param name="iosSound">iosSound.</param>
            <param name="badge">badge.</param>
            <param name="enabledNotificationEvents">enabledNotificationEvents.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.Platform">
            <summary>
            Gets or Sets Platform
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.IsNotificationEnabled">
            <summary>
            Gets or Sets IsNotificationEnabled
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.Banner">
            <summary>
            Gets or Sets Banner
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.AndroidSound">
            <summary>
            Gets or Sets AndroidSound
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.IosSound">
            <summary>
            Gets or Sets IosSound
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.Badge">
            <summary>
            Gets or Sets Badge
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.EnabledNotificationEvents">
            <summary>
            Gets or Sets EnabledNotificationEvents
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.Equals(Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput)">
            <summary>
            Returns true if UpdateNotificationSettingsByPlatformInput instances are equal
            </summary>
            <param name="input">Instance of UpdateNotificationSettingsByPlatformInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput">
            <summary>
            UpdateNotificationSettingsByPlatformOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput.#ctor(Sleekflow.Apis.UserEventHub.Model.NotificationSettingsDto)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput" /> class.
            </summary>
            <param name="notificationSettings">notificationSettings.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput.NotificationSettings">
            <summary>
            Gets or Sets NotificationSettings
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput.Equals(Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput)">
            <summary>
            Returns true if UpdateNotificationSettingsByPlatformOutput instances are equal
            </summary>
            <param name="input">Instance of UpdateNotificationSettingsByPlatformOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput">
            <summary>
            UpdateNotificationSettingsByPlatformOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.#ctor(System.Boolean,Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutput,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.Equals(Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput)">
            <summary>
            Returns true if UpdateNotificationSettingsByPlatformOutputOutput instances are equal
            </summary>
            <param name="input">Instance of UpdateNotificationSettingsByPlatformOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.UserEventHub.Model.UpdateNotificationSettingsByPlatformOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
    </members>
</doc>
