﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class OperationsSortedResponseTimeMetric : IDashboardMetric
{
    private readonly Output<string>? _resourceId;
    private readonly Output<string>? _resourceName;

    public OperationsSortedResponseTimeMetric(Output<string>? resourceId, Output<string>? resourceName)
    {
        _resourceId = resourceId;
        _resourceName = resourceName;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs =
                    new[]
                    {
                        new Dictionary<string, object>
                        {
                            {
                                "name", "ComponentId"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "ControlType"
                            },
                            {
                                "value", "AnalyticsGrid"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "DashboardId"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "Dimensions"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "DraftRequestParameters"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "IsQueryContainTimeRange"
                            },
                            {
                                "value", false
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "LegendOptions"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "PartSubTitle"
                            },
                            {
                                "value", _resourceName!
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "PartTitle"
                            },
                            {
                                "value", "Analytics"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "Query"
                            },
                            {
                                "value",
                                "let timeGrain = 1m;\nlet OperationsStats=requests\n    | where client_Type != \"Browser\"\n        and operation_Name !contains \"swagger\"\n        and operation_Name != \"GET /\"\n    | summarize count_=avg(itemCount), totalTime=sum(duration) by operation_Name, bin(timestamp, timeGrain)\n    | extend averageResponseTime = totalTime / count_\n    | project operation_Name, timestamp, averageResponseTime, count_\n    | sort by operation_Name, timestamp asc;\nOperationsStats\n| serialize\n| extend\n    prev_averageResponseTime = prev(averageResponseTime, 1),\n    prev_timestamp = prev(timestamp, 1),\n    prev_operation_Name = prev(operation_Name, 1)\n| where prev_operation_Name == operation_Name\n| extend responseTimeIncrease = averageResponseTime - prev_averageResponseTime\n| extend responseTimeIncreasePercentage = (responseTimeIncrease / prev_averageResponseTime) * 100\n| summarize percentile(responseTimeIncreasePercentage, 95), sum(count_) by operation_Name\n| sort by percentile_responseTimeIncreasePercentage_95 desc\n"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "Scope"
                            },
                            {
                                "value", new Dictionary<string, object>()
                                {
                                    {
                                        "resourceIds", new[]
                                        {
                                            _resourceId!
                                        }
                                    }
                                }
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "SpecificChart"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "TimeRange"
                            },
                            {
                                "value", "P1D"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "Version"
                            },
                            {
                                "value", "2.0"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "resourceTypeMode"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "PartTitle"
                            },
                            {
                                "value",
                                "Operations Sorted by 95th Percentile of Increase in Average Response Time Over Time (Excluding Browser Requests and Certain Operations)"
                            },
                            {
                                "isOptional", true
                            }
                        }
                    },
                Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                Settings =
                {
                    new Dictionary<string, object>
                    {
                    }
                },
            }
        };
    }
}