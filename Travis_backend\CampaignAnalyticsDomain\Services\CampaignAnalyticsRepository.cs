﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.CampaignAnalyticsDomain.Models;
using Travis_backend.CampaignAnalyticsDomain.Utils;
using Travis_backend.Database;
using Travis_backend.Exceptions;

namespace Travis_backend.CampaignAnalyticsDomain.Services;

public interface ICampaignAnalyticsRepository
{
    public IAsyncEnumerable<TOut> GetObjectEnumerableAsync<TOut>(
        CampaignAnalyticsQueryDefinition queryDefinition);

    Task<List<TOut>> GetObjectsAsync<TOut>(
        CampaignAnalyticsQueryDefinition queryDefinition,
        CancellationToken cancellationToken = default);

    Task<TOut> FirstOrDefaultAsync<TOut>(
        CampaignAnalyticsQueryDefinition queryDefinition,
        CancellationToken cancellationToken = default);

    Task<List<MetricDataPointDto>> GetCountsPerMessageStatusByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        CancellationToken cancellationToken = default);

    Task<int> GetRepliedCountByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default);

    Task<List<MetricDataPointDto>> GetCountsPerMessageStatusByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        CancellationToken cancellationToken = default);

    Task<int> GetRepliedCountByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default);

    Task<List<CampaignMessageOverviewDto>> GetRepliedMessageOverviewByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        ReplyWindow replyWindow,
        string orderBy,
        string direction,
        int offset,
        int limit,
        CancellationToken cancellationToken = default);

    Task<List<CampaignMessageOverviewDto>> GetMessageOverviewByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        string campaignMessageStatus,
        string orderBy,
        string direction,
        int offset,
        int limit,
        CancellationToken cancellationToken = default);

    Task<List<string>> GetUserProfileIdsOfRepliedByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default);

    Task<List<string>> GetUserProfileIdsOfCommonStatusesByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        string campaignMessageStatus,
        CancellationToken cancellationToken = default);

    Task<List<string>> GetUserProfileIdsOfRepliedByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default);

    Task<List<string>> GetUserProfileIdsOfCommonStatusesByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        string campaignMessageStatus,
        CancellationToken cancellationToken = default);

    Task<List<CampaignMessageOverviewDto>> GetRepliedMessageOverviewByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        ReplyWindow replyWindow,
        string orderBy,
        string direction,
        int offset,
        int limit,
        CancellationToken cancellationToken = default);

    Task<List<CampaignMessageOverviewDto>> GetMessageOverviewByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        string campaignMessageStatus,
        string orderBy,
        string direction,
        int offset,
        int limit,
        CancellationToken cancellationToken = default);

    Task<List<ReplyMessageOverviewDto>> GetRepliedMessageOverviewsAsync(
        string companyId,
        List<long> outMessageIds,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default);

    Task<string> GetBroadcastTitleAsync(
        string companyId,
        string broadcastCampaignId,
        CancellationToken cancellationToken = default);
}

public class CampaignAnalyticsRepository : ICampaignAnalyticsRepository
{
    private readonly ApplicationReadDbContext _applicationReadDbContext;
    private readonly ILogger<CampaignAnalyticsRepository> _logger;

    public CampaignAnalyticsRepository(
        ApplicationReadDbContext applicationReadDbContext,
        ILogger<CampaignAnalyticsRepository> logger)
    {
        _applicationReadDbContext = applicationReadDbContext;
        _logger = logger;
    }

    public IAsyncEnumerable<TOut> GetObjectEnumerableAsync<TOut>(
        CampaignAnalyticsQueryDefinition queryDefinition)
    {
        try
        {
            return _applicationReadDbContext.Database.SqlQueryRaw<TOut>(
                    queryDefinition.Query,
                    queryDefinition.Parameters.ToArray())
                .AsAsyncEnumerable();
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "CampaignAnalyticsRepository - error occurred while execution. {CompanyId} {Query} {SqlParameters}",
                queryDefinition.Parameters.FirstOrDefault(p => p.ParameterName == "@CompanyId")?.Value.ToString() ?? "null",
                queryDefinition.Query,
                JsonConvert.SerializeObject(queryDefinition.Parameters));

            throw new CampaignAnalyticsDbQueryException(e);
        }
    }

    public async Task<List<TOut>> GetObjectsAsync<TOut>(
        CampaignAnalyticsQueryDefinition queryDefinition,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _applicationReadDbContext.Database.SqlQueryRaw<TOut>(
                    queryDefinition.Query,
                    queryDefinition.Parameters.ToArray())
                .ToListAsync(cancellationToken: cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "CampaignAnalyticsRepository - error occurred while execution. {CompanyId} {Query} {SqlParameters}",
                queryDefinition.Parameters.FirstOrDefault(p => p.ParameterName == "@CompanyId")?.Value.ToString() ?? "null",
                queryDefinition.Query,
                JsonConvert.SerializeObject(queryDefinition.Parameters));

            throw new CampaignAnalyticsDbQueryException(e);
        }
    }

    public async Task<TOut> FirstOrDefaultAsync<TOut>(
        CampaignAnalyticsQueryDefinition queryDefinition,
        CancellationToken cancellationToken = default) // where TOut : class
    {
        try
        {
            return await GetObjectEnumerableAsync<TOut>(queryDefinition)
                .FirstOrDefaultAsync(cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "CampaignAnalyticsRepository - error occurred while execution. {CompanyId} {Query} {SqlParameters}",
                queryDefinition.Parameters.FirstOrDefault(p => p.ParameterName == "@CompanyId")?.Value.ToString() ?? "null",
                queryDefinition.Query,
                JsonConvert.SerializeObject(queryDefinition.Parameters));

            throw new CampaignAnalyticsDbQueryException(e);
        }
    }

    public async Task<List<MetricDataPointDto>> GetCountsPerMessageStatusByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        CancellationToken cancellationToken = default)
    {
        var getCountsPerMessageStatusQueryDef = CampaignAnalyticsQueryBuilder.GetCountsPerMessageStatusByBroadcastQueryDef(
            companyId,
            broadcastCampaignId);

        return await GetObjectsAsync<MetricDataPointDto>(
            getCountsPerMessageStatusQueryDef,
            cancellationToken);
    }

    public async Task<int> GetRepliedCountByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var repliedCountsQueryDef = CampaignAnalyticsQueryBuilder.GetRepliedCountByBroadcastQueryDef(
            companyId,
            broadcastCampaignId,
            replyWindow);

        return await FirstOrDefaultAsync<int>(
            repliedCountsQueryDef,
            cancellationToken);
    }

    public async Task<List<MetricDataPointDto>> GetCountsPerMessageStatusByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        CancellationToken cancellationToken = default)
    {
        var getCountsPerMessageStatusQueryDef = CampaignAnalyticsQueryBuilder.GetCountsPerMessageStatusByAnalyticTagQueryDef(
            companyId,
            analyticTag);

        return await GetObjectsAsync<MetricDataPointDto>(
            getCountsPerMessageStatusQueryDef,
            cancellationToken);
    }

    public async Task<int> GetRepliedCountByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var repliedCountsQueryDef = CampaignAnalyticsQueryBuilder.GetRepliedCountByAnalyticTagQueryDef(
            companyId,
            analyticTag,
            replyWindow);

        return await FirstOrDefaultAsync<int>(
            repliedCountsQueryDef,
            cancellationToken);
    }

    public async Task<List<CampaignMessageOverviewDto>> GetRepliedMessageOverviewByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        ReplyWindow replyWindow,
        string orderBy,
        string direction,
        int offset,
        int limit,
        CancellationToken cancellationToken = default)
    {
        var getMessageOverviewByBroadcastQueryDef = CampaignAnalyticsQueryBuilder.GetRepliedMessageOverviewByBroadcastQueryDef(
            companyId,
            broadcastCampaignId,
            replyWindow,
            orderBy,
            direction,
            offset,
            limit);

        return await GetObjectsAsync<CampaignMessageOverviewDto>(
            getMessageOverviewByBroadcastQueryDef,
            cancellationToken);
    }

    public async Task<List<CampaignMessageOverviewDto>> GetMessageOverviewByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        string campaignMessageStatus,
        string orderBy,
        string direction,
        int offset,
        int limit,
        CancellationToken cancellationToken = default)
    {
        var mappedConversationStatus = CampaignMessageStatusMapper.Map(campaignMessageStatus);

        var getMessageOverviewByBroadcastQueryDef = CampaignAnalyticsQueryBuilder.GetMessageOverviewByBroadcastQueryDef(
            companyId,
            broadcastCampaignId,
            mappedConversationStatus,
            orderBy,
            direction,
            offset,
            limit);

        return await GetObjectsAsync<CampaignMessageOverviewDto>(
            getMessageOverviewByBroadcastQueryDef,
            cancellationToken);
    }

    public async Task<List<string>> GetUserProfileIdsOfRepliedByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var getUserProfileIdsByBroadcastQueryDef = CampaignAnalyticsQueryBuilder.GetRepliedMessageUserProfileIdsByBroadcastQueryDef(
            companyId,
            broadcastCampaignId,
            replyWindow);

        var userProfileIds = await GetObjectsAsync<string>(
            getUserProfileIdsByBroadcastQueryDef,
            cancellationToken);

        return userProfileIds;
    }

    public async Task<List<string>> GetUserProfileIdsOfCommonStatusesByBroadcastAsync(
        string companyId,
        string broadcastCampaignId,
        string campaignMessageStatus,
        CancellationToken cancellationToken = default)
    {
        var mappedConversationStatus = CampaignMessageStatusMapper.Map(campaignMessageStatus);

        var getUserProfileIdsByBroadcastQueryDef = CampaignAnalyticsQueryBuilder.GetUserProfileIdsByBroadcastQueryDef(
            companyId,
            broadcastCampaignId,
            mappedConversationStatus);

        var userProfileIds = await GetObjectsAsync<string>(
            getUserProfileIdsByBroadcastQueryDef,
            cancellationToken);

        return userProfileIds;
    }

    public async Task<List<string>> GetUserProfileIdsOfRepliedByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var getUserProfileIdsByBroadcastQueryDef = CampaignAnalyticsQueryBuilder.GetRepliedMessageUserProfileIdsByAnalyticTagQueryDef(
            companyId,
            analyticTag,
            replyWindow);

        var userProfileIds = await GetObjectsAsync<string>(
            getUserProfileIdsByBroadcastQueryDef,
            cancellationToken);

        return userProfileIds;
    }

    public async Task<List<string>> GetUserProfileIdsOfCommonStatusesByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        string campaignMessageStatus,
        CancellationToken cancellationToken = default)
    {
        var mappedConversationStatus = CampaignMessageStatusMapper.Map(campaignMessageStatus);

        var getUserProfileIdsByBroadcastQueryDef = CampaignAnalyticsQueryBuilder.GetUserProfileIdsByAnalyticTagQueryDef(
            companyId,
            analyticTag,
            mappedConversationStatus);

        var userProfileIds = await GetObjectsAsync<string>(
            getUserProfileIdsByBroadcastQueryDef,
            cancellationToken);

        return userProfileIds;
    }

    public async Task<List<CampaignMessageOverviewDto>> GetRepliedMessageOverviewByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        ReplyWindow replyWindow,
        string orderBy,
        string direction,
        int offset,
        int limit,
        CancellationToken cancellationToken = default)
    {
        var getMessageOverviewQueryDef = CampaignAnalyticsQueryBuilder.GetRepliedMessageOverviewByAnalyticTagQueryDef(
            companyId,
            analyticTag,
            replyWindow,
            orderBy,
            direction,
            offset,
            limit);

        return await GetObjectsAsync<CampaignMessageOverviewDto>(
            getMessageOverviewQueryDef,
            cancellationToken);
    }

    public async Task<List<CampaignMessageOverviewDto>> GetMessageOverviewByAnalyticTagAsync(
        string companyId,
        string analyticTag,
        string campaignMessageStatus,
        string orderBy,
        string direction,
        int offset,
        int limit,
        CancellationToken cancellationToken = default)
    {
        var mappedConversationStatus = CampaignMessageStatusMapper.Map(campaignMessageStatus);

        var getMessageOverviewQueryDef = CampaignAnalyticsQueryBuilder.GetMessageOverviewByAnalyticTagQueryDef(
            companyId,
            analyticTag,
            mappedConversationStatus,
            orderBy,
            direction,
            offset,
            limit);

        return await GetObjectsAsync<CampaignMessageOverviewDto>(
            getMessageOverviewQueryDef,
            cancellationToken);
    }

    public async Task<List<ReplyMessageOverviewDto>> GetRepliedMessageOverviewsAsync(
        string companyId,
        List<long> outMessageIds,
        ReplyWindow replyWindow,
        CancellationToken cancellationToken = default)
    {
        var getRepliedMessageOverviewsQueryDef = CampaignAnalyticsQueryBuilder.GetReplyMessageOverviewsQueryDef(
            companyId,
            outMessageIds,
            replyWindow);

        return await GetObjectsAsync<ReplyMessageOverviewDto>(
            getRepliedMessageOverviewsQueryDef,
            cancellationToken);
    }

    public async Task<string> GetBroadcastTitleAsync(
        string companyId,
        string broadcastCampaignId,
        CancellationToken cancellationToken = default)
    {
        return await _applicationReadDbContext.CompanyMessageTemplates
            .Where(
                x =>
                    x.CompanyId == companyId &&
                    x.Id == broadcastCampaignId)
            .Select(x => x.TemplateName)
            .FirstOrDefaultAsync(cancellationToken);
    }
}