using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Enums;

namespace Travis_backend.CompanyDomain.Repositories;

/// <summary>
/// Repository for access CompanyBillRecord table.
/// </summary>
public interface ICompanyBillRecordRepository
{
    /// <summary>
    /// Create BillRecord for Company.
    /// </summary>
    /// <param name="billRecord">Instance of BillRecord.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    Task CreateBillRecordAsync(BillRecord billRecord);

    /// <summary>
    /// Query if company has BillRecord that match predicate.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="predicate">Predicate of Query.</param>
    /// <returns>Boolean that indicate if company has BillRecord that match predicate.</returns>
    Task<bool> HasBillRecordAsync(string companyId, Expression<Func<BillRecord, bool>> predicate = null);

    /// <summary>
    /// Query if company has BillRecord that valid and the match predicate.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="extraPredication">Predicate of Query.</param>
    /// <returns>Boolean that indicate if company has BillRecord that match predicate.</returns>
    Task<bool> HasValidBillRecordAsync(string companyId, Expression<Func<BillRecord, bool>> extraPredication = null);

    /// <summary>
    /// Query company's last BillRecord that match the predicate.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="extraPredication">Predicate of Query.</param>
    /// <returns>BillRecord.</returns>
    Task<BillRecord?> GetLastBillRecordAsync(string companyId, Expression<Func<BillRecord, bool>> extraPredication = null);

    /// <summary>
    /// Query company BillRecords that valid and the match predicate.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="extraPredication">Predicate of Query.</param>
    /// <returns>Collection of BillRecords.</returns>
    Task<IReadOnlyCollection<BillRecord>> GetValidBillRecordsAsync(string companyId, Expression<Func<BillRecord, bool>> extraPredication = null);

    /// <summary>
    /// Find Company's Current Base Subscription's BillRecord.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <returns>Current Base Subscription's BillRecord.</returns>
    Task<BillRecord> GetCompanyCurrentBaseSubscriptionBillRecord(string companyId);

    /// <summary>
    /// Get Valid BillRecords with 'Active' Status.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="extraPredication">Optional predication for query.</param>
    /// <returns>Collection of BillRecords.</returns>
    Task<IEnumerable<BillRecord>> GetActiveBillRecordAsync(string companyId, Expression<Func<BillRecord, bool>> extraPredication = null);

    /// <summary>
    /// Find CompanyBillRecord with CompanyId and PlanTier.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="tier">SubscriptionTier.</param>
    /// <returns>Collection of BillRecord.</returns>
    Task<IEnumerable<BillRecord>> FindActiveByCompanyIdAndPlanTier(string companyId, SubscriptionTier tier);

    /// <summary>
    /// Find CompanyBillRecord with CompanyId and PlanIds.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="planIds">Collection of Plan Ids.</param>
    /// <returns>Collection of BillRecord.</returns>
    Task<IEnumerable<BillRecord>> FindActiveByCompanyIdAndPlanIds(string companyId, IEnumerable<string> planIds);

    /// <summary>
    /// Get last valid payment BillRecord.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <returns>Last valid payment BillRecord.</returns>
    Task<BillRecord> GetLastValidPaymentBillRecord(string companyId);

    /// <summary>
    /// Get latest BillRecord by Stripe's Subscription Id.
    /// </summary>
    /// <param name="stripeSubscriptionId">Stripe's Subscription Id.</param>
    /// <param name="subscriptionPlanId">Subscription Plan Id.</param>
    /// <returns>BillRecord.</returns>
    Task<BillRecord> GetLatestStripeSubscriptionBillRecord(string stripeSubscriptionId, string subscriptionPlanId);

    /// <summary>
    /// Update Stripe's Subscription Cancellation DateTime to BillRecord.
    /// </summary>
    /// <param name="id">BillRecord Id.</param>
    /// <param name="cancelDateTime">Cancellation DateTime.</param>
    /// <returns>Affected Row.</returns>
    Task<int> UpdateStipeSubscriptionCancelDateTime(long id, DateTime? cancelDateTime);

    /// <summary>
    /// Set Stripe Subscription's BillRecords status to 'Terminate'.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="stripeSubscriptionId">Stripe's Subscription Id.</param>
    /// <param name="stripeSubscriptionItemId">Stripe's SubscriptionItem Id.</param>
    /// <returns>Affected Rows.</returns>
    Task<int> TerminateBillRecords(string companyId, string stripeSubscriptionId, string stripeSubscriptionItemId = null);

    /// <summary>
    /// Delete company's BillRecord that match predication.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="extraPredication">Optional predication for query.</param>
    /// <returns>Count of affected row.</returns>
    Task<int> DeleteBillRecordsAsync(string companyId, Expression<Func<BillRecord, bool>> extraPredication = null);
}