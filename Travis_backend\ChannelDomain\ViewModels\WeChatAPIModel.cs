﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Xml.Serialization;
using Newtonsoft.Json;

namespace Travis_backend.ChannelDomain.ViewModels
{
    public class WxSignatureParameter
    {
        public string Signature { get; set; }

        public string Timestamp { get; set; }

        public string Nonce { get; set; }

        public string Echostr { get; set; }
    }

    public class AccessTokenResponse
    {
        public int? errcode { get; set; }

        public string errmsg { get; set; }

        public string access_token { get; set; }

        public int expires_in { get; set; }
    }

    public class WeChatMenuGetResponse
    {
        [JsonProperty("menu", NullValueHandling = NullValueHandling.Ignore)]
        public WeChatCustomMenu WeChatCustomMenu { get; set; }
    }

    public class WeChatMenuCreateRequest
    {
        [JsonProperty("app_id", NullValueHandling = NullValueHandling.Ignore)]
        public string AppId { get; set; }
    }

    public sealed class WeChatMenuCreateResponse : WeChatAPIResponse
    {
    }

    public class WechatCustomMenuButton
    {
        [JsonProperty("type", NullValueHandling = NullValueHandling.Ignore)]
        public string Type { get; set; }

        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string Name { get; set; }

        [JsonProperty("key", NullValueHandling = NullValueHandling.Ignore)]
        public string Key { get; set; }

        [JsonProperty("url", NullValueHandling = NullValueHandling.Ignore)]
        public string Url { get; set; }

        [JsonProperty("media_id", NullValueHandling = NullValueHandling.Ignore)]
        public string MediaId { get; set; }

        [JsonProperty("appid", NullValueHandling = NullValueHandling.Ignore)]
        public string AppId { get; set; }

        // ReSharper disable once StringLiteralTypo
        [JsonProperty("pagepath", NullValueHandling = NullValueHandling.Ignore)]
        public string PagePath { get; set; }

        [JsonProperty("article_id", NullValueHandling = NullValueHandling.Ignore)]
        public string ArticleId { get; set; }


        [JsonProperty("sub_button", NullValueHandling = NullValueHandling.Ignore)]
        public List<WechatCustomMenuButton> SubButton { get; set; }
    }

    public class WeChatCustomMenu
    {
        [JsonProperty("button", NullValueHandling = NullValueHandling.Ignore)]
        public List<WechatCustomMenuButton> Button { get; set; }
    }

    public class WeChatUploadTemporaryMaterialResponse
    {
        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("media_id")]
        public string MediaId { get; set; }
    }

    public class WeChatAPIResponse
    {
        public int errcode { get; set; }

        public string errmsg { get; set; }
    }

    public class WeChatTextMessageContent
    {
        [JsonProperty("content")]
        public string Content { get; set; }
    }

    public class WeChatSendTextMessageRequest
    {
        [JsonProperty("touser")]
        public string ToUser { get; set; }

        [JsonProperty("msgtype")]
        public string MsgType { get; set; }

        [JsonProperty("text")]
        public WeChatTextMessageContent Text { get; set; }
    }

    public class WeChatImageMessageContent
    {
        [JsonProperty("media_id")]
        public string MediaId { get; set; }
    }

    public class WeChatSendImageMessageRequest
    {
        [JsonProperty("touser")]
        public string ToUser { get; set; }

        [JsonProperty("msgtype")]
        public string MsgType { get; set; }

        [JsonProperty("image")]
        public WeChatImageMessageContent Image { get; set; }
    }

    public class WeChatGetUploadedVideoResponse
    {
        [JsonProperty("video_url")]
        public string VideoUrl { get; set; }
    }

    public class WeChatBroadcastSent
    {
        [JsonProperty("filter")]
        public Filter Filter { get; set; }

        [JsonProperty("mpnews")]
        public Mpnews Mpnews { get; set; }

        [JsonProperty("msgtype")]
        public string Msgtype { get; set; }

        [JsonProperty("send_ignore_reprint")]
        public long SendIgnoreReprint { get; set; }
    }

    public class Filter
    {
        [JsonProperty("is_to_all")]
        public bool IsToAll { get; set; }

        [JsonProperty("tag_id")]
        public long TagId { get; set; }
    }

    public class Mpnews
    {
        [JsonProperty("media_id")]
        public string MediaId { get; set; }
    }

    public class SetUpBroadcastResponse
    {
        [JsonProperty("media_id")]
        public string MediaId { get; set; }

        [JsonProperty("item")]
        public object[] Item { get; set; }
    }

    public class TagModel
    {
        [JsonProperty("tag")]
        public Tag Tag { get; set; }
    }

    public class Tag
    {
        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }
    }

    public class TagUserModel
    {
        [JsonProperty("openid_list")]
        public List<string> OpenidList { get; set; }

        [JsonProperty("tagid")]
        public long Tagid { get; set; }
    }

    public class WeChatUploadedFileResponse
    {
        [JsonProperty("media_id")]
        public string MediaId { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("item")]
        public object[] Item { get; set; }
    }

    public class SetUpBroadcastRequest
    {
        [JsonProperty("articles")]
        public List<Article> Articles { get; set; }
    }

    public class Article
    {
        [JsonProperty("title")]
        public string Title { get; set; }

        [JsonProperty("thumb_media_id")]
        public string ThumbMediaId { get; set; }

        [JsonProperty("show_cover_pic")]
        public long ShowCoverPic { get; set; }

        [JsonProperty("content")]
        public string Content { get; set; }

        [JsonProperty("content_source_url")]
        public string ContentSourceUrl { get; set; }
    }

    public class SendBroadcastResponse
    {
        [JsonProperty("errcode")]
        public long Errcode { get; set; }

        [JsonProperty("errmsg")]
        public string Errmsg { get; set; }

        [JsonProperty("msg_id")]
        public long MsgId { get; set; }

        [JsonProperty("msg_data_id")]
        public long MsgDataId { get; set; }
    }

    public class WeChatUserProfile
    {
        public int subscribe { get; set; }

        public string openid { get; set; }

        public string nickname { get; set; }

        public int sex { get; set; }

        public string language { get; set; }

        public string city { get; set; }

        public string province { get; set; }

        public string country { get; set; }

        public string headimgurl { get; set; }

        public int subscribe_time { get; set; }

        public string remark { get; set; }

        public int groupid { get; set; }

        public IList<string> tagid_list { get; set; }

        public string subscribe_scene { get; set; }

        public int qr_scene { get; set; }

        public string qr_scene_str { get; set; }
    }

    public class WxComparer : IComparer
    {
        public int Compare(object oLeft, object oRight)
        {
            string sLeft = oLeft as string;
            string sRight = oRight as string;
            int iLeftLength = sLeft.Length;
            int iRightLength = sRight.Length;
            int index = 0;
            while (index < iLeftLength && index < iRightLength)
            {
                if (sLeft[index] < sRight[index])
                {
                    return -1;
                }
                else if (sLeft[index] > sRight[index])
                {
                    return 1;
                }
                else
                {
                    index++;
                }
            }

            return iLeftLength - iRightLength;
        }
    }

    [Serializable]
    [XmlRoot(ElementName = "xml")]
    public class WeChatMessage
    {
        [XmlElement(ElementName = "ToUserName")]
        public string ToUserName { get; set; }

        [XmlElement(ElementName = "FromUserName")]
        public string FromUserName { get; set; }

        [XmlElement(ElementName = "CreateTime")]
        public string CreateTime { get; set; }

        [XmlElement(ElementName = "MsgType")]
        public string MsgType { get; set; }

        [XmlElement(ElementName = "Content")]
        public string Content { get; set; }

        [XmlElement(ElementName = "MsgId")]
        public string MsgId { get; set; }

        [XmlElement(ElementName = "PicUrl")]
        public string PicUrl { get; set; }

        [XmlElement(ElementName = "MediaId")]
        public string MediaId { get; set; }

        [XmlElement(ElementName = "Format")]
        public string Format { get; set; }
    }
}