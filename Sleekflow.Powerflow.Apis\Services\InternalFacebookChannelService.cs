using Newtonsoft.Json;
using <PERSON>_backend.ChannelDomain.Services.Facebook;
using Travis_backend.ConversationServices;
using Travis_backend.ConversationServices.ViewModels;

namespace Sleekflow.Powerflow.Apis.Services;

public interface IInternalFacebookChannelService
{
    Task ConnectFacebookChannelAsync(string sleekflowCompanyId, string accessToken);
}

public class InternalFacebookChannelService : IInternalFacebookChannelService
{
    private readonly IFacebookChannelService _facebookChannelService;
    private readonly IFacebookService _facebookService;
    private readonly ILogger<InternalFacebookChannelService> _logger;

    public InternalFacebookChannelService(
        ILogger<InternalFacebookChannelService> logger,
        IFacebookService facebookService,
        IFacebookChannelService facebookChannelService)
    {
        _logger = logger;
        _facebookService = facebookService;
        _facebookChannelService = facebookChannelService;
    }


    public async Task ConnectFacebookChannelAsync(string sleekflowCompanyId, string accessToken)
    {
        var accountInfo = await GetFacebookPagesUserHasRoleOnAsync(sleekflowCompanyId, accessToken);

        foreach (var page in accountInfo.data)
        {
            await _facebookChannelService.ConnectFacebookChannelAsync(
                sleekflowCompanyId,
                page.id,
                page.name,
                page.access_token,
                null,
                null,
                true);
        }
    }

    private async Task<AccountInfo> GetFacebookPagesUserHasRoleOnAsync(string sleekflowCompanyId, string accessToken)
    {
        try
        {
            var accountInfo = await _facebookService.GetFacebookPagesUserHasRoleOnAsync(accessToken);

            _logger.LogInformation(
                "[{MethodName}] CompanyId: {CompanyId}, Pages: {Pages}",
                nameof(ConnectFacebookChannelAsync),
                sleekflowCompanyId,
                JsonConvert.SerializeObject(accountInfo));

            return accountInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "[{MethodName}] CompanyId: {CompanyId}, Access Token: {AccessToken}, Retrieve page info error",
                nameof(ConnectFacebookChannelAsync),
                sleekflowCompanyId,
                accessToken);
            throw;
        }
    }
}