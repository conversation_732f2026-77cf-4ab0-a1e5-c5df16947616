using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.Helpers;

namespace Sleekflow.Core.Tests.Helpers;

[TestFixture]
public class WhatsappCloudApiExtendedMessageHelperTests
{
    [Test]
    public void RemoveHeaderMediaLinkWhenIdExist_HeaderWithDocumentIdAndLink_RemovesLink()
    {
        // Arrange
        var components = new List<WhatsappCloudApiTemplateMessageComponentObject>
        {
            new WhatsappCloudApiTemplateMessageComponentObject
            {
                Type = "header",
                Parameters = new List<WhatsappCloudApiParameterObject>
                {
                    new WhatsappCloudApiParameterObject
                    {
                        Type = "document",
                        Document = new WhatsappCloudApiMediaObject
                        {
                            Id = "doc_id", Link = "http://example.com/doc"
                        }
                    }
                }
            }
        };

        // Act
        var result = WhatsappCloudApiExtendedMessageHelper.RemoveHeaderMediaLinkWhenIdExist(components);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(1, result.Count);
        var headerParam = result[0].Parameters.First();
        Assert.AreEqual("document", headerParam.Type.ToLower());
        Assert.IsNotNull(headerParam.Document);
        Assert.AreEqual("doc_id", headerParam.Document.Id);
        Assert.IsNull(headerParam.Document.Link);
    }

    [Test]
    public void RemoveHeaderMediaLinkWhenIdExist_HeaderWithImageIdAndLink_RemovesLink()
    {
        // Arrange
        var components = new List<WhatsappCloudApiTemplateMessageComponentObject>
        {
            new WhatsappCloudApiTemplateMessageComponentObject
            {
                Type = "header",
                Parameters = new List<WhatsappCloudApiParameterObject>
                {
                    new WhatsappCloudApiParameterObject
                    {
                        Type = "image",
                        Image = new WhatsappCloudApiMediaObject
                        {
                            Id = "img_id", Link = "http://example.com/img.jpg"
                        }
                    }
                }
            }
        };

        // Act
        var result = WhatsappCloudApiExtendedMessageHelper.RemoveHeaderMediaLinkWhenIdExist(components);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(1, result.Count);
        var headerParam = result[0].Parameters.First();
        Assert.AreEqual("image", headerParam.Type.ToLower());
        Assert.IsNotNull(headerParam.Image);
        Assert.AreEqual("img_id", headerParam.Image.Id);
        Assert.IsNull(headerParam.Image.Link);
    }

    [Test]
    public void RemoveHeaderMediaLinkWhenIdExist_HeaderWithVideoIdAndLink_RemovesLink()
    {
        // Arrange
        var components = new List<WhatsappCloudApiTemplateMessageComponentObject>
        {
            new WhatsappCloudApiTemplateMessageComponentObject
            {
                Type = "header",
                Parameters = new List<WhatsappCloudApiParameterObject>
                {
                    new WhatsappCloudApiParameterObject
                    {
                        Type = "video",
                        Video = new WhatsappCloudApiMediaObject
                        {
                            Id = "vid_id", Link = "http://example.com/vid.mp4"
                        }
                    }
                }
            }
        };

        // Act
        var result = WhatsappCloudApiExtendedMessageHelper.RemoveHeaderMediaLinkWhenIdExist(components);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(1, result.Count);
        var headerParam = result[0].Parameters.First();
        Assert.AreEqual("video", headerParam.Type.ToLower());
        Assert.IsNotNull(headerParam.Video);
        Assert.AreEqual("vid_id", headerParam.Video.Id);
        Assert.IsNull(headerParam.Video.Link);
    }


    [Test]
    public void RemoveHeaderMediaLinkWhenIdExist_HeaderWithMediaIdOnly_DoesNotChange()
    {
        // Arrange
        var components = new List<WhatsappCloudApiTemplateMessageComponentObject>
        {
            new WhatsappCloudApiTemplateMessageComponentObject
            {
                Type = "header",
                Parameters = new List<WhatsappCloudApiParameterObject>
                {
                    new WhatsappCloudApiParameterObject
                    {
                        Type = "image",
                        Image = new WhatsappCloudApiMediaObject
                        {
                            Id = "img_id"
                        } // Link is null
                    }
                }
            }
        };
        var originalComponentsJson = Newtonsoft.Json.JsonConvert.SerializeObject(components);


        // Act
        var result = WhatsappCloudApiExtendedMessageHelper.RemoveHeaderMediaLinkWhenIdExist(components);
        var resultJson = Newtonsoft.Json.JsonConvert.SerializeObject(result);


        // Assert
        Assert.AreEqual(originalComponentsJson, resultJson); // Check if the object is unchanged
        Assert.IsNotNull(result);
        Assert.AreEqual(1, result.Count);
        var headerParam = result[0].Parameters.First();
        Assert.AreEqual("image", headerParam.Type.ToLower());
        Assert.IsNotNull(headerParam.Image);
        Assert.AreEqual("img_id", headerParam.Image.Id);
        Assert.IsNull(headerParam.Image.Link); // Still null
    }

    [Test]
    public void RemoveHeaderMediaLinkWhenIdExist_HeaderWithMediaLinkOnly_DoesNotChange()
    {
        // Arrange
        var components = new List<WhatsappCloudApiTemplateMessageComponentObject>
        {
            new WhatsappCloudApiTemplateMessageComponentObject
            {
                Type = "header",
                Parameters = new List<WhatsappCloudApiParameterObject>
                {
                    new WhatsappCloudApiParameterObject
                    {
                        Type = "image",
                        Image = new WhatsappCloudApiMediaObject
                        {
                            Link = "http://example.com/img.jpg"
                        } // Id is null
                    }
                }
            }
        };
        var originalComponentsJson = Newtonsoft.Json.JsonConvert.SerializeObject(components);


        // Act
        var result = WhatsappCloudApiExtendedMessageHelper.RemoveHeaderMediaLinkWhenIdExist(components);
        var resultJson = Newtonsoft.Json.JsonConvert.SerializeObject(result);


        // Assert
        Assert.AreEqual(originalComponentsJson, resultJson); // Check if the object is unchanged
        Assert.IsNotNull(result);
        Assert.AreEqual(1, result.Count);
        var headerParam = result[0].Parameters.First();
        Assert.AreEqual("image", headerParam.Type.ToLower());
        Assert.IsNotNull(headerParam.Image);
        Assert.IsNull(headerParam.Image.Id); // Still null
        Assert.AreEqual("http://example.com/img.jpg", headerParam.Image.Link); // Link remains
    }

    [Test]
    public void RemoveHeaderMediaLinkWhenIdExist_HeaderWithTextType_DoesNotChange()
    {
        // Arrange
        var components = new List<WhatsappCloudApiTemplateMessageComponentObject>
        {
            new WhatsappCloudApiTemplateMessageComponentObject
            {
                Type = "header",
                Parameters = new List<WhatsappCloudApiParameterObject>
                {
                    new WhatsappCloudApiParameterObject
                    {
                        Type = "text", Text = "Header Text"
                    }
                }
            }
        };
        var originalComponentsJson = Newtonsoft.Json.JsonConvert.SerializeObject(components);


        // Act
        var result = WhatsappCloudApiExtendedMessageHelper.RemoveHeaderMediaLinkWhenIdExist(components);
        var resultJson = Newtonsoft.Json.JsonConvert.SerializeObject(result);

        // Assert
        Assert.AreEqual(originalComponentsJson, resultJson); // Check if the object is unchanged
    }

    [Test]
    public void RemoveHeaderMediaLinkWhenIdExist_NoHeaderComponent_DoesNotChange()
    {
        // Arrange
        var components = new List<WhatsappCloudApiTemplateMessageComponentObject>
        {
            new WhatsappCloudApiTemplateMessageComponentObject
            {
                Type = "body",
                Parameters = new List<WhatsappCloudApiParameterObject>
                {
                    new WhatsappCloudApiParameterObject
                    {
                        Type = "text", Text = "Body Text"
                    }
                }
            }
        };
        var originalComponentsJson = Newtonsoft.Json.JsonConvert.SerializeObject(components);


        // Act
        var result = WhatsappCloudApiExtendedMessageHelper.RemoveHeaderMediaLinkWhenIdExist(components);
        var resultJson = Newtonsoft.Json.JsonConvert.SerializeObject(result);


        // Assert
        Assert.AreEqual(originalComponentsJson, resultJson); // Check if the object is unchanged
    }

    [Test]
    public void RemoveHeaderMediaLinkWhenIdExist_EmptyInputList_ReturnsEmptyList()
    {
        // Arrange
        var components = new List<WhatsappCloudApiTemplateMessageComponentObject>();

        // Act
        var result = WhatsappCloudApiExtendedMessageHelper.RemoveHeaderMediaLinkWhenIdExist(components);

        // Assert
        Assert.IsNotNull(result);
        Assert.IsEmpty(result);
    }

    [Test]
    public void RemoveHeaderMediaLinkWhenIdExist_NullInputList_ReturnsNull()
    {
        // Arrange
        List<WhatsappCloudApiTemplateMessageComponentObject> components = null;

        // Act
        var result = WhatsappCloudApiExtendedMessageHelper.RemoveHeaderMediaLinkWhenIdExist(components);

        // Assert
        Assert.IsNull(result);
    }

    [Test]
    public void RemoveHeaderMediaLinkWhenIdExist_HeaderWithMultipleParams_RemovesLinkCorrectly()
    {
        // Arrange
        var components = new List<WhatsappCloudApiTemplateMessageComponentObject>
        {
            new WhatsappCloudApiTemplateMessageComponentObject
            {
                Type = "header",
                Parameters = new List<WhatsappCloudApiParameterObject>
                {
                    // This one should have its link removed
                    new WhatsappCloudApiParameterObject
                    {
                        Type = "image",
                        Image = new WhatsappCloudApiMediaObject
                        {
                            Id = "img_id_1", Link = "http://example.com/img1.jpg"
                        }
                    },
                    // This one should remain unchanged (no ID)
                    new WhatsappCloudApiParameterObject
                    {
                        Type = "image",
                        Image = new WhatsappCloudApiMediaObject
                        {
                            Link = "http://example.com/img2.jpg"
                        }
                    }
                }
            },
            new WhatsappCloudApiTemplateMessageComponentObject
            {
                Type = "BODY", // Different type, should be ignored
                Parameters = new List<WhatsappCloudApiParameterObject>
                {
                    new WhatsappCloudApiParameterObject
                    {
                        Type = "text", Text = "Body Text"
                    }
                }
            }
        };

        // Act
        var result = WhatsappCloudApiExtendedMessageHelper.RemoveHeaderMediaLinkWhenIdExist(components);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(2, result.Count); // Both components should still be there

        // Check first header parameter (link removed)
        var headerParam1 = result[0].Parameters[0];
        Assert.AreEqual("image", headerParam1.Type.ToLower());
        Assert.IsNotNull(headerParam1.Image);
        Assert.AreEqual("img_id_1", headerParam1.Image.Id);
        Assert.IsNull(headerParam1.Image.Link);

        // Check second header parameter (unchanged)
        var headerParam2 = result[0].Parameters[1];
        Assert.AreEqual("image", headerParam2.Type.ToLower());
        Assert.IsNotNull(headerParam2.Image);
        Assert.IsNull(headerParam2.Image.Id);
        Assert.AreEqual("http://example.com/img2.jpg", headerParam2.Image.Link);

        // Check body component (unchanged)
        Assert.AreEqual("BODY", result[1].Type); // Case preserved?
    }

    [Test]
    public void RemoveHeaderMediaLinkWhenIdExist_HeaderWithNonMediaParams_DoesNotChange()
    {
        // Arrange
        var components = new List<WhatsappCloudApiTemplateMessageComponentObject>
        {
            new WhatsappCloudApiTemplateMessageComponentObject
            {
                Type = "header",
                Parameters = new List<WhatsappCloudApiParameterObject>
                {
                    new WhatsappCloudApiParameterObject
                    {
                        Type = "text", Text = "param1"
                    },
                }
            }
        };
        var originalComponentsJson = Newtonsoft.Json.JsonConvert.SerializeObject(components);

        // Act
        var result = WhatsappCloudApiExtendedMessageHelper.RemoveHeaderMediaLinkWhenIdExist(components);
        var resultJson = Newtonsoft.Json.JsonConvert.SerializeObject(result);

        // Assert
        Assert.AreEqual(originalComponentsJson, resultJson); // Check if the object is unchanged
    }
}