using Travis_backend.CompanyDomain.Models;
using Travis_backend.ConversationDomain.ConversationSettingsConstants;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.StaffAccessControlAggregates.Extensions;

[TestFixture]
public class HasRemainAsCollaboratorWhenReassignedSettingEnabledTests
{
    private static readonly string CompanyId = "sleekflow";

    [Test]
    public void null_staff_returns_false()
    {
        // Arrange
        StaffAccessControlAggregate staff = null;

        // Act
        var result = staff.RemainAsCollaboratorWhenReassignedSettingEnabled();

        // Assert
        Assert.IsFalse(result);
    }

    // Bug fix: DEVS-15122
    [Test]
    public void staff_with_no_rbac_roles_and_no_role_permission_returns_false()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = null,
            RolePermission = null
        };

        // Act
        var result = staff.RemainAsCollaboratorWhenReassignedSettingEnabled();

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void staff_with_no_rbac_roles_and_setting_disabled_returns_false()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = null,
            RolePermission = new RolePermission
            {
                StoredPermission = new Permission
                {
                    AddAsCollaboratorWhenAssignedToOthers = false
                }
            }
        };

        // Act
        var result = staff.RemainAsCollaboratorWhenReassignedSettingEnabled();

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void staff_with_no_rbac_roles_and_setting_enabled_returns_true()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = null,
            RolePermission = new RolePermission
            {
                StoredPermission = new Permission
                {
                    AddAsCollaboratorWhenAssignedToOthers = true
                }
            }
        };

        // Act
        var result = staff.RemainAsCollaboratorWhenReassignedSettingEnabled();

        // Assert
        Assert.IsTrue(result);
    }

    [Test]
    public void staff_with_rbac_roles_but_no_setting_returns_false()
    {
        var permissions = new List<string>();

        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = CompanyId,
                    RbacRolePermissions = permissions
                }
            }
        };

        // Act
        var result = staff.RemainAsCollaboratorWhenReassignedSettingEnabled();

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void staff_with_rbac_roles_containing_setting_returns_true()
    {
        var permissions = new List<string>
        {
            RbacInboxSettings.RemainAsCollaboratorWhenReassigned
        };

        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = CompanyId,
                    RbacRolePermissions = permissions
                }
            }
        };

        // Act
        var result = staff.RemainAsCollaboratorWhenReassignedSettingEnabled();

        // Assert
        Assert.IsTrue(result);
    }

    [Test]
    public void staff_with_multiple_rbac_roles_containing_setting_returns_true()
    {
        // Arrange
        var permissions = new List<string>
        {
            RbacInboxSettings.RemainAsCollaboratorWhenReassigned
        };

        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = CompanyId,
                    RbacRolePermissions = permissions
                },
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole2",
                    SleekflowCompanyId = CompanyId,
                    RbacRolePermissions = permissions
                }
            }
        };

        // Act
        var result = staff.RemainAsCollaboratorWhenReassignedSettingEnabled();

        // Assert
        Assert.IsTrue(result);
    }

    [Test]
    public void staff_with_empty_rbac_roles_list_returns_false()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>()
        };

        // Act
        var result = staff.RemainAsCollaboratorWhenReassignedSettingEnabled();

        // Assert
        Assert.IsFalse(result);
    }
}