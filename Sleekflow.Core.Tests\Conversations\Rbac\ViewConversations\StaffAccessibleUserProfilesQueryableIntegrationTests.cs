using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using TestContainers.Container.Abstractions.Hosting;
using TestContainers.Container.Database.Hosting;
using TestContainers.Container.Database.MsSql;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationQueryables;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;

namespace Sleekflow.Core.Tests.Conversations.Rbac.ViewConversations;

[TestFixture]
public class StaffAccessibleUserProfilesQueryableIntegrationTests
{
    private MsSqlContainer _azureSqlContainer;
    private BaseDbContext _dbContext;
    private const string CompanyId = "sleekflow";

    [OneTimeSetUp]
    public async Task OneTimeSetUp()
    {
        // Set up the containerized test database
        _azureSqlContainer = new ContainerBuilder<MsSqlContainer>()
            .ConfigureDatabaseConfiguration("testdb", "SAERsdf123456!!", "YOURpassword123")
            .ConfigureDockerImageName("mcr.microsoft.com/azure-sql-edge:latest")
            .Build();

        try
        {
            await _azureSqlContainer.StartAsync(); // Use 'await' instead of .Wait()
            Console.WriteLine("Azure SQL TestContainer started successfully.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error starting Azure SQL TestContainer: {ex}");
            throw;
        }
        var options = new DbContextOptionsBuilder<BaseDbContext>()
            .UseSqlServer(_azureSqlContainer.GetConnectionString() + ";TrustServerCertificate=true;Encrypt=true")
            .Options;

        _dbContext = new BaseDbContext(options);

        Console.WriteLine("Ensuring database schema is created...");
        await _dbContext.Database.EnsureCreatedAsync(); // Creates schema without migrations
        Console.WriteLine("Database schema created successfully.");

        var company = new Company
        {
            Id = "TestCompanyId",
        };

        _dbContext.CompanyCompanies.Add(company);

        await SetupTestData();
    }

    private async Task SetupTestData()
    {
        // Add test company
        var company = new Company { Id = CompanyId };
        await _dbContext.CompanyCompanies.AddAsync(company);
        await _dbContext.SaveChangesAsync();

        var teamA = new CompanyTeam
        {
            CompanyId = company.Id, TeamName = "Team A",
        };

        var teamB = new CompanyTeam
        {
            CompanyId = company.Id, TeamName = "Team A",
        };

        await _dbContext.CompanyStaffTeams.AddRangeAsync(teamA, teamB);
        await _dbContext.SaveChangesAsync();

        var staffA = new Staff
        {
            CompanyId = CompanyId
        };

        var staffB = new Staff
        {
            CompanyId = CompanyId
        };

        var staffC = new Staff
        {
            CompanyId = CompanyId
        };

        var staffD = new Staff
        {
            CompanyId = CompanyId
        };

        await _dbContext.UserRoleStaffs.AddRangeAsync(staffA, staffB, staffC, staffD);
        await _dbContext.SaveChangesAsync();

        var teamATeamMemberA = new TeamMember
        {
            CompanyTeamId = teamA.Id, StaffId = staffA.Id
        };

        var teamATeamMemberB = new TeamMember
        {
            CompanyTeamId = teamA.Id, StaffId = staffB.Id
        };

        var teamBTeamMember = new TeamMember
        {
            CompanyTeamId = teamA.Id, StaffId = staffC.Id
        };

        await _dbContext.CompanyTeamMembers.AddRangeAsync(teamATeamMemberA, teamATeamMemberB, teamBTeamMember);
        await _dbContext.SaveChangesAsync();

        // TODO: Add UserProfile Test Cases


        // var assignedToConversationId = "assigned_to_me_conversation";
        // var assignedToMeUserprofileId = UserProfileTestCases.AssignedToMeUserProfile;
        // var assignedToMeUserprofile = CreateUserProfile(assignedToMeUserprofileId);
        // var assignedToMeConversation = CreateConversation(id: assignedToConversationId, assigneeId: staffA.Id, userProfileId: assignedToMeUserprofileId);
        //
        // var assignedToMyTeamConversation = CreateConversation();
        // var assignedToMyTeamUserProfile = CreateUserProfile(id: assignedToMeUserprofileId);

        // // Add all test data to context
        // await _dbContext.UserProfiles.AddRangeAsync(assignedToMeUserprofile);
        // await _dbContext.Conversations.AddRangeAsync(assignedToMeConversation);
        // await _dbContext.SaveChangesAsync();
    }

    [OneTimeTearDown]
    public async Task OneTimeTearDown()
    {
        await _dbContext.DisposeAsync();
        await _azureSqlContainer.StopAsync(); // Ensures proper container cleanup
    }

    [Test]
    public void Test_AddUserProfile()
    {
        var userProfileId = Guid.NewGuid().ToString();

        // Arrange
        var userProfile = new UserProfile
        {
            Id = userProfileId,
            CompanyId = "TestCompanyId",
            PhoneNumber = "1234567890",
            FirstName = "Test User",
            LastName = "Leo Ip"
        };

        var userProfile2 = new UserProfile
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = "TestCompanyId",
            PhoneNumber = "1234567890",
            FirstName = "Test User",
            LastName = "Leo Ip"
        };

        var conversation = new Conversation
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = "TestCompanyId",
            UserProfileId = userProfileId
        };

        // Act
        _dbContext.UserProfiles.Add(userProfile);
        _dbContext.Conversations.Add(conversation);
        _dbContext.SaveChanges();

        _dbContext.UserProfiles.Add(userProfile2);
        _dbContext.SaveChanges();

        // Assert
        var retrievedUserProfile = _dbContext.UserProfiles.Find(userProfile.Id);
        Assert.IsNotNull(retrievedUserProfile);
        Assert.That(retrievedUserProfile.FullName, Is.EqualTo(userProfile.FullName));
    }

    [Test]
    public async Task can_fetch_expected_userprofile_for_staff_with_no_permission()
    {
        var expectedVisibleUserProfileIds = new List<string>
        {
            UserProfileTestCases.AssignedToMeUserProfile,
            UserProfileTestCases.AssignedToAssociatedTeamOnlyUserProfile
        };

        var staff = CreateStaff(
            new List<string>
            {
                RbacViewConversationsPermissions.AssignedToMe
            });


        var builder = new StaffAccessibleUserProfilesQueryableBuilder(_dbContext, staff);
        var queryable = builder.Build();

        var result = await queryable.ToListAsync();

        var resultIds = result.Select(r => r.Id).ToList();
        var unexpectedIds = resultIds.Except(expectedVisibleUserProfileIds).ToList();
        var missingIds = expectedVisibleUserProfileIds.Except(resultIds).ToList();

        TestContext.WriteLine($"\nResult contains {result.Count} profiles, expected {expectedVisibleUserProfileIds.Count}");

        if (unexpectedIds.Count != 0)
        {
            TestContext.WriteLine("Unexpected profiles found:");
            foreach (var id in unexpectedIds)
            {
                var profile = result.First(p => p.Id == id);
                TestContext.WriteLine($"- {profile.Id} ({profile.FirstName} {profile.LastName})");
            }
        }

        if (missingIds.Count != 0)
        {
            TestContext.WriteLine("Expected profiles not found:");
            foreach (var id in missingIds)
            {
                TestContext.WriteLine($"- {id}");
            }
        }

        // Assert.Multiple(() =>
        // {
        //     Assert.That(unexpectedIds, Is.Empty, "Found profiles that should not be visible");
        //     Assert.That(missingIds, Is.Empty, "Expected profiles were not found");
        //     Assert.That(resultIds, Is.EquivalentTo(expectedVisibleUserProfileIds), "Result does not match expected profiles exactly");
        // });
    }

    [Test]
    public async Task can_fetch_userprofile_for_staff_with_assigned_to_me_permission_assigned_to_my_team_permission()
    {
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            RbacRoles = new List<RbacRole>()
            {
                new ()
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe,
                        RbacViewConversationsPermissions.AssignedToMyTeam,
                    ]
                }
            }
        };

        var builder = new StaffAccessibleUserProfilesQueryableBuilder(_dbContext, staff);
        var queryable = builder.Build();

        var userProfileId = Guid.NewGuid().ToString();

        // Arrange
        var userProfile = new UserProfile
        {
            Id = userProfileId,
            CompanyId = "TestCompanyId",
            PhoneNumber = "1234567890",
            FirstName = "Test User",
            LastName = "Leo Ip"
        };

        var userProfile2 = new UserProfile
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = "TestCompanyId",
            PhoneNumber = "1234567890",
            FirstName = "Test User",
            LastName = "Leo Ip"
        };

        var conversation = new Conversation
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = "TestCompanyId",
            UserProfileId = userProfileId
        };

        _dbContext.UserProfiles.Add(userProfile);
        _dbContext.Conversations.Add(conversation);
        await _dbContext.SaveChangesAsync();

        _dbContext.UserProfiles.Add(userProfile2);
        await _dbContext.SaveChangesAsync();

        // Assert
        var retrievedUserProfile = await _dbContext.UserProfiles.FindAsync(userProfile.Id);
        Assert.IsNotNull(retrievedUserProfile);
        Assert.That(retrievedUserProfile.FullName, Is.EqualTo(userProfile.FullName));

        Expression<Func<UserProfile, bool>> expression = up =>
            up.CompanyId == userProfile.CompanyId;

        queryable = queryable.Where(expression);

        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(queryable.ToQueryString());
        var result = await queryable.ToListAsync();
        Assert.That(result.Count, Is.EqualTo(0) );
    }

    private static StaffAccessControlAggregate CreateStaff(List<string>? permissions = null)
    {
        permissions ??= [];

        var teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long> { 1, 2, 3 }
        };

        return new StaffAccessControlAggregate
        {
            StaffId = 1,
            CompanyId = CompanyId,
            AssociatedTeams = new List<TeamAccessControlAggregate> { teamA },
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = CompanyId,
                    RbacRolePermissions = permissions
                }
            }
        };
    }

    private static Conversation CreateConversation(
        string id,
        long? assigneeId = null,
        long? assignedTeamId = null,
        List<AdditionalAssignee>? additionalAssignees = null,
        List<Mention>? mentions = null,
        string? userProfileId = null)
    {
        return new Conversation
        {
            Id = id,
            CompanyId = CompanyId,
            AssigneeId = assigneeId,
            AssignedTeamId = assignedTeamId,
            AdditionalAssignees = additionalAssignees,
            Mentions = mentions,
            UserProfileId = userProfileId
        };
    }

    private static UserProfile CreateUserProfile(string id)
    {
        return new UserProfile
        {
            Id = id,
            CompanyId = CompanyId,
            PhoneNumber = "1234567890",
            FirstName = "Test User",
            LastName = "Leo Ip"
        };
    }

    private static class UserProfileTestCases
    {
        public const string AssignedToMeUserProfile = "assigned_to_me_userprofile";
        public const string AssignedToAssociatedTeamOnlyUserProfile = "assigned_to_associated_team_only_userprofile";
        public const string AssignedToMeAndAssociatedTeamUserProfile = "assigned_to_me_and_associated_team_userprofile";
    }
}