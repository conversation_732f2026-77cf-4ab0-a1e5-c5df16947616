using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Perf.Components.Configs;

public class SqlDbConfig
{
    [JsonProperty("connection_string")]
    public string ConnectionString { get; set; }

    [JsonProperty("read_connection_string")]
    public string ReadConnectionString { get; set; }

    [JsonProperty("analytic_db_connection_string")]
    public string AnalyticDbConnectionString { get; set; }

    public SqlDbConfig(string connectionString, string readConnectionString, string analyticDbConnectionString)
    {
        ConnectionString = connectionString;
        ReadConnectionString = readConnectionString;
        AnalyticDbConnectionString = analyticDbConnectionString;
    }
}