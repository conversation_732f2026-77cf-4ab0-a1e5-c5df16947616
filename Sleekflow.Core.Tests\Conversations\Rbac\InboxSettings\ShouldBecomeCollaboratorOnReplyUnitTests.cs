using Travis_backend.ConversationDomain.ConversationAccessControl;
using <PERSON>_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationSettingsConstants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Rbac.InboxSettings;

[TestFixture]
public class ShouldBecomeCollaboratorOnReplyUnitTests
{
    private IInboxSettingManager _inboxSettingManager;

    [SetUp]
    public void SetUp()
    {
        _inboxSettingManager = new InboxSettingManager();
    }

    [Test]
    public void staff_is_null_return_false()
    {
        // Arrange
        StaffAccessControlAggregate staff = null;
        var conversation = new Conversation();

        // Act
        bool result = _inboxSettingManager.ShouldBecomeCollaboratorOnReply(staff, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void conversation_is_null_return_false()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate();
        Conversation conversation = null;

        // Act
        bool result = _inboxSettingManager.ShouldBecomeCollaboratorOnReply(staff, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    //BecomeCollaboratorWhenReply

    [Test]
    public void staff_with_no_become_collaborator_when_reply_setting_cannot_become_collaborator_when_reply()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacAssignConversationsPermissions.AssignToMe
                    }
                }
            }
        };
        var conversation = new Conversation
        {
            AssigneeId = 1,
            AdditionalAssignees = new List<AdditionalAssignee>
            {
                new AdditionalAssignee
                {
                    AssigneeId = 1
                }
            }
        };

        // Act
        bool result = _inboxSettingManager.ShouldBecomeCollaboratorOnReply(staff, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void staff_with_no_assign_conversation_permission_cannot_become_collaborator_when_reply()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacInboxSettings.BecomeCollaboratorWhenReply
                    }
                }
            }
        };
        var conversation = new Conversation
        {
            AssigneeId = 1,
            AdditionalAssignees = new List<AdditionalAssignee>
            {
                new AdditionalAssignee
                {
                    AssigneeId = 1
                }
            }
        };

        // Act
        bool result = _inboxSettingManager.ShouldBecomeCollaboratorOnReply(staff, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void staff_not_a_collaborator_and_with_assign_conversation_to_me_permission_and_has_become_collaborator_when_reply_setting_cannot_become_collaborator_when_reply()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacInboxSettings.BecomeCollaboratorWhenReply,
                        RbacAssignConversationsPermissions.AssignToMe
                    }
                }
            }
        };
        var conversation = new Conversation
        {
            AssigneeId = 1, AdditionalAssignees = new List<AdditionalAssignee>()
        };

        // Act
        bool result = _inboxSettingManager.ShouldBecomeCollaboratorOnReply(staff, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void staff_not_a_collaborator_and_with_assign_conversation_to_any_user_permission_and_has_become_collaborator_when_reply_setting_cannot_become_collaborator_when_reply()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacInboxSettings.BecomeCollaboratorWhenReply,
                        RbacAssignConversationsPermissions.AssignToAnyUser
                    }
                }
            }
        };
        var conversation = new Conversation
        {
            AssigneeId = 1, AdditionalAssignees = new List<AdditionalAssignee>()
        };

        // Act
        bool result = _inboxSettingManager.ShouldBecomeCollaboratorOnReply(staff, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void staff_already_a_collaborator_cannot_become_collaborator_when_reply()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacInboxSettings.BecomeCollaboratorWhenReply,
                        RbacAssignConversationsPermissions.AssignToAnyUser
                    }
                }
            }
        };
        var conversation = new Conversation
        {
            AssigneeId = 1, AdditionalAssignees = new List<AdditionalAssignee> { new AdditionalAssignee {AssigneeId = 1}}
        };

        // Act
        bool result = _inboxSettingManager.ShouldBecomeCollaboratorOnReply(staff, conversation);

        // Assert
        Assert.IsFalse(result);
    }
}