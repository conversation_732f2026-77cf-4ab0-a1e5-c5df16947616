﻿using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.Utils;

namespace Sleekflow.Core.Tests.Analytics;

[TestFixture]
public class ConversationAnalyticsMetricUtilsTests
{
    [Test]
    public void Sanitize_RemovesMetricsMarkedForDeletion_ShouldRemoveTobeDeletedRecord()
    {
        // Arrange
        var metrics = new List<ConversationAnalyticsMetric>
        {
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"asp_net_user_id\":\"user1\"}", ToBeDeleted = 0 },
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"asp_net_user_id\":\"user2\"}", ToBeDeleted = 0 },
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"asp_net_user_id\":\"user1\"}", ToBeDeleted = 1 }, // Duplicate but not deleted
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"asp_net_user_id\":\"user1\"}", ToBeDeleted = 1 } // Duplicate but not deleted
        };

        // Act
        ConversationAnalyticsMetricUtils.Sanitize(ref metrics);

        // Assert
        Assert.AreEqual(2, metrics.Count);
        Assert.IsTrue(metrics.Exists(m => m.DimensionDataJson == "{\"asp_net_user_id\":\"user2\"}"));
        Assert.IsTrue(metrics.Exists(m => m.DimensionDataJson == "{\"asp_net_user_id\":\"user1\"}")); // The non-deleted one
    }

    [Test]
    public void Sanitize_KeptUniqueMetrics_ShouldRemoveDuplicateRecord()
    {
        // Arrange
        var metrics = new List<ConversationAnalyticsMetric>
        {
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"asp_net_user_id\":\"user1\"}", ToBeDeleted = 1 },
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"asp_net_user_id\":\"user1\"}", ToBeDeleted = 1 }, // Duplicate
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"asp_net_user_id\":\"user2\"}", ToBeDeleted = 1 }
        };

        // Act
        ConversationAnalyticsMetricUtils.Sanitize(ref metrics);

        // Assert
        Assert.AreEqual(2, metrics.Count); // Should only keep unique ones
        Assert.IsTrue(metrics.Exists(m => m.DimensionDataJson == "{\"asp_net_user_id\":\"user1\"}"));
        Assert.IsTrue(metrics.Exists(m => m.DimensionDataJson == "{\"asp_net_user_id\":\"user2\"}"));
    }

    [Test]
    public void Sanitize_HandlesEmptyList()
    {
        // Arrange
        var metrics = new List<ConversationAnalyticsMetric>();

        // Act
        ConversationAnalyticsMetricUtils.Sanitize(ref metrics);

        // Assert
        Assert.AreEqual(0, metrics.Count); // Should remain empty
    }
}