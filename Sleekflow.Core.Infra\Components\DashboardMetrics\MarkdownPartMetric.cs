﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class MarkdownPartMetric : IDashboardMetric
{
    private readonly string _content;

    public MarkdownPartMetric(string content)
    {
        _content = content;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs = new InputList<object>(),
                Type = "Extension/HubsExtension/PartType/MarkdownPart",
                Settings =
                {
                    new Dictionary<string, object>
                    {
                        {
                            "content", new Dictionary<string, object>
                            {
                                {
                                    "settings", new Dictionary<string, object>
                                    {
                                        {
                                            "content", _content
                                        },
                                        {
                                            "markdownUri", ""
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}