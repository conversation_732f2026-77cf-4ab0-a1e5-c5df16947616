using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.StaffAccessControlAggregates.Extensions;

[TestFixture]
public class HasAccessToViewOtherStaffAsContactOwnerConversationsOnlyUnitTests
{
    [Test]
    public void Staff_Is_Null_Return_False()
    {
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>()
        };

        bool result = staff.HasAccessToViewOtherStaffAsContactOwnerConversationsOnly();

        Assert.IsFalse(result);
    }

    [Test]
    public void Staff_RbacRoles_Is_Null_Return_False()
    {
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = null
        };
        var tryingToAccessStaff = new StaffAccessControlAggregate
        {
            StaffId = 1
        };

        bool result = staff.HasAccessToViewOtherStaffAsContactOwnerConversationsOnly();

        Assert.IsFalse(result);
    }

    [Test]
    public void Staff_Has_View_Conversations_Full_Access_Return_False()
    {
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = RbacViewConversationsPermissions.GetAll.ToList()
                }
            }
        };

        bool result = staff.HasAccessToViewOtherStaffAsContactOwnerConversationsOnly();

        Assert.IsFalse(result);
    }

    [Test]
    public void Staff_Has_All_Assigned_Conversations_Permission_But_No_Assigned_To_My_Team_Permission_Return_True()
    {
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacViewConversationsPermissions.AllAssignedConversations
                    }
                }
            }
        };

        bool result = staff.HasAccessToViewOtherStaffAsContactOwnerConversationsOnly();

        Assert.IsTrue(result);
    }

    [Test]
    public void Staff_Has_All_Assigned_Conversations_Permission_And_Also_Has_Assigned_To_My_Team_Permission_Return_False()
    {
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacViewConversationsPermissions.AllAssignedConversations,
                        RbacViewConversationsPermissions.AssignedToMyTeam
                    }
                }
            }
        };

        bool result = staff.HasAccessToViewOtherStaffAsContactOwnerConversationsOnly();

        Assert.IsFalse(result);
    }

    [Test]
    public void Staff_Has_Assigned_To_My_Team_Permission_Return_False()
    {
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                new TeamAccessControlAggregate
                {
                    Id = 1
                }
            },
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacViewConversationsPermissions.AssignedToMyTeam
                    }
                }
            }
        };

        bool result = staff.HasAccessToViewOtherStaffAsContactOwnerConversationsOnly();

        Assert.IsFalse(result);
    }

    [Test]
    public void Staff_No_Relevant_Permissions_Return_False()
    {
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        "SomeOtherPermission"
                    }
                }
            }
        };

        bool result = staff.HasAccessToViewOtherStaffAsContactOwnerConversationsOnly();

        Assert.IsFalse(result);
    }
}