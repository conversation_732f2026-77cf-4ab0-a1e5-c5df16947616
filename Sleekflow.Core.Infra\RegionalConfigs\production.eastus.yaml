location_name: east<PERSON>
sku_config:
  sleekflow_core:
    name: P3V3
    tier: PremiumV3
  sleekflow_core_db:
    name: HS_PRMS
    tier: Hyperscale
    family: PRMS
    capacity: 2
  sleekflow_analytic_db:
    name: HS_S_Gen5
    tier: Hyperscale
    family: Gen5
    capacity: 2
  sleekflow_powerflow:
    name: P1V3
    tier: PremiumV3
  sleekflow_sleek_pay:
    name: P0V3
    tier: PremiumV3
  redis:
    default:
      name: Standard
      family: C
      capacity: 1
    caching:
      name: Standard
      family: C
      capacity: 1
sql_db_config:
  administrator_login_random_secret: nBcVapo4UqQmUrKPhCQ1qzOpp7Y+ocgkkWbS2ebUGHFhdEiXBKAdYgs3jP1+qIJY2AWouAba68k592NE
  administrator_login_password_random_secret: 7lajwUZr099YJeU8ob2az3q0xep1msoaeG6JIlnGVaMokFK/nNPv/hkmOGV5+E5xP4tYhdfO4FBjx3Tq
  whitelist_ip_ranges:
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: *************
      end_ip_address: *************
    - start_ip_address: *************
      end_ip_address: *************
    - start_ip_address: **************
      end_ip_address: **************
  is_read_scale_enable: "false"
  high_availability_replica_count: 0
vnet:
  default_address_space: ********/16
  default_subnet_address_prefix: ********/24
  sleekflow_core_db_address_prefix: ********/24
  sleekflow_core_address_prefix: ********/24
  sleekflow_powerflow_address_prefix: ********/24
  sleekflow_sleek_pay_address_prefix: ********/24
  sleekflow_core_worker_address_prefix: ********/24
auto_scale_config:
  sleekflow_core:
    capacity:
      default: "3"
      maximum: "20"
      minimum: "2"
    scale_out_instances: "1"
    scale_down_instances: "1"
sleekflow_core_config:
  aspnetcore_environment: production
  logger:
    gcp_is_enabled: "TRUE"
    gcp_project_id: my-production-project-405815
    gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  audit_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/audit-hub
    key: lHhkPbxYflu9a1xmRpsrizQT8QyTk8qoQ+1JqOaCI88wzlxI3q8+zw+8An7DqvbDhcwzVz9ZXeksOtG5V/+ral5yVsyDLmj8UFWa14x+A/Q=6amzzwvdxFdU2c33
  auth0:
    action_audience: https://api.sleekflow.io/
    action_issuer: https://sso.sleekflow.io/
    audience: https://api.sleekflow.io
    client_id: txLGs6X2eN17XOXuxZpDakoxpyxoOlHW
    client_secret: T5mXSCldVruEX58ZoZL3O9jccdflARqVm2ga1xkJPh4Np+TJvQqz/eDo0vaJsm0cJxCTZjp6ekZ04CEpFxrqpHrgQ03vr3CNZbh1Vgvsc4E=YLDBdOB=hYiBA0aw
    database_connection_name: Sleekflow-Username-Password-Authentication
    domain: sleekflow.eu.auth0.com
    http_retries: 10
    issuers:
      - https://sso.sleekflow.io/
      - https://sleekflow.eu.auth0.com
    namespace: https://app.sleekflow.io/
    role_claim_type: roles
    user_email_claim_type: email
    user_id_claim_type: user_id
    username_claim_type: user_name
    tenant_hub_secret_key: umx+ozivdACFgEQu+SP/pA7ZUsD2REvKmndk77gzHByBcoOh5HC2WUy+8Y5jNwtx0DJLs/F2TPVnNUlk3amvNHkHoqhoWJ8LBtjo4MNz22M=C0thch5TVPY0wJdq
    health_check:
      is_enabled: "false"
      client_id: OrxDhNRbAyWXKoSVoMuS0hIWNo3v2An0
      client_secret: oUZv2NmxC5I23E5iRwXvbHhSmgTmctdpTxc5bGXCVTlVbyqqDj6vk7ocRgoteaJ+1k9jUX5wnZ1jH70Bzc8QXgg92l+iHfFMCC4IDN/JhCw=nl1LEsbA3DkZS0Fr
      username: *******
      password: dsfogDGSDGeodfhghdfo23o2
  azure:
    media_service:
      account_name: sfmediaproduction
      client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
      client_secret: NToqABkDNntf5d+jSiiqWpsZdtabrVAgtud6QFkONYQsQhCgh1awnRIbnhFCsoXB6Fa6gZCAS4OKV5Ss
      resource_group: sleekflow-resource-group-production853b96c8
      subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
      tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
    text_analytics_credentials: 226f2d8217704dd6b223b4a1fd6e51c6
    text_analytics_url: https://sleekflow-prod-sp-text-analytics.cognitiveservices.azure.com/
  application_insights:
    is_telemetry_tracer_enabled: "true"
    is_sampling_disabled: "false"
  beamer:
    api_key: 6JleQZTX2PjtfV2lamd8R+r9P6iOogj1Tatnnt3XfEh0vCvHm3mEgFumCMQIzpYEDMxr21k2lc5p2m2R
    api_url: https://api.getbeamer.com/v0/nps
    webhook_verify_key: 7KASDMgETnnhbd7RUeE5WN6MmtQEepgIT1IQAEWqGeC7CCKTfCkct9l1hDLMD6OqGIaTwv1NE7ITg7DIZlqfUCdqChUl4FZsHtoH66A75q0=IRFkFeLg0PQwpRs9
  chat_api:
    api_key: osRXxWXClvqe2SLrhUmDfT1HLn4L4nWZ8pS3Jy/CFLs=ZyVEK2SLgiMsZhvG
    api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
  commerce_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/commerce-hub
    key: /y+vt4EpOfxbJ4zPKPnrOQT+mbBA5u77tus1NX2nRzeJCv1O8yFbIzppTUmiz5at9wxLoZo0ItRnmTkS2gBe6abh8mkNbanAWUkbE+IbNS6rLYK7yXErIo46Hj9ftVaxFpmLo0ErfeHmMhYQVIsV0KddNwtMBkDyxmyxANCtewkmO3JAU7ckelE08ct7BCjwLigNXYiHfK4NyaPh
  crm_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/crm-hub
    key: SIyIXeZKESHiIaT/euPFa/N/cWyYhus7kQ08YtrGCpqqShoxur25ndKGTqcxSFu81ORRTIHwiyP8Crpu
  data_snapshot:
    is_enable: "true"
  development:
    redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
  environment_features:
    is_recurring_job_enabled: "true"
  epplus:
    excel_package:
      license_context: NonCommercial
  facebook:
    client_id: 812364635796464
    client_secret: drHg2K5tgBOgzu73VfS3k556zRuthiDYGhb3tiAMBxgGyYQFRbA46oy6BQNjarbEtYFK1gVdtWyTBBwI
  ffmpeg:
    ffmpeg_exe_name: /usr/bin/ffmpeg
  flow_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/flow-hub
    key: c3Oab7++7oueqUn15h1UFfnNZOzvINOwdHReBk49qisv6TqHwnW53Rau8i7PnLNDBBtp6uvhX7ATwqnjiAu83oWrDoD5NOjZOZackqG8t58=TplqmkcMgG7MPrTM
  hub_spot:
    internal_hub_spot_api_key: Y/FmgUafERe3Hi1Q5hcYLxjZ/pGYJOYApfQq1Ljlc8WM8wig/iuZduNfuzxTjNL78N5qdjb5G0iRK3Rp
    is_enable: "true"
  instrumentation_engine_extension_version: disabled
  internal_google_cloud:
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  general_google_cloud:
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    project_id: my-production-project-405815
    server_location: us-west1
    google_storage_bucket_name: sleekflow-transcoder-prod-us
  ip_look_up:
    key: g8ENVvzk0kI9cY03IYjrlh7Ec339b86EhpPwbLIk3fydzdmOTX7N1YZZHDRBpUPMa5mjZxI2d4xI4717
  intelligent_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/intelligent-hub
    key: DPTv1t2K9sA+ByA7KXBOJWPHCcAn1oWmd3WKezEXbaK2UXCRJT1nNVNlmEOGu8yGWVSxko+zzJvS3hlLhgNk+zDor6UXw0QMIk6S7hJuSY0=RB0CDjID=JVaeZGQ
  webhook_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/webhook-hub
    key: ewmGEiDRH/usUjeK3EipyDMhq5i0ua6VdVuoFMJ0e+UrQfiCAjYFFyCwPF+L1mNzoR20JvMa69l9o0xHRihUXslPBBIXUJ5VRAb8W6PNna4=tBdV22epwDuRqoyi
    auth_secret_key: KJfVE1W2xLcKxNcNwsrum/BcueocbEUhw/1KfB+ppW8fAplY1PMqboCdrhqSCg6mW3AaqfQXCNZIzNUbTJqi6d8M7pqRYdy8lDhm6Roy5BI=1hrvCQiq26vMomTi
  message_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/messaging-hub
    key: SDYtAWM8XVKvmY9lSObeJ6kOPxxNOMRvcYfVts0umarjRmanLjTpj8SIMM6NGVLIlNoDXwTN5LOhpUNc
  mixpanel:
    token: ef72dd13a0ffccb584cfdf75d3160e25
  mobile_app_management_extension_version: latest
  notification_hub:
    connection_string: Endpoint=sb://sleekflowproduction.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=pbzIBQQoNB8rGgrHdt1vmCIuTIB/Aaey5iION3eiCbQ=
    hub_name: SleekflowProduction
  public_api_gateway:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/public-api-gateway
    key: pd2MHSphmcuIqf41t8UrI41GWT8V2PxvvbLJd5egwXD6+S8HjLaKKOGiHa7E6+qoLiONjhDXLVHOKHoo
  reseller:
    domain_name: https://partner.sleekflow.io
  rewardful:
    api_secret: HHfdQ9lYJWfPoa9s5xKeOmaxLFR+4VxpIvfnbCLrzJQYyTgqh/yOd/MPhg+oMXtju1nFQ2knn7o4Zyvq
  salesforce:
    custom_active_web_app: https://sfmc-custom-activity.vercel.app
  share_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/share-hub
    key: ooNrXHIUtrozTyQtalmRe2lbxzztl2BdFH2kEyqjNM9T07abrj63Vkwz9Jl3fH8qk05ojbWnoSmGOIzh
  shopify:
    shopify_api_key: JXlag+FZICLP/NrcxdQnXdEwtDkF9XGXO9BF36YhZvGUTXAdRq69swtyxAMldX2ygp1kPAT2YTx7iIG6
    shopify_secret_key: bXB8n1cRpDELs9F3SC9TT98dDmPLET1R+eoTsEo/35ZNxnDVXfoV8c5+ddXkJ8t/z6=eSv6arUWKc5uI
  sleek_pay:
    domain_name: https://pay-eus.sleekflow.io
    shopify_graphql_admin_api_version: 2022-10
    stripe:
      public_keys:
        default: mvrWBGysxcA6cpKQpAgUbvmH13qx108tNH5bYvWcmkcytSb/dcgKe4Jb55iI5Li6TLXjLkJ+cc+hOew3b62uvLv5bXOqtDItmLBeXEvAvy5R6P70up9W1vssTwd4H4tJy6NchROzN2M0ReGk45kNAA==4X1JpqgCJ1mvVQnH
        gb: sVYwYgR9HsiLijVwPC+cT9vnOczwK/pO0+BCoTtbxVdM5KUYPryw6VFVU3tswYYAaQtNJjYLQYlqyTRki12JLHkfh5FeYd7WyzQPW7ha4dE5RHCjeQCIRab1YlZ7ss4aHDcgS8YJvcX122gIlMXkFQ==k1nyOqBksIAZvdfu
        hk: djPbNcz7KlvbhFKATADLF2PUvK0ZY9bgT2PKf0q1zd0+JPF2WId2oUuH98ZoK1OuTZn+K2EinfQ8pLQffdS+wQ0CT0xhoEHeUMu5AbaguvuhiuSOVsoq1rmGrw5oQ6JeVo5WBL3YhVHIIOn8bpemIw==IS5h7RkF=5D0kfe4
        my: XHmZNRXG7stpzHPiUg+j5Vz3eKf1FseZux7lKZkW+yPAJczqk46lAtMMK0kERtXCTt8WKf76PQVXjr+Kradbh1HVMAtKl49CINS5adYHWwdASx3Q7lhFjjoSNkENqdcfMEzIRbC4KbBLmpJqIpr2aw==CYo=PDxIqjSqnLXl
        sg: pWqXzNTbm2luGd33QXwzSl9ExOSBRqaRJe7RFuta0dldIU94Dmd8vtKY290b0QiWFbMNjDgbUIVjhxRwTK4H4SZi6ByxrzCdFzn739tOy494tQy/n9rdEmJKxTDkEPAuPx0xf96lciJVwPhAWPqX2g==gCKVcVa4nkvP3zst
      secret_keys:
        default: sOvMAlqTPM3ho5er3cZ28HtCpICNVVIbtwDUsmgwJUjFHVpzufrwnjAhtH9IUsmmCmMT6cyXHuTvwrALfkD6X03cgOMZaa80Nj40mNE49StKMca0FXUcU9bOdFuo9aH8kBYuYdpqDarjzqirD+OdEA==71b54V5JlE8zewUz
        gb: zuR4wZaC3NjXMGJ53wH4zfw+cQJrJzRw5q8hh6NKzRRM5HRa2Y7P62UwdNdX2yJU2o6BDxw8gFUPrJipQipi4xnXS6rLvw5IX+T0yl7QbY72TahOil9Dj5gdJWc3k7MR6o739t7REI9DdHImIp139g==uV7JICVTEPwEGC0O
        hk: uz8UxvDU/mWw02lnJnp6g8EyRyfpIBN5qDi51B7mRc37k9EESSkVQCNY4e5h8+2bDEYFK2zrBUKkzMomPTakui6+HPAQAL1P18huNnrGjnHzh4kxR58w3+F6Wx1a9JTrhB4TW2tfcG23mm8/G6t3dA==c=7XGLX5h6WtHOSy
        my: Fk1f7Y6W/eyUqevlENPciVhMGzlC+PYQ1qi/1KRZtgutZhXsvzspLSE2r774dkkH6nOalT7fpp/aAA32ZGk67ymtgw7J9rbykWmXFL8MVLUMPgqT0UEu+d86VLS0bqGPhGnluSj016tZ0eh20YBgPQ==mTPI6HvMxfoH0NFm
        sg: zSUIGTWMAB1xbgtQc2lS1QyYrTILR2jc5IvnJrKVtLvKdMgmMBALTYQjsose2GHkj3Jc9azwdqyi/eJFPwEHQRu7dXeRvszyeCdDfifeDhEVyP6nWF27kkYvFCIA9G4/1aTrTkCLu79+SR6R7uWvCg==Hsezt7uNbBtZ2RXK
      connect_webhook_secrets:
        gb: ebwVHj36+VpuSbC6KXlcj3SKJyoFn46lfTO0E6WQEBDWLZ2J8wblpp3Dgym3PCfnQJNIALZWb4c96N5L
        hk: gJ46WL0vNMTI03+el7NZ1mhOf8fyTVhnzIjizh2EyBQphDn8Xxtl6n2aFCEFTzDaMIBHslm6sCqIhxtG
        my: VurSuqcQ11jmH4QO1QMPJ23vqgW0vMS5CtdXgjvkOQcH4wITwW5lQ9jlKj+Gn2r1uZP7EZpUAqAKr2ew
        sg: A81dtsBNgIZdx+0wEPFvjnjQ+ExjrHZX05QXM0W90IKbjSx0jkFmxTFpbVYJWsfMSQQwLYsCls3iwxXO
      report_webhook_secrets:
        gb: RlB5NaRK8pw5SxwYqi9GpCZfdA06aZ22vzaWqnTUHhGa8OoLVgdEUIacVFZElO/YpXWkavmI1Haemofa
        hk: 3R4pm8LdvQ4cp5g8uG+7fpfezSqbL3+DA1HpKvODAdNgF3cbZMHup/1YdG8lyReHvcJIgwGlRsJQaPn4
        my: +5bMhvSNXaMsb/oLzmQmBNIElqr3t3Qe9NSlEpRzlw5tmzYc15Gl4ZSq/Z+nUvl1tRVzWF7O=uV99UFb
        sg: Q+4le2J0z72uSJtT6NOvO47WHpxoC9Kj06THkWRhn9SgKPUnxFYP9MLIiuHIEaM9JPBFgqUeTMtnisrU
      webhook_secrets:
        default: rSVzi7WPGQf4+/DhsevSrNL/Uvk9s56Rjt3Kx/OpZpmnyKg1iG2KC5+3u/f+5P8aWjEzPus=RFdZW9Fi
        gb: jtFU+Hau3Z27xjArB1gAvagq/ktb3nw0Z7CP6hlhIkTvWbZJNt2LJmtn3bfQdHO7WJTpeT3zAwxrhLoZ
        hk: lEaWGcipCM1n6HzYZW3X8S+id8F49yzivB+9YyONkzQE2JhSUOOVBIGO9wIwy6luFnXPuJd9tXDcDgJM
        my: hvDP7699CB9H2n9QFwRQffFuXcbkj0kXVJ8WxzQJfGeIzwyxKt5ms+7hwRwoyyXKY6I6XkN90wgXLM0Q
        sg: dtrjSy7gqikEw1aJlBsibbo5Z2Lg0hBpgJkkF0X11PpB349EwT63ZnZWAn6gJLAtdbNRw4IUSe4m6nRH
  snapshot_debugger_extension_version: disabled
  sql_performance:
    from_raw_sql: "true"
    is_and_condition_enabled: "true"
    is_or_condition_enabled: "true"
    is_conversation_analytics_condition_enabled: "false"
    is_shopify_order_statistics_enabled: "false"
    is_sales_performance_enabled: "false"
    is_public_api_new_upsert_enabled: "false"
    is_zapier_new_upsert_enabled: "false"
  stripe:
    stripe_public_key: jt1VE+kabFltWV6vwlcCUiar1SkAczRlTQH+CK39uesK5uHplYDLCUvaGUk/Ou4yEdgcQMVuorJbWNWi
    stripe_report_key: kZnCWLDjN5J7pjk2libA67aX9GPdp9DsHB0xDMZ/2wn/8XKF5cZjwXFh9qNgk0DSire3L/lVwVC6zaTP9JKD44YEDBtEumHe9JYNoqrH546aRqblPbgGfiIVuZ83YBb+EWAwV6jjdAtORhNq4qIlhQ==ySgLbtuzDvZqUko9
    stripe_secret_key: J5y8vNdo5G+6+xQiaQSVxR90aTDDd1c0e9q5Dp5XkkRGqUvblSnBoMmAEMWRQZ/dqgn1neVFV5qPV2OC
    stripe_webhook_secret: ePSMaA58RsbgeV7vbMY3dpPhAIKUWUI32cGf3qwEdVjwJYqjU/ajmkoaqN5tNt04c=GZY0GkwI75qPJi
  stripe_payment:
    stripe_payment_secret_key_gb: MAttYDpwFjYshXOiS6JHke+yPf8cJ24KAU5V7qjiXAfNFJe+kpiHGRwUOoujiTwd6Vq4rSjzScSI5DP47VTc5hdFvrwUU19UtnI6i8UoWsE6xfBYeziqZgWrCHmmj1ve3JyB15x9Ww1WYoDeRxS9cw==cz6CP7rj4ZeHn1zR
    stripe_payment_secret_key_hk: RSgzOac32zPFGrs0IqhA9YBmvORz6PERHjcpB4TU/P1aqjGeMpqI6ld+/4SFEDfWtIm+nnRgWcJbgwffAqLvrPlrg/ct0RRJItXADPvXm5SXqHMxYSH9EQ0sHSgUJ531MA2fNOCn5kO2qy568AanNQ==HGnfjK1onuDX=8=7
    stripe_payment_secret_key_my: FTTR1yPNxBWlcWwCa/9Fq0NlYvIbK/CzUGPlCnAM6FFDyA7YEW9clztId9QmwE42jW0AAuoqT6w7zLPD4qoQTjGxasvHiHp4XxU/XKAqJ9g2i2mAdfGpwXBc34r2Fb37B8iWSZ1DAeTD86gJNf2jYw==4AORzjo8seWfWDBa
    stripe_payment_secret_key_sg: eC/gUCsveJrYutNOSsk7A4cwc9ZxoCUOH0R1jV4RArHsBoBmaBExrDCiJ0apD+h0xdyqc7n400PYIkPDhmxEc9lGHgR6W+YOBrsWcPu2k9v5tf5JG94P4iK0oCpjjh43rw2EI7cYVK+GQtDW8XPGdA==udEoO=rWJO2fQpFh
  stripe_report:
    stripe_report_webhook_secret_gb: L3Iex9I2cKNy3PefqMCQRkAgj3jVhJh2WSTdFmlKoUyiM+jJ22z+6P9/FEvocks5IpfPUben3Q6MSBoe
    stripe_report_webhook_secret_hk: W7Smtbaj1E3xkN/07jlKecSc9JHmKnqnitT7gQp05D1uZM2twTXJHvlw0Al3iewCYGCswJMzB=lRhjVv
    stripe_report_webhook_secret_my: VKfpOIwea3aiEBBO+gEyMzXcjCz6dL3zvgTXLSpg6sEzScgEB1ruFzmugby2t9KyM2=KxvftVoDNGpGl
    stripe_report_webhook_secret_sg: hEBH+64eExux8HXpeaWqqH2o4Sp7+crX1x0kqTsVi3ul74uqUl8j7P+Ng/K0lGU1hvD50iWdniCdpTGG
  tenant_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/tenant-hub
    key: lkfuwlGGw56TX52d5N0FdYdh/5oU96dBrP1qN7W2zWRUnGzMB5fGJ3yd+B3XUlvKXTqnOqZmoU3afCFpHVca+pmCfzZNglg1+dzn6oUuwr4=dirpbYSHqrE1YF0M
    is_enable_tenant_logic: "true"
  ticketing_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/ticketing-hub
    key: WEN1V9WGhXSJr6u54TD3tK74aLj21DQl/+OKH9Bf3L+R24K6Y/N6+zGoe9s8byn/TCKvexByypb8XCl/iSawa/5U2Y48w5z6k7EcNtAgmak=LBDF6X=i44m1Gq2f
    is_enable_ticketing_logic: "false"
  test_swaping: 1
  token:
    audience: https://sleekflow-prod-api.azurewebsites.net
  tokens:
    audience: https://travis-crm-api-hk.azurewebsites.net
    issuer: https://sleekflow-prod-api.azurewebsites.net
    key: jwLJktIS7VLk1RPY5vC80gKp8Ujq5EBhrH3HntF1SmKHP80wySQWzaXz9mEr4mWokNiBM4w5AKafZT0B+uPiFgwiKSVtBhTopQJ6e5XIvxs=9gL40cTPQstvvsA5
    lifetime: 60
  user_event_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/user-event-hub
    key: pwW/g7dZO2DXwbuf0lyc9l0Enhil7aUjA144gfZHSPtSGiVf5DU7v2LddVBGhb5UNS+YvPkw33kg3wiOR5r8PEeLEKCQCi+ho9ERinTSO4Y=TolDtj8gEbl43ODn
  values:
    app_domain_name: https://app.sleekflow.io
    app_domain_name_v1: https://v1.sleekflow.io
    app_domain_name_v2: https://app.sleekflow.io
    share_link_function: https://share.sleekflow.io
    sleekflow_api_gateway: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net
    sleekflow_company_id: 471a6289-b9b7-43c3-b6ad-395a1992baea
    sleekflow_public_api_url: https://api.sleekflow.io
    sleekflow_public_api_key: SpPW34oMJIZstgmBG+cUo8GTTLDa2hGsvPoWAlc+evTMoFJQkbjbzwz31FYEwOMbg1xqcSv5R5VVnkr0
    sleekflow_company_should_use_public_api: "true"
  website_http_logging_retention_days: 5
  website_node_default_version: 6.9.1
  whatsapp_cloud_api_template:
    default_image_blob_id: sleekflow.png
  xdt_microsoft_application_insights_base_extensions: disabled
  xdt_microsoft_application_insight_mode: default
  global_pricing:
    is_feature_enabled: "true"
    plan_migration_incentives_start_date: "2024-11-20"
    plan_migration_incentives_end_date: "2025-03-10"
  feature_flags:
    - feature_name: FlowBuilderMonetisation
      is_enabled: "true"
    - feature_name: CancelledSubscriptionTermination
      is_enabled: "true"
    - feature_name: Rbac
      is_enabled: "true"
  contact_safe_deletion:
    is_feature_enabled: "true"
  partner_stack:
    public_key: 0FPHVDSnRknCRwnxojTc5rG2KfU1WRxSrbaRJMyl4gjqJEpXHOnu5KZt4/46aC5LBFKF1fjNWxc3dFQs
    secret_key: 71PTyR1Zmi0V4tkRfQjTnjtLX+fR1akhP7ADTlIUePNJ7OC8yJpwRyk2SWzi+lRbxP=jlutkABkcHDxk
  hangfire_worker:
    worker_count: 20
  internal_integration_hub:
    endpoint: "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/internal-integration-hub"
    key: /NPhAumFdFHLGA/L3jSBRAiD+AJDjZcSwBraP1T0Bit1TrGFVr/s5Dd/3cy5MkesJw2zW7ARyBrz+coa8CyfJXDz1D9K90FwqWZBmb7655Y=cohREu3M1Nmiyr7w
  hangfire_queues:
    disable_instances: ""
  integration_alert:
    endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
    api_key: "tU0QqRU74JWiSEd3ZpxwIXp+stkd91GbuNu70R/WFDCPWuCsAOPq31GEWlKJZVetOM=H5yoy4pQy0rv7"
    host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
    from_phone_number: "17209612030"
    template_name: "integration_disconnect_noti"
    facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
  rbac:
    is_middleware_verification_enabled: "false"
  flow_builder_flow_enrollments_incentives:
    period_start: "2025-02-07"
    period_end: "2025-06-25"
  hub_spot_smtp:
    username: "*******"
    password: "yus47rNx7M7Ca0xqdiQNZVGEOXb1yh"
    send_execution_usage_reached_threshold_email:
      username: "*******"
      password: "k4nCTQARqae2a2i3yX2d2qVuiJIvTz"
  legacy_premium_opt_in_upgrade_incentives:
    period_start: "2025-03-10"
    period_end: "2025-06-10"