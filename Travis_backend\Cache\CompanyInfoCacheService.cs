using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.Cache.Models;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.Database;

namespace Travis_backend.Cache;

public interface ICompanyInfoCacheService
{
    Task RemoveCompanyInfoCache(string companyId, string keyPrefix = "GetCompanyInfo");
}

public class CompanyInfoCacheService : ICompanyInfoCacheService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ICacheManagerService _cacheManagerService;

    public CompanyInfoCacheService(ApplicationDbContext appDbContext, ICacheManagerService cacheManagerService)
    {
        _appDbContext = appDbContext;
        _cacheManagerService = cacheManagerService;
    }


    public async Task RemoveCompanyInfoCache(string companyId, string keyPrefix = "GetCompanyInfo")
    {
        var getCompanyUsageCacheKeyPattern = new GetCompanyUsageCacheKeyPattern(companyId);
        await _cacheManagerService.DeleteCacheAsync(getCompanyUsageCacheKeyPattern);

        // Remove WebClient company info cache
        var webClientCompanyInfoCacheKeyPattern = new WebClientGetCompanyInfoCacheKeyPattern(companyId);
        await _cacheManagerService.DeleteCacheAsync(webClientCompanyInfoCacheKeyPattern);

        var staffs = await _appDbContext.UserRoleStaffs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .Select(
                x => new
                {
                    x.IdentityId, x.RoleType
                })
            .ToListAsync();

        foreach (var key in staffs.Select(user => $"{keyPrefix}_{companyId}_{user.IdentityId}_{user.RoleType}"))
        {
            await _cacheManagerService.DeleteCacheWithConstantKeyAsync(key);
        }
    }
}