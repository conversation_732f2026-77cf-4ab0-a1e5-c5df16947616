using Pulumi;
using Pulumi.AzureNative.Authorization;
using Pulumi.AzureNative.Insights.Inputs;
using Pulumi.AzureNative.Resources;
using Sleekflow.Core.Infra.Components;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Components.Models;
using Sleekflow.Core.Infra.Components.SleekflowCore;
using Sleekflow.Core.Infra.Constants;
using Sleekflow.Core.Infra.Utils;
using Web = Pulumi.AzureNative.Web;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using FrontDoor = Sleekflow.Core.Infra.Components.FrontDoor;
using Insights = Pulumi.AzureNative.Insights;

namespace Sleekflow.Core.Infra;

internal class MyStack : Stack
{
    [Output]
    public Output<string> FrontDoorEndpoint { get; set; }

    public MyStack()
    {
        var myConfig = new MyConfig();
        var serverConfig = new ServerConfig();

        // Init the central resource group for handling the common modules
        // i.e. Azure container registry - Handling Docker Image, Azure Front Door to route the traffic
        var resourceGroup = new ResourceGroup(
            ResourceUtils.GetName("sleekflow-core-rg", myConfig),
            new ResourceGroupArgs
            {
                Location = LocationNames.EastAsia
            });

        var containerRegistryOutput = InitContainerRegistry(myConfig, resourceGroup);
        var envGroups = InitEnvGroups(myConfig, serverConfig);

        InitSleekflowCore(myConfig, serverConfig, containerRegistryOutput, envGroups);

        // Init Global Azure Front Door to direct the traffic to different location base on the X-Sleekflow-Location
        // i.e. Default -> East-Asia Travis Backend, eastus -> East-Us Sleekflow Core
        var frontDoorEndpoint = new FrontDoor(myConfig, envGroups, serverConfig, resourceGroup).InitFrontDoor();
        FrontDoorEndpoint = frontDoorEndpoint.HostName;

        // Init Azure Portal Dashboard to monitor the resources
        new AzurePortalDashboard(myConfig, envGroups, serverConfig).InitPortalDashboard();
    }

    private static List<EnvGroup> InitEnvGroups(MyConfig myConfig, ServerConfig serverConfig)
    {
        return myConfig.Name switch
        {
            EnvironmentNames.Dev =>
            [
                InitEnvGroup(myConfig, serverConfig, LocationNames.EastAsia)
            ],
            EnvironmentNames.Staging =>
            [
                InitEnvGroup(myConfig, serverConfig, LocationNames.EastAsia),
                InitEnvGroup(myConfig, serverConfig, LocationNames.EastUs)
            ],
            EnvironmentNames.Production =>
            [
                InitEnvGroup(myConfig, serverConfig, LocationNames.EastAsia),
                InitEnvGroup(myConfig, serverConfig, LocationNames.EastUs),
                InitEnvGroup(myConfig, serverConfig, LocationNames.SouthEastAsia),
                InitEnvGroup(myConfig, serverConfig, LocationNames.UaeNorth),
                InitEnvGroup(myConfig, serverConfig, LocationNames.WestEurope)
            ],
            _ => throw new Exception("MyStack - InitEnvGroups")
        };
    }

    private static EnvGroup InitEnvGroup(MyConfig myConfig, ServerConfig serverConfig, string locationName)
    {
        var resourceGroup = new ResourceGroup(
            ResourceUtils.GetName($"sleekflow-core-rg-{LocationNames.GetShortName(locationName)}", myConfig),
            new ResourceGroupArgs
            {
                Location = LocationNames.GetAzureLocation(locationName)
            });

        var vnet = new VNet(myConfig, locationName, serverConfig, resourceGroup).InitVNet();

        var (criticalActionGroup, preventionActionGroup) = InitActionGroups(locationName, resourceGroup, myConfig);

        if (locationName == LocationNames.EastAsia)
        {
            // LiveChat BlobStorage Only needs one location per environment
            new BlobStorage(myConfig, locationName, serverConfig.CorsConfig, resourceGroup).InitLiveChatV2BlobStorage();
        }

        return new EnvGroup(
            locationName,
            new Redis(myConfig, locationName, serverConfig, resourceGroup).InitRedis(),
            resourceGroup,
            new SignalR(myConfig, resourceGroup, locationName).InitSignalR(),
            new Dictionary<string, AppServiceConfiguration>(),
            new Dictionary<string, AppServiceConfiguration>(),
            new BlobStorage(myConfig, locationName, serverConfig.CorsConfig, resourceGroup).InitBlobStorage(),
            vnet.PrivateZone,
            vnet.VirtualNetwork,
            null,
            null,
            new LogAnalyticsWorkspace(myConfig, resourceGroup, locationName).InitLogAnalyticsWorkspace(),
            criticalActionGroup,
            preventionActionGroup,
            Output.Create(GetClientConfig.InvokeAsync()));
    }

    private static (
        Insights.ActionGroup criticalActionGroup, Insights.ActionGroup preventionActionGroup) InitActionGroups(
            string locationName,
            ResourceGroup resourceGroup,
            MyConfig myConfig)
    {
        var criticalActionGroup = new Insights.ActionGroup(
            $"sleekflow-core-critical-action-group-{LocationNames.GetShortName(locationName)}",
            new Insights.ActionGroupArgs()
            {
                Enabled = true,
                GroupShortName = "critical",
                Location = "Global",
                ResourceGroupName = resourceGroup.Name,
                ActionGroupName = "critical-action-group",
                EmailReceivers = new InputList<EmailReceiverArgs>(),
                SmsReceivers = new InputList<SmsReceiverArgs>(),
                WebhookReceivers = myConfig.Name == EnvironmentNames.Production
                    ? new[]
                    {
                        new Insights.Inputs.WebhookReceiverArgs
                        {
                            ServiceUri =
                                "https://api.opsgenie.com/v1/json/azure?apiKey=************************************",
                            Name = "Opsgenie - Engineering",
                            UseCommonAlertSchema = true,
                            UseAadAuth = false
                        }
                    }
                    : new List<Insights.Inputs.WebhookReceiverArgs>()
            });

        var preventionActionGroup = new Insights.ActionGroup(
            $"sleekflow-core-prevention-action-group-{LocationNames.GetShortName(locationName)}",
            new Insights.ActionGroupArgs()
            {
                Enabled = true,
                GroupShortName = "prevention",
                Location = "Global",
                ResourceGroupName = resourceGroup.Name,
                ActionGroupName = "prevention-action-group",
                EmailReceivers = new InputList<EmailReceiverArgs>(),
                SmsReceivers = new InputList<SmsReceiverArgs>(),
                WebhookReceivers = myConfig.Name == EnvironmentNames.Production
                    ? new[]
                    {
                        new Insights.Inputs.WebhookReceiverArgs
                        {
                            ServiceUri =
                                "https://api.opsgenie.com/v1/json/azure?apiKey=************************************",
                            Name = "Opsgenie - Engineering",
                            UseCommonAlertSchema = true,
                            UseAadAuth = false
                        }
                    }
                    : new List<Insights.Inputs.WebhookReceiverArgs>()
            });
        return (criticalActionGroup, preventionActionGroup);
    }

    private static void InitSleekflowCore(
        MyConfig myConfig,
        ServerConfig serverConfig,
        ContainerRegistryOutput containerRegistryOutput,
        List<EnvGroup> envGroups)
    {
        new SleekflowCoreDb(myConfig, serverConfig, envGroups).InitSleekflowCoreDb();
        new SleekflowCore(myConfig, envGroups, serverConfig, containerRegistryOutput).InitSleekflowCore();
        new SleekflowPowerflow(myConfig, envGroups, serverConfig, containerRegistryOutput).InitSleekflowPowerflow();
        new SleekflowSleekPay(myConfig, envGroups, serverConfig).InitSleekPay();
        // Sleekflow core worker is set up for triggering scheduler jobs to off load the load from main machine
        new SleekflowCoreWorker(myConfig, envGroups, serverConfig, containerRegistryOutput).InitSleekflowCoreWorker();
    }

    private static ContainerRegistryOutput InitContainerRegistry(MyConfig myConfig, ResourceGroup resourceGroup)
    {
        var registry = new ContainerRegistry.Registry(
            ResourceUtils.GetName("sleekflow-core-registry", myConfig),
            new ContainerRegistry.RegistryArgs
            {
                RegistryName = ResourceUtils.GetRegistryName("sleekflowcoreregistry", myConfig),
                ResourceGroupName = resourceGroup.Name,
                Sku = new ContainerRegistry.Inputs.SkuArgs
                {
                    Name = ContainerRegistry.SkuName.Basic
                },
                AdminUserEnabled = true
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var registryCredentials = ContainerRegistry.ListRegistryCredentials.Invoke(
            new ContainerRegistry.ListRegistryCredentialsInvokeArgs
            {
                ResourceGroupName = resourceGroup.Name, RegistryName = registry.Name
            });
        var adminUsername = registryCredentials
            .Apply(c => c.Username ?? string.Empty);
        var adminPassword = registryCredentials
            .Apply(c => Output.CreateSecret(c.Passwords.First().Value ?? string.Empty));

        return new ContainerRegistryOutput(
            registry,
            adminUsername,
            adminPassword);
    }
}