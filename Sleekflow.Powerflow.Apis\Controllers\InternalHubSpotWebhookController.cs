﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.Services;
using Travis_backend.InternalDomain.ViewModels;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// HubSpot Internal Integration Webhooks.
/// </summary>
[Route("/internal/hubspot/webhook/[action]")]
[AllowAnonymous]
public class InternalHubSpotWebhookController : InternalControllerBase
{
    private const string VerifyKey = "4a72eac56a3206aca4de37f70715acbc00324389286796c9132ea9f61c735e78";

    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IInternalHubSpotService _internalHubSpotService;
    private readonly IUserProfileService _userProfileService;
    private readonly IInternalPartnerStackService _internalPartnerStackService;

    public InternalHubSpotWebhookController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IConfiguration configuration,
        ILogger<InternalHubSpotWebhookController> logger,
        IInternalHubSpotService internalHubSpotService,
        IUserProfileService userProfileService,
        IInternalPartnerStackService internalPartnerStackService)
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _configuration = configuration;
        _logger = logger;
        _internalHubSpotService = internalHubSpotService;
        _userProfileService = userProfileService;
        _internalPartnerStackService = internalPartnerStackService;
    }

    /// <summary>
    /// Sync HubSpot Company Owner to Powerflow Contact Owner.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> SyncCompanyContactOwner(
        [FromBody]
        HubSpotWebhookRequest<CompanyProperties> request,
        [FromQuery(Name = "key")]
        string key)
    {
        _logger.LogInformation(
            "SyncCompanyContactOwner requested, payload: {Payload}",
            JsonConvert.SerializeObject(request));

        if (request == null || key != VerifyKey)
        {
            return BadRequest();
        }

        var all = await _appDbContext.CmsHubSpotContactOwnerMaps.AsNoTracking().ToListAsync();

        var company = await _appDbContext.CompanyCompanies
            .Include(x => x.CmsCompanyAdditionalInfo)
            .FirstOrDefaultAsync(x => x.Id == request.Properties.SleekflowCompanyIdPayload.Value);

        if (company == null)
        {
            return Ok(); // Ignore if company not found
        }

        if (request.Properties.InitialIdeaCustomerProfileTier != null)
        {
            company.CmsCompanyAdditionalInfo ??= new CmsCompanyAdditionalInfo()
            {
                CompanyId = company.Id
            };

            if (!string.IsNullOrWhiteSpace(request.Properties.InitialIdeaCustomerProfileTier.Value))
            {
                company.CmsCompanyAdditionalInfo.CompanyTier =
                    request.Properties.InitialIdeaCustomerProfileTier.Value;
                company.CmsCompanyAdditionalInfo.UpdatedAt = DateTime.UtcNow;
            }
        }

        if (request.Properties.HubspotCompanyOwnerIdPayload != null)
        {
            if (request.Properties.HubspotCompanyOwnerIdPayload.Value == "*********" &&
                company.Id != "471a6289-b9b7-43c3-b6ad-395a1992baea")
            {
                _logger.LogError("SyncCompanyContactOwner Error! Company Owner Set to Henson: ");

                return Ok();
            }

            var newAssignedCompanyOwner = all.FirstOrDefault(
                x => x.HubSpotContactOwnerId == request.Properties.HubspotCompanyOwnerIdPayload.Value);
            var salesAssignedBy = all.FirstOrDefault(
                x => x.HubSpotUserId == request.Properties.HubspotCompanyOwnerIdPayload.UpdatedByUserId);

            if (newAssignedCompanyOwner != null &&
                company.CmsCompanyOwnerId != newAssignedCompanyOwner.ContactOwnerId)
            {
                _appDbContext.CmsContactOwnerAssignLogs.Add(
                    new CmsContactOwnerAssignLog()
                    {
                        CompanyId = company.Id,
                        ContactOwnerType = CmsContactOwnerType.CompanyOwner,
                        FromContactOwnerId = company.CmsCompanyOwnerId,
                        ToContactOwnerId = newAssignedCompanyOwner.ContactOwnerId,
                        AssignedByUserId = salesAssignedBy?.ContactOwnerId ?? null
                    });

                company.CmsCompanyOwnerId = newAssignedCompanyOwner.ContactOwnerId;
            }
        }

        if (request.Properties.HubspotActivationOwnerIdPayload != null)
        {
            if (request.Properties.HubspotActivationOwnerIdPayload.Value == "*********" &&
                company.Id != "471a6289-b9b7-43c3-b6ad-395a1992baea")
            {
                _logger.LogError("SyncCompanyContactOwner Error! Company Owner Set to Henson: ");

                return Ok();
            }

            var newAssignedActivationOwnerId = all.FirstOrDefault(
                x => x.HubSpotContactOwnerId == request.Properties.HubspotActivationOwnerIdPayload.Value);
            var salesAssignedBy = all.FirstOrDefault(
                x => x.HubSpotUserId == request.Properties.HubspotActivationOwnerIdPayload.UpdatedByUserId);

            if (newAssignedActivationOwnerId != null &&
                company.CmsActivationOwnerId != newAssignedActivationOwnerId.ContactOwnerId)
            {
                _appDbContext.CmsContactOwnerAssignLogs.Add(
                    new CmsContactOwnerAssignLog()
                    {
                        CompanyId = company.Id,
                        ContactOwnerType = CmsContactOwnerType.ActivationOwner,
                        FromContactOwnerId = company.CmsActivationOwnerId,
                        ToContactOwnerId = newAssignedActivationOwnerId.ContactOwnerId,
                        AssignedByUserId = salesAssignedBy?.ContactOwnerId ?? null
                    });

                company.CmsActivationOwnerId = newAssignedActivationOwnerId.ContactOwnerId;
            }
        }

        if (request.Properties.HubspotCsOwnerIdPayload != null)
        {
            if (request.Properties.HubspotCsOwnerIdPayload.Value == "*********" &&
                company.Id != "471a6289-b9b7-43c3-b6ad-395a1992baea")
            {
                _logger.LogError("SyncCompanyContactOwner Error! Company Owner Set to Henson: ");

                return Ok();
            }

            var newAssignedCsOwnerId = all.FirstOrDefault(
                x => x.HubSpotContactOwnerId == request.Properties.HubspotCsOwnerIdPayload.Value);
            var salesAssignedBy = all.FirstOrDefault(
                x => x.HubSpotUserId == request.Properties.HubspotCsOwnerIdPayload.UpdatedByUserId);

            if (newAssignedCsOwnerId != null &&
                company.CmsCsOwnerId != newAssignedCsOwnerId.ContactOwnerId)
            {
                _appDbContext.CmsContactOwnerAssignLogs.Add(
                    new CmsContactOwnerAssignLog()
                    {
                        CompanyId = company.Id,
                        ContactOwnerType = CmsContactOwnerType.CsOwner,
                        FromContactOwnerId = company.CmsCsOwnerId,
                        ToContactOwnerId = newAssignedCsOwnerId.ContactOwnerId,
                        AssignedByUserId = salesAssignedBy?.ContactOwnerId ?? null
                    });

                company.CmsCsOwnerId = newAssignedCsOwnerId.ContactOwnerId;
            }
        }

        await _appDbContext.SaveChangesAsync();

        return Ok();
    }

    /// <summary>
    /// Sync HubSpot Company Tier to Company.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> SyncCompanyTier(
        [FromBody]
        CompanyTierHubSpotWebhook request,
        [FromQuery(Name = "key")]
        string key)
    {
        if (request == null || key != VerifyKey)
        {
            return BadRequest();
        }

        _logger.LogInformation("Sync HubSpot Company Tier to Company: " + JsonConvert.SerializeObject(request));

        var company = await _appDbContext.CompanyCompanies
            .Include(x => x.CmsCompanyAdditionalInfo)
            .FirstOrDefaultAsync(x => x.Id == request.SleekflowCompanyId);

        if (company == null)
        {
            return Ok(); // Ignore if company not found
        }

        if (!string.IsNullOrWhiteSpace(request.InitialIdeaCustomerProfileTier))
        {
            company.CmsCompanyAdditionalInfo ??= new CmsCompanyAdditionalInfo()
            {
                CompanyId = company.Id
            };

            company.CmsCompanyAdditionalInfo.CompanyTier = request.InitialIdeaCustomerProfileTier;
            company.CmsCompanyAdditionalInfo.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();
        }

        return Ok();
    }

    /// <summary>
    /// Sync HubSpot WhatsApp Application Ticket to PowerFlow.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> SyncWhatsAppApplicationTicket(
        [FromBody]
        HubSpotWebhookRequest<WhatsApplicationTicketProperties> request,
        [FromQuery(Name = "key")]
        string key)
    {
        // _logger.LogInformation(JsonConvert.SerializeObject(request));
        if (request == null || key != VerifyKey)
        {
            return BadRequest();
        }

        var newStep = HubSpotWhatsAppApplicationTicket.PipelineStatus.Parse(request.Properties.PipelineStage.Value);

        var whatsappApplication =
            await _appDbContext.CmsWhatsappApplications.FirstOrDefaultAsync(
                x => x.HubSpotTicketId == request.ObjectId.ToString());

        if (whatsappApplication != null && whatsappApplication.Step != newStep)
        {
            whatsappApplication.Step = newStep;
            whatsappApplication.UpdatedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();
        }

        return Ok();
    }

    /// <summary>
    /// Sync HubSpot Contact Owner Sleekflow Prod Account.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> SyncUserProfileContactOwner(
        [FromBody]
        HubSpotWebhookRequest<ContactProperties> request,
        [FromQuery(Name = "key")]
        string key)
    {
        // _logger.LogInformation(JsonConvert.SerializeObject(request));
        if (request == null || key != VerifyKey)
        {
            return BadRequest();
        }

        var sleekflowCompanyId = _configuration.GetValue<string>("Values:SleekFlowCompanyId");

        if (sleekflowCompanyId != "471a6289-b9b7-43c3-b6ad-395a1992baea")
        {
            return Ok();
        }

        const string phoneNumberCustomFieldId = "987dbbf1-5004-476a-a21f-62b16fe4bd96";
        const string emailCustomFieldId = "570f0995-6696-4467-b826-e58144d77d52";

        var email = request.Properties.EmailPayload?.Value ?? string.Empty;
        var phoneNumber = PhoneNumberHelper.NormalizePhoneNumber(request.Properties.PhonePayload?.Value ?? string.Empty);

        var matchedEmailUserProfileIds = await _appDbContext.UserProfileCustomFields
            .Where(x => x.CompanyDefinedFieldId == emailCustomFieldId && x.Value == email)
            .Select(x => x.UserProfileId)
            .ToListAsync();

        var matchedPhoneUserProfileIds = await _appDbContext.UserProfileCustomFields
            .Where(x => x.CompanyDefinedFieldId == phoneNumberCustomFieldId && x.Value == phoneNumber)
            .Select(x => x.UserProfileId)
            .ToListAsync();

        var userProfileIds = new List<string>();
        userProfileIds.AddRange(matchedEmailUserProfileIds);
        userProfileIds.AddRange(matchedPhoneUserProfileIds);
        userProfileIds = userProfileIds.Distinct().ToList();

        var userProfiles = await _appDbContext.UserProfiles.Where(x => userProfileIds.Contains(x.Id)).ToListAsync();

        var contactOwner = await _appDbContext.CmsHubSpotContactOwnerMaps.FirstOrDefaultAsync(
            x => x.HubSpotContactOwnerId == request.Properties.HubspotOwnerIdPayload.Value);

        if (contactOwner == null)
        {
            var updatedContactOwners = await _internalHubSpotService.SyncInternalContactOwnerMapping();
            contactOwner = updatedContactOwners.FirstOrDefault(
                x => x.HubSpotContactOwnerId == request.Properties.HubspotOwnerIdPayload.Value);
        }

        if (contactOwner != null)
        {
            foreach (var userProfile in userProfiles)
            {
                if (userProfile.ContactOwnerId != contactOwner.ContactOwnerId)
                {
                    await _userProfileService.SetFieldValueByFieldNameSafe(
                        userProfile,
                        "ContactOwner",
                        contactOwner.ContactOwnerId);
                }
            }
        }

        return Ok();
    }

    /// <summary>
    /// Sync Sleekflow Contact Owner to HubSpot.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> SyncContactOwnerToHubSpot(
        [FromBody]
        SleekflowWebhookPayload payload,
        [FromQuery(Name = "key")]
        string key)
    {
        // _logger.LogInformation(JsonConvert.SerializeObject(request));
        if (payload == null || key != VerifyKey)
        {
            return BadRequest();
        }

        var contactOwner =
            await _appDbContext.CmsHubSpotContactOwnerMaps.FirstOrDefaultAsync(
                x => x.ContactOwnerId == payload.ContactOwner);

        if (!string.IsNullOrWhiteSpace(payload.Email) && contactOwner != null)
        {
            payload.Email = payload.Email.Trim();
            await _internalHubSpotService.UpdateContactOwnerFromWebhook(
                payload,
                contactOwner.HubSpotContactOwnerId);
        }

        return Ok();
    }

    /// <summary>
    /// Sync Churn Reason From HubSpot.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> SyncChurnReasonFromHubSpot(
        [FromBody]
        HubSpotWebhookRequest<CompanyChurnReasonProperties> request,
        [FromQuery(Name = "key")]
        string key)
    {
        // _logger.LogInformation(JsonConvert.SerializeObject(request));
        if (request == null || key != VerifyKey)
        {
            return BadRequest();
        }

        var company = await _appDbContext.CompanyCompanies
            .Include(x => x.CmsCompanyAdditionalInfo)
            .FirstOrDefaultAsync(x => x.Id == request.Properties.SleekflowCompanyId.Value);

        if (company == null)
        {
            return Ok(); // Ignore if company not found
        }

        company.CmsCompanyAdditionalInfo ??= new CmsCompanyAdditionalInfo()
        {
            CompanyId = company.Id
        };

        company.CmsCompanyAdditionalInfo.ChurnReason = request.Properties.ChurnReasons.Value;
        company.CmsCompanyAdditionalInfo.UpdatedAt = DateTime.UtcNow;

        await _appDbContext.SaveChangesAsync();

        return Ok();
    }

    /// <summary>
    ///     Sync Company Industry From HubSpot.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> SyncCompanyIndustryFromHubSpot(
        [FromBody]
        CompanyIndustryHubSpotWebhook request,
        [FromQuery(Name = "key")]
        string key)
    {
        if (request == null || key != VerifyKey)
        {
            return BadRequest();
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.SleekflowCompanyId);

        if (company == null)
        {
            return Ok(); // Ignore if company not found
        }

        var cmsCompanyAdditionalInfo =
            await _appDbContext.CmsCompanyAdditionalInfos.FirstOrDefaultAsync(
                x => x.CompanyId == request.SleekflowCompanyId);

        if (cmsCompanyAdditionalInfo == null)
        {
            cmsCompanyAdditionalInfo = new CmsCompanyAdditionalInfo
            {
                CompanyId = request.SleekflowCompanyId
            };
            await _appDbContext.AddAsync(cmsCompanyAdditionalInfo);
        }

        cmsCompanyAdditionalInfo.HubSpotCompanyIndustry = request.Industry;

        await _appDbContext.SaveChangesAsync();

        return Ok();
    }

    /// <summary>
    /// Sync PartnerStack Customer Key from HubSpot.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> SyncPartnerStackCustomerKeyFromHubSpot(
        [FromBody]
        PartnerStackCustomerKeyHubSpotWebhook request,
        [FromQuery(Name = "key")]
        string key)
    {
        // _logger.LogInformation(JsonConvert.SerializeObject(request));
        if (request == null || key != VerifyKey)
        {
            return BadRequest();
        }

        _logger.LogInformation("Sync HubSpot Customer Key: {Request}", JsonConvert.SerializeObject(request));

        var staff = await _appDbContext.UserRoleStaffs
            .Include(x => x.Identity)
            .FirstOrDefaultAsync(x => x.Identity.Email == request.Email);

        if (staff == null)
        {
            return Ok(); // Ignore if staff not found
        }

        if (!string.IsNullOrWhiteSpace(request.CustomerKey))
        {
            var result = await _internalPartnerStackService.CreateOrUpdatePartnerStackCustomerMapping(
                staff.CompanyId,
                request.CustomerKey);

            if (result.IsSuccess == false)
            {
                _logger.LogWarning("[{MethodName}] Sync PartnerStack Customer Key from HubSpot failed, Contact Email: {Email}, Customer Key: {CustomerKey}, Error Message: {ErrorMessage}",
                    nameof(SyncPartnerStackCustomerKeyFromHubSpot),
                    request.Email,
                    request.CustomerKey,
                    result.ErrorMsg);
            }
        }

        return Ok();
    }

    /// <summary>
    /// Sync PartnerStack Partner Key from HubSpot.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> SyncPartnerStackPartnerKeyFromHubSpot(
        [FromBody]
        PartnerStackPartnerKeyHubSpotWebhook request,
        [FromQuery(Name = "key")]
        string key)
    {
        // _logger.LogInformation(JsonConvert.SerializeObject(request));
        if (request == null || key != VerifyKey)
        {
            return BadRequest();
        }

        _logger.LogInformation("Sync HubSpot Partner Key: {Request}", JsonConvert.SerializeObject(request));

        var staffs = await _appDbContext.UserRoleStaffs
            .Include(x => x.Company)
            .Include(x => x.Identity)
            .Where(
                x => x.Identity.Email == request.Email)
            .ToListAsync();

        if (staffs.Count <= 0)
        {
            return Ok(); // Ignore if staff not found
        }

        foreach (var staff in staffs)
        {
            if (staff.Company.CompanyType == CompanyType.Reseller)
            {
                if (string.IsNullOrWhiteSpace(request.PartnerKey))
                {
                    return BadRequest("Partner Key is required");
                }

                var result = await _internalPartnerStackService.UpdatePartnerStackPartnerKey(
                    staff.CompanyId,
                    request.PartnerKey);

                if (result.IsSuccess == false)
                {
                    _logger.LogWarning("[{MethodName}] Sync PartnerStack Partner Key from HubSpot failed, Contact Email: {Email}, Customer Key: {CustomerKey}, Error Message: {ErrorMessage}",
                        nameof(SyncPartnerStackCustomerKeyFromHubSpot),
                        request.Email,
                        request.PartnerKey,
                        result.ErrorMsg);
                }
            }
            else
            {
                var result = await _internalPartnerStackService.SyncPartnerStackInformation(staff.CompanyId);

                if (result.IsSuccess == false)
                {
                    _logger.LogWarning("[{MethodName}] Sync PartnerStack Partner Information from HubSpot failed, Contact Email: {Email}, Error Message: {ErrorMessage}",
                        nameof(SyncPartnerStackPartnerKeyFromHubSpot),
                        request.Email,
                        result.ErrorMsg);
                }
            }
        }

        return Ok();
    }

    /// <summary>
    /// Sync Partnership Deal Owner ID from HubSpot.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> SyncPartnershipDealOwnerIdFromHubSpot(
        [FromBody]
        HubSpotWebhookRequest<PartnershipDealOwnerProperties> request,
        [FromQuery(Name = "key")]
        string key)
    {
        if (request == null || key != VerifyKey)
        {
            return BadRequest();
        }

        _logger.LogInformation(
            "Sync Partnership Deal Owner ID from HubSpot: {Request}",
            JsonConvert.SerializeObject(request));

        var sleekflowCompanyId = request.Properties.SleekflowCompanyId?.Value;
        var partnershipDealOwnerId = request.Properties.PartnershipsDealOwner?.Value;

        if (string.IsNullOrWhiteSpace(sleekflowCompanyId))
        {
            _logger.LogWarning(
                "[{MethodName}] Sync Partnership Deal Owner ID from HubSpot failed, Sleekflow Company ID is empty",
                nameof(SyncPartnershipDealOwnerIdFromHubSpot));
            return Ok(); // Ignore if company ID is empty
        }

        if (string.IsNullOrWhiteSpace(partnershipDealOwnerId))
        {
            _logger.LogWarning(
                "[{MethodName}] Sync Partnership Deal Owner ID from HubSpot failed, Partnership Deal Owner ID is empty, Company ID: {CompanyId}",
                nameof(SyncPartnershipDealOwnerIdFromHubSpot),
                sleekflowCompanyId);
            return Ok(); // Ignore if partnership deal owner ID is empty
        }

        var contactOwner =
            await _appDbContext.CmsHubSpotContactOwnerMaps.FirstOrDefaultAsync(x =>
                x.HubSpotContactOwnerId == partnershipDealOwnerId);

        if (contactOwner == null)
        {
            _logger.LogWarning(
                "[{MethodName}] Sync Partnership Deal Owner ID from HubSpot failed, Partnership Deal Owner ID not found in HubSpot contact owner mappings, Company ID: {CompanyId}, Partnership Deal Owner ID: {PartnershipDealOwnerId}",
                nameof(SyncPartnershipDealOwnerIdFromHubSpot),
                sleekflowCompanyId,
                partnershipDealOwnerId);
            return Ok(); // Ignore if partnership deal owner ID is not found in HubSpot contact owner mappings
        }

        var result = await _internalPartnerStackService.UpdatePartnershipDealOwnerId(
            sleekflowCompanyId,
            contactOwner.ContactOwnerId);

        if (result.IsSuccess == false)
        {
            _logger.LogWarning(
                "[{MethodName}] Sync Partnership Deal Owner ID from HubSpot failed, Company ID: {CompanyId}, Partnership Deal Owner ID: {PartnershipDealOwnerId}, Error Message: {ErrorMessage}",
                nameof(SyncPartnershipDealOwnerIdFromHubSpot),
                sleekflowCompanyId,
                partnershipDealOwnerId,
                result.ErrorMsg);
        }

        return Ok();
    }
}