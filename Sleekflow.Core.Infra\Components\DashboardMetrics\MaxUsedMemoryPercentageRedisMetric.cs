﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class MaxUsedMemoryPercentageRedisMetric : IDashboardMetric
{
    private readonly Output<string>? _resourceId;
    private readonly Output<string>? _resourceName;
    private readonly Output<string>? _locationName;
    private readonly Output<string>? _subscriptionId;

    public MaxUsedMemoryPercentageRedisMetric(
        Output<string>? resourceId,
        Output<string>? resourceName,
        Output<string>? locationName,
        Output<string>? subscriptionId)
    {
        _resourceId = resourceId;
        _resourceName = resourceName;
        _locationName = locationName;
        _subscriptionId = subscriptionId;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs =
                    new[]
                    {
                        new Dictionary<string, object>
                        {
                            {
                                "name", "options"
                            },
                            {
                                "value", new Dictionary<string, object>
                                {
                                    {
                                        "chart", new Dictionary<string, object>
                                        {
                                            {
                                                "metrics", new[]
                                                {
                                                    new Dictionary<string, object>
                                                    {
                                                        {
                                                            "aggregationType", 3
                                                        },
                                                        {
                                                            "metricVisualization", new Dictionary<string, object>
                                                            {
                                                                {
                                                                    "displayName", "Used Memory"
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "name", "usedmemory"
                                                        },
                                                        {
                                                            "namespace", "microsoft.cache/redis"
                                                        },
                                                        {
                                                            "resourceMetadata", new Dictionary<string, object>
                                                            {
                                                                {
                                                                    "id", _resourceId!
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            {
                                                "timespan", new Dictionary<string, object>
                                                {
                                                    {
                                                        "grain", 1
                                                    },
                                                    {
                                                        "relative", new Dictionary<string, object>
                                                        {
                                                            {
                                                                "duration", 86400000
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "showUTCTime", false
                                                    }
                                                }
                                            },
                                            {
                                                "title",
                                                _resourceName!.Apply(r => $"Redis: Max Used Memory Percentage for {r}")
                                            },
                                            {
                                                "titleKind", 1
                                            },
                                            {
                                                "visualization", new Dictionary<string, object>
                                                {
                                                    {
                                                        "axisVisualization", new Dictionary<string, object>
                                                        {
                                                            {
                                                                "x", new Dictionary<string, object>
                                                                {
                                                                    {
                                                                        "axisType", 2
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "y", new Dictionary<string, object>
                                                                {
                                                                    {
                                                                        "axisType", 1
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "chartType", 2
                                                    },
                                                    {
                                                        "legendVisualization", new Dictionary<string, object>
                                                        {
                                                            {
                                                                "hideHoverCard", false
                                                            },
                                                            {
                                                                "hideLabelNames", true
                                                            },
                                                            {
                                                                "isVisible", true
                                                            },
                                                            {
                                                                "position", 2
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "sharedTimeRange"
                            },
                            {
                                "isOptional", true
                            }
                        }
                    },
                Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                Settings =
                {
                    new Dictionary<string, object>
                    {
                        {
                            "content", new Dictionary<string, object>
                            {
                                {
                                    "options", new Dictionary<string, object>
                                    {
                                        {
                                            "chart", new Dictionary<string, object>
                                            {
                                                {
                                                    "grouping", new Dictionary<string, object>
                                                    {
                                                        {
                                                            "dimension", "Microsoft.ResourceId"
                                                        }
                                                    }
                                                },
                                                {
                                                    "metrics", new[]
                                                    {
                                                        new Dictionary<string, object>
                                                        {
                                                            {
                                                                "aggregationType", 3
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>
                                                                {
                                                                    {
                                                                        "displayName", "Used Memory Percentage"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", "Pay-As-You-Go"
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "usedmemorypercentage"
                                                            },
                                                            {
                                                                "namespace", "microsoft.cache/redis"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>
                                                                {
                                                                    {
                                                                        "region", _locationName!
                                                                    },
                                                                    {
                                                                        "resourceType", "microsoft.cache/redis"
                                                                    },
                                                                    {
                                                                        "subscription", new Dictionary<string, object>
                                                                        {
                                                                            {
                                                                                "displayName", "Pay-As-You-Go"
                                                                            },
                                                                            {
                                                                                "subscriptionId",
                                                                                _subscriptionId!
                                                                            },
                                                                            {
                                                                                "uniqueDisplayName", "Pay-As-You-Go"
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    "title",
                                                    _resourceName!.Apply(r => $"Redis: Max Used Memory Percentage for {r}")
                                                },
                                                {
                                                    "titleKind", 1
                                                },
                                                {
                                                    "visualization", new Dictionary<string, object>
                                                    {
                                                        {
                                                            "axisVisualization", new Dictionary<string, object>
                                                            {
                                                                {
                                                                    "x", new Dictionary<string, object>
                                                                    {
                                                                        {
                                                                            "axisType", 2
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    "y", new Dictionary<string, object>
                                                                    {
                                                                        {
                                                                            "axisType", 1
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "chartType", 2
                                                        },
                                                        {
                                                            "disablePinning", true
                                                        },
                                                        {
                                                            "legendVisualization", new Dictionary<string, object>
                                                            {
                                                                {
                                                                    "hideHoverCard", false
                                                                },
                                                                {
                                                                    "hideLabelNames", true
                                                                },
                                                                {
                                                                    "isVisible", true
                                                                },
                                                                {
                                                                    "position", 2
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}