using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;
using isRock.LineBot.Extensions;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.ClientCustomDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FileDomain.Models;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using SendGrid.Helpers.Mail;

namespace Travis_backend.ClientCustomDomain.Services;

public interface IZDotComService
{
    public ValueTask<bool> ShouldBackupChatHistoryAsync(
        Conversation conversation,
        string oldStatus,
        string newStatus,
        DateTime conversationStatusModifiedAt);

    public Task CreateChatHistoryBackupAndSendEmailAsync(string conversationId, string staffId);
    Task<string> GetCompanyTimeZoneIdAsync(string companyId);
}

public class ZDotComService : IZDotComService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<ZDotComService> _logger;
    private readonly IEmailNotificationService _emailNotificationService;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly IAuditHubAuditLogService _auditHubAuditLogService;

    public ZDotComService(
        ApplicationDbContext appDbContext,
        ILogger<ZDotComService> logger,
        IEmailNotificationService emailNotificationService,
        IAzureBlobStorageService azureBlobStorageService,
        IAuditHubAuditLogService auditHubAuditLogService)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _emailNotificationService = emailNotificationService;
        _azureBlobStorageService = azureBlobStorageService;
        _auditHubAuditLogService = auditHubAuditLogService;
    }

    public async ValueTask<bool> ShouldBackupChatHistoryAsync(
        Conversation conversation,
        string oldStatus,
        string newStatus,
        DateTime conversationStatusModifiedAt)
    {
        // Currently only z.com forex is using this backup chat history through email module
        var result = await FindChatHistoryBackupConfigAsync(conversation.CompanyId);
        var chatHistoryBackupConfigNotFound = result is null;

        if (chatHistoryBackupConfigNotFound)
        {
            _logger.LogInformation(
                $"[ShouldBackupChatHistory] - conversationId:{conversation.Id}, companyId:{conversation.CompanyId} no chat history backup config was found");

            return false;
        }

        var conversationStatusChangedToClosed = await UpdateConversationStatusChangeTimesAsync(
            conversation,
            oldStatus,
            newStatus,
            conversationStatusModifiedAt);

        if (conversationStatusChangedToClosed)
        {
            _logger.LogInformation(
                $"[ShouldBackupChatHistory] - conversationId:{conversation.Id} status changed to closed and should back up chat history");

            return true;
        }

        _logger.LogInformation(
            $"[ShouldBackupChatHistory] - conversationId:{conversation.Id} status is not changed to closed and should not back up chat history");

        return false;
    }

    public async Task CreateChatHistoryBackupAndSendEmailAsync(string conversationId, string staffId)
    {
        var chatHistoryBackup = await CreateChatHistoryBackupAsync(conversationId);
        var msg = await PrepareChatHistoryBackupEmailInfoAndContentAsync(conversationId, chatHistoryBackup.BackupZipSasUrl, chatHistoryBackup.Attachments);
        await SendChatHistoryBackupEmailAsync(msg, conversationId, staffId);
    }

    #region Helper Methods

    public async Task<string> GetCompanyTimeZoneIdAsync(string companyId)
    {
        return await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .Where(x => x.Id == companyId && !x.IsDeleted)
            .Select(x => x.TimeZoneInfoId)
            .FirstOrDefaultAsync();
    }

    private async Task<Company> GetCompanyAsync(string companyId)
    {
        return await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .Where(x => x.Id == companyId && !x.IsDeleted)
            .FirstOrDefaultAsync();
    }

    private async Task<ChatHistoryBackupConfig> FindChatHistoryBackupConfigAsync(string companyId)
    {
        return await _appDbContext.ChatHistoryBackupConfig
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId && x.IsEnabled)
            .FirstOrDefaultAsync();
    }

    private async Task<ChatHistoryBackupConfig> GetChatHistoryBackupConfigAsync(string companyId)
    {
        var chatHistoryConfig = await FindChatHistoryBackupConfigAsync(companyId);
        var chatHistoryConfigNotFound = chatHistoryConfig is null;

        if (chatHistoryConfigNotFound)
        {
            throw new Exception($"[CreateChatHistoryBackupAsync] - chatHistoryBackupConfig cannot be found companyId:{companyId}");
        }

        return chatHistoryConfig;
    }

    private async Task<Conversation> FindConversationAsync(string conversationId)
    {
        return await _appDbContext.Conversations
            .AsNoTracking()
            .Where(x => x.Id == conversationId)
            .Include(x => x.UserProfile)
            .Include(x => x.Assignee.Identity)
            .FirstOrDefaultAsync();
    }

    private async Task<Conversation> GetConversationAsync(string conversationId)
    {
        var conversation = await FindConversationAsync(conversationId);
        var conversationNotFound = conversation is null;

        if (conversationNotFound)
        {
            throw new Exception($"[CreateChatHistoryBackupAsync] - conversation cannot be found conversationId:{conversationId}");
        }

        return conversation;
    }

    private async Task<List<ConversationMessage>> FindConversationMessagesAsync(Conversation conversation)
    {
        // Get all the uploaded files between the open to closed period
        // If the StatusChangedFromClosedToOpenUpdatedAt is null then using conversation.CreatedAt
        // If the StatusChangedFromClosedToOpenUpdatedAt is not null then using StatusChangedFromClosedToOpenUpdatedAt
        var conversationMessages = await _appDbContext.ConversationMessages
            .AsNoTracking()
            .Where(x => x.ConversationId == conversation.Id)
            .WhereIf(
                conversation.StatusChangedFromClosedToOpenAt != null,
                x => x.CreatedAt >= conversation.StatusChangedFromClosedToOpenAt &&
                     x.CreatedAt <= conversation.StatusChangedToClosedAt)

            // CS found there are some missing conversation messages from the conversation initialed by customer
            // This causes by the delay between the conversation and conversation message
            // Conversation created at is later than conversation message created at (360 dialog)
            // Cannot use conversation created at as the the reference point, conversation message created at should be the reference point
            // ChangeConversationStatus method is related to this issue - handling reopen conversation StatusChangedFromClosedToOpenAt
            .WhereIf(
                conversation.StatusChangedFromClosedToOpenAt == null,
                x => x.CreatedAt <= conversation.StatusChangedToClosedAt)
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync();

        return conversationMessages;
    }

    private async Task<List<ConversationMessage>> GetConversationMessagesAsync(Conversation conversation)
    {
        var conversationMessages = await FindConversationMessagesAsync(conversation);
        var conversationMessageIsEmpty = conversationMessages.Count == 0;

        if (conversationMessageIsEmpty)
        {
            // Display a log message for showing there is no conversation messages between that period
            _logger.LogWarning(
                conversation.StatusChangedFromClosedToOpenAt == null
                    ? $"[CreateChatHistoryBackup] - conversationId:{conversation.Id} there is no conversation message between that period {conversation.CreatedAt.ToString()} <> {conversation.StatusChangedToClosedAt.ToString()}"
                    : $"[CreateChatHistoryBackup] - conversationId:{conversation.Id} there is no conversation message between that period {conversation.StatusChangedFromClosedToOpenAt.ToString()} <> {conversation.StatusChangedToClosedAt.ToString()}");
        }

        return conversationMessages;
    }

    private static bool IsConversationStatusChangedFromClosedToOpen(string oldStatus, string newStatus)
    {
        return oldStatus.ToLower() == "closed" && newStatus.ToLower() == "open";
    }

    private static bool IsConversationStatusChangedToClosed(string newStatus)
    {
        return newStatus.ToLower() == "closed";
    }

    private async Task<bool> UpdateConversationStatusChangeTimesAsync(
        Conversation conversation,
        string oldStatus,
        string newStatus,
        DateTime conversationStatusModifiedAt)
    {
        if (IsConversationStatusChangedFromClosedToOpen(oldStatus, newStatus))
        {
            conversation.StatusChangedFromClosedToOpenAt = conversationStatusModifiedAt;
            await _appDbContext.SaveChangesAsync();

            return false;
        }

        if (IsConversationStatusChangedToClosed(newStatus))
        {
            conversation.StatusChangedToClosedAt = conversationStatusModifiedAt;
            await _appDbContext.SaveChangesAsync();

            return true;
        }

        return false;
    }

    private static List<ConversationMessage> FilterConversationMessagesBasedOnSupportedChannels(
        List<ConversationMessage> conversationMessages,
        bool isLiveChatSupported)
    {
        var pred = PredicateBuilder.New<ConversationMessage>(true);

        // Default channel for chat history backup is 360 dialog
        // Fail-to-send messages will not be backed up
        // Live Chat is supported by us
        pred = pred.And(p => p.ChannelStatusMessage == null);
        pred = pred.And(p => p.Whatsapp360DialogSenderId != null || p.Whatsapp360DialogReceiverId != null);

        // Since z.com migrated from 360 dialog to cloud api
        // 360 dialog and whatsapp cloud api will be the default channels both are supported
        pred = pred.Or(p => p.WhatsappCloudApiSender != null || p.WhatsappCloudApiReceiver != null);

        if (isLiveChatSupported)
        {
            pred = pred.Or(p => p.WebClientSenderId != null || p.WebClientReceiverId != null);
        }

        var conversationMessagesBasedOnSupportedChannels = conversationMessages.Where(pred).ToList();

        return conversationMessagesBasedOnSupportedChannels;
    }

    private static List<long> FilterConversationMessageIdsThatMessageTypeAreFile(
        List<ConversationMessage> conversationMessages)
    {
        const string fileMessageType = "file";
        return conversationMessages
            .Where(x => x.MessageType.Equals(fileMessageType))
            .Select(x => x.Id)
            .ToList();
    }

    private async Task<List<UploadedFile>> GetUploadedFilesForMessagesAsync(IReadOnlyList<long> messageIds)
    {
        return await _appDbContext.ConversationMessageUploadedFiles
            .AsNoTracking()
            .Where(x => messageIds.Contains(x.ConversationMessageId))
            .ToListAsync();
    }

    private async Task<MemoryStream> DownloadUploadedFileAsync(UploadedFile file)
    {
        var fileMemoryStream =
            await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);
        return fileMemoryStream;
    }

    private static string CreateUniqueFilename(string filename)
    {
        var filenameArray = filename.Split("/");
        var time = filenameArray[^2].Replace(":", ".");
        return time + "-" + filenameArray[^1];
    }

    private async Task<(string Filename, byte[] FileBytes)> DownloadAndPrepareFileAsync(UploadedFile file)
    {
        var fileMemoryStream = await DownloadUploadedFileAsync(file);
        var filename = CreateUniqueFilename(file.Filename);
        return (filename, fileMemoryStream.ToArray());
    }

    private async Task<Dictionary<string, byte[]>> DownloadUploadedFilesFromBlobStorageAsync(
        List<ConversationMessage> conversationMessages)
    {
        if (conversationMessages is null)
        {
            throw new ArgumentNullException(nameof(conversationMessages));
        }

        var downloadedFiles = new Dictionary<string, byte[]>();

        var conversationMessageIdsThatMessageTypeAreFile =
            FilterConversationMessageIdsThatMessageTypeAreFile(conversationMessages);

        if (!conversationMessageIdsThatMessageTypeAreFile.Any())
        {
            return downloadedFiles;
        }

        var files = await GetUploadedFilesForMessagesAsync(conversationMessageIdsThatMessageTypeAreFile);
        var downloadTasks = files.Select(DownloadAndPrepareFileAsync);
        var downloadedFileData = await Task.WhenAll(downloadTasks);

        foreach (var data in downloadedFileData)
        {
            downloadedFiles.Add(data.Filename, data.FileBytes);
        }

        return downloadedFiles;
    }

    private async Task<byte[]> CreateUploadedFilesBackupZipAsync(
        Dictionary<string, byte[]> downloadedBlobFiles)
    {
        if (downloadedBlobFiles.IsNullOrEmpty())
        {
            return Array.Empty<byte>();
        }

        byte[] backupZipFileBytes;

        using (var zipFileStream = new MemoryStream())
        {
            using (var archive = new ZipArchive(zipFileStream, ZipArchiveMode.Create, true))
            {
                foreach (var file in downloadedBlobFiles)
                {
                    // Open a file stream
                    using (var fileStream = new MemoryStream(file.Value))
                    {
                        var zipEntry = archive.CreateEntry(file.Key);

                        // Open the zip entry file stream
                        await using var stream = zipEntry.Open();

                        // Point the stream to the start point
                        fileStream.Position = 0;

                        // Copy the downloaded file stream to zip entry stream
                        await fileStream.CopyToAsync(stream);
                    }
                }
            }

            backupZipFileBytes = zipFileStream.ToArray();
        }

        return backupZipFileBytes;
    }

    private static string CreateBackupZipFilename(Conversation conversation, string timeZoneId)
    {
        var currentDate = DateTime.UtcNow.ConvertUtcDateTimeToSpecificTimeZoneDateTime(timeZoneId);
        var currentDateString = $"{currentDate:yyyy-mm-dd}";

        var backupZipFileName = conversation.UserProfile?.PhoneNumber;

        if (backupZipFileName.IsNullOrEmpty())
        {
            return conversation.UserProfile?.FirstName + "_" + currentDateString;
        }

        return "+" + backupZipFileName + "_" + currentDateString;
    }

    private async Task<string> UploadBackupZipFileToBlobStorageAsync(
            Conversation conversation,
            byte[] backupZipFileByte,
            string backupZipFileName)
        {
            var backupZipBlobStorageUrl = string.Empty;
            const string zipFileExtension = ".zip";
            var backupZipBlobStorageFilePath =
                $"ConversationBackupFiles/{conversation.Id}" +
                $"/{DateTime.UtcNow:o}/{backupZipFileName + zipFileExtension}";

            var storageConfig = await _appDbContext.ConfigStorageConfigs.AsNoTracking()
                .FirstOrDefaultAsync(x => x.CompanyId == conversation.CompanyId);

            if (storageConfig is null)
            {
                throw new Exception($"storageConfig cannot be found. companyId:{conversation.CompanyId}");
            }

            // If the byte is not larger than zero no upload zip to blob storage
            if (!backupZipFileByte.IsNullOrEmpty())
            {
                using (var backupZipStream = new MemoryStream(backupZipFileByte))
                {
                    _logger.LogInformation(
                        $"[UploadBackupZipFileToBlobStorage] - conversationId:{conversation.Id} start uploading file to blob storage");
                    backupZipBlobStorageUrl = await _azureBlobStorageService.UploadFileAsBlob(
                        backupZipStream,
                        backupZipBlobStorageFilePath,
                        storageConfig.ContainerName,
                        "application/zip");

                    if (backupZipBlobStorageUrl.IsNullOrEmpty())
                    {
                        _logger.LogWarning(
                            $"[UploadBackupZipFileToBlobStorage] - conversationId:{conversation.Id} the backup file is failed to upload to blob storage:{backupZipBlobStorageUrl}");
                        return backupZipBlobStorageUrl;
                    }
                }

                _logger.LogInformation(
                    $"[UploadBackupZipFileToBlobStorage] - conversationId:{conversation.Id} the backup file is uploaded to blob storage:{backupZipBlobStorageUrl}");
                backupZipBlobStorageUrl = _azureBlobStorageService.GetAzureBlobSasUriForever(
                    backupZipBlobStorageFilePath,
                    storageConfig.ContainerName);
            }
            else
            {
                _logger.LogWarning(
                    conversation.StatusChangedFromClosedToOpenAt == null
                        ? $"[UploadBackupZipFileToBlobStorage] - conversationId:{conversation.Id} there is no uploaded file between {conversation.CreatedAt.ToString()} <> {conversation.StatusChangedToClosedAt.ToString()}"
                        : $"[UploadBackupZipFileToBlobStorage] - conversationId:{conversation.Id} there is no uploaded file between {conversation.StatusChangedFromClosedToOpenAt.ToString()} <> {conversation.StatusChangedToClosedAt.ToString()}");
            }

            return backupZipBlobStorageUrl;
        }

    private async Task<string> GenerateBackupZipSasUrlAsync(
        Conversation conversation,
        string timeZoneId,
        Dictionary<string, byte[]> downloadedBlobFiles)
    {
        var backupZipBytes = await CreateUploadedFilesBackupZipAsync(downloadedBlobFiles);
        var backupZipFileName = CreateBackupZipFilename(conversation, timeZoneId);
        var backupZipSasUrl = await UploadBackupZipFileToBlobStorageAsync(
            conversation,
            backupZipBytes,
            backupZipFileName);
        return backupZipSasUrl;
    }

    private async Task<string> GetTextFromConversationMessageAsync(ConversationMessage conversationMessage)
    {
        string text = null;
        if (conversationMessage.MessageType == "interactive")
        {
            var whatsapp360DialogExtendedMessagePayload = await _appDbContext
                .Whatsapp360DialogExtendedMessagePayload
                .AsNoTracking()
                .Where(x => x.ConversationMessageId == conversationMessage.Id)
                .FirstOrDefaultAsync();

            if (whatsapp360DialogExtendedMessagePayload != null)
            {
                text = whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogInteractiveObject.Body.Text;
            }

            var whatsappCloudApiExtendedMessagePayload = await _appDbContext
                .ExtendedMessagePayloads
                .AsNoTracking()
                .Where(x => x.ConversationMessageId == conversationMessage.Id)
                .FirstOrDefaultAsync();

            if (whatsappCloudApiExtendedMessagePayload is not null)
            {
                text = whatsappCloudApiExtendedMessagePayload.ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject.Body.Text;
            }
        }
        else
        {
            text = conversationMessage.MessageContent;
        }

        return text;
    }

    private async Task<string> GetConversationMessageFromUserAsync(
            ConversationMessage conversationMessage,
            Company company,
            UserProfile userProfile,
            bool isLiveChatSupported)
    {
        string fromUser;

        if (conversationMessage.IsSentFromSleekflow)
        {
            fromUser = conversationMessage.Channel switch
            {
                ChannelTypes.WhatsappCloudApi => conversationMessage.ChannelIdentityId,

                ChannelTypes.Whatsapp360Dialog => await _appDbContext.SenderWhatsApp360DialogSenders
                    .AsNoTracking()
                    .Where(wa360 => wa360.Id == conversationMessage.Whatsapp360DialogReceiverId)
                    .Select(wa360 => wa360.ChannelWhatsAppPhoneNumber)
                    .FirstOrDefaultAsync(),

                _ => null
            };

            // If the conversation message channel is Live Chat
            if (isLiveChatSupported && conversationMessage.Channel == ChannelTypes.LiveChat)
            {
                fromUser = company.CompanyName;
            }
        }
        else
        {
            fromUser = conversationMessage.Channel switch
            {
                ChannelTypes.WhatsappCloudApi => await _appDbContext.WhatsappCloudApiSenders.AsNoTracking()
                    .Where(sender => sender.WhatsappId == conversationMessage.WhatsappCloudApiSender.WhatsappId)
                    .Select(sender => sender.WhatsappId)
                    .FirstOrDefaultAsync(),

                ChannelTypes.Whatsapp360Dialog => await _appDbContext.SenderWhatsApp360DialogSenders.AsNoTracking()
                    .Where(wa360 => wa360.Id == conversationMessage.Whatsapp360DialogSenderId)
                    .Select(wa360 => wa360.WhatsAppId)
                    .FirstOrDefaultAsync(),

                _ => null
            };

            // If the conversation message channel is Live Chat
            if (isLiveChatSupported && conversationMessage.Channel == ChannelTypes.LiveChat)
            {
                var fromUserUserProfile = userProfile;

                fromUser = await _appDbContext.SenderWebClientSenders
                    .AsNoTracking()
                    .Where(sender => sender.Id == conversationMessage.WebClientSenderId)
                    .Select(sender => sender.Name)
                    .FirstOrDefaultAsync();

                // Live chat phone number, email and name can be optional
                if (!fromUserUserProfile.FirstName.IsNullOrEmpty())
                {
                    fromUser = fromUserUserProfile.FirstName;
                }

                if (!fromUserUserProfile.Email.IsNullOrEmpty())
                {
                    fromUser = fromUserUserProfile.Email;
                }

                if (!fromUserUserProfile.PhoneNumber.IsNullOrEmpty())
                {
                    fromUser = fromUserUserProfile.PhoneNumber;
                }
            }
        }

        return fromUser;
    }

    private async Task<string> GetConversationMessageToUserAsync(
         ConversationMessage conversationMessage,
         Company company,
         UserProfile userProfile,
         bool isLiveChatSupported)
     {
         string toUser;

         if (conversationMessage.IsSentFromSleekflow)
         {
             toUser = conversationMessage.Channel switch
             {
                 ChannelTypes.WhatsappCloudApi => conversationMessage.WhatsappCloudApiReceiver.WhatsappId,

                 ChannelTypes.Whatsapp360Dialog => await _appDbContext.SenderWhatsApp360DialogSenders.AsNoTracking()
                     .Where(wa360 => wa360.Id == conversationMessage.Whatsapp360DialogReceiverId)
                     .Select(wa360 => wa360.WhatsAppId)
                     .FirstOrDefaultAsync(),

                 _ => null
             };

             // If the conversation message channel is Live Chat
             if (isLiveChatSupported && conversationMessage.Channel == ChannelTypes.LiveChat)
             {
                 var toUserUserProfile = userProfile;

                 toUser = await _appDbContext.SenderWebClientSenders
                     .AsNoTracking()
                     .Where(sender => sender.Id == conversationMessage.WebClientReceiverId)
                     .Select(sender => sender.Name)
                     .FirstOrDefaultAsync();

                 // Live chat phone number, email and name can be optional
                 if (!toUserUserProfile.FirstName.IsNullOrEmpty())
                 {
                     toUser = toUserUserProfile.FirstName;
                 }

                 if (!toUserUserProfile.Email.IsNullOrEmpty())
                 {
                     toUser = toUserUserProfile.Email;
                 }

                 if (!toUserUserProfile.PhoneNumber.IsNullOrEmpty())
                 {
                     toUser = toUserUserProfile.PhoneNumber;
                 }
             }
         }
         else
         {
             toUser = conversationMessage.Channel switch
             {
                 ChannelTypes.WhatsappCloudApi => conversationMessage.WhatsappCloudApiSender.WhatsappChannelPhoneNumber,

                 ChannelTypes.Whatsapp360Dialog => await _appDbContext.SenderWhatsApp360DialogSenders.AsNoTracking()
                     .Where(wa360 => wa360.Id == conversationMessage.Whatsapp360DialogSenderId)
                     .Select(wa360 => wa360.ChannelWhatsAppPhoneNumber)
                     .FirstOrDefaultAsync(),

                 _ => null
             };

             // If the conversation message channel is Live Chat
             if (isLiveChatSupported && conversationMessage.Channel == ChannelTypes.LiveChat)
             {
                 toUser = company.CompanyName;
             }
         }

         return toUser;
     }

    private async Task<List<string>> GetConversationMessageFilenamesAsync(ConversationMessage conversationMessage)
    {
        var filenameList = new List<string>();

        if (conversationMessage.MessageType != "file")
        {
            return null;
        }

        var filenames = await _appDbContext.ConversationMessageUploadedFiles
            .AsNoTracking()
            .Where(x => x.ConversationMessageId == conversationMessage.Id)
            .Select(x => x.Filename)
            .ToListAsync();

        // filename: Conversation/e17f4666-fc74-47f7-bc59-f874263c8f46/2022-07-15T07:06:06.1913600Z/螢幕截圖 2022-06-01 10.50.11.png
        foreach (var filename in filenames)
        {
            // uniqueFilename: 2022-07-15T07.06.06.1913600Z-螢幕截圖 2022-06-01 10.50.11.png
            var uniqueFilename = CreateUniqueFilename(filename);
            filenameList.Add(uniqueFilename);
        }

        return filenameList;
    }

    private async Task<(string Filename, byte[] FileBytes)> CreateChatDotJsonAsync(
        List<ConversationMessage> conversationMessages,
        Conversation conversation,
        string timeZoneId,
        bool isLiveChatSupported)
    {
        var userProfile = conversation.UserProfile;
        var company = await GetCompanyAsync(conversation.CompanyId);
        var exportMessageHistories = new List<ZDotComConversationSnapshotDto>();

        foreach (var conversationMessage in conversationMessages)
        {
            var text = await GetTextFromConversationMessageAsync(conversationMessage);

            var exportMessageHistory = new ZDotComConversationSnapshotDto
            {
                Text = text,
                FromUser =
                    await GetConversationMessageFromUserAsync(
                        conversationMessage,
                        company,
                        userProfile,
                        isLiveChatSupported),
                ToUser =
                    await GetConversationMessageToUserAsync(
                        conversationMessage,
                        company,
                        userProfile,
                        isLiveChatSupported),
                Filenames = await GetConversationMessageFilenamesAsync(conversationMessage),
                FormattedTimeString = conversationMessage.CreatedAt.TimeZoned(timeZoneId).ToString("yyyy-MM-dd HH:mm")
            };
            exportMessageHistories.Add(exportMessageHistory);
        }

        var chatDotJsonFileName = CreateChatDotJsonFilename(conversation, timeZoneId);
        var chatDotJsonFileBytes = JsonFileExtension.PrettyWrite(exportMessageHistories);

        return (chatDotJsonFileName, chatDotJsonFileBytes);
    }

    private static string CreateChatDotJsonFilename(Conversation conversation, string timeZoneId)
    {
        var ticketClosedDateTimeString = string.Empty;
        var userProfile = conversation.UserProfile;

        var customerName = string.Join(" ", userProfile.FirstName, userProfile.LastName).Trim();

        if (conversation.StatusChangedToClosedAt.HasValue)
        {
            var ticketClosedDateTime = (DateTime) conversation.StatusChangedToClosedAt;
            ticketClosedDateTimeString = ticketClosedDateTime
                .ConvertUtcDateTimeToSpecificTimeZoneDateTime(timeZoneId)
                .ToString("yyyy-MM-dd HH:mm");
        }

        // {First Name} {Last Name} - {Ticket close time}
        var filename = string.Join(" - ", customerName, ticketClosedDateTimeString) + ".json";
        return filename;
    }

    private static Dictionary<string, byte[]> CreateAttachmentsForChatHistoryBackupEmail(
        (string ChatDotJsonFilename, byte[] ChatDotJsonFileBytes) chatDotJson,
        Dictionary<string, byte[]> uploadedFilesDownloadedFromBlobStorage)
    {
        var attachments = new Dictionary<string, byte[]>
        {
            {
                chatDotJson.ChatDotJsonFilename, chatDotJson.ChatDotJsonFileBytes
            }
        };

        if (uploadedFilesDownloadedFromBlobStorage.IsNullOrEmpty())
        {
            return attachments;
        }

        foreach (var file in uploadedFilesDownloadedFromBlobStorage)
        {
            attachments.Add(file.Key, file.Value);
        }

        return attachments;
    }

    private async Task<(string BackupZipSasUrl, Dictionary<string, byte[]> Attachments)> CreateChatHistoryBackupAsync(string conversationId)
    {
        var conversation = await GetConversationAsync(conversationId);
        var chatHistoryConfig = await GetChatHistoryBackupConfigAsync(conversation.CompanyId);
        var conversationMessages = await GetConversationMessagesAsync(conversation);

        var filteredConversationMessages = FilterConversationMessagesBasedOnSupportedChannels(
            conversationMessages,
            chatHistoryConfig.IsLiveChatSupported);

        var downloadedBlobFiles = await DownloadUploadedFilesFromBlobStorageAsync(filteredConversationMessages);

        var timeZoneId = await GetCompanyTimeZoneIdAsync(conversation.CompanyId);

        var backupZipSasUrl =
            await GenerateBackupZipSasUrlAsync(conversation, timeZoneId, downloadedBlobFiles);

        var chatDotJson = await CreateChatDotJsonAsync(
            conversationMessages,
            conversation,
            timeZoneId,
            chatHistoryConfig.IsLiveChatSupported);

        var attachments = CreateAttachmentsForChatHistoryBackupEmail(chatDotJson, downloadedBlobFiles);

        return (backupZipSasUrl, attachments);
    }

    private async Task<SendGridMessage> PrepareChatHistoryBackupEmailInfoAndContentAsync(
        string conversationId,
        string backupFileBlobStorageSharingUrl,
        Dictionary<string, byte[]> attachments)
    {
        var conversation = await GetConversationAsync(conversationId);
        var timeZoneId = await GetCompanyTimeZoneIdAsync(conversation.CompanyId);
        var chatHistoryConfig = await GetChatHistoryBackupConfigAsync(conversation.CompanyId);
        var backupDateTime = $"{DateTime.UtcNow.ConvertUtcDateTimeToSpecificTimeZoneDateTime(timeZoneId):yyyy-MM-dd HH:mm}";

        var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
            .AsNoTracking()
            .Where(x => x.NotificationType == NotificationType.ChatHistoryBackup)
            .Select(x => x.EmailTemplate)
            .FirstOrDefaultAsync();

        if (emailTemplate is null)
        {
            throw new Exception(
                $"[PrepareChatHistoryBackupEmailInfoAndContent] - emailTemplate cannot be found, conversationId:{conversation.Id}");
        }

        // Conversation can be unassigned therefore Assignee can be null, will cause unexpected exception
        var staffName = conversation.Assignee?.Identity?.DisplayName;
        var customerName = conversation.UserProfile?.FirstName + conversation.UserProfile?.LastName;
        var customerPhoneNo = conversation.UserProfile?.PhoneNumber;
        var subject = staffName + " <> " + customerName + " (" + customerPhoneNo + ") " + backupDateTime;
        var plainTextContent = subject;

        var htmlContent = emailTemplate
            .Replace(
                oldValue: "{HiddenBlobStorageMessage}",
                newValue: string.IsNullOrEmpty(backupFileBlobStorageSharingUrl) ? "none" : "inline")
            .Replace(oldValue: "{BlobStorageUrl}", newValue: backupFileBlobStorageSharingUrl);
        var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
        var from = new EmailAddress(email: coreEmailConfig?.Email, name: coreEmailConfig?.SenderName);
        var to = chatHistoryConfig.Emails.Select(email => new EmailAddress(email: email)).ToList();

        var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
            @from: from,
            tos: to,
            subject: subject,
            plainTextContent: plainTextContent,
            htmlContent: htmlContent);

        await AddAttachmentsToChatHistoryBackupEmailAsync(conversationId, attachments, msg);

        // Bypass Unsubscribe list, Spam and Bounce management
        msg.MailSettings = new MailSettings
        {
            BypassUnsubscribeManagement = new BypassUnsubscribeManagement
            {
                Enable = true
            },
            BypassSpamManagement = new BypassSpamManagement()
            {
                Enable = true
            },
            BypassBounceManagement = new BypassBounceManagement()
            {
                Enable = true
            }
        };

        return msg;
    }

    private async Task AddAttachmentsToChatHistoryBackupEmailAsync(
        string conversationId,
        Dictionary<string, byte[]> attachments,
        SendGridMessage msg)
    {
        long attachmentSizeCount = 0;

        foreach (var file in attachments)
        {
            attachmentSizeCount += file.Value.LongLength;

            // 23000000MB is the current system email attachment Limit
            if (attachmentSizeCount >= 23000000)
            {
                _logger.LogInformation(
                    "[SendChatHistoryBackupEmail] - conversationId:{ConversationId} - current size of the email attachments: {AttachmentSizeCount}",
                    conversationId,
                    attachmentSizeCount);

                _logger.LogError(
                    "[SendChatHistoryBackupEmail] - conversationId:{ConversationId} Fail to add the attachment - {FileKey} size:{FileValueLongLength}," +
                    " since the attachments exceed the size limit for attachments 23000000",
                    conversationId,
                    file.Key,
                    file.Value.LongLength);

                attachmentSizeCount -= file.Value.LongLength;
            }
            else
            {
                using (var fileStream = new MemoryStream(buffer: file.Value))
                {
                    await msg.AddAttachmentAsync(filename: file.Key, contentStream: fileStream);
                }
            }
        }

        _logger.LogInformation(
            "[SendChatHistoryBackupEmail] - conversationId:{ConversationId} - current size of the email attachments: {AttachmentSizeCount}",
            conversationId,
            attachmentSizeCount);
    }

    private async Task NotifyCustomerOfSuccessfulBackup(string companyId, string userProfileId, string staffId)
    {
        // ActivityLogs for notifying the customer we have sent the email to them
        await _auditHubAuditLogService.CreateUserProfileChatHistoryBackedUpAsync(
            companyId,
            userProfileId,
            staffId,
            null);
    }

    private async Task SendChatHistoryBackupEmailAsync(SendGridMessage msg, string conversationId, string staffId)
    {
        var response = await _emailNotificationService.SendChatHistoryBackupEmailAsync(msg);
        var backupEmailSentSuccessfully = response.IsSuccessStatusCode;

        var conversation = await GetConversationAsync(conversationId);
        var chatHistoryBackupConfig = await GetChatHistoryBackupConfigAsync(conversation.CompanyId);

        if (backupEmailSentSuccessfully)
        {
            _logger.LogInformation(
                "[SendChatHistoryBackupEmailAsync] - conversationId:{ConversationId} the backup email is sent successfully",
                conversation.Id);

            await NotifyCustomerOfSuccessfulBackup(conversation.CompanyId, conversation.UserProfileId, staffId);
        }
        else
        {
            // A 413 HTTP error code occurs when the size of a client's request exceeds the server's file size limit.
            _logger.LogError(
                "[SendChatHistoryBackupEmailAsync] - conversationId:{ConversationId} fail to send the backup email. response:{JsonResponse}",
                conversation.Id,
                response.ToJson());

            chatHistoryBackupConfig.FailToSendAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();
        }
    }

    #endregion
}