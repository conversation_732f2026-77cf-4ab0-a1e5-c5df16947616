using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Perf.Components.Configs.SleekflowCore;

public class HubSpotSmtpConfig
{
    [JsonProperty("username")]
    public string Username { get; set; }

    [JsonProperty("password")]
    public string Password { get; set; }

    [Json<PERSON>roperty("send_execution_usage_reached_threshold_email")]
    public SendExecutionUsageReachedThresholdEmailConfig SendExecutionUsageReachedThresholdEmail { get; set; }

    public HubSpotSmtpConfig(string username, string password)
    {
        Username = username;
        Password = password;
    }
}

public class SendExecutionUsageReachedThresholdEmailConfig
{
    [<PERSON><PERSON><PERSON>roper<PERSON>("username")]
    public string Username { get; set; }

    [JsonProperty("password")]
    public string Password { get; set; }
}