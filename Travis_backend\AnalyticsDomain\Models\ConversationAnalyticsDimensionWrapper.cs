﻿using Travis_backend.Constants;

namespace Travis_backend.AnalyticsDomain.Models;

public class ConversationAnalyticsDimensionWrapper
{
    public ConversationAnalyticsDimensions Dimension { get; }

    public bool IsContactDimension { get; }

    public bool IsBusinessHourDimension { get; }

    public ConversationAnalyticsDimensionWrapper(
        ConversationAnalyticsDimensions dimension,
        bool isContactDimension,
        bool isBusinessHourDimension)
    {
        Dimension = dimension;
        IsContactDimension = isContactDimension;
        IsBusinessHourDimension = isBusinessHourDimension;
    }
}