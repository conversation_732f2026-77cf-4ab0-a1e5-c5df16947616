﻿using System;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.Models.Interfaces;
using Travis_backend.ConversationServices.Models;
using Travis_backend.Models.ChatChannelConfig;

namespace Travis_backend.ChannelDomain.ViewModels.Interfaces;

public interface IMessagingChannelDto
{
    string ChannelType { get; }

    string ChannelIdentityId { get; }

    string ChannelDisplayName { get; }
}

public static class IMessagingChannelDtoExtension
{
    public static IMessagingChannelDto MapFromIMessagingChannel(this IMessagingChannel channel)
    {
        if (channel is EmailConfig emailConfig)
        {
            return new EmailConfigViewModel
            {
                Name = emailConfig.Name,
                Domain = emailConfig.Domain,
                Email = emailConfig.Email,
                ConnectedDateTime = emailConfig.ConnectedDateTime,
                IsShowInWidget = emailConfig.IsShowInWidget,
                ChannelType = emailConfig.ChannelType,
                ChannelIdentityId = emailConfig.ChannelIdentityId,
                ChannelDisplayName = emailConfig.ChannelDisplayName,
            };
        }

        if (channel is WeChatConfig wechatConfig)
        {
            return new WeChatConfigViewModel
            {
                Name = wechatConfig.Name,
                WebChatId = wechatConfig.WebChatId,
                AppId = wechatConfig.AppId,
                QRCodeURL = wechatConfig.QRCodeURL,
                ConnectedDateTime = wechatConfig.ConnectedDateTime,
                IsShowInWidget = wechatConfig.IsShowInWidget,
                ChannelType = wechatConfig.ChannelType,
                ChannelIdentityId = wechatConfig.ChannelIdentityId,
                ChannelDisplayName = wechatConfig.ChannelDisplayName
            };
        }

        if (channel is InstagramConfig config)
        {
            return new InstagramConfigResponse
            {
                InstagramPageId = config.InstagramPageId,
                Name = config.Name,
                PageName = config.PageName,
                ConnectedDateTime = config.ConnectedDateTime,
                IsShowInWidget = config.IsShowInWidget,
                Status = config.Status.ToString(),
                ChannelType = config.ChannelType,
                ChannelIdentityId = config.ChannelIdentityId,
                ChannelDisplayName = config.ChannelDisplayName
            };
        }

        if (channel is FacebookConfig facebookConfig)
        {
            return new FacebookConfigViewModel
            {
                Id = facebookConfig.Id,
                PageId = facebookConfig.PageId,
                Name = facebookConfig.Name,
                PageName = facebookConfig.PageName,
                ConnectedDateTime = facebookConfig.ConnectedDateTime,
                IsShowInWidget = facebookConfig.IsShowInWidget,
                Status = facebookConfig.Status.ToString(),
                ChannelType = facebookConfig.ChannelType,
                ChannelIdentityId = facebookConfig.ChannelIdentityId,
                ChannelDisplayName = facebookConfig.ChannelDisplayName
            };
        }

        if (channel is LineConfig lineConfig)
        {
            return new LineConfigViewModel
            {
                Name = lineConfig.Name,
                BasicId = lineConfig.BasicId,
                ChannelID = lineConfig.ChannelID,
                NumberOfMessagesSentThisMonth = lineConfig.NumberOfMessagesSentThisMonth,
                NumberOfTargetLimitForAdditionalMessages = lineConfig.NumberOfTargetLimitForAdditionalMessages,
                ConnectedDateTime = lineConfig.ConnectedDateTime,
                IsShowInWidget = lineConfig.IsShowInWidget,
                ChannelType = lineConfig.ChannelType,
                ChannelIdentityId = lineConfig.ChannelIdentityId,
                ChannelDisplayName = lineConfig.ChannelDisplayName,
            };
        }

        if (channel is ViberConfig viberConfig)
        {
            return new ViberConfigResponse
            {
                Id = viberConfig.Id,
                DisplayName = viberConfig.DisplayName,
                ViberBotId = viberConfig.ViberBotId,
                ViberBotName = viberConfig.ViberBotName,
                Uri = viberConfig.Uri,
                ViberBotSenderName = viberConfig.ViberBotSenderName,
                IconUrl = viberConfig.IconUrl,
                ConnectedDateTime = viberConfig.ConnectedDateTime,
                IsShowInWidget = viberConfig.IsShowInWidget,
                ChannelType = viberConfig.ChannelType,
                ChannelIdentityId = viberConfig.ChannelIdentityId,
                ChannelDisplayName = viberConfig.ChannelDisplayName,
            };
        }

        if (channel is SMSConfig smsConfig)
        {
            return new SMSViewConfigViewModel
            {
                TwilioAccountId = smsConfig.TwilioAccountId,
                Name = smsConfig.Name,
                SMSSender = smsConfig.SMSSender,
                ConnectedDateTime = smsConfig.ConnectedDateTime,
                IsShowInWidget = smsConfig.IsShowInWidget,
                ChannelType = smsConfig.ChannelType,
                ChannelIdentityId = smsConfig.ChannelIdentityId,
                ChannelDisplayName = smsConfig.ChannelDisplayName,
            };
        }

        if (channel is TelegramConfig telegramConfig)
        {
            return new TelegramConfigDto
            {
                Id = telegramConfig.Id,
                DisplayName = telegramConfig.DisplayName,
                TelegramBotId = telegramConfig.TelegramBotId,
                TelegramBotDisplayName = telegramConfig.TelegramBotDisplayName,
                TelegramBotUserName = telegramConfig.TelegramBotUserName,
                ConnectedDateTime = telegramConfig.ConnectedDateTime,
                IsShowInWidget = telegramConfig.IsShowInWidget,
                ChannelType = telegramConfig.ChannelType,
                ChannelIdentityId = telegramConfig.ChannelIdentityId,
                ChannelDisplayName = telegramConfig.ChannelDisplayName
            };
        }

        if (channel is WhatsAppConfig whatsappConfig)
        {
            return new WhatsAppConfigViewModel
            {
                TwilioAccountId = $"{whatsappConfig.TwilioAccountId};{whatsappConfig.WhatsAppSender}",
                WhatsAppSender = whatsappConfig.WhatsAppSender,
                Name = whatsappConfig.Name,
                ConnectedDateTime = whatsappConfig.ConnectedDateTime,
                IsShowInWidget = whatsappConfig.IsShowInWidget,
                ReadMoreTemplateId = whatsappConfig.ReadMoreTemplateId,
                ReadMoreTemplateMessage = whatsappConfig.ReadMoreTemplateMessage,
                IsSubaccount = whatsappConfig.IsSubaccount,
                MessagingServiceSid = whatsappConfig.MessagingServiceSid,
                ChannelType = whatsappConfig.ChannelType,
                ChannelIdentityId = whatsappConfig.ChannelIdentityId,
                ChannelDisplayName = whatsappConfig.ChannelDisplayName
            };
        }

        if (channel is WhatsApp360DialogConfig whatsapp360DialogConfig)
        {
            return new WhatsApp360DialogConfigViewModel
            {
                Id = whatsapp360DialogConfig.Id,
                CompanyId = whatsapp360DialogConfig.CompanyId,
                ChannelName = whatsapp360DialogConfig.ChannelName,
                WhatsAppPhoneNumber = whatsapp360DialogConfig.WhatsAppPhoneNumber,
                WhatsAppChannelSetupName = whatsapp360DialogConfig.WhatsAppChannelSetupName,
                ChannelStatus = whatsapp360DialogConfig.ChannelStatus,
                AccountMode = whatsapp360DialogConfig.AccountMode,
                ClientId = whatsapp360DialogConfig.ClientId,
                ChannelId = whatsapp360DialogConfig.ChannelId,
                CurrentQualityRating = whatsapp360DialogConfig.CurrentQualityRating,
                CurrentLimit = whatsapp360DialogConfig.CurrentLimit,
                WabaAccountId = whatsapp360DialogConfig.WabaAccountId,
                WabaStatus = whatsapp360DialogConfig.WabaStatus,
                WabaBusinessId = whatsapp360DialogConfig.WabaBusinessId,
                WabaAccountName = whatsapp360DialogConfig.WabaAccountName,
                WabaBusinessStatus = whatsapp360DialogConfig.WabaBusinessStatus,
                WabaAccountType = whatsapp360DialogConfig.WabaAccountType,
                TemplateNamespace = whatsapp360DialogConfig.TemplateNamespace,
                IsClient = whatsapp360DialogConfig.IsClient,
                AccessLevel = whatsapp360DialogConfig.AccessLevel,
                IsOptInEnable = whatsapp360DialogConfig.IsOptInEnable,
                OptInConfig = whatsapp360DialogConfig.OptInConfig == null
                    ? null
                    : new WhatsApp360DialogOptInConfigViewModel
                    {
                        TemplateName = whatsapp360DialogConfig.OptInConfig.TemplateName,
                        TemplateNamespace = whatsapp360DialogConfig.OptInConfig.TemplateNamespace,
                        Language = whatsapp360DialogConfig.OptInConfig.Language,
                        TemplateMessageContent = whatsapp360DialogConfig.OptInConfig.TemplateMessageContent,
                        ReadMoreTemplateButtonMessage =
                            whatsapp360DialogConfig.OptInConfig.ReadMoreTemplateButtonMessage,
                    },
                IsSuspended = whatsapp360DialogConfig.IsSuspended,
                ChannelErrorStatus = whatsapp360DialogConfig.ChannelErrorStatus,
                ChannelErrorStatusStartAt = whatsapp360DialogConfig.ChannelErrorStatusStartAt,
                CreatedAt = whatsapp360DialogConfig.CreatedAt,
                UpdatedAt = whatsapp360DialogConfig.UpdatedAt,
                ChannelType = whatsapp360DialogConfig.ChannelType,
                ChannelIdentityId = whatsapp360DialogConfig.ChannelIdentityId,
                ChannelDisplayName = whatsapp360DialogConfig.ChannelDisplayName
            };
        }

        if (channel is WhatsappCloudApiConfig whatsappCloudApiConfig)
        {
            return new WhatsappCloudApiConfigViewModel
            {
                Id = whatsappCloudApiConfig.Id,
                CompanyId = whatsappCloudApiConfig.CompanyId,
                ChannelName = whatsappCloudApiConfig.ChannelName,
                MessagingHubWabaPhoneNumberId = whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                MessagingHubWabaId = whatsappCloudApiConfig.MessagingHubWabaId,
                WhatsappPhoneNumber = whatsappCloudApiConfig.WhatsappPhoneNumber,
                FacebookDisplayPhoneNumber = whatsappCloudApiConfig.WabaPhoneNumber.FacebookPhoneNumber,
                WhatsappDisplayName = whatsappCloudApiConfig.WhatsappDisplayName,
                FacebookWabaName = whatsappCloudApiConfig.FacebookWabaName,
                FacebookWabaBusinessName = whatsappCloudApiConfig.FacebookWabaBusinessName,
                FacebookWabaBusinessId = whatsappCloudApiConfig.FacebookBusinessId,
                FacebookWabaId = whatsappCloudApiConfig.FacebookWabaId,
                FacebookPhoneNumberId = whatsappCloudApiConfig.FacebookPhoneNumberId,
                TemplateNamespace = whatsappCloudApiConfig.TemplateNamespace,
                FacebookWabaBusinessVerificationStatus =
                    whatsappCloudApiConfig.Waba?.FacebookWabaBusinessVerificationStatus,
                FacebookPhoneNumberIsPinEnabled =
                    whatsappCloudApiConfig.WabaPhoneNumber?.FacebookPhoneNumberIsPinEnabled,
                FacebookPhoneNumberStatus = whatsappCloudApiConfig.WabaPhoneNumber?.FacebookPhoneNumberStatus,
                FacebookPhoneNumberQualityRating =
                    whatsappCloudApiConfig.WabaPhoneNumber?.FacebookPhoneNumberQualityRating,
                FacebookPhoneNumberNameStatus = whatsappCloudApiConfig.WabaPhoneNumber?.FacebookPhoneNumberNameStatus,
                FacebookPhoneNumberNewNameStatus =
                    whatsappCloudApiConfig.WabaPhoneNumber?.FacebookPhoneNumberNewNameStatus,
                FacebookPhoneNumberAccountMode = whatsappCloudApiConfig.WabaPhoneNumber?.FacebookPhoneNumberAccountMode,
                FacebookPhoneNumberCodeVerificationStatus =
                    whatsappCloudApiConfig.WabaPhoneNumber?.FacebookPhoneNumberAccountMode,
                FacebookPhoneNumberIsOfficialBusinessAccount =
                    whatsappCloudApiConfig.WabaPhoneNumber?.FacebookPhoneNumberIsOfficialBusinessAccount,
                FacebookPhoneNumberMessagingLimitTier =
                    whatsappCloudApiConfig.WabaPhoneNumber?.FacebookPhoneNumberMessagingLimitTier,
                FacebookProductCatalogId = whatsappCloudApiConfig.Waba?.WabaProductCatalog?.FacebookProductCatalogId,
                FacebookProductCatalogName =
                    whatsappCloudApiConfig.Waba?.WabaProductCatalog?.FacebookProductCatalogName,
                AccessLevel = whatsappCloudApiConfig.AccessLevel,
                FacebookPhoneNumberQualityScore =
                    whatsappCloudApiConfig.WabaPhoneNumber?.FacebookPhoneNumberQualityScore,
                IsOptInEnable = whatsappCloudApiConfig.IsOptInEnable,
                OptInConfig = whatsappCloudApiConfig.OptInConfig,
                ProductCatalogSetting = whatsappCloudApiConfig.ProductCatalogSetting,
                CreatedAt = whatsappCloudApiConfig.CreatedAt,
                UpdatedAt = whatsappCloudApiConfig.UpdatedAt,
                ChannelType = whatsappCloudApiConfig.ChannelType,
                ChannelIdentityId = whatsappCloudApiConfig.ChannelIdentityId,
                ChannelDisplayName = whatsappCloudApiConfig.ChannelDisplayName,
            };
        }

        if (channel is TikTokConfig tiktokConfig)
        {
            return new TikTokConfigViewModel
            {
                Id = tiktokConfig.Id,
                CompanyId = tiktokConfig.CompanyId,
                TikTokConfigId = tiktokConfig.TikTokConfigId,
                ChannelType = tiktokConfig.ChannelType,
                ChannelIdentityId = tiktokConfig.ChannelIdentityId,
                ChannelDisplayName = tiktokConfig.ChannelDisplayName,
                Metadata = tiktokConfig.Metadata
            };
        }

        throw new InvalidOperationException("Invalid type for channel DTO mapping");
    }
}