using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ChannelDomain.Helpers;
using Travis_backend.ChannelDomain.Helpers.Instagram;
using Travis_backend.ChannelDomain.Repositories;
using Travis_backend.ChannelDomain.Services.Facebook;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.ConversationServices.Models;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.FacebookInstagramIntegrationDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;
using Travis_backend.SignalR;

namespace Travis_backend.ChannelDomain.Services.Instagram;

public interface IInstagramChannelService
{
    Task<string> ConnectInstagramChannelAsync(
        string companyId,
        string pageId,
        string pageName,
        string pageAccessToken,
        string staffIdentityId,
        string businessIntegrationSystemUserAccessToken);

    Task RemoveInstagramChannelAsync(string companyId, string instagramPageId);
}

public class InstagramChannelService : IInstagramChannelService
{
    private readonly ICompanyUsageService _companyUsageService;
    private readonly IFacebookConfigRepository _facebookConfigRepository;
    private readonly IInstagramChannelRepository _instagramChannelRepository;
    private readonly IFacebookService _facebookService;
    private readonly IConfiguration _configuration;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ICompanyService _companyService;
    private readonly ISignalRService _signalRService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<InstagramChannelService> _logger;
    private readonly IMetaChannelConnectionMeters _metaChannelConnectionMeters;

    public InstagramChannelService(
        IFacebookConfigRepository facebookChannelRepository,
        IInstagramChannelRepository instagramChannelRepository,
        IConfiguration configuration,
        IHttpClientFactory httpClientFactory,
        ICompanyService companyService,
        ICompanyInfoCacheService companyInfoCacheService,
        ISignalRService signalRService,
        ICacheManagerService cacheManagerService,
        ApplicationDbContext appDbContext,
        ILogger<InstagramChannelService> logger,
        ICompanyUsageService companyUsageService,
        IFacebookService facebookService,
        IMetaChannelConnectionMeters metaChannelConnectionMeters)
    {
        _facebookConfigRepository = facebookChannelRepository;
        _instagramChannelRepository = instagramChannelRepository;
        _configuration = configuration;
        _httpClientFactory = httpClientFactory;
        _companyService = companyService;
        _signalRService = signalRService;
        _cacheManagerService = cacheManagerService;
        _companyInfoCacheService = companyInfoCacheService;
        _appDbContext = appDbContext;
        _logger = logger;
        _companyUsageService = companyUsageService;
        _facebookService = facebookService;
        _metaChannelConnectionMeters = metaChannelConnectionMeters;
    }

    public async Task<string> ConnectInstagramChannelAsync(
        string companyId,
        string pageId,
        string pageName,
        string pageAccessToken,
        string staffIdentityId,
        string businessIntegrationSystemUserAccessToken)
    {
        try
        {
            return await ConnectChannelAsync(
                companyId,
                pageId,
                pageName,
                pageAccessToken,
                staffIdentityId,
                businessIntegrationSystemUserAccessToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Company {CompanyId} An error occurred while trying to connect to a Instagram Channel - PageId: {PageId}. Exception Message: {ExceptionMessage}",
                nameof(ConnectInstagramChannelAsync),
                companyId,
                pageId,
                ex.Message);

            throw;
        }
    }

    private async Task<string> ConnectChannelAsync(
        string companyId,
        string pageId,
        string pageName,
        string pageAccessToken,
        string staffIdentityId,
        string businessIntegrationSystemUserAccessToken)
    {
        // Don't check for duplicate channel for instagram channel
        // Tim gor: A use case will be channel status disconnected,
        // and the client need to reconnect again to enable the status is active

        var connectChannelType = ResolveConnectChannelType(businessIntegrationSystemUserAccessToken);
        var instagramChannel = connectChannelType switch
        {
            // Re-enable standard Facebook Login due to issues with Facebook Login for Business.
            // Note: SleekFlow app continues to use Facebook Login for Business where it is operational.
            FacebookLogin => await ConnectChannelWithFacebookLoginAsync(
                companyId,
                pageId,
                pageName,
                pageAccessToken,
                staffIdentityId),
            FacebookLoginForBusiness => await ConnectChannelWithFacebookLoginForBusinessAsync(
                companyId,
                pageId,
                pageName,
                pageAccessToken,
                staffIdentityId,
                businessIntegrationSystemUserAccessToken),
            _ => throw new NotImplementedException()
        };

        await PostChannelConnectionActionsAsync(instagramChannel);
        return await GetInstagramPageSubscriptionStatusAsync(pageId, instagramChannel.PageAccessToken);
    }

    #region Facebook Login For Business

    private async Task<InstagramConfig> ConnectChannelWithFacebookLoginForBusinessAsync(
        string companyId,
        string pageId,
        string pageName,
        string pageAccessToken,
        string staffIdentityId,
        string businessIntegrationSystemUserAccessToken)
    {
        if (businessIntegrationSystemUserAccessToken.IsNullOrEmpty())
        {
            throw new ArgumentException(
                "Business Integration System User Access Token could not be null or empty while connecting channel with Facebook Login For Business");
        }

        var existingInstagramChannel =
            await _instagramChannelRepository.FindInstagramChannelByPageIdAsync(companyId, pageId);
        var instagramChannelExists = existingInstagramChannel is not null;

        InstagramConfig instagramChannel;

        var facebookBusinessId = await _facebookService.GetBusinessIdAssociatedWithPageAsync(pageId, businessIntegrationSystemUserAccessToken);

        if (instagramChannelExists)
        {
            instagramChannel = await ReconnectExistingInstagramChannelWithFacebookLoginForBusinessAsync(
                pageAccessToken,
                pageName,
                facebookBusinessId,
                businessIntegrationSystemUserAccessToken,
                existingInstagramChannel);
        }
        else
        {
            await _companyUsageService.EnsureNotExceedingChannelLimitAsync(
                companyId,
                staffIdentityId);

            instagramChannel = await CreateNewInstagramChannelWithFacebookLoginForBusinessAsync(
                companyId,
                pageId,
                pageName,
                pageAccessToken,
                facebookBusinessId,
                businessIntegrationSystemUserAccessToken);
        }

        return instagramChannel;
    }

    private async Task<InstagramConfig> ReconnectExistingInstagramChannelWithFacebookLoginForBusinessAsync(
        string pageAccessToken,
        string pageName,
        string facebookBusinessId,
        string businessIntegrationSystemUserAccessToken,
        InstagramConfig existingInstagramChannel)
    {
        existingInstagramChannel.PageName = pageName;
        existingInstagramChannel.PageAccessToken = pageAccessToken;
        existingInstagramChannel.FacebookBusinessId = facebookBusinessId;
        existingInstagramChannel.BusinessIntegrationSystemUserAccessToken = businessIntegrationSystemUserAccessToken;

        await UpdateInstagramChannelConfigAsync(existingInstagramChannel);

        await _instagramChannelRepository.UpdateInstagramChannelAsync(existingInstagramChannel);

        return existingInstagramChannel;
    }

    private async Task<InstagramConfig> CreateNewInstagramChannelWithFacebookLoginForBusinessAsync(
        string companyId,
        string pageId,
        string pageName,
        string pageAccessToken,
        string facebookBusinessId,
        string businessIntegrationSystemUserAccessToken)
    {
        var instagramChannel = new InstagramConfig()
        {
            PageId = pageId,
            PageName = pageName,
            CompanyId = companyId,
            PageAccessToken = pageAccessToken,
            FacebookBusinessId = facebookBusinessId,
            BusinessIntegrationSystemUserAccessToken = businessIntegrationSystemUserAccessToken
        };

        await UpdateInstagramChannelConfigAsync(instagramChannel);

        await _instagramChannelRepository.CreateInstagramChannelAsync(instagramChannel);

        return instagramChannel;
    }

    #endregion

    #region Facebook Login

    private async Task<InstagramConfig> ReconnectExistingInstagramChannelWithFacebookLoginAsync(
        string shortLivedPageAccessToken,
        InstagramConfig existingInstagramChannel)
    {
        var longLivedPageAccessToken =
            await ExchangeShortLivedAccessTokenToLongLivedAccessTokenAsync(
                shortLivedPageAccessToken);

        existingInstagramChannel.PageAccessToken = longLivedPageAccessToken.AccessToken;

        if (longLivedPageAccessToken.ExpiresIn > 0)
        {
            existingInstagramChannel.ExpireDate = DateTime.UtcNow.AddSeconds(longLivedPageAccessToken.ExpiresIn - 10);
        }

        await UpdateInstagramChannelConfigAsync(existingInstagramChannel);

        await _instagramChannelRepository.UpdateInstagramChannelAsync(existingInstagramChannel);

        return existingInstagramChannel;
    }

    private async Task PostChannelConnectionActionsAsync(InstagramConfig instagramChannel)
    {
        var isSandBoxCompany = await _appDbContext.CompanySandboxes
            .AnyAsync(x => x.CompanyId == instagramChannel.CompanyId);

        if (isSandBoxCompany)
        {
            await _companyService.DeleteSandbox(instagramChannel.CompanyId);
        }

        BackgroundJob.Enqueue<IFacebookInstagramMessageService>(
            x => x.FetchConversationsIGBackground(instagramChannel.InstagramPageId, 10, false));

        var company = await GetCompanyAsync(instagramChannel.CompanyId);

        await _signalRService.SignalROnChannelAdded(
            company,
            new ChannelSignal
            {
                ChannelName = ChannelTypes.Instagram
            });

        await _companyInfoCacheService.RemoveCompanyInfoCache(instagramChannel.CompanyId);
        var connectedFacebookWebhookCacheKeyPattern = new ConnectedFacebookWebhookCacheKeyPattern();
        await _cacheManagerService.DeleteCacheAsync(connectedFacebookWebhookCacheKeyPattern);
    }

    private async Task<InstagramConfig> ConnectChannelWithFacebookLoginAsync(
        string companyId,
        string pageId,
        string pageName,
        string pageAccessToken,
        string staffIdentityId)
    {
        var existingInstagramChannel =
            await _instagramChannelRepository.FindInstagramChannelByPageIdAsync(companyId, pageId);
        var instagramChannelExists = existingInstagramChannel is not null;

        InstagramConfig instagramChannel;
        if (instagramChannelExists)
        {
            // Check if a business integration user access token exists to avoid reconnecting
            // channels already connected via Facebook Login for Business.
            if (IsInstagramChannelConnectedWithFacebookLoginForBusiness(existingInstagramChannel))
            {
                return existingInstagramChannel;
            }

            instagramChannel =
                await ReconnectExistingInstagramChannelWithFacebookLoginAsync(
                    pageAccessToken,
                    existingInstagramChannel);
        }
        else
        {
            await _companyUsageService.EnsureNotExceedingChannelLimitAsync(
                companyId,
                staffIdentityId);

            instagramChannel = await CreateNewInstagramChannelWithFacebookLoginAsync(
                companyId,
                pageId,
                pageName,
                pageAccessToken);
        }

        return instagramChannel;
    }

    private async Task<InstagramConfig> CreateNewInstagramChannelWithFacebookLoginAsync(
        string companyId,
        string pageId,
        string pageName,
        string shortLivedPageAccessToken)
    {
        var instagramChannel = new InstagramConfig()
        {
            PageId = pageId,
            PageName = pageName,
            CompanyId = companyId,
            Name = pageName
        };

        var longLivedPageAccessToken =
            await ExchangeShortLivedAccessTokenToLongLivedAccessTokenAsync(
                shortLivedPageAccessToken);

        instagramChannel.PageAccessToken = longLivedPageAccessToken.AccessToken;

        if (longLivedPageAccessToken.ExpiresIn > 0)
        {
            instagramChannel.ExpireDate = DateTime.UtcNow.AddSeconds(longLivedPageAccessToken.ExpiresIn - 10);
        }

        await UpdateInstagramChannelConfigAsync(instagramChannel);

        await _instagramChannelRepository.CreateInstagramChannelAsync(instagramChannel);

        return instagramChannel;
    }

    private static bool IsInstagramChannelConnectedWithFacebookLoginForBusiness(InstagramConfig instagramConfig)
    {
        return !instagramConfig.BusinessIntegrationSystemUserAccessToken.IsNullOrEmpty();
    }

    #endregion

    #region Connect Instagram Channel

    private async Task UpdateInstagramChannelConfigAsync(InstagramConfig instagramChannel)
    {
        var instagramBusinessAccountResult = await GetInstagramBusinessAccountAsync(
            instagramChannel.PageId,
            instagramChannel.PageAccessToken);

        instagramChannel.InstagramPageId = instagramBusinessAccountResult.InstagramBusinessAccount.Id;
        instagramChannel.ChannelIdentityId = instagramBusinessAccountResult.InstagramBusinessAccount.Id;

        instagramChannel.SubscribedFields = await AssignInstagramChannelSubscribedFields(instagramChannel);

        await SubscribeInstagramPageAsync(
            instagramChannel.PageId,
            instagramChannel.PageAccessToken,
            instagramChannel.SubscribedFields);

        await UpdateMessengerProfileWhitelistDomainAsync(
            instagramChannel.PageAccessToken);

        var usernameResult = await GetInstagramBusinessAccountUsernameAsync(
            instagramChannel.InstagramPageId,
            instagramChannel.PageAccessToken);

        instagramChannel.Name = usernameResult.Username;
        instagramChannel.PageName = usernameResult.Name;
        instagramChannel.PageName ??= instagramChannel.Name;
        instagramChannel.ConnectedDateTime = DateTime.UtcNow;
    }

    private async Task<FacebookAppAccessToken> ExchangeShortLivedAccessTokenToLongLivedAccessTokenAsync(
        string shortLivedAccessToken)
    {
        var clientId = _configuration.GetValue<string>("Facebook:ClientId");
        var clientSecret = _configuration.GetValue<string>("Facebook:ClientSecret");

        var exchangeShortLivedAccessTokenToLongLivedAccessTokenUri =
            FacebookAccessTokenUriBuilder.GetExchangeShortLivedAccessTokenToLongLivedAccessTokenUri(
                clientId,
                clientSecret,
                shortLivedAccessToken);

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var response = await httpClient.GetAsync(exchangeShortLivedAccessTokenToLongLivedAccessTokenUri);

        if (!response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);

            var errorMessage =
                $"Failed to Exchange Short Lived Access Token To Long Lived Access Token. Error Response: {errorResponse}, Request Url: {exchangeShortLivedAccessTokenToLongLivedAccessTokenUri}";

            throw new Exception(errorMessage);
        }

        var content = await response.Content.ReadAsStringAsync();
        var tokenResponse = JsonConvert.DeserializeObject<FacebookAppAccessToken>(content);
        return tokenResponse;
    }

    private async Task<InstagramBusinessAccountResult> GetInstagramBusinessAccountAsync(
        string pageId,
        string pageAccessToken)
    {
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var instagramBusinessAccountUri =
            InstagramBusinessAccountUriBuilder.GetInstagramBusinessAccountUri(pageId, pageAccessToken);

        var response = await httpClient.GetAsync(instagramBusinessAccountUri);

        if (!response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);

            var errorMessage =
                $"Failed to get Instagram Business Account. Error Response: {errorResponse}, Request Url: {instagramBusinessAccountUri}";

            throw new Exception(errorMessage);
        }

        var content = await response.Content.ReadAsStringAsync();
        var instagramBusinessAccountIdResult = JsonConvert.DeserializeObject<InstagramBusinessAccountResult>(content);

        if (instagramBusinessAccountIdResult.InstagramBusinessAccount is null)
        {
            throw new Exception(
                $"Instagram Business Account not found. Ensure your configuration settings are correct. " +
                $"Page ID: {pageId}, Page Access Token: {pageAccessToken} " +
                $"Please verify these details and that the specified Instagram Business Account exists." +
                $"Response: {content}");
        }

        return instagramBusinessAccountIdResult;
    }

    private async Task SubscribeInstagramPageAsync(string pageId, string pageAccessToken, string subscribedFields)
    {
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var appSubscribeFacebookPageUri =
            FacebookPageAppSubscriptionUriBuilder.GetAppSubscribeFacebookPageUri(
                pageId,
                pageAccessToken,
                subscribedFields);

        var response = await httpClient.PostAsync(appSubscribeFacebookPageUri, null);

        if (!response.IsSuccessStatusCode)
        {
            _metaChannelConnectionMeters.IncrementCounter(
                MetaChannelConnectionApis.PageIdSubscribedApps,
                MetaChannelConnectionApiCallResults.Failure);
            var responseContent = await response.Content.ReadAsStringAsync();
            var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);
            var errorMessage =
                $"Failed to subscribe the Instagram page. Error Response: {errorResponse}, Request Url: {appSubscribeFacebookPageUri}";

            throw new Exception(errorMessage);
        }

        _metaChannelConnectionMeters.IncrementCounter(
            MetaChannelConnectionApis.PageIdSubscribedApps,
            MetaChannelConnectionApiCallResults.Success);
    }

    // If sleekflow is not included in 'whitelisted_domains', this could disrupt the message flow of the webhook.
    private async Task UpdateMessengerProfileWhitelistDomainAsync(string accessToken)
    {
        var domainName = _configuration.GetValue<string>("Values:DomainName");

        var whitelistDomain = new List<string>
        {
            domainName
        };

        var properties = new
        {
            whitelisted_domains = whitelistDomain
        };

        var updateMessengerProfileUri =
            FacebookMessengerProfileUriBuilder.GetUpdateMessengerProfileUri(accessToken);

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var response = await httpClient.PostAsJsonAsync(updateMessengerProfileUri, properties);

        if (!response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);
            var errorMessage =
                $"Failed to update the messenger profile. Error Response: {errorResponse}, Request Url: {updateMessengerProfileUri}";

            _logger.LogError(errorMessage);
        }
    }

    private async Task<UsernameResponse> GetInstagramBusinessAccountUsernameAsync(
        string instagramPageId,
        string pageAccessToken)
    {
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var instagramBusinessAccountUsernameUri =
            InstagramBusinessAccountUsernameUriBuilder.GetInstagramBusinessAccountUsernameUri(
                instagramPageId,
                pageAccessToken);

        var response = await httpClient.GetAsync(instagramBusinessAccountUsernameUri);

        if (!response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);

            var errorMessage =
                $"Failed to get Instagram Business Account Username. Error Response: {errorResponse}, Request Url: {instagramBusinessAccountUsernameUri}";

            _logger.LogError(errorMessage);
        }

        var content = await response.Content.ReadAsStringAsync();
        var usernameResponse = JsonConvert.DeserializeObject<UsernameResponse>(content);

        if (usernameResponse is not null)
        {
            return usernameResponse;
        }

        var usernameResponseIsNotErrorMessage =
            $"The Instagram Business Account Username response is null. Ensure your configuration settings are correct. " +
            $"instagramPageId Page ID: {instagramPageId}, Page Access Token: {pageAccessToken}";

        _logger.LogError(usernameResponseIsNotErrorMessage);

        return usernameResponse;
    }

    private async Task<string> GetInstagramPageSubscriptionStatusAsync(string pageId, string pageAccessToken)
    {
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var getFacebookPageAppSubscriptionsStatusUri =
            FacebookPageAppSubscriptionUriBuilder.GetFacebookPageAppSubscriptionsStatusUri(pageId, pageAccessToken);

        var response = await httpClient.GetAsync(getFacebookPageAppSubscriptionsStatusUri);

        if (response.IsSuccessStatusCode)
        {
            return await response.Content.ReadAsStringAsync();
        }

        var responseContent = await response.Content.ReadAsStringAsync();
        var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);
        var errorMessage =
            $"Failed to unsubscribe the Instagram page. Error Response: {errorResponse}, Request Url: {getFacebookPageAppSubscriptionsStatusUri}";

        _logger.LogError(errorMessage);

        return string.Empty;
    }

    private async Task<string> AssignInstagramChannelSubscribedFields(InstagramConfig instagramChannel)
    {
        var facebookChannel = await _facebookConfigRepository.FindFacebookConfigAsync(
            instagramChannel.CompanyId,
            instagramChannel.PageId);

        var isFacebookSubscribed = facebookChannel is not null;
        var isFacebookLeadAdsSubscribed = isFacebookSubscribed &&
                                          !string.IsNullOrWhiteSpace(facebookChannel.SubscribedFields) &&
                                          facebookChannel.SubscribedFields.Contains(
                                              FacebookInstagramChannelSubscribedFieldsHelper
                                                  .FacebookLeadAdsSubscribedField);

        var facebookInstagramChannelShouldBeSubscribed = new FacebookInstagramChannelShouldBeSubscribed(
            isFacebookSubscribed,
            true,
            isFacebookLeadAdsSubscribed);

        var subscribedFields =
            FacebookInstagramChannelSubscribedFieldsHelper.ResolveFacebookInstagramChannelsSubscribedFields(
                facebookInstagramChannelShouldBeSubscribed);

        return string.Join(",", subscribedFields);
    }

    #endregion

    #region Remove Channel

    public async Task RemoveInstagramChannelAsync(string companyId, string instagramPageId)
    {
        try
        {
            await RemoveChannelAsync(
                companyId,
                instagramPageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Company {CompanyId} An error occurred while trying to remove a Instagram Channel - InstagramPageId: {instagramPageId}. Exception Message: {ExceptionMessage}",
                nameof(RemoveInstagramChannelAsync),
                companyId,
                instagramPageId,
                ex.Message);

            throw;
        }
    }

    private async Task RemoveChannelAsync(string companyId, string instagramPageId)
    {
        var instagramChannel = await _instagramChannelRepository.GetInstagramChannelByInstagramPageIdAsync(
            companyId,
            instagramPageId);

        await UnsubscribeInstagramPageAsync(
            instagramChannel.PageId,
            instagramChannel.PageAccessToken);

        await _instagramChannelRepository.DeleteInstagramChannelAsync(instagramChannel);

        var company = await GetCompanyAsync(companyId);

        await _signalRService.SignalROnChannelDeleted(
            company,
            new ChannelSignal
            {
                ChannelName = ChannelTypes.Instagram
            });

        await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
        var connectedFacebookWebhookCacheKeyPattern = new ConnectedFacebookWebhookCacheKeyPattern();
        await _cacheManagerService.DeleteCacheAsync(connectedFacebookWebhookCacheKeyPattern);
    }

    private async Task UnsubscribeInstagramPageAsync(string pageId, string pageAccessToken)
    {
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var appUnsubscribeFacebookPageUri =
            FacebookPageAppSubscriptionUriBuilder.GetAppUnsubscribeFacebookPageUri(pageId, pageAccessToken);

        var response = await httpClient.DeleteAsync(appUnsubscribeFacebookPageUri);

        if (!response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);
            var errorMessage =
                $"Failed to unsubscribe the Instagram page. Error Response: {errorResponse}, Request Url: {appUnsubscribeFacebookPageUri}";

            _logger.LogError(errorMessage);
        }
    }

    #endregion


    private const string FacebookLogin = "FacebookLogin";
    private const string FacebookLoginForBusiness = "FacebookLoginForBusiness";
    private static string ResolveConnectChannelType(string businessIntegrationSystemUserAccessToken)
    {
        var facebookLoginType = businessIntegrationSystemUserAccessToken.IsNullOrEmpty()
            ? FacebookLogin
            : FacebookLoginForBusiness;

        return facebookLoginType;
    }

    private async Task<Company> GetCompanyAsync(string companyId)
    {
        var company = await _appDbContext.CompanyCompanies
            .Where(c => c.Id == companyId)
            .FirstOrDefaultAsync();
        return company;
    }
}