### Login

POST https://localhost:5000/auth0/account/getusertoken
content-type: application/json-patch+json

{

}

> {% client.global.set("token", response.body.accessToken); %}

### POST request to turn on the remain as collaborator when reassigned to Team Unassigned setting
POST https://localhost:5000/Company/UserRole/permission
content-type: application/json
Authorization: Bearer {{token}}

[
  {
    "staffUserRole": "Admin",
    "storedPermission": {
      "isShowDefaultChannelMessagesOnly": false,
      "isShowDefaultChannelBroadcastOnly": false,
      "receiveUnassignedNotifications": false,
      "isMaskPhoneNumber": false,
      "isMaskEmail": false,
      "addAsCollaboratorWhenReply": false,
      "addAsCollaboratorWhenAssignedToOthers": true,
      "filterMessageWithSelectedChannel": false,
      "remainAsCollaboratorWhenReassignedToTeamUnassigned": true
    }
  },
  {
    "staffUserRole": "TeamAdmin",
    "storedPermission": {
      "isShowDefaultChannelMessagesOnly": false,
      "isShowDefaultChannelBroadcastOnly": false,
      "receiveUnassignedNotifications": false,
      "isMaskPhoneNumber": true,
      "isMaskEmail": true,
      "addAsCollaboratorWhenReply": true,
      "addAsCollaboratorWhenAssignedToOthers": true,
      "filterMessageWithSelectedChannel": false,
      "remainAsCollaboratorWhenReassignedToTeamUnassigned": true
    }
  },
  {
    "staffUserRole": "Staff",
    "storedPermission": {
      "isShowDefaultChannelMessagesOnly": true,
      "isShowDefaultChannelBroadcastOnly": false,
      "receiveUnassignedNotifications": false,
      "isMaskPhoneNumber": true,
      "isMaskEmail": true,
      "addAsCollaboratorWhenReply": true,
      "addAsCollaboratorWhenAssignedToOthers": false,
      "filterMessageWithSelectedChannel": false,
      "remainAsCollaboratorWhenReassignedToTeamUnassigned": true
    }
  }
]
###

### Reassign Conversation to Team Unassigned
POST https://{{host}}/v2/conversation/assignee/260e988a-c494-4306-89a2-b30152480afa
content-type: application/json
Authorization: Bearer {{token}}

{
  "assignmentType": "SpecificGroup",
  "teamAssignmentType": "Unassigned",
  "teamId": 6
}

### Update Contact - Assign Conversation to Team Unassigned

### Leo Ip 1cd8c909-efac-4057-a9b2-ea69a0bf3600
POST https://localhost:5000/api/contact/update/1cd8c909-efac-4057-a9b2-ea69a0bf3600
X-Sleekflow-Api-Key: BmRu1DrI4hEh01Up864pLdoTHKKxOJJCvuxIqPAsw
Content-Type: application/json

{
  "userProfileFields": [
    {
      "customFieldName": "AssignedTeam",
      "customValue": "6"
    },
    {
      "customFieldName": "ContactOwner",
      "customValue": ""
    }
  ]
}

### Leo Ip 1cd8c909-efac-4057-a9b2-ea69a0bf3600
POST https://localhost:5000/api/contact/addOrUpdate
X-Sleekflow-Api-Key: BmRu1DrI4hEh01Up864pLdoTHKKxOJJCvuxIqPAsw
Content-Type: application/json

[
  {
    "email": "<EMAIL>",
    "userProfileFields": [
      {
        "customFieldName": "AssignedTeam",
        "customValue": "6"
      },
      {
        "customFieldName": "ContactOwner",
        "customValue": ""
      }
    ]
  }
]

### Peter
POST https://localhost:5000/api/contact/addOrUpdate
X-Sleekflow-Api-Key: O9wErzd3jIlaD92503O2BKtDTwA5NAN65kEaQmGFlfw
Content-Type: application/json

[
  {
    "firstName": "Sekti",
    "lastName": "Admin",
    "phoneNumber": "81548427221",
    "email": "<EMAIL>",
    "userProfileFields": [
      {
        "customFieldId": "ec1671bb-c0cb-4729-8286-065b5a404982",
        "customFieldName": "ContactOwner",
        "customValue": "9c4ef2d9-482e-41da-813b-50ec1ac24b2b"
      },
      {
        "customFieldId": "a7aaa48c-c3fd-4128-9f77-f332f64e11c5",
        "customFieldName": "AssignedTeam",
        "customValue": "10030"
      },
      {
        "customFieldId": "d837dc30-d1bd-47b9-8d7a-44de1cec67d7",
        "customFieldName": "JobTitle",
        "customValue": "QA"
      }
    ]
  }
]
