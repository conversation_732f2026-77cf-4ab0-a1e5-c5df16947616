﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;
using Sleekflow.Core.Infra.Utils;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class AvgSystemRuntimeLOHSizeMetric : IDashboardMetric
{
    private readonly Output<string>? _sleekflowCoreName;
    private readonly Output<string>? _sleekflowCoreAppInsightResourceId;
    private readonly Output<string>? _sleekflowCoreAppInsightName;

    public AvgSystemRuntimeLOHSizeMetric(
        Output<string>? sleekflowCoreName,
        Output<string>? sleekflowCoreAppInsightResourceId,
        Output<string>? sleekflowCoreAppInsightName)
    {
        _sleekflowCoreName = sleekflowCoreName;
        _sleekflowCoreAppInsightResourceId = sleekflowCoreAppInsightResourceId;
        _sleekflowCoreAppInsightName = sleekflowCoreAppInsightName;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(
        DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs =
                    new[]
                    {
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "options"
                            },
                            {
                                "value", new Dictionary<string, object>()
                                {
                                    {
                                        "chart", new Dictionary<string, object>()
                                        {
                                            {
                                                "filterCollection", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "filters", new[]
                                                        {
                                                            new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "key", "cloud/roleName"
                                                                },
                                                                {
                                                                    "operator", 0
                                                                },
                                                                {
                                                                    "values", new[]
                                                                    {
                                                                        _sleekflowCoreName!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            {
                                                "grouping", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "dimension", "cloud/roleInstance"
                                                    },
                                                    {
                                                        "sort", 2
                                                    },
                                                    {
                                                        "top", 10
                                                    }
                                                }
                                            },
                                            {
                                                "metrics", new[]
                                                {
                                                    new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "aggregationType", 4
                                                        },
                                                        {
                                                            "metricVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "displayName", "System.Runtime|Allocation Rate"
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "name", "customMetrics/System.Runtime|Allocation Rate"
                                                        },
                                                        {
                                                            "namespace", "microsoft.insights/components/kusto"
                                                        },
                                                        {
                                                            "resourceMetadata", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "id", _sleekflowCoreAppInsightResourceId!
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            {
                                                "timespan", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "grain", 1
                                                    },
                                                    {
                                                        "relative", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "duration", 86400000
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "showUTCTime", false
                                                    }
                                                }
                                            },
                                            {
                                                "title", Output.Tuple(
                                                    _sleekflowCoreAppInsightName!,
                                                    _sleekflowCoreName!)?.Apply(
                                                    a =>
                                                        $"Avg System.Runtime|Allocation Rate for {a.Item1} by Cloud role instance where Cloud role name = {a.Item2}")
                                            },
                                            {
                                                "titleKind", 1
                                            },
                                            {
                                                "visualization", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "axisVisualization", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "x", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "axisType", 2
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "y", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "axisType", 1
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "chartType", 2
                                                    },
                                                    {
                                                        "legendVisualization", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "hideSubtitle", false
                                                            },
                                                            {
                                                                "isVisible", true
                                                            },
                                                            {
                                                                "position", 2
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "sharedTimeRange"
                            },
                            {
                                "isOptional", true
                            }
                        }
                    },
                Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                Settings =
                {
                    new Dictionary<string, object>()
                    {
                        {
                            "content", new Dictionary<string, object>()
                            {
                                {
                                    "options", new Dictionary<string, object>()
                                    {
                                        {
                                            "chart", new Dictionary<string, object>()
                                            {
                                                {
                                                    "grouping", new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "dimension", "cloud/roleInstance"
                                                        },
                                                        {
                                                            "sort", 2
                                                        },
                                                        {
                                                            "top", 10
                                                        }
                                                    }
                                                },
                                                {
                                                    "metrics", new[]
                                                    {
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 4
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "System.Runtime|LOH Size"
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "customMetrics/System.Runtime|LOH Size"
                                                            },
                                                            {
                                                                "namespace", "microsoft.insights/components/kusto"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreAppInsightResourceId!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    "title", Output.Tuple(
                                                        _sleekflowCoreAppInsightName!,
                                                        _sleekflowCoreName!)?.Apply(
                                                        a =>
                                                            $"Avg System.Runtime|LOH Size for {a.Item1} by cloud/roleInstance where cloud/roleName = {a.Item2}")
                                                },
                                                {
                                                    "titleKind", 1
                                                },
                                                {
                                                    "visualization", new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "axisVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "x", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 2
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    "y", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 1
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "chartType", 2
                                                        },
                                                        {
                                                            "disablePinning", true
                                                        },
                                                        {
                                                            "legendVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "hideSubtitle", false
                                                                },
                                                                {
                                                                    "isVisible", true
                                                                },
                                                                {
                                                                    "position", 2
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}