name: performance_sleekflow-core-deploy(performance)

on:
  push:
    branches:
      - performance

env:
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages

jobs:
  set-env:
    name: Set Environment Variables
    runs-on: ubuntu-latest
    outputs:
      branch_name: ${{ steps.set-branch.outputs.name }}
    steps:
      - id: set-branch
        run: |
          # Get the branch name and replace '/' with '-'
          branch="${{ github.ref_name }}"
          if [ -z "$branch" ]; then
            echo "name=latest" >> $GITHUB_OUTPUT
          else
            echo "name=${branch//\//-}" >> $GITHUB_OUTPUT
          fi

  update:
    needs: set-env
    name: Update
    runs-on:
      group: "Self Hosted Group"
    concurrency:
      group: sleekflow-core-deploy--${{ needs.set-env.outputs.branch_name }}
      cancel-in-progress: false
    steps:
      - uses: actions/checkout@v3

      - name: Set IMAGE_TAG
        run: |
          echo "IMAGE_TAG=perf-$(date +%Y%m%d-%H%M%S)" >> $GITHUB_ENV

      - name: Set up .NET Core
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.303'
          include-prerelease: false

      - name: Install Azure Cli
        run: |
          curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

      - uses: actions/cache@v3
        with:
          path: ${{ github.workspace }}/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-

      - name: Restore with dotnet
        run: dotnet restore

      - name: Build with dotnet
        run: |
          dotnet publish Travis_backend.Auth0 -c Release --self-contained /p:ExcludeBuildDbMigration=TRUE

      - name: Build images locally
        run: |
          sudo IMAGE_TAG=$IMAGE_TAG docker compose -f docker-compose.common.yml build
          sudo IMAGE_TAG=$IMAGE_TAG docker compose -f docker-compose.yml build

      - name: Set Pulumi Organization
        run: pulumi org set-default sleekflowio
        working-directory: ./Sleekflow.Core.Infra.Perf/
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}

      - name: Deploy Everything
        uses: pulumi/actions@v4
        if: github.ref == 'refs/heads/performance' || github.event.pull_request.base.ref == 'performance'
        with:
          command: up
          stack-name: sleekflowio/Sleekflow.Core.Infra.Perf/performance
          work-dir: ./Sleekflow.Core.Infra.Perf/
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          ARM_CLIENT_ID: ${{ secrets.AZURE_SP_ARM_CLIENT_ID }}
          ARM_CLIENT_SECRET: ${{ secrets.AZURE_SP_ARM_CLIENT_SECRET }}
          ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SP_ARM_SUBSCRIPTION_ID }}
          ARM_TENANT_ID: ${{ secrets.AZURE_SP_ARM_TENANT_ID }}
          IMAGE_TAG: ${{ env.IMAGE_TAG }}