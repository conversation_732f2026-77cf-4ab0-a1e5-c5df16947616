﻿using System.ComponentModel.DataAnnotations;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class InternalCmsAnalyticRequest
{
    [Required]
    public DateTime Start { get; set; }

    [Required]
    public DateTime End { get; set; }

    public int TimezoneHourOffset { get; set; } = 8;

    public bool AllowCache { get; set; } = true;

    public bool AllowCompaniesCache { get; set; } = true;

    public bool IsExcludeMarkupPlan { get; set; } = false;
}

public class InternalCmsAnalyticByOwnersRequest : InternalCmsAnalyticRequest
{
    public bool IsByOwnerTeams { get; set; }

    public List<string> OwnerIds { get; set; }

    public List<string> TeamNames { get; set; }
}

public class InternalCmsAnalyticByPartnerStackGroupNamesRequest : InternalCmsAnalyticRequest
{
    public List<string> GroupNames { get; set; }
}