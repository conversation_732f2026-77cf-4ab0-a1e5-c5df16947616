using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.Powerflow.Apis.Services;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Configuration;
using Travis_backend.Helper.Pagination;
using Travis_backend.ResellerDomain.ViewModels;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Route("/internal/bill-record/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsUser)]
public class InternalCmsBillRecordController : InternalControllerBase
{
    private readonly IInternalBillRecordService _internalBillRecordService;
    private readonly IMapper _mapper;

    public InternalCmsBillRecordController(
        UserManager<ApplicationUser> userManager,
        IInternalBillRecordService internalBillRecordService,
        IMapper mapper
    )
        : base(userManager)
    {
        _internalBillRecordService = internalBillRecordService;
        _mapper = mapper;
    }

    [HttpPost]
    public async Task<IActionResult> GetBillRecordsAsync([FromBody] GetBillRecordsRequest request)
    {
        var billRecords = await _internalBillRecordService.GetBillRecordsAsync(
            paymentMethod: request.PaymentMethod,
            offset: request.Offset,
            limit: request.Limit,
            enableNoTracking: true);

        var billRecordDtos = _mapper.Map<List<AllBillRecordsDto>>(billRecords.BillRecords);

        var result = new PagedResult<AllBillRecordsDto>(
            page: (request.Offset / request.Limit) + 1,
            pageSize: request.Limit,
            totalRecords: billRecords.TotalCount,
            data: billRecordDtos);

        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateAutoRenewalAsync([FromBody] UpdateAutoRenewalRequest request)
    {
        try
        {
            var currentUser = await GetCurrentValidInternalUser([ApplicationUserRole.InternalCmsUser]);

            if (currentUser == null)
            {
                return Unauthorized();
            }

            var success = await _internalBillRecordService.UpdateAutoRenewalAsync(
                request.BillRecordId,
                request.Enabled,
                currentUser.Id);

            if (success)
            {
                return Ok(
                    new
                    {
                        message = $"Auto-renewal has been {(request.Enabled ? "enabled" : "disabled")}"
                    });
            }

            return BadRequest(
                new
                {
                    message = "Failed to update auto-renewal status"
                });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(
                new
                {
                    message = ex.Message
                });
        }
        catch (Exception)
        {
            return StatusCode(
                500,
                new
                {
                    message = "An error occurred while updating auto-renewal status"
                });
        }
    }
}