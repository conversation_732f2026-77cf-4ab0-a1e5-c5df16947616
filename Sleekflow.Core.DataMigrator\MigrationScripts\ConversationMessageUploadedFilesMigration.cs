using Microsoft.EntityFrameworkCore;
using Sleekflow.Core.DataMigrator.Migrations;
using Travis_backend.FileDomain.Models;

namespace Sleekflow.Core.DataMigrator.MigrationScripts;

public class ConversationMessageUploadedFilesMigration : BaseMigration
{
    private readonly MigrationHelper<UploadedFile> _helper;

    public ConversationMessageUploadedFilesMigration(Configurations configurations)
        : base(configurations)
    {
        _helper = new MigrationHelper<UploadedFile>(OriginalContext, MigrationContext);
    }

    public override async Task<int> ExecuteAsync(string companyId)
    {
        var conversationMessageIdsOrigin = OriginalContext.ConversationMessages.Where(x => x.CompanyId == companyId)
            .Select(x => x.Id).ToHashSet();
        var conversationMessageIdsMigrated = OriginalContext.ConversationMessageUploadedFiles
            .Select(x => x.ConversationMessageId).ToHashSet();
        conversationMessageIdsOrigin.IntersectWith(conversationMessageIdsMigrated);
        var originItems = new List<UploadedFile>();
        foreach (var conversationMessageId in conversationMessageIdsOrigin)
        {
            var uploadedFiles =
                await OriginalContext.ConversationMessageUploadedFiles.FirstOrDefaultAsync(
                    x => x.ConversationMessageId == conversationMessageId);
            if (uploadedFiles != null)
            {
                originItems.Add(uploadedFiles);
            }
        }

        var results = new List<UploadedFile>();
        if (FilterDuplicate)
        {
            var migratedItems = MigrationContext.ConversationMessageUploadedFiles.Select(x => x.Id)
                .ToHashSet();
            foreach (var originItem in originItems)
            {
                if (!migratedItems.Contains(originItem.Id))
                {
                    results.Add(originItem);
                }
            }
        }

        return await TransactionalExecuteAsync(
            nameof(MigrationContext.ConversationMessageUploadedFiles),
            async () =>
            {
                var status = await _helper.MigrationSaveChangesAsync(
                    results,
                    MigrationContext.ConversationMessageUploadedFiles, MigrationContext);

                return status;
            });
    }
}