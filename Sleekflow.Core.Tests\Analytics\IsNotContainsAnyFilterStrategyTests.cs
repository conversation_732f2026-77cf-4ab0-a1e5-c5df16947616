﻿using Newtonsoft.Json;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.Services.ConversationAnalyticsAdvanceFilters;
using Travis_backend.Constants;

namespace Sleekflow.Core.Tests.Analytics;

[TestFixture]
public class IsNotContainsAnyFilterStrategyTests
{
    private IsNotContainsAnyFilterStrategy _filterStrategy;

    [SetUp]
    public void SetUp()
    {
        _filterStrategy = new IsNotContainsAnyFilterStrategy();
    }

    [Test]
    public void BuildPredicate_WithAssigneeField_ReturnsCorrectPredicate()
    {
        // Arrange
        var metrics = new List<ConversationAnalyticsMetric>
        {
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, "user1", null, null, null, null)) },
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, "user2", null, null, null, null)) }
        };
        var values = new List<string> { "user1", "userX" };
        var fieldName = ConversationAnalyticsAdvancedFilterFieldNames.Assignee;

        // Act
        var predicate = _filterStrategy.BuildPredicate(fieldName, values);
        var result = metrics.AsQueryable().Where(predicate).ToList();

        // Assert
        Assert.That(result.Count, Is.EqualTo(1));
        Assert.That(JsonConvert.DeserializeObject<ConversationAnalyticsDimensionData>(result.First().DimensionDataJson)?.UserIdentityId, Is.EqualTo("user2"));
    }

    [Test]
    public void BuildPredicate_WithChannelField_ReturnsCorrectPredicate()
    {
        // Arrange
        var metrics = new List<ConversationAnalyticsMetric>
        {
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, null, "id1", "type1", null, null)) },
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, null, "id2", "type2", null, null)) },
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, null, "id3", "type3", null, null)) },
        };
        var values = new List<string> { "type1:id1", "type3:id3", "typeX:idX" };
        var fieldName = ConversationAnalyticsAdvancedFilterFieldNames.Channel;

        // Act
        var predicate = _filterStrategy.BuildPredicate(fieldName, values);
        var result = metrics.AsQueryable().Where(predicate).ToList();

        // Assert
        Assert.That(result.Count, Is.EqualTo(1));
    }

    [Test]
    public void BuildPredicate_WithMixedField_ReturnsCorrectPredicate()
    {
        // Arrange
        var metrics = new List<ConversationAnalyticsMetric>
        {
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, "user1", null, null, null, null)) },
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, "user2", null, null, null, null)) },
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, null, "id1", "type1", null, null)) },
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, null, "id2", "type2", null, null)) },
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, null, "id3", "type3", null, null)) },
        };
        var values = new List<string> { "type1:id1", "type3:id3", "typeX:idX" };
        var fieldName = ConversationAnalyticsAdvancedFilterFieldNames.Channel;

        // Act
        var predicate = _filterStrategy.BuildPredicate(fieldName, values);
        var result = metrics.AsQueryable().Where(predicate).ToList();

        // Assert
        Assert.That(result.Count, Is.EqualTo(3));
    }

    [Test]
    public void BuildPredicate_WithUnsupportedField_ThrowsArgumentException()
    {
        // Arrange
        var values = new List<string> { "someValue" };
        var fieldName = "unsupportedField";

        // Act & Assert
        var ex = Assert.Throws<ArgumentException>(() => _filterStrategy.BuildPredicate(fieldName, values));
        Assert.That(ex?.Message, Is.EqualTo("Field 'unsupportedField' not supported."));
    }

    [Test]
    public void BuildPredicate_WithEmptyValues_ReturnsCorrectPredicate()
    {
        // Arrange
        var metrics = new List<ConversationAnalyticsMetric>
        {
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, "user1", null, null, null, null)) },
            new ConversationAnalyticsMetric { DimensionDataJson = JsonConvert.SerializeObject(new ConversationAnalyticsDimensionData(null, "user2", null, null, null, null)) }
        };
        var values = new List<string> ();
        var fieldName = ConversationAnalyticsAdvancedFilterFieldNames.Assignee;

        // Act
        var predicate = _filterStrategy.BuildPredicate(fieldName, values);
        var result = metrics.AsQueryable().Where(predicate).ToList();

        // Assert
        Assert.That(result.Count, Is.EqualTo(2));
    }
}