using System;

namespace Travis_backend.Cache.Models.CacheKeyPatterns;

public class ConversationHashTagSummaryV4CacheKeyPattern(
    long companyUserId,
    string status,
    string assignedTo,
    string channels,
    DateTime? afterUpdatedAt,
    DateTime? afterModifiedAt,
    string channelIds,
    string tags,
    long? teamId,
    bool? isTeamUnassigned,
    bool? isUnread,
    string behaviourVersion)
    : ICacheKeyPattern
{
    public long CompanyUserId { get; set; } = companyUserId;

    public string Status { get; set; } = status;

    public string AssignedTo { get; set; } = assignedTo;

    public string Channels { get; set; } = channels;

    public string AfterUpdatedAt { get; set; } = afterUpdatedAt != null ? afterUpdatedAt.ToString() : string.Empty;

    public string AfterModifiedAt { get; set; } = afterModifiedAt != null ? afterModifiedAt.ToString() : string.Empty;

    public string ChannelIds { get; set; } = channelIds;

    public string Tags { get; set; } = tags;

    public string TeamId { get; set; } = teamId != null ? teamId.ToString() : string.Empty;

    public string IsTeamUnassigned { get; set; } = isTeamUnassigned != null ? isTeamUnassigned.ToString() : string.Empty;

    public string IsUnread { get; set; } = isUnread != null ? isUnread.ToString() : string.Empty;

    public string BehaviourVersion { get; set; } = behaviourVersion;

    public string GenerateKeyPattern()
    {
        var keyName = CacheHelper.BuildKeyNameFromPattern(
            new object[]
            {
                CompanyUserId,
                Status,
                AssignedTo,
                Channels,
                AfterUpdatedAt,
                AfterModifiedAt,
                ChannelIds,
                Tags,
                TeamId,
                IsTeamUnassigned,
                IsUnread,
                BehaviourVersion
            });
        return keyName;
    }
}