using Microsoft.Extensions.Logging;
using Moq;
using NSubstitute;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.FlowHubs;
using Travis_backend.SubscriptionPlanDomain.Services;

namespace Sleekflow.Core.Tests.FlowHubs;

public class FlowHubConfigServiceTest
{
    private IFeatureQuantityService _mockFeatureQuantityService;

    private IFlowHubConfigsApi _mockFlowHubConfigsApi;

    private ILogger<FlowHubConfigService> _mockLogger;

    private IFlowHubConfigService _flowHubConfigService;

    [SetUp]
    public void Setup()
    {
        _mockFeatureQuantityService = Substitute.For<IFeatureQuantityService>();
        _mockFlowHubConfigsApi = Substitute.For<IFlowHubConfigsApi>();
        _mockLogger = Substitute.For<ILogger<FlowHubConfigService>>();

        _flowHubConfigService = new FlowHubConfigService(
            _mockFeatureQuantityService,
            _mockFlowHubConfigsApi,
            _mockLogger
        );
    }

    [Test]
    public async Task Test_ToggleFlowHubUsageLimitAsync_Success()
    {
        //// Arrange
        var apiResponse = new ToggleFlowHubUsageLimitOutputOutput
        {
            Data = new ToggleFlowHubUsageLimitOutput
            {
                FlowHubConfig = new FlowHubConfig
                {
                    IsEnrolled = true,
                    IsUsageLimitEnabled = true
                }
            }
        };

        _mockFlowHubConfigsApi.FlowHubConfigsToggleFlowHubUsageLimitPostAsync(null, null, Arg.Any<ToggleFlowHubUsageLimitInput>()).Returns(apiResponse);

        //// Act
        var result = await _flowHubConfigService.ToggleFlowHubUsageLimitAsync("any-company-id", true);

        //// Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.IsEnrolled, Is.True);
            Assert.That(result.IsUsageLimitEnabled, Is.True);
        });
    }
}