using Newtonsoft.Json;
using Travis_backend.CompanyDomain.Models;

namespace Travis_backend.CompanyDomain.ViewModels;

public class CreateBusinessHourConfigModel

{
    public bool IsEnabled { get; set; }

    public WeeklyHours WeeklyHours { get; set; }

    [JsonConstructor]
    public CreateBusinessHourConfigModel
    (bool isEnabled, WeeklyHours weeklyHours)
    {
        IsEnabled = isEnabled;
        WeeklyHours = weeklyHours;
    }

    public CreateBusinessHourConfigModel
    (BusinessHourConfig businessHourConfig)
    {
        IsEnabled = businessHourConfig.IsEnabled;

        WeeklyHours = businessHourConfig.WeeklyHours;

    }
}