using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using <PERSON>_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;

namespace Sleekflow.Core.Tests.Conversations.Rbac.AssignConversations;

[TestFixture]
public class CanAssignUnitTests
{
    private IRbacConversationPermissionManager _rbacConversationPermissionManager;

    private StaffAccessControlAggregate _adminA;
    private StaffAccessControlAggregate _teamAdminA;
    private StaffAccessControlAggregate _staffA;
    private StaffAccessControlAggregate _adminB;
    private StaffAccessControlAggregate _teamAdminB;
    private StaffAccessControlAggregate _staffB;
    private StaffAccessControlAggregate _adminC;
    private StaffAccessControlAggregate _adminInAnotherCompany;
    private StaffAccessControlAggregate _staffC;
    private StaffAccessControlAggregate _staffD;
    private TeamAccessControlAggregate _teamA;
    private TeamAccessControlAggregate _teamB;
    private RbacRole _admin;
    private RbacRole _teamAdmin;
    private RbacRole _staff;
    private string _companyId;
    private string _anotherCompanyId;
    private StaffAssigneeChangeViewModel _assignToStaffC;
    private StaffAssignmentViewModel _assignToStaffCAssignmentViewModel;
    private StaffAssigneeChangeViewModel _staffAssigneeChange;
    private StaffAssignmentViewModel _staffAssignmentViewModel;
    private StaffAssignmentViewModel _unassignedStaffAssignmentViewModel;
    private StaffAssigneeChangeViewModel _unassignedStaffAssigneeChangeViewModel;

    [SetUp]
    public void Setup()
    {
        _rbacConversationPermissionManager =
            new RbacConversationPermissionManager(new RbacDefaultChannelPermissionManager());

        _companyId = "sleekflow";
        _anotherCompanyId = "another_companyId";

        _assignToStaffC = new StaffAssigneeChangeViewModel
        {
            StaffId = "StaffC",
            AssignmentType = AssignmentType.SpecificPerson,
        };

        _assignToStaffCAssignmentViewModel = new StaffAssignmentViewModel()
        {
            StaffId = "StaffC",
            AssignmentType = AssignmentType.SpecificPerson,
        };

        _unassignedStaffAssignmentViewModel = new StaffAssignmentViewModel()
        {
            AssignmentType = AssignmentType.Unassigned,
        };

        _unassignedStaffAssigneeChangeViewModel = new StaffAssigneeChangeViewModel()
        {
            AssignmentType = AssignmentType.Unassigned,
        };

        // Teams
        _teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long>
            {
                1, 2, 3
            }
        };
        _teamB = new TeamAccessControlAggregate
        {
            Id = 2,
            TeamMemberStaffIds = new List<long>
            {
                4, 5, 6
            }
        };

        _admin = new RbacRole
        {
            SleekflowRoleName = "Admin",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllAssignedConversations,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations,
                RbacAssignConversationsPermissions.AssignToAnyUser
            ]
        };

        // Conversation access rules for a team admin:
        // - Assigned to Associated Teams: Must be linked to at least one team in `associatedTeamIds`.
        // - Team Member as Contact Owner: Must have a contact owner from `teamMemberStaffIds`.
        // - Team Member as Collaborator: Must include a collaborator from `teamMemberStaffIds`.
        _teamAdmin = new RbacRole
        {
            SleekflowRoleName = "TeamAdmin",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations,
                RbacAssignConversationsPermissions.AssignToAnyUser
            ]
        };

        _staff = new RbacRole
        {
            SleekflowRoleName = "Staff",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations,
                RbacAssignConversationsPermissions.AssignToMe
            ]
        };

        #region Team A members

        _adminA = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = [_admin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };

        _teamAdminA = new StaffAccessControlAggregate
        {
            StaffId = 2,
            RbacRoles = [_teamAdmin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };


        _staffA = new StaffAccessControlAggregate
        {
            StaffId = 3,
            CompanyId = _companyId,
            RbacRoles = [_staff],
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            },
            StaffIdentityId = "StaffA"
        };

        #endregion

        #region Team B members

        _adminB = new StaffAccessControlAggregate
        {
            StaffId = 4,
            RbacRoles = [_admin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _teamAdminB = new StaffAccessControlAggregate
        {
            StaffId = 5,
            RbacRoles = [_teamAdmin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _staffB = new StaffAccessControlAggregate
        {
            StaffId = 6,
            RbacRoles = [_staff],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };

        #endregion


        // Others
        _adminC = new StaffAccessControlAggregate
        {
            StaffId = 7, RbacRoles = [_admin], CompanyId = _companyId
        };

        _staffC = new StaffAccessControlAggregate
        {
            StaffId = 8, RbacRoles = [_staff], CompanyId = _companyId, StaffIdentityId = "StaffC"
        };

        _staffD = new StaffAccessControlAggregate
        {
            StaffId = 9, RbacRoles = [], CompanyId = _companyId, StaffIdentityId = "StaffD"
        };


    }

    [Test]
    public void staff_with_can_assign_conversation_to_any_user_permission_can_assign_any_user_as_contact_owner()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId
        };

        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_adminA, conversation, _assignToStaffC));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_adminA, conversation, _assignToStaffCAssignmentViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_teamAdminA, conversation, _assignToStaffC));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_teamAdminA, conversation, _assignToStaffCAssignmentViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_adminC, conversation, _assignToStaffC));
    }

    [Test]
    public void staff_with_can_assign_conversation_to_me_permission_can_only_assign_themselves_as_contact_owner()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId
        };

        Assert.IsFalse(_rbacConversationPermissionManager.CanAssign(_staffA, conversation, _assignToStaffC));
        Assert.IsFalse(_rbacConversationPermissionManager.CanAssign(_staffA, conversation, _assignToStaffCAssignmentViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_staffC, conversation, _assignToStaffC));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_staffC, conversation, _assignToStaffCAssignmentViewModel));
    }

    [Test]
    public void staff_with_can_assign_conversation_to_me_permission_can_only_reassign_themselves_as_contact_owner()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId, AssigneeId = _staffA.StaffId
        };

        Assert.IsFalse(_rbacConversationPermissionManager.CanAssign(_staffA, conversation, _assignToStaffC));
        Assert.IsFalse(_rbacConversationPermissionManager.CanAssign(_staffA, conversation, _assignToStaffCAssignmentViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_staffC, conversation, _assignToStaffC));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_staffC, conversation, _assignToStaffCAssignmentViewModel));
    }

    [Test]
    public void staff_with_can_assign_conversation_to_any_user_permission_can_reassign_any_user_as_contact_owner()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId, AssigneeId = _staffA.StaffId
        };

        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_adminA, conversation, _assignToStaffC));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_adminA, conversation, _assignToStaffCAssignmentViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_teamAdminA, conversation, _assignToStaffC));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_teamAdminA, conversation, _assignToStaffCAssignmentViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_adminC, conversation, _assignToStaffC));
    }

    [Test]
    public void staff_with_can_assign_conversation_to_me_permission_can_unassign_any_user()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId, AssigneeId = _staffC.StaffId
        };

        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_staffA, conversation, _unassignedStaffAssignmentViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_staffA, conversation, _unassignedStaffAssigneeChangeViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_staffC, conversation, _unassignedStaffAssigneeChangeViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_staffC, conversation, _unassignedStaffAssignmentViewModel));
    }

    [Test]
    public void staff_with_can_assign_conversation_to_any_user_permission_can_unassign_any_user()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId, AssigneeId = _staffC.StaffId
        };

        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_adminA, conversation, _unassignedStaffAssignmentViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_teamAdminA, conversation, _unassignedStaffAssigneeChangeViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_adminA, conversation, _unassignedStaffAssigneeChangeViewModel));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssign(_adminC, conversation, _unassignedStaffAssignmentViewModel));
    }

    [Test]
    public void staff_with_no_assign_conversation_permission_cannot_unassign_user()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId, AssigneeId = _staffC.StaffId
        };

        Assert.IsFalse(_rbacConversationPermissionManager.CanAssign(_staffD, conversation, _unassignedStaffAssignmentViewModel));
        Assert.IsFalse(_rbacConversationPermissionManager.CanAssign(_staffD, conversation, _unassignedStaffAssigneeChangeViewModel));
    }

    [Test]
    public void staff_with_no_assign_conversation_permission_cannot_assign_user()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId
        };

        Assert.IsFalse(_rbacConversationPermissionManager.CanAssign(_staffD, conversation, _assignToStaffC));
        Assert.IsFalse(_rbacConversationPermissionManager.CanAssign(_staffD, conversation, _assignToStaffCAssignmentViewModel));
    }
}