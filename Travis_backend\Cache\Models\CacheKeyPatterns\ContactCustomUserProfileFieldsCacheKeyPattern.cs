namespace Travis_backend.Cache.Models.CacheKeyPatterns;

public class ContactCustomUserProfileFieldsCacheKeyPattern : ICacheKeyPattern
{
    public string CompanyId { get; set; }

    public ContactCustomUserProfileFieldsCacheKeyPattern(string companyId)
    {
        CompanyId = companyId;
    }

    public string GenerateKeyPattern()
    {
        var keyName = CacheHelper.BuildKeyNameFromPattern(
            new object[]
            {
                CompanyId
            });
        return keyName;
    }
} 