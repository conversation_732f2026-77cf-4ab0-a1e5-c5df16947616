﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class OperationsSortedDependencyCountMetric : IDashboardMetric
{
    private readonly Output<string>? _resourceId;
    private readonly Output<string>? _resourceName;

    public OperationsSortedDependencyCountMetric(Output<string>? resourceId, Output<string>? resourceName)
    {
        _resourceId = resourceId;
        _resourceName = resourceName;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return
            new DashboardPartsArgs
            {
                Position = position,
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                        new[]
                        {
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "ComponentId"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "ControlType"
                                },
                                {
                                    "value", "AnalyticsGrid"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "DashboardId"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "Dimensions"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "DraftRequestParameters"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "IsQueryContainTimeRange"
                                },
                                {
                                    "value", false
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "LegendOptions"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "PartSubTitle"
                                },
                                {
                                    "value", _resourceId!
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "PartTitle"
                                },
                                {
                                    "value", "Analytics"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "Query"
                                },
                                {
                                    "value",
                                    "let timeGrain = 1m;\nlet OperationsStats=dependencies\n    | join kind=inner (requests\n        | project operation_Id, operation_Name\n        | distinct operation_Id, operation_Name)\n        on operation_Id\n    | where client_Type != \"Browser\"\n        and operation_Name !contains \"swagger\"\n        and operation_Name != \"GET /\"\n    | summarize count_=avg(itemCount) by operation_Name1, bin(timestamp, timeGrain)\n    | project operation_Name=operation_Name1, timestamp, count_\n    | sort by operation_Name, timestamp asc;\nOperationsStats\n| serialize\n| extend\n    prev_count = prev(count_, 1),\n    prev_timestamp = prev(timestamp, 1),\n    prev_operation_Name = prev(operation_Name, 1)\n| where prev_operation_Name == operation_Name\n| extend countIncrease = count_ - prev_count\n| extend countIncreasePercentage = (countIncrease / prev_count) * 100\n| summarize percentile(countIncreasePercentage, 95), avg(count_) by operation_Name\n| sort by percentile_countIncreasePercentage_95 desc\n\n"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "Scope"
                                },
                                {
                                    "value", new Dictionary<string, object>()
                                    {
                                        {
                                            "resourceIds", new[]
                                            {
                                                _resourceId!
                                            }
                                        }
                                    }
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "SpecificChart"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "TimeRange"
                                },
                                {
                                    "value", "P1D"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "Version"
                                },
                                {
                                    "value", "2.0"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "resourceTypeMode"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>
                            {
                                {
                                    "name", "PartTitle"
                                },
                                {
                                    "value",
                                    "Operations Sorted by 95th Percentile of Increase in Average Response Time Over Time (Excluding Browser Requests and Certain Operations)"
                                },
                                {
                                    "isOptional", true
                                }
                            }
                        },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                        new Dictionary<string, object>
                        {
                        }
                    },
                }
            };
    }
}