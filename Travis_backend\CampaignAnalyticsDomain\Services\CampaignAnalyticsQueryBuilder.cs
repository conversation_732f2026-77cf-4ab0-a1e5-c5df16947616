﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Data.SqlClient;
using Travis_backend.CampaignAnalyticsDomain.Constants;
using Travis_backend.CampaignAnalyticsDomain.Models;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.CampaignAnalyticsDomain.Services;

public static class CampaignAnalyticsQueryBuilder
{
    private const string ParamNameCompanyId = "@CompanyId";
    private const string ParamNameBroadcastCampaignId = "@BroadcastCampaignId";
    private const string ParamNameAnalyticTag = "@AnalyticTag";
    private const string ParamNameOffset = "@Offset";
    private const string ParamNameLimit = "@Limit";
    private const string ParamNameReplyWindowLengthInHours = "@ReplyWindowLengthInHours";

    #region Broadcast

    public static CampaignAnalyticsQueryDefinition GetCountsPerMessageStatusByBroadcastQueryDef(
        string companyId,
        string broadcastCampaignId)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameBroadcastCampaignId, broadcastCampaignId),
        };

        var query = $"""
                    SELECT
                        CM.Status AS {nameof(MetricDataPointDto.MessageStatus)},
                        COUNT(*) AS {nameof(MetricDataPointDto.Count)}
                    FROM
                        ConversationMessages CM
                            JOIN BroadcastCompaignHistories BCH
                            ON CM.BroadcastHistoryId = BCH.Id
                    WHERE
                        BCH.BroadcastCampaignId = {ParamNameBroadcastCampaignId}
                        AND CM.CompanyId = {ParamNameCompanyId}
                    GROUP BY
                        CM.Status
                    """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    public static CampaignAnalyticsQueryDefinition GetMessageOverviewByBroadcastQueryDef(
        string companyId,
        string broadcastCampaignId,
        IEnumerable<MessageStatus> includedMessageStatuses,
        string orderBy,
        string direction,
        int offset,
        int limit)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameBroadcastCampaignId, broadcastCampaignId),
            new SqlParameter(ParamNameOffset, offset),
            new SqlParameter(ParamNameLimit, limit),
        };

        var query = $"""
                    SELECT
                        CM.Id AS {nameof(CampaignMessageOverviewDto.MessageId)},
                        CM.ConversationId AS {nameof(CampaignMessageOverviewDto.ConversationId)},
                        UP.Id AS {nameof(CampaignMessageOverviewDto.UserProfileId)},
                        UP.FirstName AS {nameof(CampaignMessageOverviewDto.FirstName)},
                        UP.LastName AS {nameof(CampaignMessageOverviewDto.LastName)},
                        UP.FullName AS {nameof(CampaignMessageOverviewDto.FullName)},
                        UP.PhoneNumber AS {nameof(CampaignMessageOverviewDto.PhoneNumber)},
                        CM.Status AS {nameof(CampaignMessageOverviewDto.MessageStatus)},
                        CM.ChannelStatusMessage AS {nameof(CampaignMessageOverviewDto.ChannelStatusMessage)},
                        CM.CreatedAt AS {nameof(CampaignMessageOverviewDto.CreatedAt)}
                    FROM
                        ConversationMessages CM
                            JOIN BroadcastCompaignHistories BCH
                                 ON CM.BroadcastHistoryId = BCH.Id
                            JOIN Conversations C
                                 ON CM.ConversationId = C.Id
                            JOIN UserProfiles UP
                                 ON C.UserProfileId = UP.Id
                    WHERE
                        BCH.BroadcastCampaignId = {ParamNameBroadcastCampaignId}
                        AND CM.CompanyId = {ParamNameCompanyId}
                        AND CM.Status IN ({string.Join(", ", includedMessageStatuses.Cast<int>())})
                    {ConstructOrderByClause(orderBy, direction)}
                    OFFSET {ParamNameOffset} ROWS
                        FETCH NEXT {ParamNameLimit} ROWS ONLY;
                    """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    public static CampaignAnalyticsQueryDefinition GetUserProfileIdsByBroadcastQueryDef(
        string companyId,
        string broadcastCampaignId,
        IEnumerable<MessageStatus> includedMessageStatuses)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameBroadcastCampaignId, broadcastCampaignId),
        };

        var query = $"""
                    SELECT
                        UP.Id as UserProfileId
                    FROM
                        ConversationMessages CM
                            JOIN BroadcastCompaignHistories BCH
                                 ON CM.BroadcastHistoryId = BCH.Id
                            JOIN Conversations C
                                 ON CM.ConversationId = C.Id
                            JOIN UserProfiles UP
                                 ON C.UserProfileId = UP.Id
                    WHERE
                        BCH.BroadcastCampaignId = {ParamNameBroadcastCampaignId}
                        AND CM.CompanyId = {ParamNameCompanyId}
                        AND CM.Status IN  ({string.Join(", ", includedMessageStatuses.Cast<int>())});
                    """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    public static CampaignAnalyticsQueryDefinition GetRepliedCountByBroadcastQueryDef(
        string companyId,
        string broadcastCampaignId,
        ReplyWindow replyWindow)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameBroadcastCampaignId, broadcastCampaignId),
            new SqlParameter(ParamNameReplyWindowLengthInHours, GetReplyWindowLengthInHours(replyWindow)),
        };

        var query = $"""
                    WITH
                    OutMessages AS (
                        {BroadcastOutMessageQ(MessageStatusGroups.Delivered)}
                    ),
                    RepliedMessages AS (
                        {RepliedMessageCountQ()}
                    )
                    SELECT
                        Count(1) AS Count
                    FROM
                        RepliedMessages;
                    """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    public static CampaignAnalyticsQueryDefinition GetRepliedMessageOverviewByBroadcastQueryDef(
        string companyId,
        string broadcastCampaignId,
        ReplyWindow replyWindow,
        string orderBy,
        string direction,
        int offset,
        int limit)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameBroadcastCampaignId, broadcastCampaignId),
            new SqlParameter(ParamNameReplyWindowLengthInHours, GetReplyWindowLengthInHours(replyWindow)),
            new SqlParameter(ParamNameOffset, offset),
            new SqlParameter(ParamNameLimit, limit),
        };

        var query = $"""
                    WITH
                    OutMessages AS (
                        {BroadcastOutMessageQ(MessageStatusGroups.Delivered)}
                    ),
                    RepliedMessages AS (
                        {RepliedMessageOverviewQ(orderBy, direction)}
                    )
                    SELECT
                        OutMessageId AS {nameof(CampaignMessageOverviewDto.MessageId)},
                        ConversationId AS {nameof(CampaignMessageOverviewDto.ConversationId)},
                        UserProfileId AS {nameof(CampaignMessageOverviewDto.UserProfileId)},
                        FirstName AS {nameof(CampaignMessageOverviewDto.FirstName)},
                        LastName AS {nameof(CampaignMessageOverviewDto.LastName)},
                        FullName AS {nameof(CampaignMessageOverviewDto.FullName)},
                        PhoneNumber AS {nameof(CampaignMessageOverviewDto.PhoneNumber)},
                        Status AS {nameof(CampaignMessageOverviewDto.MessageStatus)},
                        ChannelStatusMessage AS {nameof(CampaignMessageOverviewDto.ChannelStatusMessage)},
                        OutCreatedAt AS {nameof(CampaignMessageOverviewDto.CreatedAt)}
                    FROM
                        RepliedMessages;
                    """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    public static CampaignAnalyticsQueryDefinition GetRepliedMessageUserProfileIdsByBroadcastQueryDef(
        string companyId,
        string broadcastCampaignId,
        ReplyWindow replyWindow)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameBroadcastCampaignId, broadcastCampaignId),
            new SqlParameter(ParamNameReplyWindowLengthInHours, GetReplyWindowLengthInHours(replyWindow)),
        };

        var query = $"""
                    WITH
                    OutMessages AS (
                        {BroadcastOutMessageQ(MessageStatusGroups.Delivered)}
                    ),
                    RepliedMessages AS (
                        {RepliedMessageUserProfileIdQ()}
                    )
                    SELECT
                        UserProfileId
                    FROM
                        RepliedMessages;
                    """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    private static string BroadcastOutMessageQ(IEnumerable<MessageStatus> includedMessageStatuses) =>
        $"""
        SELECT
            CM.Id AS OutMessageId,
            CM.ConversationId,
            CM.CreatedAt AS OutCreatedAt,
            CM.ChannelStatusMessage,
            CM.Status
        FROM
            BroadcastCompaignHistories CMH
        JOIN
            ConversationMessages CM
        ON
            CMH.Id = CM.BroadcastHistoryId
            AND CM.CompanyId = {ParamNameCompanyId}
            AND CM.Status IN ({string.Join(", ", includedMessageStatuses.Cast<int>())})
        WHERE
            BroadcastCampaignId = {ParamNameBroadcastCampaignId}
        """;

    #endregion

    #region Analytic Tag

    public static CampaignAnalyticsQueryDefinition GetCountsPerMessageStatusByAnalyticTagQueryDef(
        string companyId,
        string analyticTag)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameAnalyticTag, GetAnalyticTagSqlParamValue(analyticTag)),
        };

        var query = $"""
                    SELECT
                        CM.Status AS {nameof(MetricDataPointDto.MessageStatus)},
                        COUNT(*) AS {nameof(MetricDataPointDto.Count)}
                    FROM
                        ConversationMessages CM
                    WHERE
                        CM.CompanyId = {ParamNameCompanyId}
                        AND CM.AnalyticTags LIKE {ParamNameAnalyticTag}
                    GROUP BY
                        CM.Status;
                    """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    public static CampaignAnalyticsQueryDefinition GetMessageOverviewByAnalyticTagQueryDef(
        string companyId,
        string analyticTag,
        IEnumerable<MessageStatus> includedMessageStatuses,
        string orderBy,
        string direction,
        int offset,
        int limit)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameOffset, offset),
            new SqlParameter(ParamNameLimit, limit),
            new SqlParameter(ParamNameAnalyticTag, GetAnalyticTagSqlParamValue(analyticTag)),
        };

        var query = $"""
                     SELECT
                         CM.Id AS {nameof(CampaignMessageOverviewDto.MessageId)},
                         CM.ConversationId AS {nameof(CampaignMessageOverviewDto.ConversationId)},
                         UP.Id AS {nameof(CampaignMessageOverviewDto.UserProfileId)},
                         UP.FirstName AS {nameof(CampaignMessageOverviewDto.FirstName)},
                         UP.LastName AS {nameof(CampaignMessageOverviewDto.LastName)},
                         UP.FullName AS {nameof(CampaignMessageOverviewDto.FullName)},
                         UP.PhoneNumber AS {nameof(CampaignMessageOverviewDto.PhoneNumber)},
                         CM.Status AS {nameof(CampaignMessageOverviewDto.MessageStatus)},
                         CM.ChannelStatusMessage AS {nameof(CampaignMessageOverviewDto.ChannelStatusMessage)},
                         CM.CreatedAt AS {nameof(CampaignMessageOverviewDto.CreatedAt)}
                     FROM
                         ConversationMessages CM
                             JOIN Conversations C
                                  ON CM.ConversationId = C.Id
                             JOIN UserProfiles UP
                                  ON C.UserProfileId = UP.Id
                     WHERE
                         CM.CompanyId = {ParamNameCompanyId}
                         AND CM.Status IN ({string.Join(", ", includedMessageStatuses.Cast<int>())})
                         AND CM.AnalyticTags LIKE {ParamNameAnalyticTag}
                     {ConstructOrderByClause(orderBy, direction)}
                     OFFSET {ParamNameOffset} ROWS
                         FETCH NEXT {ParamNameLimit} ROWS ONLY;
                     """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    public static CampaignAnalyticsQueryDefinition GetUserProfileIdsByAnalyticTagQueryDef(
        string companyId,
        string analyticTag,
        IEnumerable<MessageStatus> includedMessageStatuses)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameAnalyticTag, GetAnalyticTagSqlParamValue(analyticTag)),
        };

        var query = $"""
                    SELECT
                        C.UserProfileId as UserProfileId
                    FROM
                        ConversationMessages CM
                          JOIN Conversations C
                               ON CM.ConversationId = C.Id
                    WHERE
                        CM.CompanyId = {ParamNameCompanyId}
                        AND CM.Status IN ({string.Join(", ", includedMessageStatuses.Cast<int>())})
                        AND CM.AnalyticTags LIKE {ParamNameAnalyticTag};
                    """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    public static CampaignAnalyticsQueryDefinition GetRepliedCountByAnalyticTagQueryDef(
        string companyId,
        string analyticTag,
        ReplyWindow replyWindow)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameReplyWindowLengthInHours, GetReplyWindowLengthInHours(replyWindow)),
            new SqlParameter(ParamNameAnalyticTag, GetAnalyticTagSqlParamValue(analyticTag)),
        };

        var query = $"""
                     WITH
                     OutMessages AS (
                         {AnalyticTagOutMessageQ(MessageStatusGroups.Delivered)}
                     ),
                     RepliedMessages AS (
                         {RepliedMessageCountQ()}
                     )
                     SELECT
                         Count(1) AS Count
                     FROM
                         RepliedMessages;
                     """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    public static CampaignAnalyticsQueryDefinition GetRepliedMessageOverviewByAnalyticTagQueryDef(
        string companyId,
        string analyticTag,
        ReplyWindow replyWindow,
        string orderBy,
        string direction,
        int offset,
        int limit)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameAnalyticTag, GetAnalyticTagSqlParamValue(analyticTag)),
            new SqlParameter(ParamNameReplyWindowLengthInHours, GetReplyWindowLengthInHours(replyWindow)),
            new SqlParameter(ParamNameOffset, offset),
            new SqlParameter(ParamNameLimit, limit),
        };

        var query = $"""
                     WITH
                     OutMessages AS (
                         {AnalyticTagOutMessageQ(MessageStatusGroups.Delivered)}
                     ),
                     RepliedMessages AS (
                         {RepliedMessageOverviewQ(orderBy, direction)}
                     )
                     SELECT
                         OutMessageId AS {nameof(CampaignMessageOverviewDto.MessageId)},
                         ConversationId AS {nameof(CampaignMessageOverviewDto.ConversationId)},
                         UserProfileId AS {nameof(CampaignMessageOverviewDto.UserProfileId)},
                         FirstName AS {nameof(CampaignMessageOverviewDto.FirstName)},
                         LastName AS {nameof(CampaignMessageOverviewDto.LastName)},
                         FullName AS {nameof(CampaignMessageOverviewDto.FullName)},
                         PhoneNumber AS {nameof(CampaignMessageOverviewDto.PhoneNumber)},
                         Status AS {nameof(CampaignMessageOverviewDto.MessageStatus)},
                         ChannelStatusMessage AS {nameof(CampaignMessageOverviewDto.ChannelStatusMessage)},
                         OutCreatedAt AS {nameof(CampaignMessageOverviewDto.CreatedAt)}
                     FROM
                         RepliedMessages;
                     """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    public static CampaignAnalyticsQueryDefinition GetRepliedMessageUserProfileIdsByAnalyticTagQueryDef(
        string companyId,
        string analyticTag,
        ReplyWindow replyWindow)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameAnalyticTag, GetAnalyticTagSqlParamValue(analyticTag)),
            new SqlParameter(ParamNameReplyWindowLengthInHours, GetReplyWindowLengthInHours(replyWindow)),
        };

        var query = $"""
                     WITH
                     OutMessages AS (
                         {AnalyticTagOutMessageQ(MessageStatusGroups.Delivered)}
                     ),
                     RepliedMessages AS (
                         {RepliedMessageUserProfileIdQ()}
                     )
                     SELECT
                         UserProfileId
                     FROM
                         RepliedMessages;
                     """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    private static string GetAnalyticTagSqlParamValue(string analyticTag) =>
        $"%\"{analyticTag}\"%";

    private static string AnalyticTagOutMessageQ(IEnumerable<MessageStatus> includedMessageStatuses) =>
        $"""
         SELECT
             CM.Id AS OutMessageId,
             CM.ConversationId,
             CM.CreatedAt AS OutCreatedAt,
             CM.ChannelStatusMessage,
             CM.Status
         FROM
           ConversationMessages CM
         WHERE
            CM.CompanyId = {ParamNameCompanyId}
            AND CM.Status IN ({string.Join(", ", includedMessageStatuses.Cast<int>())})
            AND CM.AnalyticTags LIKE {ParamNameAnalyticTag}
         """;

    #endregion

    #region Common

    public static CampaignAnalyticsQueryDefinition FilterRepliedMessageIdsQueryDef(
        List<long> messageIds,
        string companyId,
        ReplyWindow replyWindow)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameReplyWindowLengthInHours, GetReplyWindowLengthInHours(replyWindow)),
        };

        var query = $"""
                     WITH
                     OutMessages AS (
                         SELECT
                             Id AS OutMessageId,
                             ConversationId,
                             CreatedAt AS OutCreatedAt
                         FROM
                             ConversationMessages CM
                         WHERE
                             CompanyId = {ParamNameCompanyId}
                             AND Id IN ({string.Join(", ", messageIds)})
                     ),
                     RepliedMessages AS (
                         {RepliedMessageCountQ()}
                     )
                     SELECT
                         OutMessageId as MessageId
                     FROM
                         RepliedMessages;
                     """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    public static CampaignAnalyticsQueryDefinition GetReplyMessageOverviewsQueryDef(
        string companyId,
        List<long> messageIds,
        ReplyWindow replyWindow)
    {
        var sqlParams = new List<SqlParameter>
        {
            new SqlParameter(ParamNameCompanyId, companyId),
            new SqlParameter(ParamNameReplyWindowLengthInHours, GetReplyWindowLengthInHours(replyWindow)),
        };

        var query = $"""
                    SELECT
                        OutMessages.OutMessageId,
                        RepliedMessages.Id             AS ReplyMessageId,
                        RepliedMessages.MessageContent AS ReplyMessageContent,
                        RepliedMessages.CreatedAt As RepliedAt
                    FROM
                        (
                            SELECT
                                Id AS OutMessageId,
                                conversationId,
                                createdAt AS OutCreatedAt
                            FROM
                                ConversationMessages
                            WHERE
                                CompanyId = {ParamNameCompanyId}
                                AND Id IN ({string.Join(", ", messageIds)})
                        ) OutMessages
                    CROSS APPLY
                        (
                            SELECT TOP 1
                                Id,
                                MessageContent,
                                CreatedAt
                            FROM
                                ConversationMessages CM
                            WHERE
                              CM.conversationId = OutMessages.conversationId
                              AND CompanyId = {ParamNameCompanyId}
                              AND CM.IsSentFromSleekflow = 0
                              AND CM.createdAt BETWEEN  OutMessages.OutCreatedAt AND DATEADD(HOUR, {ParamNameReplyWindowLengthInHours}, OutMessages.OutCreatedAt)
                            ORDER BY
                                CM.createdAt ASC
                        ) RepliedMessages
                    ORDER BY
                        OutMessages.OutMessageId;
                    """;

        return new CampaignAnalyticsQueryDefinition(query, sqlParams);
    }

    private static int GetReplyWindowLengthInHours(ReplyWindow replyWindow)
    {
        return replyWindow.Unit.ToLower() switch
        {
            "hour" => replyWindow.Length,
            "day" => replyWindow.Length * 24,
            _ => throw new ArgumentOutOfRangeException(replyWindow.Unit, $"Invalid unit: {replyWindow.Unit}")
        };
    }

    private static string ConstructOrderByClause(
        string orderBy,
        string direction)
    {
        var orderByStr = orderBy switch
        {
            GetMessageOverviewSortableFields.FirstName => $"UP.FirstName",
            GetMessageOverviewSortableFields.LastName => $"UP.LastName",
            GetMessageOverviewSortableFields.FullName => $"UP.FullName",
            GetMessageOverviewSortableFields.CreatedAt => "CM.CreatedAt",
            _ => throw new ArgumentOutOfRangeException(orderBy, $"Invalid order by field: {orderBy}")
        };

        var directionStr = direction.ToLower() switch
        {
            "asc" => "ASC",
            "desc" => "DESC",
            _ => throw new ArgumentOutOfRangeException(direction, $"Invalid direction: {direction}")
        };

        return $"ORDER BY {orderByStr} {directionStr}";
    }

    private static string RepliedMessageCountQ() =>
        $"""
        SELECT
            O.OutMessageId
        FROM
            OutMessages o
        WHERE
            EXISTS (
            SELECT 1
            FROM
                ConversationMessages CM_E
            WHERE
                O.ConversationId = CM_E.ConversationId
                AND CM_E.CompanyId = {ParamNameCompanyId}
                AND CM_E.IsSentFromSleekflow = 0
                AND CM_E.CreatedAt BETWEEN O.OutCreatedAt AND DATEADD(HOUR, {ParamNameReplyWindowLengthInHours}, O.OutCreatedAt)
            )
        """;

    private static string RepliedMessageOverviewQ(
        string orderBy,
        string direction)
    {
        var orderByStr = orderBy switch
        {
            GetMessageOverviewSortableFields.FirstName => "U.FirstName",
            GetMessageOverviewSortableFields.LastName => "U.LastName",
            GetMessageOverviewSortableFields.FullName => "U.FullName",
            GetMessageOverviewSortableFields.CreatedAt => "O.OutCreatedAt",
            _ => throw new ArgumentOutOfRangeException(orderBy, $"Invalid order by field: {orderBy}")
        };

        var directionStr = direction.ToLower() switch
        {
            "asc" => "ASC",
            "desc" => "DESC",
            _ => throw new ArgumentOutOfRangeException(direction, $"Invalid direction: {direction}")
        };

        var q = $"""
                SELECT
                    O.OutMessageId,
                    C.Id as ConversationId,
                    C.UserProfileId,
                    U.FirstName,
                    U.LastName,
                    U.FullName,
                    U.PhoneNumber,
                    O.ChannelStatusMessage,
                    O.Status,
                    O.OutCreatedAt
                FROM
                    OutMessages O
                JOIN
                    Conversations C
                ON
                    O.ConversationId = C.Id
                JOIN
                    UserProfiles U
                ON
                    C.UserProfileId = U.Id
                 WHERE
                    EXISTS(
                        SELECT
                            1
                        FROM
                            ConversationMessages CM_E
                        WHERE
                            O.ConversationId = CM_E.ConversationId
                            AND CM_E.CompanyId = {ParamNameCompanyId}
                            AND CM_E.IsSentFromSleekflow = 0
                            AND CM_E.CreatedAt BETWEEN O.OutCreatedAt AND DATEADD(HOUR, {ParamNameReplyWindowLengthInHours}, O.OutCreatedAt)
                    )
                 ORDER BY
                     {orderByStr} {directionStr}
                 OFFSET {ParamNameOffset} ROWS
                     FETCH NEXT {ParamNameLimit} ROWS ONLY
                """;

        return q;
    }

    private static string RepliedMessageUserProfileIdQ() =>
        $"""
        SELECT
            C.UserProfileId
        FROM
            OutMessages O
        JOIN
            Conversations C
        ON
            O.ConversationId = C.Id
        WHERE
            EXISTS(
                SELECT
                    1
                FROM
                    ConversationMessages CM_E
                WHERE
                    O.ConversationId = CM_E.ConversationId
                    AND CM_E.CompanyId = {ParamNameCompanyId}
                    AND CM_E.IsSentFromSleekflow = 0
                    AND CM_E.CreatedAt BETWEEN O.OutCreatedAt AND DATEADD(HOUR, {ParamNameReplyWindowLengthInHours}, O.OutCreatedAt)
            )
        """;

    #endregion
}