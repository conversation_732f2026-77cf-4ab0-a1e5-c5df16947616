using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.Database;
using Travis_backend.Database.Services;

namespace Travis_backend.AnalyticsDomain.Services;

public interface ITopicAnalyticsMetricsRepository
{
    Task<List<TopicAnalyticsMetric>> GetDailyDataAsync(
        string companyId,
        DateOnly date,
        string topicId,
        CancellationToken cancellationToken);

    Task<DateTime?> GetLastUpdateTime(CancellationToken cancellationToken = default);
}

/// <summary>
/// For the implementation, we are avoiding using sql param here because found some performance issue
/// when play with EF Core .FromRawSql()
/// </summary>
public class TopicAnalyticsMetricsRepository : ITopicAnalyticsMetricsRepository
{
    private readonly AnalyticDbContext _analyticDbContext;

    public TopicAnalyticsMetricsRepository(IDbContextService dbContextService)
    {
        _analyticDbContext = dbContextService.GetAnalyticDbContext();
    }

    public async Task<List<TopicAnalyticsMetric>> GetDailyDataAsync(
        string companyId,
        DateOnly date,
        string topicId,
        CancellationToken cancellationToken)
    {
        var query =
            $@"
            SELECT *
            FROM topic_analytics_metrics tam
            WHERE tam.sleekflow_company_id = '{companyId}'
                AND tam.topic_id = '{topicId}'
                AND tam.date = '{date.ToString("O")}';";

        var res = await _analyticDbContext.TopicAnalyticsMetrics
            .FromSqlRaw(query)
            .ToListAsync(cancellationToken);
        return res;
    }

    public async Task<DateTime?> GetLastUpdateTime(CancellationToken cancellationToken = default)
    {
        var res = await _analyticDbContext.TopicAnalyticsUpdateLog
            .Where(x => x.StartTimeUtc != null && x.EndTimeUtc != null)
            .OrderByDescending(x => x.EndTimeUtc)
            .FirstOrDefaultAsync(cancellationToken);

        return res?.StartTimeUtc;
    }
}