using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.Utils;
using Travis_backend.Database;
using Travis_backend.Database.Services;

namespace Travis_backend.AnalyticsDomain.Services;

public interface IConversationAnalyticsMetricsRepository
{
    Task<List<ConversationAnalyticsMetric>> GetDailyDataByDimensionAsync(
        string companyId,
        DateOnly date,
        ConversationAnalyticsDimensionWrapper dimensionWrapper,
        ICollection<string> userProfileIds,
        CancellationToken cancellationToken);

    Task<List<ConversationAnalyticsMetric>> GetPaginatedDailyDataAsync(
        string companyId,
        DateOnly date,
        ConversationAnalyticsDimensionWrapper dimensionWrapper,
        int skip,
        int take,
        CancellationToken cancellationToken);

    Task<DateTime?> GetLastUpdateTime(CancellationToken cancellationToken = default);
}


/// <summary>
/// For the implementation, we are avoiding using sql param here because found some performance issue
/// when play with EF Core .FromRawSql()
/// </summary>
public class ConversationAnalyticsMetricsRepository : IConversationAnalyticsMetricsRepository
{
    private readonly AnalyticDbContext _analyticDbContext;

    public ConversationAnalyticsMetricsRepository(IDbContextService dbContextService)
    {
        _analyticDbContext = dbContextService.GetAnalyticDbContext();
    }

    public async Task<List<ConversationAnalyticsMetric>> GetDailyDataByDimensionAsync(
        string companyId,
        DateOnly date,
        ConversationAnalyticsDimensionWrapper dimensionWrapper,
        ICollection<string> userProfileIds,
        CancellationToken cancellationToken)
    {
        var dimensionKey = dimensionWrapper.Dimension.GetDimensionKey();

        var (userProfileIdsClause, userProfileIdsParameter) = GetUserProfileIdsClause(dimensionWrapper, userProfileIds);

        var query =
            $@"
            SELECT *
            FROM conversation_analytics_metrics cam
            WHERE cam.sleekflow_company_id = '{companyId}'
                {userProfileIdsClause}
                AND cam.period_start_date = '{date.ToString("O")}'
                AND cam.dim_json_array = '{dimensionKey}'
                AND cam.period_type = 'day';";

        var res = userProfileIdsParameter is null
            ? await _analyticDbContext.ConversationAnalyticsMetrics
                .FromSqlRaw(query)
                .ToListAsync(cancellationToken)
            : await _analyticDbContext.ConversationAnalyticsMetrics
                .FromSqlRaw(query, userProfileIdsParameter)
                .ToListAsync(cancellationToken);

        return res;
    }

    public async Task<List<ConversationAnalyticsMetric>> GetPaginatedDailyDataAsync(
        string companyId,
        DateOnly date,
        ConversationAnalyticsDimensionWrapper dimensionWrapper,
        int skip,
        int take,
        CancellationToken cancellationToken)
    {
        var dimensionKey = dimensionWrapper.Dimension.GetDimensionKey();

        var query = $@"
        SELECT *
        FROM conversation_analytics_metrics cam
        WHERE cam.sleekflow_company_id = '{companyId}'
            AND cam.period_start_date = '{date.ToString("O")}'
            AND cam.dim_json_array = '{dimensionKey}'
            AND cam.period_type = 'day'
        ORDER BY cam.id
        OFFSET {skip.ToString()} ROWS
        FETCH NEXT {take.ToString()} ROWS ONLY;";

        var res = await _analyticDbContext.ConversationAnalyticsMetrics
            .FromSqlRaw(query)
            .ToListAsync(cancellationToken);

        return res;
    }

    public async Task<DateTime?> GetLastUpdateTime(CancellationToken cancellationToken = default)
    {
        var res = await _analyticDbContext.ConversationAnalyticsUpdateLog
            .Where(x=>x.StartTimeUtc != null && x.EndTimeUtc != null)
            .OrderByDescending(x=>x.EndTimeUtc)
            .FirstOrDefaultAsync(cancellationToken);

        return res?.StartTimeUtc;
    }


    private static (string, SqlParameter?) GetUserProfileIdsClause(
        ConversationAnalyticsDimensionWrapper dimensionWrapper,
        ICollection<string> userProfileIds)
    {
        string clause;
        SqlParameter? sqlParam = null;

        if (!dimensionWrapper.IsContactDimension)
        {
            clause = " AND cam.user_profile_id IS NULL ";
        }
        else
        {
            var jsonArray = JsonConvert.SerializeObject(userProfileIds);
            clause = @"
            AND EXISTS (
                SELECT 1
                FROM OPENJSON(@UserProfileIds) AS n
                WHERE n.value = cam.user_profile_id
            )";

            sqlParam = new SqlParameter("@UserProfileIds", SqlDbType.NVarChar)
            {
                Value = jsonArray
            };
        }

        return (clause, sqlParam);
    }
}