﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Travis_backend.CampaignAnalyticsDomain.Models;

public class ReplyWindow : IValidatableObject
{
    [Required]
    [RegularExpression("^(day|hour)$", ErrorMessage = "Unit must be either 'day' or 'hour'.")]
    public string Unit { get; set; }

    [Required]
    [Range(1, int.MaxValue, ErrorMessage = "Length must be greater than 0.")]
    public int Length { get; set; }

    public ReplyWindow(string unit, int length)
    {
        Unit = unit;
        Length = length;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if ((Unit == "day" && Length > 7) ||
            (Unit == "hour" && Length > 24 * 7))
        {
            yield return new ValidationResult(
                "Window length must be less than 7 days or 7 * 24 hours.",
                new[] { nameof(Length) });
        }
    }
}