﻿using System.Collections.Immutable;

namespace Travis_backend.CampaignAnalyticsDomain.Constants;

public static class GetMessageOverviewSortableFields
{
    public const string FirstName = "FirstName";
    public const string LastName = "LastName";
    public const string FullName = "FullName";
    public const string CreatedAt = "CreatedAt";

    public static ImmutableList<string> All = ImmutableList.Create(
        FirstName,
        LastName,
        FullName,
        CreatedAt);
}