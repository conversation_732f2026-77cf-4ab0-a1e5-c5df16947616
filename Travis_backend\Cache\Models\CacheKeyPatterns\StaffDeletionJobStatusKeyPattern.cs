using Travis_backend.Cache.Models.CacheKeyPatterns;

namespace Travis_backend.Cache.Models.CacheKeyPatterns;

/// <summary>
/// Cache key pattern for staff deletion job status
/// </summary>
public sealed class StaffDeletionJobStatusKeyPattern : ICacheKeyPattern
{
    public string CompanyId { get; }

    public long StaffId { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="StaffDeletionJobStatusKeyPattern"/> class.
    /// Creates a new instance of StaffDeletionJobStatusKeyPattern
    /// </summary>
    /// <param name="companyId">The company ID</param>
    /// <param name="staffId">The staff ID being deleted</param>
    public StaffDeletionJobStatusKeyPattern(string companyId, long staffId)
    {
        CompanyId = companyId;
        StaffId = staffId;
    }

    /// <summary>
    /// Generates a unique cache key pattern for the staff deletion job status
    /// </summary>
    /// <returns>The generated cache key pattern</returns>
    public string GenerateKeyPattern()
    {
        return CacheHelper.BuildKeyNameFromPattern(
            new object[]
            {
                CachePrefixType.StaffDeletion,
                CompanyId,
                StaffId
            });
    }

    /// <summary>
    /// Creates a new instance of StaffDeletionJobStatusKeyPattern
    /// </summary>
    /// <param name="companyId">The company ID</param>
    /// <param name="staffId">The staff ID being deleted</param>
    /// <returns>A new StaffDeletionJobStatusKeyPattern instance</returns>
    public static StaffDeletionJobStatusKeyPattern Create(string companyId, long staffId)
    {
        return new StaffDeletionJobStatusKeyPattern(companyId, staffId);
    }
}
