using System.Collections.Generic;
using Travis_backend.ChannelDomain.ViewModels.Interfaces;

namespace Travis_backend.ChannelDomain.ViewModels;

public class TikTokConfigViewModel : IMessagingChannelDto
{
    public string Id { get; set; }

    public string CompanyId { get; set; }
    public string ChannelType { get; set; }
    public string ChannelIdentityId { get; set; }
    public string ChannelDisplayName { get; set; }
    public string TikTokConfigId { get; set; }
    public Dictionary<string, object> Metadata { get; set; }
}