﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.BackgroundTaskServices.Models;
using Travis_backend.CampaignAnalyticsDomain.Constants;
using Travis_backend.CampaignAnalyticsDomain.Models;
using Travis_backend.CampaignAnalyticsDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.FileDomain.Services;

namespace Travis_backend.CampaignAnalyticsDomain.Services;

public interface ICampaignAnalyticsDataExporter
{
    Task<ExportCampaignAnalyticMessageOverviewsToCsvBackgroundTaskResultPayload>
        ExportMessageOverviewsAsync(
            string companyId,
            ExportCampaignMessageOverviewsRequest request,
            CancellationToken cancellationToken,
            Func<int, ValueTask> onComplete = null);

    Task<int> GetTotalCountAsync(
        string companyId,
        ExportCampaignMessageOverviewsRequest request,
        CancellationToken cancellationToken);
}

public class CampaignAnalyticsExporter : ICampaignAnalyticsDataExporter
{
    private const int PageSize = 200;

    private readonly ILogger<CampaignAnalyticsExporter> _logger;
    private readonly ICampaignAnalyticsService _campaignAnalyticsService;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly ApplicationReadDbContext _applicationReadDbContext;
    private readonly IUploadService _uploadService;
    private readonly ICampaignAnalyticsRepository _campaignAnalyticsRepository;

    public CampaignAnalyticsExporter(
        ILogger<CampaignAnalyticsExporter> logger,
        ICampaignAnalyticsService campaignAnalyticsService,
        IAzureBlobStorageService azureBlobStorageService,
        IUploadService uploadService,
        ApplicationReadDbContext applicationReadDbContext,
        ICampaignAnalyticsRepository campaignAnalyticsRepository)
    {
        _logger = logger;
        _campaignAnalyticsService = campaignAnalyticsService;
        _azureBlobStorageService = azureBlobStorageService;
        _uploadService = uploadService;
        _applicationReadDbContext = applicationReadDbContext;
        _campaignAnalyticsRepository = campaignAnalyticsRepository;
    }

    public async Task<ExportCampaignAnalyticMessageOverviewsToCsvBackgroundTaskResultPayload> ExportMessageOverviewsAsync(
        string companyId,
        ExportCampaignMessageOverviewsRequest request,
        CancellationToken cancellationToken,
        Func<int, ValueTask> onComplete = null)
    {
        await using var csvStream = new MemoryStream();
        await using var streamWriter = new StreamWriter(csvStream, Encoding.UTF8);
        await using var csvWriter = new CsvWriter(streamWriter, CultureInfo.InvariantCulture);

        await WriteCsvHeaderAsync(csvWriter);

        List<CampaignMessageWithReplyOverviewViewModel> campaignMessageWithReplyOverviews;
        var offset = 0;
        var total = 0;

        do
        {
            campaignMessageWithReplyOverviews = await GetCampaignMessageWithReplyOverviewsAsync(
                companyId,
                request,
                offset,
                cancellationToken);

            if (campaignMessageWithReplyOverviews.Count == 0)
            {
                break;
            }

            await WriteCsvRecordsAsync(csvWriter, campaignMessageWithReplyOverviews);

            if (onComplete != null)
            {
                await onComplete(campaignMessageWithReplyOverviews.Count);
            }

            offset += PageSize;
            total += campaignMessageWithReplyOverviews.Count;
        }
        while (campaignMessageWithReplyOverviews.Count > 0);

        await csvWriter.FlushAsync();
        await streamWriter.FlushAsync(cancellationToken);

        var fileName = await GetFileNameAsync(companyId, request, cancellationToken);
        var csvUploadResult = await UploadCsvStreamToBlobAsync(companyId, fileName, csvStream, cancellationToken);

        _logger.Log(
            LogLevel.Information,
            "Completed export campaign message overviews for Company: {CompanyId}. Total: {Total}",
            companyId,
            total);

        return new ExportCampaignAnalyticMessageOverviewsToCsvBackgroundTaskResultPayload
        {
            FileName = csvUploadResult.FileName,
            FilePath = csvUploadResult.FilePath,
            MIMEType = "text/csv",
            Url = csvUploadResult.DownloadUri,
            FileSize = csvUploadResult.FileSizeInByte
        };
    }

    public async Task<int> GetTotalCountAsync(
        string companyId,
        ExportCampaignMessageOverviewsRequest request,
        CancellationToken cancellationToken)
    {
        CampaignCommonMetrics campaignCommonMetrics = request switch
        {
            ExportCampaignMessageOverviewsByBroadcastRequest broadcastRequest =>
                await _campaignAnalyticsService.GetCampaignCommonMetricsByBroadcastAsync(
                    companyId,
                    broadcastRequest.BroadcastCampaignId,
                    broadcastRequest.ReplyWindow,
                    cancellationToken),

            ExportCampaignMessageOverviewsByAnalyticTagRequest analyticTagRequest =>
                await _campaignAnalyticsService.GetCampaignCommonMetricsByAnalyticTagAsync(
                    companyId,
                    analyticTagRequest.AnalyticTag,
                    analyticTagRequest.ReplyWindow,
                    cancellationToken),

            _ => throw new ArgumentException($"Invalid request type: {request.GetType().Name}")
        };

        return request.Status switch
        {
            CampaignMessageStatuses.Sent => campaignCommonMetrics.Sent,
            CampaignMessageStatuses.Delivered => campaignCommonMetrics.Delivered,
            CampaignMessageStatuses.Read => campaignCommonMetrics.Read,
            CampaignMessageStatuses.Replied => campaignCommonMetrics.Replied,
            CampaignMessageStatuses.Bounced => campaignCommonMetrics.Bounced,
            _ => throw new ArgumentException($"Invalid status: {request.Status}")
        };
    }

    public async Task<List<CampaignMessageWithReplyOverviewViewModel>> GetCampaignMessageWithReplyOverviewsAsync(
        string companyId,
        ExportCampaignMessageOverviewsRequest request,
        int offset,
        CancellationToken cancellationToken)
    {
        List<CampaignMessageOverviewViewModel> campaignMessageOverviews = request switch
        {
            ExportCampaignMessageOverviewsByBroadcastRequest broadcastRequest =>
                await _campaignAnalyticsService.GetCampaignMessageOverviewByBroadcastAsync(
                    companyId,
                    broadcastRequest.BroadcastCampaignId,
                    broadcastRequest.Status,
                    broadcastRequest.OrderBy,
                    broadcastRequest.Direction,
                    offset,
                    PageSize,
                    broadcastRequest.ReplyWindow,
                    cancellationToken),

            ExportCampaignMessageOverviewsByAnalyticTagRequest analyticTagRequest =>
                await _campaignAnalyticsService.GetCampaignMessageOverviewByAnalyticTagAsync(
                    companyId,
                    analyticTagRequest.AnalyticTag,
                    analyticTagRequest.Status,
                    analyticTagRequest.OrderBy,
                    analyticTagRequest.Direction,
                    offset,
                    PageSize,
                    analyticTagRequest.ReplyWindow,
                    cancellationToken),

            _ => throw new ArgumentException($"Invalid request type: {request.GetType().Name}")
        };

        var repliedCampaignMessageIds = campaignMessageOverviews
            .Where(x => x.Status == CampaignMessageStatuses.Replied)
            .Select(x => x.MessageId).ToList();

        var replyMessageOverviews = new List<ReplyMessageOverviewDto>();
        if (repliedCampaignMessageIds.Count > 0)
        {
            replyMessageOverviews = await _campaignAnalyticsRepository.GetRepliedMessageOverviewsAsync(
                companyId,
                repliedCampaignMessageIds,
                request.ReplyWindow,
                cancellationToken);
        }

        var campaignMessageWithReplyOverviews = campaignMessageOverviews.Select(
                x => new CampaignMessageWithReplyOverviewViewModel(
                    x,
                    replyMessageOverviews.FirstOrDefault(y => y.OutMessageId == x.MessageId)))
            .ToList();

        return campaignMessageWithReplyOverviews;
    }

    private static async Task WriteCsvHeaderAsync(IWriter csvWriter)
    {
        csvWriter.WriteField("ContactId");
        csvWriter.WriteField("FirstName");
        csvWriter.WriteField("LastName");
        csvWriter.WriteField("PhoneNumber");
        csvWriter.WriteField("Status");
        csvWriter.WriteField("Datetime (UTC)");
        csvWriter.WriteField("Replied At (UTC)");
        csvWriter.WriteField("Replied Message");
        csvWriter.WriteField("Status Reason");

        await csvWriter.NextRecordAsync();
    }

    private static async Task WriteCsvRecordsAsync(
        IWriter csvWriter,
        List<CampaignMessageWithReplyOverviewViewModel> records)
    {
        foreach (var record in records)
        {
            csvWriter.WriteField(record.RecipientOverview.UserProfileId);
            csvWriter.WriteField(record.RecipientOverview.FirstName);
            csvWriter.WriteField(record.RecipientOverview.LastName);
            csvWriter.WriteField(record.RecipientOverview.PhoneNumber);
            csvWriter.WriteField(record.Status);
            csvWriter.WriteField(record.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));
            csvWriter.WriteField(record.RepliedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? string.Empty);
            csvWriter.WriteField(record.ReplyMessageContent ?? string.Empty);
            csvWriter.WriteField(record.FailedReason ?? string.Empty);

            await csvWriter.NextRecordAsync();
        }
    }

    public sealed record CsvUploadResult
    {
        public string FileName { get; set; }

        public string FilePath { get; set; }

        public string DownloadUri { get; set; }

        public long FileSizeInByte { get; set; }
    }

    public async Task<CsvUploadResult> UploadCsvStreamToBlobAsync(
        string companyId,
        string fileName,
        Stream csvStream,
        CancellationToken cancellationToken)
    {
        csvStream.Position = 0;

        var filePath = $"ExportAnalytic/Campaign/{companyId}/{fileName}";

        try
        {
            var storageConfig = await _applicationReadDbContext.ConfigStorageConfigs
                .AsNoTracking()
                .FirstOrDefaultAsync(
                    x => x.CompanyId == companyId,
                    cancellationToken: cancellationToken);

            var containerName = storageConfig?.ContainerName ?? companyId;

            var uploadFileResult = await _uploadService.UploadFileBySteam(
                containerName,
                filePath,
                csvStream,
                "text/csv");

            var sasUri = _azureBlobStorageService.GetAzureBlobSasUri(
                filePath,
                containerName,
                24);

            return new CsvUploadResult
            {
                FileName = fileName,
                FilePath = filePath,
                DownloadUri = sasUri,
                FileSizeInByte = uploadFileResult.FileSizeInByte
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Campaign Analytics Exporter - failed to upload CSV file for company {CompanyId}",
                companyId);

            throw;
        }
    }

    private async Task<string> GetFileNameAsync(
        string companyId,
        ExportCampaignMessageOverviewsRequest request,
        CancellationToken cancellationToken)
    {
        var campaignName = request switch
        {
            ExportCampaignMessageOverviewsByBroadcastRequest broadcastRequest =>
                await _campaignAnalyticsRepository.GetBroadcastTitleAsync(
                    companyId,
                    broadcastRequest.BroadcastCampaignId,
                    cancellationToken),

            ExportCampaignMessageOverviewsByAnalyticTagRequest analyticTagRequest =>
                analyticTagRequest.AnalyticTag,

            _ => throw new ArgumentException($"Invalid request type: {request.GetType().Name}")
        };

        var fileName = $"[{NormalizeFileName(campaignName)}]_{request.Status}_report_{DateTimeOffset.UtcNow:yyyy-MM-dd-HHmmss}.csv";
        return fileName;
    }

    private static string NormalizeFileName(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return string.Empty;
        }

        // includes: \ / : * ? " < > | and control characters (ASCII 0-31)
        const string pattern = @"[\\/:*?""<>|]|[\x00-\x1F]";

        var sanitized = Regex.Replace(input, pattern, "_");

        return sanitized.Trim(' ');
    }
}