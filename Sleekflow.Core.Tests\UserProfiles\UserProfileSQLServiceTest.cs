using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationServices.Operations;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using static Travis_backend.Constants.OrderCondition;

namespace Sleekflow.Core.Tests.UserProfiles;

public class UserProfileSqlServiceTest
{
    private const string UserProfile = "UP";
    private UserProfileSqlService _userProfileSqlService;
    private ManageableOperation _manageableOperation;
    private ApplicationDbContext _dbContext;

    [SetUp]
    public void Setup()
    {
        var connectionString =
            "Server=tcp:sleekflow-core-sql-server-eas-dev928ea268.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=s81604a6f;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=100;";
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseSqlServer(connectionString)
            .Options;
        _dbContext = new ApplicationDbContext(options);
        var channelOperation = new ChannelOperation(NullLogger<ChannelOperation>.Instance, _dbContext);
        var hashtagOperation = new HashtagOperation(NullLogger<HashtagOperation>.Instance, _dbContext);
        var shopifyOperation = new ShopifyOperation(NullLogger<ShopifyOperation>.Instance);
        var dateTimeOperation = new DateTimeOperation(_dbContext, NullLogger<DateTimeOperation>.Instance);
        _manageableOperation = new ManageableOperation(_dbContext, NullLogger<ManageableOperation>.Instance);
        var customFieldOperation = new CustomFieldOperation(_dbContext, NullLogger<CustomFieldOperation>.Instance);
        var broadcastHistoriesOperation = new BroadcastHistoriesOperation(
            _dbContext,
            NullLogger<BroadcastHistoriesOperation>.Instance);

        _userProfileSqlService = new UserProfileSqlService(
            new Mock<IDbContextService>().Object,
            channelOperation,
            hashtagOperation,
            shopifyOperation,
            dateTimeOperation,
            NullLogger<UserProfileSqlService>.Instance,
            _manageableOperation,
            customFieldOperation,
            broadcastHistoriesOperation);
    }

    [Test]
    public async Task Name_Test()
    {
        var firstnameContain = new List<Condition>
        {
            new Condition
            {
                FieldName = "firstname",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "will"
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var firstnameNotContain = new List<Condition>
        {
            new Condition
            {
                FieldName = "firstname",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "will", "johnson"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var firstnameKnown = new List<Condition>
        {
            new Condition
            {
                FieldName = "firstname",
                ConditionOperator = SupportedOperator.IsNotNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var firstnameUnknown = new List<Condition>
        {
            new Condition
            {
                FieldName = "firstname",
                ConditionOperator = SupportedOperator.IsNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var lastnameContain = new List<Condition>
        {
            new Condition
            {
                FieldName = "lastname",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "shi", "chong"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var lastnameNotContain = new List<Condition>
        {
            new Condition
            {
                FieldName = "lastname",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "shi"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var lastnameKnown = new List<Condition>
        {
            new Condition
            {
                FieldName = "lastname",
                ConditionOperator = SupportedOperator.IsNotNull,
                Values = new List<string>
                {
                    "shi"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var lastnameUnknown = new List<Condition>
        {
            new Condition
            {
                FieldName = "lastname",
                ConditionOperator = SupportedOperator.IsNotNull,
                Values = new List<string>
                {
                    "shi"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var displayNameContain = new List<Condition>
        {
            new Condition
            {
                FieldName = "displayname",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "J"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var displayNameNotContain = new List<Condition>
        {
            new Condition
            {
                FieldName = "displayname",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "J"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var displayNameKnown = new List<Condition>
        {
            new Condition
            {
                FieldName = "displayname",
                ConditionOperator = SupportedOperator.IsNotNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var displayNameUnknown = new List<Condition>
        {
            new Condition
            {
                FieldName = "displayname",
                ConditionOperator = SupportedOperator.IsNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var queryF1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", firstnameContain);
        var queryF2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", firstnameNotContain);
        var queryF3 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", firstnameKnown);
        var queryF4 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", firstnameUnknown);

        var queryL1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", lastnameContain);
        var queryL2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", lastnameNotContain);
        var queryL3 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", lastnameKnown);
        var queryL4 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", lastnameUnknown);

        var queryD1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", displayNameContain);
        var queryD2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", displayNameNotContain);
        var queryD3 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", displayNameKnown);
        var queryD4 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", displayNameUnknown);


        var expectedF1 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.FirstName IS NOT NULL  AND (UP.FirstName LIKE @UP_First_Name_0_0)  ";
        var expectedF2 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.FirstName IS NOT NULL  AND (UP.FirstName NOT LIKE @UP_First_Name_0_0 OR UP.FirstName NOT LIKE @UP_First_Name_0_1)  ";
        var expectedF3 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.FirstName IS NOT NULL AND UP.FirstName != ''  ";
        var expectedF4 =
            "SELECT UP.* FROM UserProfiles UP  WHERE (UP.FirstName IS NULL OR UP.FirstName = '' COLLATE latin1_general_100_ci_as)  ";
        var expectedL1 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.LastName IS NOT NULL  AND (UP.LastName LIKE @UP_Last_Name_0_0 OR UP.LastName LIKE @UP_Last_Name_0_1)  ";
        var expectedL2 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.LastName IS NOT NULL  AND (UP.LastName NOT LIKE @UP_Last_Name_0_0)  ";
        var expectedL3 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.LastName IS NOT NULL AND UP.LastName != ''  ";
        var expectedL4 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.LastName IS NOT NULL AND UP.LastName != ''  ";
        var expectedD1 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.FullName LIKE @UP_FullName_0   ";
        var expectedD2 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.FullName NOT LIKE @UP_FullName_0   ";
        var expectedD3 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.FullName IS NOT NULL AND UP.FullName != ''   ";
        var expectedD4 =
            "SELECT UP.* FROM UserProfiles UP  WHERE (UP.FullName IS NULL OR UP.FullName = '' COLLATE latin1_general_100_ci_as)   ";

        logTestResult(nameof(lastnameContain), queryF1);
        logTestResult(nameof(lastnameNotContain), queryF2);
        logTestResult(nameof(lastnameKnown), queryF3);
        logTestResult(nameof(lastnameUnknown), queryF4);

        logTestResult(nameof(lastnameContain), queryL1);
        logTestResult(nameof(lastnameNotContain), queryL2);
        logTestResult(nameof(lastnameKnown), queryL3);
        logTestResult(nameof(lastnameUnknown), queryL4);

        logTestResult(nameof(displayNameContain), queryD1);
        logTestResult(nameof(displayNameNotContain), queryD2);
        logTestResult(nameof(displayNameKnown), queryD3);
        logTestResult(nameof(displayNameUnknown), queryD4);

        Assert.That(queryF1, Is.EqualTo(expectedF1));
        Assert.That(queryF2, Is.EqualTo(expectedF2));
        Assert.That(queryF3, Is.EqualTo(expectedF3));
        Assert.That(queryF4, Is.EqualTo(expectedF4));

        Assert.That(queryL1, Is.EqualTo(expectedL1));
        Assert.That(queryL2, Is.EqualTo(expectedL2));
        Assert.That(queryL3, Is.EqualTo(expectedL3));
        Assert.That(queryL4, Is.EqualTo(expectedL4));

        Assert.That(queryD1, Is.EqualTo(expectedD1));
        Assert.That(queryD2, Is.EqualTo(expectedD2));
        Assert.That(queryD3, Is.EqualTo(expectedD3));
        Assert.That(queryD4, Is.EqualTo(expectedD4));
    }


    [Test]
    public async Task ImportFrom_Test()
    {
        var importFromContains = new List<Condition>
        {
            new Condition
            {
                FieldName = "ImportFrom",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "5542", "5441"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var importFromContainsAny = new List<Condition>
        {
            new Condition
            {
                FieldName = "ImportFrom",
                ConditionOperator = SupportedOperator.ContainsAny,
                Values = new List<string>
                {
                    "5542", "5441"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var importFromNotContains = new List<Condition>
        {
            new Condition
            {
                FieldName = "ImportFrom",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "5542", "5441"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var importFromNotContainsAny = new List<Condition>
        {
            new Condition
            {
                FieldName = "ImportFrom",
                ConditionOperator = SupportedOperator.IsNotContainsAny,
                Values = new List<string>
                {
                    "5542", "5441"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var importFromContainsAll = new List<Condition>
        {
            new Condition
            {
                FieldName = "ImportFrom",
                ConditionOperator = SupportedOperator.ContainsAll,
                Values = new List<string>
                {
                    "4548", "4550"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var importFromNotContainsAll = new List<Condition>
        {
            new Condition
            {
                FieldName = "ImportFrom",
                ConditionOperator = SupportedOperator.IsNotContainsAll,
                Values = new List<string>
                {
                    "4548", "4550"
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var query1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", importFromContains);
        var query2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", importFromContainsAny);
        var query3 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", importFromNotContains);
        var query4 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", importFromNotContainsAny);
        var query5 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", importFromContainsAll);
        var query6 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", importFromNotContainsAll);

        var expected1 =
            "SELECT UP.* FROM UserProfiles UP JOIN (SELECT UP_I_0.UserProfileId FROM CompanyImportedUserProfiles UP_I_0 WHERE UP_I_0.ImportContactHistoryId IN (@UP_ImportFrom_0_0,@UP_ImportFrom_0_1)  GROUP BY UP_I_0.UserProfileId) UP_SUB_0 ON UP_SUB_0.UserProfileId = UP.Id  ";
        var expected2 =
            "SELECT UP.* FROM UserProfiles UP JOIN (SELECT UP_I_0.UserProfileId FROM CompanyImportedUserProfiles UP_I_0 WHERE UP_I_0.ImportContactHistoryId IN (@UP_ImportFrom_0_0,@UP_ImportFrom_0_1)  GROUP BY UP_I_0.UserProfileId) UP_SUB_0 ON UP_SUB_0.UserProfileId = UP.Id  ";
        var expected3 =
            "SELECT UP.* FROM UserProfiles UP LEFT JOIN (SELECT UP_I_0.UserProfileId FROM CompanyImportedUserProfiles UP_I_0 WHERE UP_I_0.ImportContactHistoryId IN (@UP_ImportFrom_0_0,@UP_ImportFrom_0_1)  GROUP BY UP_I_0.UserProfileId) UP_SUB_0 ON UP_SUB_0.UserProfileId = UP.Id  WHERE UP_SUB_0.UserProfileId IS NULL  ";
        var expected4 =
            "SELECT UP.* FROM UserProfiles UP LEFT JOIN (SELECT UP_I_0.UserProfileId FROM CompanyImportedUserProfiles UP_I_0 WHERE UP_I_0.ImportContactHistoryId IN (@UP_ImportFrom_0_0,@UP_ImportFrom_0_1)  GROUP BY UP_I_0.UserProfileId) UP_SUB_0 ON UP_SUB_0.UserProfileId = UP.Id  WHERE UP_SUB_0.UserProfileId IS NULL  ";
        var expected5 =
            "SELECT UP.* FROM UserProfiles UP JOIN (SELECT UP_I_0.UserProfileId FROM CompanyImportedUserProfiles UP_I_0 WHERE UP_I_0.ImportContactHistoryId = @UP_ImportFrom_0_0 AND EXISTS (SELECT 1 FROM CompanyImportedUserProfiles UP_I_0_1 WHERE UP_I_0_1.UserProfileId = UP_I_0.UserProfileId AND UP_I_0_1.ImportContactHistoryId = @UP_ImportFrom_0_1) GROUP BY UP_I_0.UserProfileId) UP_SUB_0 ON UP_SUB_0.UserProfileId = UP.Id  ";
        var expected6 =
            "SELECT UP.* FROM UserProfiles UP LEFT JOIN (SELECT UP_I_0.UserProfileId FROM CompanyImportedUserProfiles UP_I_0 WHERE UP_I_0.ImportContactHistoryId = @UP_ImportFrom_0_0 AND EXISTS (SELECT 1 FROM CompanyImportedUserProfiles UP_I_0_1 WHERE UP_I_0_1.UserProfileId = UP_I_0.UserProfileId AND UP_I_0_1.ImportContactHistoryId = @UP_ImportFrom_0_1) GROUP BY UP_I_0.UserProfileId) UP_SUB_0 ON UP_SUB_0.UserProfileId = UP.Id  WHERE UP_SUB_0.UserProfileId IS NULL  ";

        Assert.That(query1, Is.EqualTo(expected1));
        Assert.That(query2, Is.EqualTo(expected2));
        Assert.That(query3, Is.EqualTo(expected3));
        Assert.That(query4, Is.EqualTo(expected4));
        Assert.That(query5, Is.EqualTo(expected5));
        Assert.That(query6, Is.EqualTo(expected6));

        logTestResult(nameof(importFromContains), query1);
        logTestResult(nameof(importFromContainsAny), query2);
        logTestResult(nameof(importFromNotContains), query3);
        logTestResult(nameof(importFromNotContainsAny), query4);
        logTestResult(nameof(importFromContainsAll), query5);
        logTestResult(nameof(importFromNotContainsAll), query6);
    }


    [Test]
    public async Task CreatedOrUpdated_Test()
    {
        var createLessThan = new List<Condition>
        {
            new Condition
            {
                FieldName = "createdat",
                ConditionOperator = SupportedOperator.LessThan,
                Values = new List<string>
                {
                    "2023-04-13T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var createMoreThan = new List<Condition>
        {
            new Condition
            {
                FieldName = "createdat",
                ConditionOperator = SupportedOperator.HigherThan,
                Values = new List<string>
                {
                    "2023-04-13T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var updateLessThan = new List<Condition>
        {
            new Condition
            {
                FieldName = "updatedat",
                ConditionOperator = SupportedOperator.LessThan,
                Values = new List<string>
                {
                    "2023-04-13T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var updateMoreThan = new List<Condition>
        {
            new Condition
            {
                FieldName = "updatedat",
                ConditionOperator = SupportedOperator.HigherThan,
                Values = new List<string>
                {
                    "2023-04-13T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var query1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", createLessThan);
        var query2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", createMoreThan);
        var query3 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", updateLessThan);
        var query4 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", updateMoreThan);

        var expected1 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.CreatedAt < @UP_0_DateTime  ";
        var expected2 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.CreatedAt > @UP_0_DateTime  ";
        var expected3 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.UpdatedAt < @UP_0_DateTime  ";
        var expected4 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.UpdatedAt > @UP_0_DateTime  ";

        Assert.That(query1, Is.EqualTo(expected1));
        Assert.That(query2, Is.EqualTo(expected2));
        Assert.That(query3, Is.EqualTo(expected3));
        Assert.That(query4, Is.EqualTo(expected4));

        logTestResult(nameof(createLessThan), query1);
        logTestResult(nameof(createMoreThan), query2);
        logTestResult(nameof(updateLessThan), query3);
        logTestResult(nameof(updateMoreThan), query4);
    }


    [Test]
    public async Task ConversationStatus_Test()
    {
        var conversationStatusAnyOf = new List<Condition>
        {
            new Condition
            {
                FieldName = "ConversationStatus",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "open", "pending"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var conversationStatusEquals = new List<Condition>
        {
            new Condition
            {
                FieldName = "ConversationStatus",
                ConditionOperator = SupportedOperator.Equals,
                Values = new List<string>
                {
                    "closed"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var conversationStatusExcept = new List<Condition>
        {
            new Condition
            {
                FieldName = "ConversationStatus",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "open", "pending"
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var query1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", conversationStatusAnyOf);
        var query2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", conversationStatusEquals);
        var query3 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", conversationStatusExcept);

        var expected1 =
            "SELECT UP.* FROM UserProfiles UP JOIN Conversations UP_C_0 ON UP_C_0.UserProfileId = UP.Id AND UP_C_0.Status IN (@UP_ConversationStatus_0_0,@UP_ConversationStatus_0_1)  ";
        var expected2 =
            "SELECT UP.* FROM UserProfiles UP JOIN Conversations UP_C_0 ON UP_C_0.UserProfileId = UP.Id AND UP_C_0.Status IN (@UP_ConversationStatus_0_0)  ";
        var expected3 =
            "SELECT UP.* FROM UserProfiles UP JOIN Conversations UP_C_0 ON UP_C_0.UserProfileId = UP.Id AND UP_C_0.Status NOT IN (@UP_ConversationStatus_0_0,@UP_ConversationStatus_0_1)  ";

        Assert.That(query1, Is.EqualTo(expected1));
        Assert.That(query2, Is.EqualTo(expected2));
        Assert.That(query3, Is.EqualTo(expected3));

        logTestResult(nameof(conversationStatusAnyOf), query1);
        logTestResult(nameof(conversationStatusEquals), query2);
        logTestResult(nameof(conversationStatusExcept), query3);
    }


    [Test]
    public async Task ContactOwner_Test()
    {
        var contactOwnerAnyOf = new List<Condition>
        {
            new Condition
            {
                FieldName = "ContactOwner",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "03ae5415-61fe-45f7-98d2-39333389a25c", "049ec8f6-b9b9-4be6-bcdc-c60bd0c12750"
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var contactOwnerExcept = new List<Condition>
        {
            new Condition
            {
                FieldName = "ContactOwner",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "06ba3575-20cd-4a03-b33a-c165b37a6d13"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var contactOwnerKnown = new List<Condition>
        {
            new Condition
            {
                FieldName = "ContactOwner",
                ConditionOperator = SupportedOperator.IsNotNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var contactOwnerUnknown = new List<Condition>
        {
            new Condition
            {
                FieldName = "ContactOwner",
                ConditionOperator = SupportedOperator.IsNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var query1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", contactOwnerAnyOf);
        var query2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", contactOwnerExcept);
        var query3 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", contactOwnerKnown);
        var query4 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", contactOwnerUnknown);

        var expected1 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.ContactOwnerId IN (@UP_ConversationStatus_0_0,@UP_ConversationStatus_0_1)  ";
        var expected2 =
            "SELECT UP.* FROM UserProfiles UP  WHERE (UP.ContactOwnerId NOT IN (@UP_ConversationStatus_0_0)  OR UP.ContactOwnerId IS NULL )  ";
        var expected3 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.ContactOwnerId IS NOT NULL AND UP.ContactOwnerId != ''  ";
        var expected4 =
            "SELECT UP.* FROM UserProfiles UP  WHERE (UP.ContactOwnerId IS NULL OR UP.ContactOwnerId = '' COLLATE latin1_general_100_ci_as)  ";

        Assert.That(query1, Is.EqualTo(expected1));
        Assert.That(query2, Is.EqualTo(expected2));
        Assert.That(query3, Is.EqualTo(expected3));
        Assert.That(query4, Is.EqualTo(expected4));

        logTestResult(nameof(contactOwnerAnyOf), query1);
        logTestResult(nameof(contactOwnerExcept), query2);
        logTestResult(nameof(contactOwnerKnown), query3);
    }


    [Test]
    public async Task Collaborators_Test()
    {
        var collaboratorContainsAny = new List<Condition>
        {
            new Condition
            {
                FieldName = "Collaborators",
                ConditionOperator = SupportedOperator.ContainsAny,
                Values = new List<string>
                {
                    "a0316c5a-2f31-4f80-98b9-7ebc1a6cfcfa", "049ec8f6-b9b9-4be6-bcdc-c60bd0c12750"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var collaboratorContainsAll = new List<Condition>
        {
            new Condition
            {
                FieldName = "Collaborators",
                ConditionOperator = SupportedOperator.ContainsAll,
                Values = new List<string>
                {
                    "03ae5415-61fe-45f7-98d2-39333389a25c", "049ec8f6-b9b9-4be6-bcdc-c60bd0c12750"
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var query1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", collaboratorContainsAny);
        var query2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", collaboratorContainsAll);

        var expected1 =
            "SELECT UP.* FROM UserProfiles UP " +
            "JOIN Conversations UP_C_0 ON UP_C_0.UserProfileId = UP.Id AND " +
            "EXISTS( SELECT 1 FROM ConversationAdditionalAssignees as UP_CAA_0 " +
            "JOIN UserRoleStaffs UP_URS_0 ON UP_CAA_0.AssigneeId = UP_URS_0.Id " +
            "JOIN AspNetUsers UP_ANU_0 ON UP_URS_0.IdentityId = UP_ANU_0.Id " +
            "WHERE UP_C_0.Id = UP_CAA_0.ConversationId AND UP_CAA_0.CompanyId = @UP_CAA_CompanyId_0 AND UP_ANU_0.Id IN (@UP_Collaborators_0_0,@UP_Collaborators_0_1) )  ";

        var expected2 =
            "SELECT UP.* FROM UserProfiles UP " +
            "JOIN Conversations AS UP_C_0 ON UP_C_0.UserProfileId = UP.Id " +
            "AND EXISTS" +
            "( SELECT 1 " +
            "FROM ConversationAdditionalAssignees UP_CAA_0 " +
            "JOIN UserRoleStaffs UP_URS_0 ON UP_CAA_0.AssigneeId = UP_URS_0.Id " +
            "JOIN AspNetUsers UP_ANU_0 ON UP_URS_0.IdentityId = UP_ANU_0.Id WHERE UP_C_0.Id = UP_CAA_0.ConversationId AND UP_CAA_0.CompanyId = @UP_CAA_0_CompanyId AND UP_ANU_0.Id = @UP_Collaborators_0_0 ) " +
            "AND EXISTS" +
            "( SELECT 1 FROM ConversationAdditionalAssignees UP_CAA_1 JOIN UserRoleStaffs UP_URS_1 ON UP_CAA_1.AssigneeId = UP_URS_1.Id JOIN AspNetUsers UP_ANU_1 ON UP_URS_1.IdentityId = UP_ANU_1.Id " +
            "WHERE UP_C_0.Id = UP_CAA_1.ConversationId AND UP_CAA_1.CompanyId = @UP_CAA_1_CompanyId AND UP_ANU_1.Id = @UP_Collaborators_0_1 )  ";

        logTestResult(nameof(collaboratorContainsAny), query1);
        logTestResult(nameof(collaboratorContainsAll), query2);

        Assert.That(query1, Is.EqualTo(expected1));
        Assert.That(query2, Is.EqualTo(expected2));
    }


    [Test]
    public async Task PhoneNumber_Test()
    {
        var numberContains = new List<Condition>
        {
            new Condition
            {
                FieldName = "PhoneNumber",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "213"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var numberNotContains = new List<Condition>
        {
            new Condition
            {
                FieldName = "PhoneNumber",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "213"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var numberNotNull = new List<Condition>
        {
            new Condition
            {
                FieldName = "PhoneNumber",
                ConditionOperator = SupportedOperator.IsNotNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var numberIsNull = new List<Condition>
        {
            new Condition
            {
                FieldName = "PhoneNumber",
                ConditionOperator = SupportedOperator.IsNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var query1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", numberContains);
        var query2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", numberNotContains);
        var query3 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", numberNotNull);
        var query4 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", numberIsNull);

        var expected1 = "SELECT UP.* FROM UserProfiles UP  WHERE (UP.PhoneNumber LIKE @UP_phonenumber_0_0)  ";
        var expected2 = "SELECT UP.* FROM UserProfiles UP  WHERE (UP.PhoneNumber NOT LIKE @UP_phonenumber_0_0)  ";
        var expected3 =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.PhoneNumber != '' COLLATE latin1_general_100_ci_as  ";
        var expected4 =
            "SELECT UP.* FROM UserProfiles UP  WHERE (UP.PhoneNumber IS NULL OR UP.PhoneNumber = '' COLLATE latin1_general_100_ci_as)  ";

        Assert.AreEqual(expected1, query1);
        Assert.AreEqual(expected2, query2);
        Assert.AreEqual(expected3, query3);
        Assert.AreEqual(expected4, query4);

        logTestResult(nameof(numberContains), query1);
        logTestResult(nameof(numberNotContains), query2);
        logTestResult(nameof(numberNotNull), query3);
        logTestResult(nameof(numberIsNull), query4);
    }


    [Test]
    public async Task Email_Test()
    {
        var emailContains = new List<Condition>
        {
            new Condition
            {
                FieldName = "Email",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "shi"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var emailNotContains = new List<Condition>
        {
            new Condition
            {
                FieldName = "Email",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "shi"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var emailNotNull = new List<Condition>
        {
            new Condition
            {
                FieldName = "Email",
                ConditionOperator = SupportedOperator.IsNotNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var emailIsNull = new List<Condition>
        {
            new Condition
            {
                FieldName = "Email",
                ConditionOperator = SupportedOperator.IsNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var query1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", emailContains);
        var query2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", emailNotContains);
        var query3 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", emailNotNull);
        var query4 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", emailIsNull);


        var expected1 = "SELECT UP.* FROM UserProfiles UP  WHERE (UP.Email LIKE @UP_email_0_0)  ";
        var expected2 = "SELECT UP.* FROM UserProfiles UP  WHERE (UP.Email NOT LIKE @UP_email_0_0)  ";
        var expected3 = "SELECT UP.* FROM UserProfiles UP  WHERE UP.Email != '' COLLATE latin1_general_100_ci_as  ";
        var expected4 =
            "SELECT UP.* FROM UserProfiles UP  WHERE (UP.Email IS NULL OR UP.Email = '' COLLATE latin1_general_100_ci_as)  ";

        Assert.That(query1, Is.EqualTo(expected1));
        Assert.That(query2, Is.EqualTo(expected2));
        Assert.That(query3, Is.EqualTo(expected3));
        Assert.That(query4, Is.EqualTo(expected4));

        logTestResult(nameof(emailContains), query1);
        logTestResult(nameof(emailNotContains), query2);
        logTestResult(nameof(emailNotNull), query3);
        logTestResult(nameof(emailIsNull), query4);
    }


    [Test]
    public async Task Hashtag_Test()
    {
        var hashtagAny = new List<Condition>
        {
            new Condition
            {
                ContainHashTag = "hashtags",
                ConditionOperator = SupportedOperator.ContainsAny,
                Values = new List<string>
                {
                    "#testhashtaglabel", "      wj"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var hashtagAll = new List<Condition>
        {
            new Condition
            {
                ContainHashTag = "hashtags",
                ConditionOperator = SupportedOperator.ContainsAll,
                Values = new List<string>
                {
                    "$288 运动伸展", "son"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var query1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", hashtagAny);
        var query2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", hashtagAll);

        var expected1 = "SELECT UP.* FROM UserProfiles UP " +
                        "JOIN ( SELECT HT_C_0.UserProfileId " +
                        "FROM CompanyDefinedHashtags HT_CDH_0 JOIN ConversationHashtags HT_CH_0 ON HT_CH_0.HashtagId = HT_CDH_0.Id AND HT_CH_0.CompanyId = @UP_HT_CompanyId " +
                        "JOIN Conversations HT_C_0 ON HT_C_0.Id = HT_CH_0.ConversationId " +
                        "AND HT_C_0.CompanyId = @UP_HT_CompanyId WHERE HT_CDH_0.CompanyId = @UP_HT_CompanyId AND HT_CDH_0.Hashtag IN (@UP_HT_Hashtag_0_0,@UP_HT_Hashtag_0_1)  " +
                        "GROUP BY HT_C_0.UserProfileId ) SUB_HT_0 ON SUB_HT_0.UserProfileId = UP.Id  ";

        var expected2 = "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT HT_C_0.UserProfileId " +
                        "FROM CompanyDefinedHashtags HT_CDH_0 JOIN ConversationHashtags HT_CH_0 ON HT_CH_0.HashtagId = HT_CDH_0.Id AND HT_CH_0.CompanyId = @UP_HT_CompanyId " +
                        "JOIN Conversations HT_C_0 ON HT_C_0.Id = HT_CH_0.ConversationId " +
                        "AND HT_C_0.CompanyId = @UP_HT_CompanyId WHERE HT_CDH_0.CompanyId = @UP_HT_CompanyId AND HT_CDH_0.Hashtag IN (@UP_HT_Hashtag_0_0,@UP_HT_Hashtag_0_1)  " +
                        "GROUP BY HT_C_0.UserProfileId HAVING COUNT(HT_C_0.UserProfileId) >= 2 ) SUB_HT_0 ON SUB_HT_0.UserProfileId = UP.Id  ";

        logTestResult(nameof(hashtagAny), query1);
        logTestResult(nameof(hashtagAll), query2);

        Assert.That(query1, Is.EqualTo(expected1));
        Assert.That(query2, Is.EqualTo(expected2));
    }


    [Test]
    public async Task MultipleCondition_Test()
    {
        var mult1 = new List<Condition>
        {
            new Condition
            {
                FieldName = "firstname",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "phil"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "lastname",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "qi"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                ContainHashTag = "hashtags",
                ConditionOperator = SupportedOperator.ContainsAny,
                Values = new List<string>
                {
                    "son"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var mult2 = new List<Condition>
        {
            new Condition
            {
                FieldName = "lastname",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "qi"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "Email",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "email"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "ContactOwner",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "a0316c5a-2f31-4f80-98b9-7ebc1a6cfcfa"
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var mult3 = new List<Condition>
        {
            new Condition
            {
                FieldName = "lastname",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "qi"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "Email",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "email"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "Phil Number",
                ConditionOperator = SupportedOperator.Equal,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "Phil Number",
                ConditionOperator = SupportedOperator.IsNotNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.HigherThan,
                Values = new List<string>
                {
                    "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            },
        };


        var mult4 = new List<Condition>
        {
            new Condition
            {
                FieldName = "updatedat",
                ConditionOperator = SupportedOperator.HigherThan,
                Values = new List<string>
                {
                    "2023-04-13T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "createdat",
                ConditionOperator = SupportedOperator.LessThan,
                Values = new List<string>
                {
                    "2023-04-13T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.HigherThan,
                Values = new List<string>
                {
                    "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            },
        };


        var mult5 = new List<Condition>
        {
            new Condition
            {
                ContainHashTag = "hashtags",
                ConditionOperator = SupportedOperator.ContainsAny,
                Values = new List<string>
                {
                    "      wj"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "ConversationStatus",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "open", "pending"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "ConversationStatus",
                ConditionOperator = SupportedOperator.Equals,
                Values = new List<string>
                {
                    "oepn"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "Collaborators",
                ConditionOperator = SupportedOperator.ContainsAny,
                Values = new List<string>
                {
                    "a0316c5a-2f31-4f80-98b9-7ebc1a6cfcfa", "049ec8f6-b9b9-4be6-bcdc-c60bd0c12750"
                },
                NextOperator = SupportedNextOperator.And
            },
        };


        var mult6 = new List<Condition>
        {
            new Condition
            {
                CompanyMessageTemplateId = "9a4fbc5e-6b5b-4978-81b4-6a905b243b6c",
                BroadcastMessageStatus = BroadcastMessageStatus.Sent
            },
            new Condition
            {
                FieldName = "ImportFrom",
                ConditionOperator = SupportedOperator.IsNotContainsAll,
                Values = new List<string>
                {
                    "4548", "4550"
                },
                NextOperator = SupportedNextOperator.And
            },
            new Condition
            {
                FieldName = "ContactOwner",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "06ba3575-20cd-4a03-b33a-c165b37a6d13"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var query1 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", mult1);
        var query2 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", mult2);
        var query3 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", mult3);
        var query4 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", mult4);
        var query5 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", mult5);
        var query6 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", mult6);

        var expected1 =
            "SELECT UP.* FROM UserProfiles UP " +
            "JOIN ( SELECT HT_C_0.UserProfileId " +
            "FROM CompanyDefinedHashtags HT_CDH_0 JOIN ConversationHashtags HT_CH_0 ON HT_CH_0.HashtagId = HT_CDH_0.Id AND HT_CH_0.CompanyId = @UP_HT_CompanyId " +
            "JOIN Conversations HT_C_0 ON HT_C_0.Id = HT_CH_0.ConversationId " +
            "AND HT_C_0.CompanyId = @UP_HT_CompanyId WHERE HT_CDH_0.CompanyId = @UP_HT_CompanyId AND HT_CDH_0.Hashtag IN (@UP_HT_Hashtag_0_0)  GROUP BY HT_C_0.UserProfileId ) " +
            "SUB_HT_0 ON SUB_HT_0.UserProfileId = UP.Id  WHERE UP.FirstName IS NOT NULL  " +
            "AND (UP.FirstName LIKE @UP_First_Name_0_0) AND UP.LastName IS NOT NULL  AND (UP.LastName NOT LIKE @UP_Last_Name_1_0)  ";

        var expected2 =
            "SELECT UP.* FROM UserProfiles UP  " +
            "WHERE UP.LastName IS NOT NULL  AND (UP.LastName LIKE @UP_Last_Name_0_0) " +
            "AND (UP.Email LIKE @UP_email_1_0) AND UP.ContactOwnerId IN (@UP_ConversationStatus_2_0)  ";
        var expected3 =
            "SELECT UP.* FROM UserProfiles UP " +
            "JOIN ( SELECT UPCF_2.UserProfileId FROM UserProfileCustomFields UPCF_2 " +
            "WHERE UPCF_2.CompanyDefinedFieldId = @UP_CCUPF_2_Id AND UPCF_2.Value IS NOT NULL AND UPCF_2.Value != '' AND UPCF_2.CompanyId = @UP_UPCF_2_CompanyId " +
            "INTERSECT SELECT UPCF_3.UserProfileId FROM UserProfileCustomFields UPCF_3 " +
            "WHERE UPCF_3.CompanyDefinedFieldId = @UP_CCUPF_3_Id AND UPCF_3.Value IS NOT NULL AND UPCF_3.Value != '' AND UPCF_3.CompanyId = @UP_UPCF_3_CompanyId " +
            "AND UPCF_3.Value IS NOT NULL  INTERSECT SELECT UPCF_4.UserProfileId FROM UserProfileCustomFields UPCF_4 " +
            "WHERE UPCF_4.CompanyDefinedFieldId = @UP_CCUPF_4_Id AND UPCF_4.Value IS NOT NULL AND UPCF_4.Value != '' AND UPCF_4.CompanyId = @UP_UPCF_4_CompanyId " +
            "AND CONVERT(VARCHAR(19), UPCF_4.Value, 120) > @UP_UPCF_4   ) UPCF ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  " +
            "WHERE UP.LastName IS NOT NULL  AND (UP.LastName LIKE @UP_Last_Name_0_0) AND (UP.Email LIKE @UP_email_1_0)  ";
        var expected4 =
            "SELECT UP.* FROM UserProfiles UP " +
            "JOIN ( SELECT UPCF_2.UserProfileId FROM UserProfileCustomFields UPCF_2 " +
            "WHERE UPCF_2.CompanyDefinedFieldId = @UP_CCUPF_2_Id AND UPCF_2.Value IS NOT NULL " +
            "AND UPCF_2.Value != '' AND UPCF_2.CompanyId = @UP_UPCF_2_CompanyId AND CONVERT(VARCHAR(19), UPCF_2.Value, 120) > @UP_UPCF_2  GROUP BY UPCF_2.UserProfileId  ) UPCF " +
            "ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  WHERE UP.UpdatedAt > @UP_0_DateTime AND UP.CreatedAt < @UP_1_DateTime  ";

        var expected5 =
            "SELECT UP.* FROM UserProfiles UP " +
            "JOIN Conversations UP_C_1 ON UP_C_1.UserProfileId = UP.Id AND UP_C_1.Status IN (@UP_ConversationStatus_1_0,@UP_ConversationStatus_1_1)  " +
            "JOIN Conversations UP_C_2 ON UP_C_2.UserProfileId = UP.Id AND UP_C_2.Status IN (@UP_ConversationStatus_2_0)  " +
            "JOIN Conversations UP_C_3 ON UP_C_3.UserProfileId = UP.Id AND " +
            "EXISTS( SELECT 1 FROM ConversationAdditionalAssignees as UP_CAA_3 JOIN UserRoleStaffs UP_URS_3 ON UP_CAA_3.AssigneeId = UP_URS_3.Id " +
            "JOIN AspNetUsers UP_ANU_3 ON UP_URS_3.IdentityId = UP_ANU_3.Id WHERE UP_C_3.Id = UP_CAA_3.ConversationId AND UP_CAA_3.CompanyId = @UP_CAA_CompanyId_3 AND UP_ANU_3.Id IN (@UP_Collaborators_3_0,@UP_Collaborators_3_1) )  " +
            "JOIN ( SELECT HT_C_0.UserProfileId FROM CompanyDefinedHashtags HT_CDH_0 JOIN ConversationHashtags HT_CH_0 ON HT_CH_0.HashtagId = HT_CDH_0.Id AND HT_CH_0.CompanyId = @UP_HT_CompanyId JOIN " +
            "Conversations HT_C_0 ON HT_C_0.Id = HT_CH_0.ConversationId AND HT_C_0.CompanyId = @UP_HT_CompanyId WHERE HT_CDH_0.CompanyId = @UP_HT_CompanyId AND HT_CDH_0.Hashtag IN (@UP_HT_Hashtag_0_0)  " +
            "GROUP BY HT_C_0.UserProfileId ) SUB_HT_0 ON SUB_HT_0.UserProfileId = UP.Id  ";

        var expected6 =
            "SELECT UP.* FROM UserProfiles UP JOIN (SELECT UP_0.Id FROM BroadcastCompaignHistories BCH_0 LEFT JOIN ConversationMessages CM_0 ON CM_0.BroadcastHistoryId = BCH_0.Id JOIN Conversations C_0 ON BCH_0.ConversationId = C_0.Id AND C_0.CompanyId = @UP_Company_Id  JOIN UserProfiles UP_0 ON C_0.UserProfileId = UP_0.Id AND UP_0.CompanyId = @UP_Company_Id   WHERE BCH_0.BroadcastCampaignId = @UP_BCH_0_Broadcast_Campaign_Id AND C_0.CompanyId = @UP_Company_Id AND CM_0.Status IN (@UP_CM_0_Status_0,@UP_CM_0_Status_1,@UP_CM_0_Status_2,@UP_CM_0_Status_3,@UP_CM_0_Status_4,@UP_CM_0_Status_5)  ) SUB_0 ON SUB_0.Id = UP.Id  LEFT JOIN (SELECT UP_I_1.UserProfileId FROM CompanyImportedUserProfiles UP_I_1 WHERE UP_I_1.ImportContactHistoryId = @UP_ImportFrom_1_0 AND EXISTS (SELECT 1 FROM CompanyImportedUserProfiles UP_I_1_1 WHERE UP_I_1_1.UserProfileId = UP_I_1.UserProfileId AND UP_I_1_1.ImportContactHistoryId = @UP_ImportFrom_1_1) GROUP BY UP_I_1.UserProfileId) UP_SUB_1 ON UP_SUB_1.UserProfileId = UP.Id  WHERE UP_SUB_1.UserProfileId IS NULL AND (UP.ContactOwnerId NOT IN (@UP_ConversationStatus_2_0)  OR UP.ContactOwnerId IS NULL )  ";

        logTestResult(nameof(mult1), query1);
        logTestResult(nameof(mult2), query2);
        logTestResult(nameof(mult3), query3);
        logTestResult(nameof(mult4), query4);
        logTestResult(nameof(mult5), query5);
        logTestResult(nameof(mult6), query6);

        Assert.That(query1, Is.EqualTo(expected1));
        Assert.That(query2, Is.EqualTo(expected2));
        Assert.That(query3, Is.EqualTo(expected3));
        Assert.That(query4, Is.EqualTo(expected4));
        Assert.That(query5, Is.EqualTo(expected5));
        Assert.That(query6, Is.EqualTo(expected6));
    }


    [Test]
    public async Task BroadcastHistories_Test()
    {
        var broadcastSent = new List<Condition>
        {
            new Condition
            {
                CompanyMessageTemplateId = "9a4fbc5e-6b5b-4978-81b4-6a905b243b6c",
                BroadcastMessageStatus = BroadcastMessageStatus.Sent
            }
        };

        var broadcastDelivered = new List<Condition>
        {
            new Condition
            {
                CompanyMessageTemplateId = "9a4fbc5e-6b5b-4978-81b4-6a905b243b6c",
                BroadcastMessageStatus = BroadcastMessageStatus.Delivered
            }
        };

        var broadcastRead = new List<Condition>
        {
            new Condition
            {
                CompanyMessageTemplateId = "9a4fbc5e-6b5b-4978-81b4-6a905b243b6c",
                BroadcastMessageStatus = BroadcastMessageStatus.Read
            }
        };

        var broadcastReplied = new List<Condition>
        {
            new Condition
            {
                CompanyMessageTemplateId = "9a4fbc5e-6b5b-4978-81b4-6a905b243b6c",
                BroadcastMessageStatus = BroadcastMessageStatus.Replied
            }
        };

        var broadcastFailed = new List<Condition>
        {
            new Condition
            {
                CompanyMessageTemplateId = "9a4fbc5e-6b5b-4978-81b4-6a905b243b6c",
                BroadcastMessageStatus = BroadcastMessageStatus.Failed
            }
        };


        var query1 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", broadcastSent);
        var query2 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", broadcastDelivered);
        var query3 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", broadcastRead);
        var query4 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", broadcastReplied);
        var query5 = await ConstructSql("b6d7e442-38ae-4b9a-b100-2951729768bc", broadcastFailed);

        var expected1 =
            "SELECT UP.* FROM UserProfiles UP JOIN (SELECT UP_0.Id FROM BroadcastCompaignHistories BCH_0 LEFT JOIN ConversationMessages CM_0 ON CM_0.BroadcastHistoryId = BCH_0.Id JOIN Conversations C_0 ON BCH_0.ConversationId = C_0.Id AND C_0.CompanyId = @UP_Company_Id  JOIN UserProfiles UP_0 ON C_0.UserProfileId = UP_0.Id AND UP_0.CompanyId = @UP_Company_Id   WHERE BCH_0.BroadcastCampaignId = @UP_BCH_0_Broadcast_Campaign_Id AND C_0.CompanyId = @UP_Company_Id AND CM_0.Status IN (@UP_CM_0_Status_0,@UP_CM_0_Status_1,@UP_CM_0_Status_2,@UP_CM_0_Status_3,@UP_CM_0_Status_4,@UP_CM_0_Status_5)  ) SUB_0 ON SUB_0.Id = UP.Id  ";

        var expected2 =
            "SELECT UP.* FROM UserProfiles UP JOIN (SELECT UP_0.Id FROM BroadcastCompaignHistories BCH_0 LEFT JOIN ConversationMessages CM_0 ON CM_0.BroadcastHistoryId = BCH_0.Id JOIN Conversations C_0 ON BCH_0.ConversationId = C_0.Id AND C_0.CompanyId = @UP_Company_Id  JOIN UserProfiles UP_0 ON C_0.UserProfileId = UP_0.Id AND UP_0.CompanyId = @UP_Company_Id   WHERE BCH_0.BroadcastCampaignId = @UP_BCH_0_Broadcast_Campaign_Id AND C_0.CompanyId = @UP_Company_Id AND CM_0.Status IN (@UP_CM_0_Status_0,@UP_CM_0_Status_1)  ) SUB_0 ON SUB_0.Id = UP.Id  ";

        var expected3 =
            "SELECT UP.* FROM UserProfiles UP JOIN (SELECT UP_0.Id FROM BroadcastCompaignHistories BCH_0 LEFT JOIN ConversationMessages CM_0 ON CM_0.BroadcastHistoryId = BCH_0.Id JOIN Conversations C_0 ON BCH_0.ConversationId = C_0.Id AND C_0.CompanyId = @UP_Company_Id  JOIN UserProfiles UP_0 ON C_0.UserProfileId = UP_0.Id AND UP_0.CompanyId = @UP_Company_Id   WHERE BCH_0.BroadcastCampaignId = @UP_BCH_0_Broadcast_Campaign_Id AND C_0.CompanyId = @UP_Company_Id AND CM_0.Status = @UP_CM_0_Status  ) SUB_0 ON SUB_0.Id = UP.Id  ";

        var expected4 =
            "SELECT UP.* FROM UserProfiles UP JOIN (SELECT UP_0.Id FROM ConversationMessages CM_0 JOIN Conversations C_0 ON CM_0.ConversationId = C_0.Id AND C_0.CompanyId = @UP_Company_Id  JOIN UserProfiles UP_0 ON C_0.UserProfileId = UP_0.Id AND UP_0.CompanyId = @UP_Company_Id  JOIN BroadcastCompaignHistories BCH_0 ON C_0.Id = BCH_0.ConversationId AND BCH_0.BroadcastCampaignId = @UP_BCH_0_Broadcast_Campaign_Id  WHERE BCH_0.Status IN (@UP_BCH_0_Status_0,@UP_BCH_0_Status_1,@UP_BCH_0_Status_2,@UP_BCH_0_Status_3)  AND (CM_0.CreatedAt > BCH_0.CreatedAt AND CM_0.CreatedAt <= DATEADD(DAY, 3, BCH_0.CreatedAt)) AND CM_0.CompanyId = @UP_Company_Id  AND CM_0.IsSentFromSleekflow = @UP_CM_0_Is_Sent_From_Sleekflow  AND EXISTS(SELECT 1   FROM ConversationMessages UP_sub_CM_0_Status  WHERE UP_sub_CM_0_Status.BroadcastHistoryId = BCH_0.Id  AND UP_sub_CM_0_Status.Status IN (@UP_sub_CM_0_Status_0,@UP_sub_CM_0_Status_1,@UP_sub_CM_0_Status_2) ) GROUP BY UP_0.Id ) SUB_0 ON SUB_0.Id = UP.Id  ";

        var expected5 =
            "SELECT UP.* FROM UserProfiles UP JOIN (SELECT UP_0.Id FROM BroadcastCompaignHistories BCH_0 LEFT JOIN ConversationMessages CM_0 ON CM_0.BroadcastHistoryId = BCH_0.Id JOIN Conversations C_0 ON BCH_0.ConversationId = C_0.Id AND C_0.CompanyId = @UP_Company_Id  JOIN UserProfiles UP_0 ON C_0.UserProfileId = UP_0.Id AND UP_0.CompanyId = @UP_Company_Id   WHERE BCH_0.BroadcastCampaignId = @UP_BCH_0_Broadcast_Campaign_Id AND C_0.CompanyId = @UP_Company_Id AND ((SELECT COUNT(1) FROM ConversationMessages CM_0_0 WHERE BCH_0.Id = CM_0_0.BroadcastHistoryId AND CM_0_0.CompanyId = @UP_Company_Id  AND CM_0_0.BroadcastHistoryId IS NOT NULL  ) = 0OR EXISTS(SELECT 1 FROM ConversationMessages CM_0_1 WHERE BCH_0.Id = CM_0_1.BroadcastHistoryId AND CM_0_1.CompanyId = @UP_Company_Id  AND CM_0_1.BroadcastHistoryId IS NOT NULL  AND CM_0_1.Status IN (@UP_CM_0_Status_0,@UP_CM_0_Status_1,@UP_CM_0_Status_2) ))  ) SUB_0 ON SUB_0.Id = UP.Id  ";


        Assert.That(query1, Is.EqualTo(expected1));
        Assert.That(query2, Is.EqualTo(expected2));
        Assert.That(query3, Is.EqualTo(expected3));
        Assert.That(query4, Is.EqualTo(expected4));
        Assert.That(query5, Is.EqualTo(expected5));

        logTestResult(nameof(broadcastSent), query1);
        logTestResult(nameof(broadcastDelivered), query2);
        logTestResult(nameof(broadcastRead), query3);
        logTestResult(nameof(broadcastReplied), query4);
        logTestResult(nameof(broadcastFailed), query5);
    }


    [Test]
    public async Task CustomFields_Test()
    {
        var customNumberEqual = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Number",
                ConditionOperator = SupportedOperator.Equal,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customDateBefore = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.LessThan,
                Values = new List<string>
                {
                    "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customDateAfter = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.HigherThan,
                Values = new List<string>
                {
                    "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customNumberContains = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Number",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "852"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customDropdownAny = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Dropdown",
                ConditionOperator = SupportedOperator.ContainsExactly,
                Values = new List<string>
                {
                    "A"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customNumberKnown = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Number",
                ConditionOperator = SupportedOperator.IsNotNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customNumberNotContains = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Number",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "852"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customDropdownExcept = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Dropdown",
                ConditionOperator = SupportedOperator.IsNotContainsExactly,
                Values = new List<string>
                {
                    "A"
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var customNumberUnknown = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Number",
                ConditionOperator = SupportedOperator.IsNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };


        var customDateBeforeDayAgo = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.DateBeforeDayAgo,
                Values = new List<string>
                {
                    "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customDateAfterDayAgo = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.DateAfterDayAgo,
                Values = new List<string>
                {
                    "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customDateExactlyDaysBefore = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.IsExactlyDaysBefore,
                Values = new List<string>
                {
                    "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customDateExactlyDaysAfter = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.IsExactlyDaysAfter,
                Values = new List<string>
                {
                    "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customDateIsToday = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.IsToday,
                Values = new List<string>
                {
                    "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customTimeBefore = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.TimeBefore,
                Values = new List<string>
                {
                    "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customTimeAfter = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.TimeAfter,
                Values = new List<string>
                {
                    "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customTimeIsBetween = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.IsBetween,
                Values = new List<string>
                {
                    "2023-03-01T16:00:00.000Z", "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var customTimeIsNotBetween = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Date",
                ConditionOperator = SupportedOperator.IsNotBetween,
                Values = new List<string>
                {
                    "2023-03-01T16:00:00.000Z", "2023-04-24T16:00:00.000Z"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var query1 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customNumberEqual);
        var query2 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customNumberKnown);
        var query3 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customNumberUnknown);
        var query4 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customDateBefore);
        var query5 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customDateAfter);
        var query6 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customDropdownAny);
        var query7 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customNumberContains);
        var query8 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customNumberNotContains);
        var query9 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customDateIsToday);
        var query10 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customTimeBefore);
        var query11 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customTimeAfter);
        var query12 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customTimeIsBetween);
        var query13 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customTimeIsNotBetween);
        var query14 = await ConstructSql("3e4556b4-35d8-4b9b-a60f-583f056521ff", customDropdownExcept);

        var expected1 = "SELECT UP.* FROM UserProfiles UP " +
                        "JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 " +
                        "WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId GROUP BY UPCF_0.UserProfileId  ) UPCF " +
                        "ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected2 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 " +
            "WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId AND UPCF_0.Value IS NOT NULL  GROUP BY UPCF_0.UserProfileId  ) UPCF " +
            "ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected3 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UP_0.Id AS UserProfileId FROM UserProfiles UP_0 " +
            "LEFT JOIN (SELECT UPCF_0.UserProfileId from UserProfileCustomFields UPCF_0 WHERE UPCF_0.CompanyDefinedFieldId = @UP_UPCF_0_Company_Defined_Field_Prefix  " +
            "AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != ''  AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId GROUP BY UPCF_0.UserProfileId ) SUB_0 ON UP_0.Id = SUB_0.UserProfileId " +
            "WHERE UP_0.CompanyId = @UP_UPCF_0_CompanyId AND SUB_0.UserProfileId IS NULL  ) UPCF ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected4 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 " +
            "WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId AND CONVERT(VARCHAR(19), UPCF_0.Value, 120) < @UP_UPCF_0  " +
            "GROUP BY UPCF_0.UserProfileId  ) UPCF ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected5 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 " +
            "WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId AND CONVERT(VARCHAR(19), UPCF_0.Value, 120) > @UP_UPCF_0  " +
            "GROUP BY UPCF_0.UserProfileId  ) UPCF ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected6 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id " +
            "AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId AND UPCF_0.Value = @UP_UPCF_0  GROUP BY UPCF_0.UserProfileId  ) UPCF " +
            "ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected7 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 " +
            "WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId AND (UPCF_0.Value LIKE @UP_UPCF_0_0)  " +
            "GROUP BY UPCF_0.UserProfileId  ) UPCF ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected8 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id " +
            "AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId AND (UPCF_0.Value NOT LIKE @UP_UPCF_0_0)  GROUP BY UPCF_0.UserProfileId  ) UPCF " +
            "ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected9 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 " +
            "WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId AND CONVERT(VARCHAR(19), UPCF_0.Value, 120) >= @UP_UPCF_0_Today_Start " +
            "AND CONVERT(VARCHAR(19), UPCF_0.Value, 120) < @UP_UPCF_0_Today_End  GROUP BY UPCF_0.UserProfileId  ) UPCF " +
            "ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected10 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id " +
            "AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId GROUP BY UPCF_0.UserProfileId  ) UPCF ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected11 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 " +
            "WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId GROUP BY UPCF_0.UserProfileId  ) UPCF " +
            "ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected12 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId AND UPCF_0.Value > @UP_UPCF_0_From_Date AND UPCF_0.Value < @UP_UPCF_0_To_Date  GROUP BY UPCF_0.UserProfileId  ) UPCF ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected13 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId AND (UPCF_0.Value < @UP_UPCF_0_From_Date OR UPCF_0.Value > @UP_UPCF_0_To_Date)  GROUP BY UPCF_0.UserProfileId  ) UPCF ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        var expected14 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 " +
            "WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId " +
            "AND UPCF_0.Value != @UP_UPCF_0  GROUP BY UPCF_0.UserProfileId  ) UPCF ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ";

        logTestResult(nameof(customNumberEqual), query1);
        logTestResult(nameof(customNumberKnown), query2);
        logTestResult(nameof(customNumberUnknown), query3);
        logTestResult(nameof(customDateBefore), query4);
        logTestResult(nameof(customDateAfter), query5);
        logTestResult(nameof(customDropdownAny), query6);
        logTestResult(nameof(customNumberContains), query7);
        logTestResult(nameof(customNumberNotContains), query8);
        logTestResult(nameof(customNumberNotContains), query9);
        logTestResult(nameof(customNumberNotContains), query10);
        logTestResult(nameof(customNumberNotContains), query11);
        logTestResult(nameof(customNumberNotContains), query12);
        logTestResult(nameof(customDateIsToday), query13);
        logTestResult(nameof(customTimeBefore), query14);

        Assert.That(query1, Is.EqualTo(expected1));
        Assert.That(query2, Is.EqualTo(expected2));
        Assert.That(query3, Is.EqualTo(expected3));
        Assert.That(query4, Is.EqualTo(expected4));
        Assert.That(query5, Is.EqualTo(expected5));
        Assert.That(query6, Is.EqualTo(expected6));
        Assert.That(query7, Is.EqualTo(expected7));
        Assert.That(query8, Is.EqualTo(expected8));
        Assert.That(query9, Is.EqualTo(expected9));
        Assert.That(query10, Is.EqualTo(expected10));
        Assert.That(query11, Is.EqualTo(expected11));
        Assert.That(query12, Is.EqualTo(expected12));
        Assert.That(query13, Is.EqualTo(expected13));
        Assert.That(query14, Is.EqualTo(expected14));
    }


    [Test]
    public async Task Pagination_Test()
    {
        var emptyCondition = new List<Condition>
        {
            new Condition()
        };

        // OrderBy Test
        var queryOrderByUpdatedAt = await ConstructSqlWithPagination(
            "3e4556b4-35d8-4b9b-a60f-583f056521ff",
            emptyCondition,
            null,
            null,
            "updatedat",
            "desc");
        var queryOrderByFirstName = await ConstructSqlWithPagination(
            "3e4556b4-35d8-4b9b-a60f-583f056521ff",
            emptyCondition,
            0,
            100,
            "firstname",
            "desc");
        var queryOrderByLastName = await ConstructSqlWithPagination(
            "3e4556b4-35d8-4b9b-a60f-583f056521ff",
            emptyCondition,
            null,
            null,
            "lastname",
            "desc");
        var queryOrderByDisplayName = await ConstructSqlWithPagination(
            "3e4556b4-35d8-4b9b-a60f-583f056521ff",
            emptyCondition,
            null,
            null,
            "displayname",
            "desc");
        var queryOrderByCreatedAt = await ConstructSqlWithPagination(
            "3e4556b4-35d8-4b9b-a60f-583f056521ff",
            emptyCondition,
            null,
            null,
            "createdat",
            "desc");
        var queryOrderByLastContactFromCustomers = await ConstructSqlWithPagination(
            "3e4556b4-35d8-4b9b-a60f-583f056521ff",
            emptyCondition,
            null,
            null,
            "lastcontactfromcustomers",
            "desc");
        var queryOrderByLastContact = await ConstructSqlWithPagination(
            "3e4556b4-35d8-4b9b-a60f-583f056521ff",
            emptyCondition,
            null,
            null,
            "lastcontact",
            "desc");
        var queryOrderByAscending = await ConstructSqlWithPagination(
            "3e4556b4-35d8-4b9b-a60f-583f056521ff",
            emptyCondition,
            null,
            null,
            "updatedat",
            "asc");

        var expectedUpdateAt =
            "SELECT UP.* FROM UserProfiles UP  ORDER BY UP.UpdatedAt DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expectedFirstName =
            "SELECT UP.* FROM UserProfiles UP  ORDER BY UP.FirstName DESC OFFSET 0 ROWS FETCH NEXT 100 ROWS ONLY ";
        var expectedLastName =
            "SELECT UP.* FROM UserProfiles UP  ORDER BY UP.LastName DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expectedDisplayName =
            "SELECT UP.* FROM UserProfiles UP  ORDER BY UP.FirstName DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expectedCreatedAt =
            "SELECT UP.* FROM UserProfiles UP  ORDER BY UP.CreatedAt DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expectedLastContactFromCustomers =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.LastContactFromCustomers IS NOT NULL ORDER BY UP.LastContactFromCustomers DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expectedLastContact =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.LastContact IS NOT NULL ORDER BY UP.LastContact DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expectedAscending =
            "SELECT UP.* FROM UserProfiles UP  ORDER BY UP.UpdatedAt ASC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";

        Assert.AreEqual(expectedUpdateAt, queryOrderByUpdatedAt);
        Assert.AreEqual(expectedFirstName, queryOrderByFirstName);
        Assert.AreEqual(expectedLastName, queryOrderByLastName);
        Assert.AreEqual(expectedDisplayName, queryOrderByDisplayName);
        Assert.AreEqual(expectedCreatedAt, queryOrderByCreatedAt);
        Assert.AreEqual(expectedLastContactFromCustomers, queryOrderByLastContactFromCustomers);
        Assert.AreEqual(expectedLastContact, queryOrderByLastContact);
        Assert.AreEqual(expectedAscending, queryOrderByAscending);

        logTestResult(nameof(queryOrderByUpdatedAt), queryOrderByUpdatedAt);
        logTestResult(nameof(queryOrderByFirstName), queryOrderByFirstName);
        logTestResult(nameof(queryOrderByLastName), queryOrderByLastName);
        logTestResult(nameof(queryOrderByDisplayName), queryOrderByDisplayName);
        logTestResult(nameof(queryOrderByCreatedAt), queryOrderByCreatedAt);
        logTestResult(nameof(queryOrderByLastContactFromCustomers), queryOrderByLastContactFromCustomers);
        logTestResult(nameof(queryOrderByLastContact), queryOrderByLastContact);
        logTestResult(nameof(queryOrderByAscending), queryOrderByAscending);
    }

    [Test]
    public async Task Channel_Test()
    {
        var channelAny = new List<Condition>
        {
            new Condition
            {
                FieldName = "LastChannel",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "whatsappcloudapi:16005556746", "whatsappcloudapi:14056213733"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var channelKnown = new List<Condition>
        {
            new Condition
            {
                FieldName = "LastChannel",
                ConditionOperator = SupportedOperator.IsNotNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var channelUnknown = new List<Condition>
        {
            new Condition
            {
                FieldName = "LastChannel",
                ConditionOperator = SupportedOperator.IsNull,
                Values = new List<string>
                {
                    ""
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var channelAnyInstagram = new List<Condition>
        {
            new Condition
            {
                FieldName = "LastChannel",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "instagram:17841448325029731", "instagram:17841448950110035"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var channelAnyMultiChannels = new List<Condition>
        {
            new Condition
            {
                FieldName = "LastChannel",
                ConditionOperator = SupportedOperator.Contains,
                Values = new List<string>
                {
                    "instagram:17841448325029731",
                    "instagram:17841448950110035",
                    "whatsappcloudapi:15734946372",
                    "whatsappcloudapi:601125868915",
                    ChannelTypes.Sms,
                    ChannelTypes.Viber,
                    ChannelTypes.Telegram,
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        var query1 = await ConstructSqlWithPagination("3e4556b4-35d8-4b9b-a60f-583f056521ff", channelAny);
        var query2 = await ConstructSqlWithPagination("3e4556b4-35d8-4b9b-a60f-583f056521ff", channelKnown);
        var query3 = await ConstructSqlWithPagination("3e4556b4-35d8-4b9b-a60f-583f056521ff", channelUnknown);
        var query4 = await ConstructSqlWithPagination("b6d7e442-38ae-4b9a-b100-2951729768bc", channelAnyInstagram);
        var query5 = await ConstructSqlWithPagination("b6d7e442-38ae-4b9a-b100-2951729768bc", channelAnyMultiChannels);


        var expected1 =
            "SELECT UP.* FROM UserProfiles UP  WHERE (EXISTS (SELECT 1 FROM WhatsappCloudApiSenders UP_CH_WACAS, UserProfileCustomFields UP_CH_UPCF WHERE UP.Id = UP_CH_WACAS.UserProfileId AND UP_CH_WACAS.CompanyId = @UP_CH_CompanyId AND UP_CH_WACAS.WhatsappChannelPhoneNumber IN (@UP_CH_WACAS_Whatsapp_Channel_Phone_Number_0,@UP_CH_WACAS_Whatsapp_Channel_Phone_Number_1)  AND UP_CH_UPCF.UserProfileId = UP.Id AND UP_CH_UPCF.CompanyId = @UP_CH_CompanyId AND UP_CH_UPCF.CompanyDefinedFieldId = @UP_FieldId AND UP_CH_UPCF.Value = 'whatsappcloudapi' ) ) ORDER BY UP.CreatedAt DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expected2 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UPCF_0.UserProfileId FROM UserProfileCustomFields UPCF_0 WHERE UPCF_0.CompanyDefinedFieldId = @UP_CCUPF_0_Id AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != '' AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId AND UPCF_0.Value IS NOT NULL  GROUP BY UPCF_0.UserProfileId  ) UPCF ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ORDER BY UP.CreatedAt DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expected3 =
            "SELECT UP.* FROM UserProfiles UP JOIN ( SELECT UP_0.Id AS UserProfileId FROM UserProfiles UP_0 LEFT JOIN (SELECT UPCF_0.UserProfileId from UserProfileCustomFields UPCF_0 WHERE UPCF_0.CompanyDefinedFieldId = @UP_UPCF_0_Company_Defined_Field_Prefix  AND UPCF_0.Value IS NOT NULL AND UPCF_0.Value != ''  AND UPCF_0.CompanyId = @UP_UPCF_0_CompanyId GROUP BY UPCF_0.UserProfileId ) SUB_0 ON UP_0.Id = SUB_0.UserProfileId WHERE UP_0.CompanyId = @UP_UPCF_0_CompanyId AND SUB_0.UserProfileId IS NULL  ) UPCF ON UP.Id = UPCF.UserProfileId AND UP.CompanyId = @UP_Custom_Field_Company_Id  ORDER BY UP.CreatedAt DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expected4 =
            "SELECT UP.* FROM UserProfiles UP  WHERE (EXISTS (SELECT 1 FROM SenderInstagramSenders UP_CH_SIS, UserProfileCustomFields UP_CH_UPCF WHERE UP.InstagramUserId = UP_CH_SIS.Id AND UP_CH_SIS.CompanyId = @UP_CH_CompanyId AND UP_CH_SIS.InstagramPageId IN (@UP_CH_SIS_Instagram_Page_Id_0,@UP_CH_SIS_Instagram_Page_Id_1)  AND UP_CH_UPCF.UserProfileId = UP.Id AND UP_CH_UPCF.CompanyId = @UP_CH_CompanyId AND UP_CH_UPCF.CompanyDefinedFieldId = @UP_FieldId AND UP_CH_UPCF.Value = 'instagram' ) ) ORDER BY UP.CreatedAt DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expected5 =
            "SELECT UP.* FROM UserProfiles UP  WHERE (EXISTS (SELECT 1 FROM SenderInstagramSenders UP_CH_SIS, UserProfileCustomFields UP_CH_UPCF WHERE UP.InstagramUserId = UP_CH_SIS.Id AND UP_CH_SIS.CompanyId = @UP_CH_CompanyId AND UP_CH_SIS.InstagramPageId IN (@UP_CH_SIS_Instagram_Page_Id_0,@UP_CH_SIS_Instagram_Page_Id_1)  AND UP_CH_UPCF.UserProfileId = UP.Id AND UP_CH_UPCF.CompanyId = @UP_CH_CompanyId AND UP_CH_UPCF.CompanyDefinedFieldId = @UP_FieldId AND UP_CH_UPCF.Value = 'instagram' ) OR EXISTS (SELECT 1 FROM WhatsappCloudApiSenders UP_CH_WACAS, UserProfileCustomFields UP_CH_UPCF WHERE UP.Id = UP_CH_WACAS.UserProfileId AND UP_CH_WACAS.CompanyId = @UP_CH_CompanyId AND UP_CH_WACAS.WhatsappChannelPhoneNumber IN (@UP_CH_WACAS_Whatsapp_Channel_Phone_Number_0,@UP_CH_WACAS_Whatsapp_Channel_Phone_Number_1)  AND UP_CH_UPCF.UserProfileId = UP.Id AND UP_CH_UPCF.CompanyId = @UP_CH_CompanyId AND UP_CH_UPCF.CompanyDefinedFieldId = @UP_FieldId AND UP_CH_UPCF.Value = 'whatsappcloudapi' ) OR EXISTS (SELECT 1 FROM UserProfileCustomFields UP_CH_Single_Integrated_Channel WHERE UP.Id = UP_CH_Single_Integrated_Channel.UserProfileId AND UP_CH_Single_Integrated_Channel.CompanyId = @UP_CH_CompanyId AND UP_CH_Single_Integrated_Channel.CompanyDefinedFieldId = @UP_FieldId AND UP_CH_Single_Integrated_Channel.Value IN (@UP_CH_Single_Integrated_Channel_Name_0,@UP_CH_Single_Integrated_Channel_Name_1,@UP_CH_Single_Integrated_Channel_Name_2)  ) ) ORDER BY UP.CreatedAt DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";

        logTestResult(nameof(channelAny), query1);
        logTestResult(nameof(channelKnown), query2);
        logTestResult(nameof(channelUnknown), query3);
        logTestResult(nameof(channelAnyInstagram), query4);
        logTestResult(nameof(channelAnyMultiChannels), query5);

        Assert.That(query1, Is.EqualTo(expected1));
        Assert.That(query2, Is.EqualTo(expected2));
        Assert.That(query3, Is.EqualTo(expected3));
        Assert.That(query4, Is.EqualTo(expected4));
        Assert.That(query5, Is.EqualTo(expected5));
    }


    [Test]
    public async Task WholeQuery_Test()
    {
        var companyId = "3e4556b4-35d8-4b9b-a60f-583f056521ff";
        var emptyCondition = new List<Condition>
        {
            new Condition()
        };
        var sampleCondition = new List<Condition>
        {
            new Condition
            {
                FieldName = "Phil Number",
                ConditionOperator = SupportedOperator.IsNotContains,
                Values = new List<string>
                {
                    "852"
                },
                NextOperator = SupportedNextOperator.And
            }
        };

        // Admin
        var queryAdmin = await ConstructSqlWithWholeQuery(
            companyId,
            emptyCondition,
            offset: 0,
            limit: 30,
            sortBy: "updatedat",
            order: "desc",
            assigneeId: null,
            channels: null,
            channelIds: null,
            StaffUserRole.Admin);

        // Staff
        var queryStaff = await ConstructSqlWithWholeQuery(
            companyId,
            emptyCondition,
            offset: 0,
            limit: 30,
            sortBy: "updatedat",
            order: "desc",
            assigneeId: null,
            channels: null,
            channelIds: null,
            StaffUserRole.Staff);

        // Team Admin
        var queryTeamAdmin = await ConstructSqlWithWholeQuery(
            companyId,
            emptyCondition,
            offset: 0,
            limit: 30,
            sortBy: "updatedat",
            order: "desc",
            assigneeId: null,
            channels: null,
            channelIds: null,
            StaffUserRole.TeamAdmin);

        var expectedAdmin =
            "SELECT UP.* FROM UserProfiles UP  WHERE UP.CompanyId = @UP_ROLE_Company_Id AND UP.ActiveStatus = @UP_ROLE_Active_Status  ORDER BY UP.UpdatedAt DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expectedStaff =
            "SELECT UP.* FROM UserProfiles UP JOIN Conversations UP_ROLE_C ON UP.Id = UP_ROLE_C.UserProfileId AND UP_ROLE_C.CompanyId = @UP_ROLE_Company_Id AND (UP_ROLE_C.AssigneeId = @UP_ROLE_Company_Staff_Id OR (UP_ROLE_C.AssignedTeamId IS NULL  AND UP_ROLE_C.AssigneeId IS NULL ) OR EXISTS( SELECT 1 FROM ConversationAdditionalAssignees UP_ROLE_CAA WHERE UP_ROLE_C.Id = UP_ROLE_CAA.ConversationId AND UP_ROLE_CAA.CompanyId = @UP_ROLE_Company_Id AND UP_ROLE_CAA.AssigneeId = @UP_ROLE_Company_Staff_Id ))  WHERE UP.CompanyId = @UP_ROLE_Company_Id AND UP.ActiveStatus = @UP_ROLE_Active_Status  ORDER BY UP.UpdatedAt DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";
        var expectedTeamAdmin =
            "SELECT UP.* FROM UserProfiles UP JOIN Conversations UP_ROLE_C ON UP.Id = UP_ROLE_C.UserProfileId AND UP_ROLE_C.CompanyId = @UP_ROLE_Company_Id AND (UP_ROLE_C.AssigneeId = @UP_ROLE_Company_Staff_Id OR (UP_ROLE_C.AssignedTeamId IS NULL  AND UP_ROLE_C.AssigneeId IS NULL ) OR UP_ROLE_C.AssignedTeamId IN (@UP_ROLE_UP_TeamId_0)  OR EXISTS( SELECT 1 FROM ConversationAdditionalAssignees UP_ROLE_CAA WHERE UP_ROLE_C.Id = UP_ROLE_CAA.ConversationId AND UP_ROLE_CAA.CompanyId = @UP_ROLE_Company_Id AND UP_ROLE_CAA.AssigneeId = @UP_ROLE_Company_Staff_Id ))  WHERE UP.CompanyId = @UP_ROLE_Company_Id AND UP.ActiveStatus = @UP_ROLE_Active_Status  ORDER BY UP.UpdatedAt DESC OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY ";

        logTestResult(nameof(queryAdmin), queryAdmin);
        logTestResult(nameof(queryStaff), queryStaff);
        logTestResult(nameof(queryTeamAdmin), queryTeamAdmin);

        Assert.That(queryAdmin, Is.EqualTo(expectedAdmin));
        Assert.That(queryStaff, Is.EqualTo(expectedStaff));
        Assert.That(queryTeamAdmin, Is.EqualTo(expectedTeamAdmin));
    }


    private async Task<string> ConstructSql(string companyId, List<Condition> conditions)
    {
        var (whereClauses, joinClauses, _) =
            await _userProfileSqlService.ConstructorQueryFromCondition(
                UserProfile,
                companyId,
                conditions);
        var query =
            $"SELECT {UserProfile}.* "
            + $"FROM UserProfiles {UserProfile} "
            + $"{string.Join(" ", joinClauses)} ";
        if (whereClauses?.Count > 0)
        {
            query += $"WHERE {string.Join("AND ", whereClauses)} ";
        }

        return query;
    }

    private async Task<string> ConstructSqlWithPagination(
        string companyId,
        List<Condition> conditions,
        int? offset = null,
        int? limit = null,
        string sortBy = "createdat",
        string order = "desc")
    {
        var (whereClauses, joinClauses, _) =
            await _userProfileSqlService.ConstructorQueryFromCondition(
                UserProfile,
                companyId,
                conditions);
        var query =
            $"SELECT {UserProfile}.* "
            + $"FROM UserProfiles {UserProfile} "
            + $"{string.Join(" ", joinClauses)} ";

        var sorting = sortBy.ToLower();
        switch (sorting)
        {
            case LastContactFromCustomers:
                whereClauses.Add($"{UserProfile}.LastContactFromCustomers IS NOT NULL");
                break;
            case LastContact:
                whereClauses.Add($"{UserProfile}.LastContact IS NOT NULL");
                break;
        }

        if (whereClauses?.Count > 0)
        {
            query += $"WHERE {string.Join("AND ", whereClauses)} ";
        }

        var by = order.ToUpper() is DESC ? "DESC" : "ASC";
        var orderClause = sortBy.ToLower() switch
        {
            UpdatedAt => $"{UserProfile}.UpdatedAt",
            FirstName => $"{UserProfile}.FirstName",
            LastName => $"{UserProfile}.LastName",
            DisplayName => $"{UserProfile}.FirstName",
            CreatedAt => $"{UserProfile}.CreatedAt",
            LastContactFromCustomers => $"{UserProfile}.LastContactFromCustomers",
            LastContact => $"{UserProfile}.LastContact"
        };
        // Take 30 rows if limit is not specified
        query += $"ORDER BY {orderClause} {by} " +
                 $"OFFSET {offset ?? 0} ROWS FETCH NEXT {limit ?? 30} ROWS ONLY ";

        return query;
    }


    private async Task<string> ConstructSqlWithWholeQuery(
        string companyId,
        List<Condition> conditions,
        int? offset = null,
        int? limit = null,
        string sortBy = CreatedAt,
        string order = DESC,
        long? assigneeId = null,
        List<string> channels = null,
        List<string> channelIds = null,
        StaffUserRole staffUserRole = StaffUserRole.Admin)
    {
        var joinClauses = new List<string>();
        var whereClauses = new List<string>();

        var companyStaff = await _dbContext.UserRoleStaffs
            .Where(x => x.CompanyId == companyId && x.RoleType == staffUserRole)
            .WhereIf(assigneeId.HasValue, x => x.Id == assigneeId)
            .FirstOrDefaultAsync();

        if (companyStaff is null)
        {
            throw new Exception(
                $"Unable to locate any company staff information. CompanyId: {companyId}, AssigneeId: {assigneeId ?? -1}, RoleType: {staffUserRole}");
        }

        // apply RoleStuff filter
        var (manageableWhereClauses,
                manageablePaJoinClauses,
                manageableParameters) =
            await _manageableOperation.ConditionClause(UserProfile, companyId, companyStaff, assigneeId, staffUserRole);

        joinClauses = joinClauses.Concat(manageablePaJoinClauses).ToList();
        whereClauses = whereClauses.Concat(manageableWhereClauses).ToList();

        // apply conditions
        var (conditionWhereClauses, conditionJoinClauses, _) =
            await _userProfileSqlService.ConstructorQueryFromCondition(
                UserProfile,
                companyId,
                conditions);

        var query =
            $"SELECT {UserProfile}.* "
            + $"FROM UserProfiles {UserProfile} "
            + $"{string.Join(" ", joinClauses)} ";

        joinClauses = joinClauses.Concat(conditionJoinClauses).ToList();
        whereClauses = whereClauses.Concat(conditionWhereClauses).ToList();

        // apply sorting
        var sorting = sortBy.ToLower();
        switch (sorting)
        {
            case LastContactFromCustomers:
                whereClauses.Add($"{UserProfile}.LastContactFromCustomers IS NOT NULL");
                break;
            case LastContact:
                whereClauses.Add($"{UserProfile}.LastContact IS NOT NULL");
                break;
        }

        if (whereClauses?.Count > 0)
        {
            query += $"WHERE {string.Join("AND ", whereClauses)} ";
        }

        var by = order.ToUpper() is DESC ? "DESC" : "ASC";
        var orderClause = sortBy.ToLower() switch
        {
            UpdatedAt => $"{UserProfile}.UpdatedAt",
            FirstName => $"{UserProfile}.FirstName",
            LastName => $"{UserProfile}.LastName",
            DisplayName => $"{UserProfile}.FirstName",
            CreatedAt => $"{UserProfile}.CreatedAt",
            LastContactFromCustomers => $"{UserProfile}.LastContactFromCustomers",
            LastContact => $"{UserProfile}.LastContact"
        };
        // Take 30 rows if limit is not specified
        query += $"ORDER BY {orderClause} {by} " +
                 $"OFFSET {offset ?? 0} ROWS FETCH NEXT {limit ?? 30} ROWS ONLY ";

        return query;
    }


    private void logTestResult(string testCase, string generatedSql)
    {
        // MemberExpression memberExpr = (MemberExpression)testCase.Body;
        Console.WriteLine($"\n\n-------------------- {testCase} --------------------\n");
        Console.WriteLine(generatedSql);
    }
}