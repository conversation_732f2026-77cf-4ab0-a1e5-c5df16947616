namespace Sleekflow.Core.DataMigrator.Constants;

public static class MigrationOptions
{
    public const string AspNetUsers = "AspNetUsers";
    public const string AspNetRoles = "AspNetRoles";
    public const string AspNetUserRoles = "AspNetUserRoles";
    public const string UserRoleGuests = "UserRoleGuests";
    public const string UserRoleAdmins = "UserRoleAdmins";
    public const string UserRoleStaffs = "UserRoleStaffs";
    public const string UserRegisteredSessions = "UserRegisteredSessions";
    public const string UserRoleOperators = "UserRoleOperators";
    public const string UserStaffNotificationSettings = "UserStaffNotificationSettings";
    public const string UserStaffProfilePictures = "UserStaffProfilePictures";
    public const string CompanyCompanies = "CompanyCompanies";
    public const string CompanyCompanyCustomFields = "CompanyCompanyCustomFields";
    public const string CompanyDefinedHashtags = "CompanyDefinedHashtags";
    public const string CompanyMessageTemplates = "CompanyMessageTemplates";
    public const string CompanyAssignmentRules = "CompanyAssignmentRules";
    public const string CompanyAssignmentQueues = "CompanyAssignmentQueues";
    public const string CompanyAutomationActions = "CompanyAutomationActions";
    public const string CompanyAutomationHistories = "CompanyAutomationHistories";
    public const string CompanyAutomationActionRecords = "CompanyAutomationActionRecords";
    public const string CompanyAssignmentUploadedFiles = "CompanyAssignmentUploadedFiles";
    public const string CompanySandboxes = "CompanySandboxes";
    public const string CompanyAPIKeys = "CompanyAPIKeys";
    public const string CompanyRequestChannels = "CompanyRequestChannels";
    public const string CompanyIconFiles = "CompanyIconFiles";
    public const string CompanyStaffTeams = "CompanyStaffTeams";
    public const string CompanyTeamMembers = "CompanyTeamMembers";
    public const string CompanyTeamAssignmentQueues = "CompanyTeamAssignmentQueues";
    public const string CompanyShareableInvitations = "CompanyShareableInvitations";
    public const string CompanyShareableInvitationRecords = "CompanyShareableInvitationRecords";
    public const string CompanyRolePermissions = "CompanyRolePermissions";
    public const string CompanyTwilioUsageRecords = "CompanyTwilioUsageRecords";
    public const string CompanyAnalyticSegment = "CompanyAnalyticSegment";
    public const string BroadcastCompaignHistories = "BroadcastCompaignHistories";
    public const string CampaignUploadedFiles = "CampaignUploadedFiles";
    public const string CampaignChannelMessages = "CampaignChannelMessages";
    public const string CampaignAutomationActions = "CampaignAutomationActions";
    public const string CampaignAutomationUploadedFiles = "CampaignAutomationUploadedFiles";
    public const string CompanyCustomUserProfileFieldLinguals = "CompanyCustomUserProfileFieldLinguals";
    public const string CompanyCustomFieldFieldLinguals = "CompanyCustomFieldFieldLinguals";
    public const string CompanyCustomUserProfileFields = "CompanyCustomUserProfileFields";
    public const string CompanyCustomUserProfileFieldOptions = "CompanyCustomUserProfileFieldOptions";
    public const string CompanyCustomUserProfileFieldOptionLinguals = "CompanyCustomUserProfileFieldOptionLinguals";
    public const string UserProfileFieldOptionLinguals = "UserProfileFieldOptionLinguals";
    public const string UserUserDevices = "UserUserDevices";
    public const string FacebookFetchedIds = "FacebookFetchedIds";
    public const string SenderFacebookSenders = "SenderFacebookSenders";
    public const string SenderWhatsappSenders = "SenderWhatsappSenders";
    public const string SenderEmailSenders = "SenderEmailSenders";
    public const string SenderWebClientSenders = "SenderWebClientSenders";
    public const string SenderWeChatSenders = "SenderWeChatSenders";
    public const string SenderLineSender = "SenderLineSender";
    public const string SenderViberSenders = "SenderViberSenders";
    public const string SenderTelegramSenders = "SenderTelegramSenders";
    public const string SenderTikTokSenders = "SenderTikTokSenders";
    public const string ConfigInstagramConfigs = "ConfigInstagramConfigs";
    public const string ConfigTikTokConfigs = "ConfigTikTokConfigs";
    public const string ConfigStorageConfigs = "ConfigStorageConfigs";
    public const string ConfigDialogflowServiceAccountConfigs = "ConfigDialogflowServiceAccountConfigs";
    public const string UserProfiles = "UserProfiles";
    public const string UserProfilePictureFiles = "UserProfilePictureFiles";
    public const string UserProfileCustomFields = "UserProfileCustomFields";
    public const string UserProfileShopifyOrders = "UserProfileShopifyOrders";
    public const string UserProfileShopifyAbandonedCarts = "UserProfileShopifyAbandonedCarts";
    public const string CrmHubEntities = "CrmHubEntities";
    public const string Conversations = "Conversations";
    public const string ConversationWhatsappHistories = "ConversationWhatsappHistories";
    public const string ConversationMessages = "ConversationMessages";
    public const string ConversationAdditionalAssignees = "ConversationAdditionalAssignees";
    public const string ConversationHashtags = "ConversationHashtags";
    public const string ConversationMessageUploadedFiles = "ConversationMessageUploadedFiles";
    public const string CoreEmailConfigs = "CoreEmailConfigs";
    public const string CoreSubscriptionPlans = "CoreSubscriptionPlans";
    public const string CompanyBillRecords = "CompanyBillRecords";
    public const string CoreTwilioTopupPlans = "CoreTwilioTopupPlans";
    public const string CoreTwilioTopupRecords = "CoreTwilioTopupRecords";
    public const string CoreEmailNotificationTemplates = "CoreEmailNotificationTemplates";
    public const string CoreTwilioConfigs = "CoreTwilioConfigs";
    public const string CoreCustomFields = "CoreCustomFields";
    public const string CoreSandboxTwilioConfigs = "CoreSandboxTwilioConfigs";
    public const string CoreCancelSubscriptionRecords = "CoreCancelSubscriptionRecords";
    public const string CoreWhatsappRegistrationFiles = "CoreWhatsappRegistrationFiles";
    public const string CompanyNotificationRecords = "CompanyNotificationRecords";
    public const string CompanyImportContactHistories = "CompanyImportContactHistories";
    public const string CompanyImportedUserProfiles = "CompanyImportedUserProfiles";
    public const string CompanyImportContactToListRecords = "CompanyImportContactToListRecords";
    public const string CompanyQuickReplies = "CompanyQuickReplies";
    public const string CompanyQuickReplyLinguals = "CompanyQuickReplyLinguals";
    public const string CompanyQuickReplyFiles = "CompanyQuickReplyFiles";
    public const string QRcodeRecords = "QRcodeRecords";
    public const string CoreCountries = "CoreCountries";
    public const string NotificationRegistrations = "NotificationRegistrations";
    public const string CoreZapierPollingRecord = "CoreZapierPollingRecord";
    public const string CoreRedeemPromotionRecords = "CoreRedeemPromotionRecords";
    public const string CorePromotions = "CorePromotions";
    public const string AnalyticsRecords = "AnalyticsRecords";
    public const string CmsSalesPaymentRecords = "CmsSalesPaymentRecords";
    public const string CmsSalesPaymentRecordFiles = "CmsSalesPaymentRecordFiles";
    public const string CmsContactOwnerAssignLogs = "CmsContactOwnerAssignLogs";
    public const string CmsWhatsappApplications = "CmsWhatsappApplications";
    public const string CmsLoginAsHistories = "CmsLoginAsHistories";
    public const string CmsHubSpotCompanyMaps = "CmsHubSpotCompanyMaps";
    public const string CmsHubSpotContactOwnerMaps = "CmsHubSpotContactOwnerMaps";
    public const string CmsHubSpotUserContactMaps = "CmsHubSpotUserContactMaps";
    public const string CmsHubSpotCompanySyncHistories = "CmsHubSpotCompanySyncHistories";
    public const string CmsCompanyDataSnapshots = "CmsCompanyDataSnapshots";
    public const string CmsCompanyAdditionalInfos = "CmsCompanyAdditionalInfos";
    public const string SupportTickets = "SupportTickets";
    public const string SupportTicketFiles = "SupportTicketFiles";
    public const string TwilioTopUpLogs = "TwilioTopUpLogs";
    public const string TwilioTemplateBookmarkRecords = "TwilioTemplateBookmarkRecords";
    public const string BackgroundTasks = "BackgroundTasks";
    public const string CompanyPaymentFailedLogs = "CompanyPaymentFailedLogs";
    public const string FbIgAutoReplies = "FbIgAutoReplies";
    public const string FbIgAutoReplyHistoryRecords = "FbIgAutoReplyHistoryRecords";
    public const string FbIgAutoReplyFiles = "FbIgAutoReplyFiles";
    public const string FbIgIcebreakers = "FbIgIcebreakers";
    public const string IcebreakerReplyRules = "IcebreakerReplyRules";
    public const string IcebreakerHistoryRecords = "IcebreakerHistoryRecords";
    public const string SenderWhatsApp360DialogSenders = "SenderWhatsApp360DialogSenders";
    public const string ConfigWhatsApp360DialogConfigs = "ConfigWhatsApp360DialogConfigs";
    public const string WhatsApp360DialogTemplateBookmarks = "WhatsApp360DialogTemplateBookmarks";
    public const string CompanyWhatsApp360DialogUsageRecords = "CompanyWhatsApp360DialogUsageRecords";
    public const string CompanyWhatsApp360DialogUsageTransactionLogs = "CompanyWhatsApp360DialogUsageTransactionLogs";
    public const string CoreWhatsApp360DialogPartnerAuthCredentials = "CoreWhatsApp360DialogPartnerAuthCredentials";
    public const string WhatsApp360DialogMediaFiles = "WhatsApp360DialogMediaFiles";
    public const string Whatsapp360DialogExtendedMessagePayload = "Whatsapp360DialogExtendedMessagePayload";
    public const string ConversationBookmarks = "ConversationBookmarks";
    public const string HubSpotIntegrationContactOwnerMaps = "HubSpotIntegrationContactOwnerMaps";
    public const string HubSpotIntegrationCustomFieldMaps = "HubSpotIntegrationCustomFieldMaps";
    public const string HubSpotIntegrationConfigs = "HubSpotIntegrationConfigs";
    public const string HubSpotIntegrationSyncTasks = "HubSpotIntegrationSyncTasks";
    public const string HubSpotIntegrationSyncTaskErrorRecords = "HubSpotIntegrationSyncTaskErrorRecords";
    public const string HubSpotIntegrationWebhookErrorRecords = "HubSpotIntegrationWebhookErrorRecords";
    public const string CoreShareLinkTrackingRecords = "CoreShareLinkTrackingRecords";
    public const string CoreShareLinkGenerationRecords = "CoreShareLinkGenerationRecords";
    public const string CompanyWhatsapp360DialogTopUpConfigs = "CompanyWhatsapp360DialogTopUpConfigs";
    public const string CmsCurrencyExchangeRates = "CmsCurrencyExchangeRates";
    public const string Cms360DialogItemCosts = "Cms360DialogItemCosts";
    public const string ResellerCompanyProfiles = "ResellerCompanyProfiles";
    public const string ResellerStaffs = "ResellerStaffs";
    public const string ResellerClientCompanyProfiles = "ResellerClientCompanyProfiles";
    public const string ResellerTransactionLogs = "ResellerTransactionLogs";
    public const string ResellerProfileLogos = "ResellerProfileLogos";
    public const string CoreResellerStripeTopUpOptions = "CoreResellerStripeTopUpOptions";
    public const string ExtendedMessagePayloads = "ExtendedMessagePayloads";
    public const string ShopifyCollectionProductRecords = "ShopifyCollectionProductRecords";
    public const string SenderWebClientIPAddressInfos = "SenderWebClientIPAddressInfos";
    public const string FacebookOtnTopics = "FacebookOtnTopics";
    public const string FacebookUserOneTimeTokens = "FacebookUserOneTimeTokens";
    public const string StripeCompanyLogos = "StripeCompanyLogos";
    public const string ConfigWeChatConfigs = "ConfigWeChatConfigs";
    public const string ConfigEmailConfigs = "ConfigEmailConfigs";
    public const string AnalyticsEmailNotificationConfigs = "AnalyticsEmailNotificationConfigs";
    public const string BlastMessageConfigs = "BlastMessageConfigs";
    public const string BlastMessageTemplates = "BlastMessageTemplates";
    public const string ChatHistoryBackupConfig = "ChatHistoryBackupConfig";
    public const string CompanyRegionalInfos = "CompanyRegionalInfos";
    public const string ConfigFacebookConfigs = "ConfigFacebookConfigs";
    public const string ConfigLineConfigs = "ConfigLineConfigs";
    public const string ConfigStripePaymentConfigs = "ConfigStripePaymentConfigs";
    public const string ConfigViberConfigs = "ConfigViberConfigs";
    public const string ConfigWhatsappCloudApiConfigs = "ConfigWhatsappCloudApiConfigs";
    public const string ConfigWhatsAppConfigs = "ConfigWhatsAppConfigs";
    public const string ConversationUnreadRecords = "ConversationUnreadRecords";
    public const string CustomSubscriptionPlanTranslationMaps = "CustomSubscriptionPlanTranslationMaps";
    public const string DemoConversations = "DemoConversations";
    public const string DemoConversationMessages = "DemoConversationMessages";
    public const string ExtendedMessagePayloadFiles = "ExtendedMessagePayloadFiles";
    public const string StripePaymentMessageTemplates = "StripePaymentMessageTemplates";
    public const string SenderInstagramSenders = "SenderInstagramSenders";
    public const string ImportWhatsappHistoryRecords = "ImportWhatsappHistoryRecords";
    public const string WhatsappCloudApiWabaConnections = "WhatsappCloudApiWabaConnections";
    public const string ShopifyProductRecords = "ShopifyProductRecords";
    public const string ConfigSMSConfigs = "ConfigSMSConfigs";
    public const string ConfigTelegramConfigs = "ConfigTelegramConfigs";
    public const string ConfigShopifyConfigs = "ConfigShopifyConfigs";
    public const string SenderSandboxSenders = "SenderSandboxSenders";
    public const string StripePaymentReportExportRecords = "StripePaymentReportExportRecords";
    public const string SenderSMSSenders = "SenderSMSSenders";
    public const string ShopifyCollectionRecords = "ShopifyCollectionRecords";
    public const string ShopifyProductMessageTemplates = "ShopifyProductMessageTemplates";
    public const string StripePaymentRecords = "StripePaymentRecords";
    public const string WhatsappCloudApiSenders = "WhatsappCloudApiSenders";
    public const string WhatsAppCloudApiTemplateBookmarks = "WhatsAppCloudApiTemplateBookmarks";
    public const string WhatsappTemplateQuickReplyCallbacks = "WhatsappTemplateQuickReplyCallbacks";

    public static List<string> GetAllTableNames()
    {
        return typeof(MigrationOptions).GetProperties().Select(x=>x.Name).ToList();
    }
}
