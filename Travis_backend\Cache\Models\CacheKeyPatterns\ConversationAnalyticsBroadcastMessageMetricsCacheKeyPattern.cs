﻿using System;

namespace Travis_backend.Cache.Models.CacheKeyPatterns;

public class ConversationAnalyticsBroadcastMessageMetricsCacheKeyPattern : ICacheKeyPattern
{
    private const string MetricType = "BroadcastMessage";

    public string CompanyId { get; set; }

    public DateOnly Date { get; set; }

    public string SearchConditionHash { get; set; }

    public ConversationAnalyticsBroadcastMessageMetricsCacheKeyPattern(
        string companyId,
        DateOnly date,
        string searchConditionHash)
    {
        CompanyId = companyId;
        Date = date;
        SearchConditionHash = searchConditionHash;
    }

    public string GenerateKeyPattern()
    {
        var keyName = CacheHelper.BuildKeyNameFromPattern(
            new object[]
            {
                CompanyId,
                Date,
                MetricType,
                SearchConditionHash
            });
        return keyName;
    }
}