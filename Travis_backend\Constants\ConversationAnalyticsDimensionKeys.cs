namespace Travis_backend.Constants;

public static class ConversationAnalyticsDimensionKeys
{
    // base dimensions. when business hour disabled
    public const string Company = "[\"sleekflow_company_id\"]";
    public const string CompanyChannel = "[\"channel_identity_id\",\"channel_type\",\"sleekflow_company_id\"]";
    public const string CompanyUser = "[\"asp_net_user_id\",\"sleekflow_company_id\"]";
    public const string CompanyUserChannel = "[\"asp_net_user_id\",\"channel_identity_id\",\"channel_type\",\"sleekflow_company_id\"]";
    public const string CompanyContact = "[\"sleekflow_company_id\",\"user_profile_id\"]";
    public const string CompanyContactChannel = "[\"channel_identity_id\",\"channel_type\",\"sleekflow_company_id\",\"user_profile_id\"]";
    public const string CompanyContactUser = "[\"asp_net_user_id\",\"sleekflow_company_id\",\"user_profile_id\"]";
    public const string CompanyContactUserChannel = "[\"asp_net_user_id\",\"channel_identity_id\",\"channel_type\",\"sleekflow_company_id\",\"user_profile_id\"]";

    // business hour enabled
    public const string CompanyWithBusinessHour = "[\"is_by_working_hour\",\"sleekflow_company_id\"]";
    public const string CompanyChannelWithBusinessHour = "[\"channel_identity_id\",\"channel_type\",\"is_by_working_hour\",\"sleekflow_company_id\"]";
    public const string CompanyUserWithBusinessHour = "[\"asp_net_user_id\",\"is_by_working_hour\",\"sleekflow_company_id\"]";
    public const string CompanyUserChannelWithBusinessHour = "[\"asp_net_user_id\",\"channel_identity_id\",\"channel_type\",\"is_by_working_hour\",\"sleekflow_company_id\"]";
    public const string CompanyContactWithBusinessHour = "[\"is_by_working_hour\",\"sleekflow_company_id\",\"user_profile_id\"]";
    public const string CompanyContactChannelWithBusinessHour = "[\"channel_identity_id\",\"channel_type\",\"is_by_working_hour\",\"sleekflow_company_id\",\"user_profile_id\"]";
    public const string CompanyContactUserWithBusinessHour = "[\"asp_net_user_id\",\"is_by_working_hour\",\"sleekflow_company_id\",\"user_profile_id\"]";
    public const string CompanyContactUserChannelWithBusinessHour = "[\"asp_net_user_id\",\"channel_identity_id\",\"channel_type\",\"is_by_working_hour\",\"sleekflow_company_id\",\"user_profile_id\"]";
}