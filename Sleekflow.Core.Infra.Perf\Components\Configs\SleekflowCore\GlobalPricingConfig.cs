using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Perf.Components.Configs.SleekflowCore;

public class GlobalPricingConfig
{
    [JsonProperty("is_feature_enabled")]
    public string IsFeatureEnabled { get; set; }

    [JsonProperty("plan_migration_incentives_start_date")]
    public string PlanMigrationIncentivesStartDate { get; set; }

    [JsonProperty("plan_migration_incentives_end_date")]
    public string PlanMigrationIncentivesEndDate { get; set; }

    public GlobalPricingConfig(
        string isFeatureEnabled,
        string planMigrationIncentivesStartDate,
        string planMigrationIncentivesEndDate)
    {
        IsFeatureEnabled = isFeatureEnabled;
        PlanMigrationIncentivesStartDate = planMigrationIncentivesStartDate;
        PlanMigrationIncentivesEndDate = planMigrationIncentivesEndDate;
    }
}