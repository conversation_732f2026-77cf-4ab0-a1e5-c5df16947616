﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class ServerExceptionsAndFailuresCountMetric : IDashboardMetric
{
    private readonly Output<string>? _sleekflowCoreAppInsightName;
    private readonly Output<string>? _sleekflowCoreAppInsightResourceId;
    private readonly Output<string>? _sleekflowCoreWorkerAppInsightResourceId;

    public ServerExceptionsAndFailuresCountMetric(
        Output<string>? sleekflowCoreAppInsightName,
        Output<string>? sleekflowCoreAppInsightResourceId,
        Output<string>? sleekflowCoreWorkerAppInsightResourceId)
    {
        _sleekflowCoreAppInsightName = sleekflowCoreAppInsightName;
        _sleekflowCoreAppInsightResourceId = sleekflowCoreAppInsightResourceId;
        _sleekflowCoreWorkerAppInsightResourceId = sleekflowCoreWorkerAppInsightResourceId;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = new DashboardPartsPositionArgs
            {
                X = 24, Y = 8, ColSpan = 4, RowSpan = 4
            },
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs =
                    new[]
                    {
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "sharedTimeRange"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "options"
                            },
                            {
                                "value", new Dictionary<string, object>()
                                {
                                    {
                                        "chart", new Dictionary<string, object>()
                                        {
                                            {
                                                "metrics", new[]
                                                {
                                                    new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "aggregationType", 7
                                                        },
                                                        {
                                                            "metricVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "color", "#47BDF5"
                                                                },
                                                                {
                                                                    "displayName", "Server exceptions"
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "name", "exceptions/server"
                                                        },
                                                        {
                                                            "namespace", "microsoft.insights/components"
                                                        },
                                                        {
                                                            "resourceMetadata", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "id", _sleekflowCoreWorkerAppInsightResourceId!
                                                                }
                                                            }
                                                        }
                                                    },
                                                    new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "aggregationType", 7
                                                        },
                                                        {
                                                            "metricVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "color", "#7E58FF"
                                                                },
                                                                {
                                                                    "displayName", "Dependency failures"
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "name", "dependencies/failed"
                                                        },
                                                        {
                                                            "namespace", "microsoft.insights/components"
                                                        },
                                                        {
                                                            "resourceMetadata", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "id", _sleekflowCoreWorkerAppInsightResourceId!
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            {
                                                "title", "Server exceptions and Dependency failures"
                                            },
                                            {
                                                "visualization", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "axisVisualization", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "x", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "axisType", 2
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "y", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "axisType", 1
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "chartType", 2
                                                    },
                                                    {
                                                        "legendVisualization", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "hideSubtitle", false
                                                            },
                                                            {
                                                                "isVisible", true
                                                            },
                                                            {
                                                                "position", 2
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                Settings =
                {
                    new Dictionary<string, object>()
                    {
                        {
                            "content", new Dictionary<string, object>()
                            {
                                {
                                    "options", new Dictionary<string, object>()
                                    {
                                        {
                                            "chart", new Dictionary<string, object>()
                                            {
                                                {
                                                    "metrics", new[]
                                                    {
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 1
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "Dependency failures"
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "dependencies/failed"
                                                            },
                                                            {
                                                                "namespace", "microsoft.insights/components/kusto"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreWorkerAppInsightResourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 1
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "Server exceptions"
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "exceptions/server"
                                                            },
                                                            {
                                                                "namespace", "microsoft.insights/components/kusto"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreWorkerAppInsightResourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "color", "#47BDF5"
                                                                    },
                                                                    {
                                                                        "displayName", "Server exceptions"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _sleekflowCoreAppInsightName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "exceptions/server"
                                                            },
                                                            {
                                                                "namespace", "microsoft.insights/components"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreAppInsightResourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "Dependency call failures"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _sleekflowCoreAppInsightName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "dependencies/failed"
                                                            },
                                                            {
                                                                "namespace", "microsoft.insights/components"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreAppInsightResourceId!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    "title", "Server exceptions and Dependency failures"
                                                },
                                                {
                                                    "titleKind", 2
                                                },
                                                {
                                                    "visualization", new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "axisVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "x", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 2
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    "y", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 1
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "chartType", 2
                                                        },
                                                        {
                                                            "disablePinning", true
                                                        },
                                                        {
                                                            "legendVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "hideHoverCard", false
                                                                },
                                                                {
                                                                    "hideLabelNames", true
                                                                },
                                                                {
                                                                    "hideSubtitle", false
                                                                },
                                                                {
                                                                    "isVisible", true
                                                                },
                                                                {
                                                                    "position", 2
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}