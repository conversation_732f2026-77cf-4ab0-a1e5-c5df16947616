name: staging_sleekflow-core-deploy(staging)

on:
  push:
    branches:
      - staging

env:
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages
  DOTNET_INSTALL_DIR: "./.dotnet"

jobs:
  set-env:
    name: Set Environment Variables
    runs-on: ubuntu-latest
    outputs:
      branch_name: ${{ steps.set-branch.outputs.name }}
    steps:
      - id: set-branch
        run: |
          # Get the branch name and replace '/' with '-'
          branch="${{ github.ref_name }}"
          if [ -z "$branch" ]; then
            echo "name=latest" >> $GITHUB_OUTPUT
          else
            echo "name=${branch//\//-}" >> $GITHUB_OUTPUT
          fi

  update:
    needs: set-env
    name: Update
    runs-on:
      group: "Self Hosted Group"
    concurrency:
      group: sleekflow-core-deploy--${{ needs.set-env.outputs.branch_name }}
      cancel-in-progress: false
    steps:
      - uses: actions/checkout@v3

      - name: Set IMAGE_TAG
        run: |
          echo "IMAGE_TAG=staging-$(date +%Y%m%d-%H%M%S)" >> $GITHUB_ENV

      - name: Set up .NET Core
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.303'
          include-prerelease: false

      - name: Install Azure Cli
        run: |
          curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

      - uses: actions/cache@v3
        with:
          path: ${{ github.workspace }}/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-

      - name: Restore with dotnet
        run: dotnet restore

      - name: Build with dotnet
        run: |
          dotnet publish Travis_backend.Auth0 -c Release --self-contained /p:ExcludeBuildDbMigration=TRUE /p:INTEGRATION_TESTING=TRUE
          dotnet publish Sleekflow.SleekPay -c Release
          dotnet publish Sleekflow.Core.Tests -c Release

      - name: Pull TestContainers dependencies
        run: docker pull docker.io/testcontainers/ryuk

      - name: Build images locally
        run: |
          sudo IMAGE_TAG=$IMAGE_TAG docker compose -f docker-compose.common.yml build --build-arg ENVIRONMENT=staging
          sudo IMAGE_TAG=$IMAGE_TAG docker compose -f docker-compose.yml build --build-arg ENVIRONMENT=staging

      - name: Set Pulumi Organization
        run: pulumi org set-default sleekflowio
        working-directory: ./Sleekflow.Core.Infra/
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}

      - name: Deploy Everything
        uses: pulumi/actions@v4
        if: github.ref == 'refs/heads/staging' || github.event.pull_request.base.ref == 'staging'
        with:
          command: up
          stack-name: sleekflowio/Sleekflow.Core.Infra/staging
          work-dir: ./Sleekflow.Core.Infra/
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          ARM_CLIENT_ID: ${{ secrets.AZURE_SP_ARM_CLIENT_ID }}
          ARM_CLIENT_SECRET: ${{ secrets.AZURE_SP_ARM_CLIENT_SECRET }}
          ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SP_ARM_SUBSCRIPTION_ID }}
          ARM_TENANT_ID: ${{ secrets.AZURE_SP_ARM_TENANT_ID }}
          IMAGE_TAG: ${{ env.IMAGE_TAG }}

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_SP_LOGIN_CREDS }}

      - name: Swap Standby to Production
        run: |
          az webapp deployment slot swap -g sleekflow-core-rg-eas-stagingf0d2834c -n sleekflow-core-app-eas-staging --slot standby --target-slot production
          az webapp deployment slot swap -g sleekflow-core-rg-eus-stagingbe64ad00 -n sleekflow-core-app-eus-staging --slot standby --target-slot production

      - name: Deploy Everything Again
        uses: pulumi/actions@v4
        if: github.ref == 'refs/heads/staging' || github.event.pull_request.base.ref == 'staging'
        with:
          command: up
          stack-name: sleekflowio/Sleekflow.Core.Infra/staging
          work-dir: ./Sleekflow.Core.Infra/
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          ARM_CLIENT_ID: ${{ secrets.AZURE_SP_ARM_CLIENT_ID }}
          ARM_CLIENT_SECRET: ${{ secrets.AZURE_SP_ARM_CLIENT_SECRET }}
          ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SP_ARM_SUBSCRIPTION_ID }}
          ARM_TENANT_ID: ${{ secrets.AZURE_SP_ARM_TENANT_ID }}
          IMAGE_TAG: ${{ env.IMAGE_TAG }}
