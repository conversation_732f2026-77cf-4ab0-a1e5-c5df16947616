﻿namespace Travis_backend.ContactDomain.Constants;

public static class UpdateUserProfileTriggerSource
{
    public const string UserOperation = "user-operation"; // when cannot detect the source, last resort only
    public const string StaffManual = "staff-manual";
    public const string PublicApi = "public-api";
    public const string ShopifyRedact = "shopify-redact";
    public const string WebClientMerge = "webclient-merge";
    public const string DuplicateContactMerge = "duplicate-contact-merge";
    public const string ConversationMerge = "conversation-merge";
    public const string ContactSafeDeletionCleaner = "contact-safe-deletion-cleaner";
    public const string FlowHubAction = "flowhub-action";
    public const string AiAgentAction = "ai-agent-action";
    public const string AutomationAction = "automation-action";
    public const string IncomingMessage = "incoming-message";
    public const string CrmHubAction = "crmhub-action";
    public const string ContactImport = "contact-import";
    public const string ZapierIntegration = "zapier-integration";
    public const string HubSpotIntegration = "hubspot-integration";
    public const string WebClientConnection = "webclient-connection";
    public const string RawSqlImport = "raw-sql-import";
}