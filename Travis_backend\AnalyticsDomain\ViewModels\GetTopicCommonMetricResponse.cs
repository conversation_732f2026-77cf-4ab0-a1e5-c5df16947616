using System;
using System.Collections.Generic;

namespace Travis_backend.AnalyticsDomain.ViewModels;

public class GetTopicCommonMetricResponse
{
    public string StartDate { get; set; }

    public string EndDate { get; set; }

    public List<TopicCommonMetricViewModel> Analytics { get; set; }

    public DateTime? LastUpdateTime { get; set; }

    public GetTopicCommonMetricResponse(
        DateOnly startDate,
        DateOnly endDate,
        List<TopicCommonMetricViewModel> analytics,
        DateTime? lastUpdateTime = null)
    {
        StartDate = startDate.ToString("yyyy-MM-dd");
        EndDate = endDate.ToString("yyyy-MM-dd");
        Analytics = analytics;
        LastUpdateTime = lastUpdateTime;
    }
}