﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.Models;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.Enums;
using Travis_backend.IntegrationServices.Models;
using Travis_backend.MessageDomain.Models;
using Travis_backend.SleekflowCrmHubDomain.Models;

namespace Travis_backend.ContactDomain.Models
{
    public class UserProfile
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();

        public string CompanyId { get; set; }

        // public Company Company { get; set; }
        [JsonIgnore]
        public Conversation Conversation { get; set; }

        private string _firstName = "Anonymous";

        public string FirstName
        {
            get
            {
                return this._firstName;
            }

            set
            {
                this._firstName = value;
                this.FullName = (this._firstName + " " + this.LastName).Trim();
            }
        }

        private string _lastName = string.Empty;

        public string LastName
        {
            get
            {
                return this._lastName;
            }

            set
            {
                this._lastName = value;
                this.FullName = (this.FirstName + " " + this._lastName).Trim();
            }
        }

        private string _fullName = "Anonymous";

        [MaxLength(450)]
        public string FullName
        {
            get => _fullName;
            private set => _fullName = value;
        }

        public FacebookSender FacebookAccount { get; set; }

        public long? FacebookAccountId { get; set; }

        public WhatsAppSender WhatsAppAccount { get; set; }

        public long? WhatsAppAccountId { get; set; }

        // public ChatAPIWhatsAppSender ChatAPIWhatsAppAccount { get; set; }
        public UserDevice UserDevice { get; set; }

        public long? UserDeviceId { get; set; }

        public Guest RegisteredUser { get; set; }

        public long? RegisteredUserId { get; set; }

        public EmailSender EmailAddress { get; set; }

        public long? EmailAddressId { get; set; }

        public WebClientSender WebClient { get; set; }

        public long? WebClientId { get; set; }

        public WeChatSender WeChatUser { get; set; }

        public long? WeChatUserId { get; set; }

        public LineSender LineUser { get; set; }

        public long? LineUserId { get; set; }

        public ViberSender ViberUser { get; set; }

        public long? ViberUserId { get; set; }

        public SMSSender SMSUser { get; set; }

        public long? SMSUserId { get; set; }

        public long? InstagramUserId { get; set; }

        public InstagramSender InstagramUser { get; set; }

        [ForeignKey(nameof(WhatsApp360DialogUserId))]
        public WhatsApp360DialogSender WhatsApp360DialogUser { get; set; }

        public long? WhatsApp360DialogUserId { get; set; }

        public WhatsappCloudApiSender WhatsappCloudApiUser { get; set; }

        public TelegramSender TelegramUser { get; set; }

        public TikTokSender TikTokUser { get; set; }

        public IList<UserProfileCustomField> CustomFields { get; set; }

        // public List<UserProfileActivityLog> UserProfileActivityLogs { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastContact { get; set; }

        public DateTime? LastContactFromCustomers { get; set; }

        public string PictureUrl { get; set; }

        public long? UserProfilePictureFileId { get; set; }

        public UserProfilePictureFile UserProfilePictureFile { get; set; }

        public ActiveStatus ActiveStatus { get; set; } = ActiveStatus.Active;

        public bool IsSandbox { get; set; }

        public string Description { get; set; }

        // public long? AssigneeId { get; set; }
        // public Staff Assignee { get; set; }

        // public long? AssignedTeamId { get; set; }
        // public CompanyTeam AssignedTeam { get; set; }

        // public SandboxSender SandboxUser { get; set; }
        // public long? SandboxUserId { get; set; }
        public string ContactOwnerId { get; set; }

        [MaxLength(100)]
        public string PhoneNumber { get; set; }

        [MaxLength(350)]
        public string Email { get; set; }

        public bool IsShopifyProfile { get; set; }

        public List<ShopifyOrderRecord> ShopifyOrderRecords { get; set; }

        public List<ShopifyAbandonedCart> ShopifyAbandonedCarts { get; set; }

        public long? ShopifyCustomerId { get; set; }

        [MaxLength(200)]
        public string StripeCustomerId { get; set; }

        [MaxLength(200)]
        public string StripeCustomerSGId { get; set; }

        [MaxLength(200)]
        public string StripeCustomerMYId { get; set; }

        [MaxLength(200)]
        public string StripeCustomerGBId { get; set; }

        public List<CrmHubEntity> CrmHubEntities { get; set; }
    }

    // public class UserProfileActivityLog
    // {
    //    public long Id { get; set; }
    //    public string UserProfileId { get; set; }
    //    public string LogMessage { get; set; }
    //    public string Status { get; set; }
    //    public string Type { get; set; }
    //    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    //    public Staff ModifiedBy { get; set; }

    // public FacebookSender FacebookAccount { get; set; }
    //    public WhatsAppSender WhatsAppAccount { get; set; }
    //    public UserDevice UserDevice { get; set; }
    //    public Guest RegisteredUser { get; set; }
    //    public EmailSender EmailAddress { get; set; }
    //    public WebClientSender WebClient { get; set; }
    //    public WeChatSender WeChatUser { get; set; }

    // public UserProfileCustomField CustomField { get; set; }
    // }
}