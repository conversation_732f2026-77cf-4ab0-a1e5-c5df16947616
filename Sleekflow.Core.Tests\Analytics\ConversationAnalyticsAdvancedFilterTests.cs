﻿using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.Utils;
using Travis_backend.Constants;
using Travis_backend.Enums;

namespace Sleekflow.Core.Tests.Analytics;

[TestFixture]
public class ConversationAnalyticsAdvancedFilterTests
{
    private List<ConversationAnalyticsMetric> _metrics;
    private AdvancedFilter _advancedFilter;

    [SetUp]
    public void SetUp()
    {
        _metrics = new List<ConversationAnalyticsMetric>
        {
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"asp_net_user_id\":\"user1\"}" },
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"asp_net_user_id\":\"user2\"}" },
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"asp_net_user_id\":\"user3\"}" },
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"channel_type\":\"type1\",\"channel_identity_id\":\"id1\"}" },
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"channel_type\":\"type2\",\"channel_identity_id\":\"id2\"}" },
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"channel_type\":\"type3\",\"channel_identity_id\":\"id3\"}" },
            new ConversationAnalyticsMetric { DimensionDataJson = "{\"asp_net_user_id\":\"user1\",\"channel_type\":\"type1\",\"channel_identity_id\":\"id1\"}" },
        };

        var filters = new List<Filter>
        {
            new Filter(
                ConversationAnalyticsAdvancedFilterFieldNames.Assignee,
                SupportedOperator.ContainsAny,
                ["user1"]),
            new Filter(
                ConversationAnalyticsAdvancedFilterFieldNames.Channel,
                SupportedOperator.ContainsAny,
                ["type1:id1"]),
        };

        _advancedFilter = new AdvancedFilter("AND", filters);
    }

    [Test]
    public void ApplyAdvancedFilter_WithEmptyMetrics_ReturnsWithoutModification()
    {
        // Arrange
        var metrics = new List<ConversationAnalyticsMetric>();

        // Act
        ConversationAnalyticsMetricUtils.ApplyAdvancedFilter(ref metrics, _advancedFilter);

        // Assert
        Assert.IsEmpty(metrics);
    }

    [Test]
    public void ApplyAdvancedFilter_WithEmptyFilters_ReturnsWithoutModification()
    {
        // Arrange
        var metrics = _metrics;
        var advancedFilter = AdvancedFilter.EmptyFilter();

        // Act
        ConversationAnalyticsMetricUtils.ApplyAdvancedFilter(ref metrics, advancedFilter);

        // Assert
        Assert.That(metrics.Count, Is.EqualTo(7));
    }

    [Test]
    public void ApplyAdvancedFilter_WithAndOperator_ReturnsFilteredMetrics()
    {
        // Arrange
        var metrics = _metrics;

        // Act
        ConversationAnalyticsMetricUtils.ApplyAdvancedFilter(ref metrics, _advancedFilter);

        // Assert
        Assert.That(metrics.Count, Is.EqualTo(1)); // Should match only the metric with "user1" and "type1:id1"
    }

    [Test]
    public void ApplyAdvancedFilter_WithOrOperator_ReturnsFilteredMetrics()
    {
        // Arrange
        var metrics = _metrics;
        _advancedFilter.LogicalOperator = "or";

        // Act
        ConversationAnalyticsMetricUtils.ApplyAdvancedFilter(ref metrics, _advancedFilter);

        // Assert
        Assert.That(metrics.Count, Is.EqualTo(3)); // Should match metrics with "user1" or "type1:id1"
    }

    [Test]
    public void ApplyAdvancedFilter_WithIsNotContainsAnyFilter_ReturnsFilteredMetrics()
    {
        // Arrange
        var metrics = _metrics;

        var advancedFilter = new AdvancedFilter(
            "and",
            [
                new Filter(
                    ConversationAnalyticsAdvancedFilterFieldNames.Assignee,
                    SupportedOperator.ContainsAny,
                    ["user1", "user2"])
            ]);

        // Act
        ConversationAnalyticsMetricUtils.ApplyAdvancedFilter(ref metrics, advancedFilter);

        // Assert
        Assert.That(metrics.Count, Is.EqualTo(3));
    }

    [Test]
    public void ApplyAdvancedFilter_WithOppositeFilterLogics_ReturnsFilteredMetrics()
    {
        // Arrange
        var metrics = _metrics;
        _advancedFilter.Filters.Add(
            new Filter(
                ConversationAnalyticsAdvancedFilterFieldNames.Assignee,
                SupportedOperator.IsNotContainsAny,
                ["user1", "user2"]));

        // Act
        ConversationAnalyticsMetricUtils.ApplyAdvancedFilter(ref metrics, _advancedFilter);

        // Assert
        Assert.That(metrics.Count, Is.EqualTo(0));
    }

    [Test]
    public void ApplyAdvancedFilter_WithComplicatedAndFilters_ReturnsFilteredMetrics()
    {
        // Arrange
        var metrics = _metrics;

        var advancedFilter = new AdvancedFilter(
            "AND",
            [
                new Filter(
                    ConversationAnalyticsAdvancedFilterFieldNames.Assignee,
                    SupportedOperator.ContainsAny,
                    ["user1"]),
                new Filter(
                    ConversationAnalyticsAdvancedFilterFieldNames.Assignee,
                    SupportedOperator.ContainsAny,
                    ["user1", "user2"]),
                new Filter(
                    ConversationAnalyticsAdvancedFilterFieldNames.Assignee,
                    SupportedOperator.IsNotContainsAny,
                    ["user2"]),
                new Filter(
                    ConversationAnalyticsAdvancedFilterFieldNames.Channel,
                    SupportedOperator.ContainsAny,
                    ["type1:id1", "type2:id2", "typeX:idX"])
            ]);
        // Act
        ConversationAnalyticsMetricUtils.ApplyAdvancedFilter(ref metrics, advancedFilter);

        // Assert
        Assert.That(metrics.Count, Is.EqualTo(1));
    }

    [Test]
    public void ApplyAdvancedFilter_WithComplicatedOrFilters_ReturnsFilteredMetrics()
    {
        // Arrange
        var metrics = _metrics;

        var advancedFilter = new AdvancedFilter(
            "OR",
            [
                new Filter(
                    ConversationAnalyticsAdvancedFilterFieldNames.Assignee,
                    SupportedOperator.ContainsAny,
                    ["user1"]),
                new Filter(
                    ConversationAnalyticsAdvancedFilterFieldNames.Assignee,
                    SupportedOperator.IsNotContainsAny,
                    ["user2"]),
                new Filter(
                    ConversationAnalyticsAdvancedFilterFieldNames.Channel,
                    SupportedOperator.ContainsAny,
                    ["type1:id1", "type2:id2", "typeX:idX"])
            ]);
        // Act
        ConversationAnalyticsMetricUtils.ApplyAdvancedFilter(ref metrics, advancedFilter);

        // Assert
        Assert.That(metrics.Count, Is.EqualTo(6)); // only user3 not match the condition
    }

    [Test]
    public void ApplyAdvancedFilter_WithUnsupportedOperator_ThrowsArgumentException()
    {
        // Arrange
        var metrics = _metrics;
        _advancedFilter.Filters[0].Operator = SupportedOperator.GroupBy;

        // Act & Assert
        Assert.Throws<ArgumentOutOfRangeException>(() => ConversationAnalyticsMetricUtils.ApplyAdvancedFilter(ref metrics, _advancedFilter));
    }
}