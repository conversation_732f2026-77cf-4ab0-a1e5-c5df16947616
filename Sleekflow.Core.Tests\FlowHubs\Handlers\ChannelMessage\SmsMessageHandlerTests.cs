using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.FlowHubs.Handlers.ChannelMessage;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.FlowHubs.Handlers.ChannelMessage;

[TestFixture]
public class SmsMessageHandlerTests
{
  private SmsMessageHandler _handler;
  private ILogger<SmsMessageHandler> _mockLogger;

  [SetUp]
  public void SetUp()
  {
    _mockLogger = Substitute.For<ILogger<SmsMessageHandler>>();

    _handler = new SmsMessageHandler(
        null,
        _mockLogger);
  }

  [Test]
  public void ChannelType_ShouldReturnSms()
  {
    // Act
    var result = _handler.ChannelType;

    // Assert
    Assert.That(result, Is.EqualTo(ChannelTypes.Sms));
  }

  [Test]
  public async Task PrepareConversationMessageAsync_WithTextMessage_ShouldSetCorrectProperties()
  {
    // Arrange
    var companyId = "test-company-id";
    var channelIdentityId = "test-channel-id";
    var messageType = "text";
    var messageText = "Hello, this is a test message";

    var messageBody = new MessageBody
    {
      SmsMessage = new SmsMessageObject
      {
        Text = messageText
      }
    };

    var conversation = new Conversation
    {
      Id = "test-conversation-id",
      SMSUser = new SMSSender
      {
        phone_number = "+1234567890",
        name = "Test User"
      }
    };

    var files = new List<IFormFile>();

    // Act
    var result = await _handler.PrepareConversationMessageAsync(
        companyId, channelIdentityId, messageType, messageBody, conversation, files);

    // Assert
    Assert.Multiple(() =>
    {
      Assert.That(result.Channel, Is.EqualTo(ChannelTypes.Sms));
      Assert.That(result.ConversationId, Is.EqualTo(conversation.Id));
      Assert.That(result.MessageType, Is.EqualTo(messageType));
      Assert.That(result.MessageContent, Is.EqualTo(messageText));
      Assert.That(result.SMSReceiver, Is.EqualTo(conversation.SMSUser));
      Assert.That(result.DeliveryType, Is.EqualTo(DeliveryType.FlowHubAction));
      Assert.That(result.AnalyticTags, Is.Null);
      Assert.That(result.IsSentFromSleekflow, Is.True);
      Assert.That(files, Is.Empty);
    });
  }

  [Test]
  public void PrepareConversationMessageAsync_WithUnsupportedMessageType_ShouldThrowNotImplementedException()
  {
    // Arrange
    var companyId = "test-company-id";
    var channelIdentityId = "test-channel-id";
    var messageType = "unsupported";
    var messageBody = new MessageBody();
    var conversation = new Conversation { Id = "test-conversation-id" };
    var files = new List<IFormFile>();

    // Act & Assert
    var exception = Assert.ThrowsAsync<NotImplementedException>(async () =>
        await _handler.PrepareConversationMessageAsync(
            companyId, channelIdentityId, messageType, messageBody, conversation, files));

    Assert.That(exception.Message, Is.EqualTo($"Message type {messageType} not implemented for {ChannelTypes.Sms}"));
  }
}