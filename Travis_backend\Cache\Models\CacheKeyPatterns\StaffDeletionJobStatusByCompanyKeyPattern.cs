using Travis_backend.Cache.Models.CacheKeyPatterns;

namespace Travis_backend.Cache.Models.CacheKeyPatterns;

public sealed class StaffDeletionJobsByCompanyKeyPattern : ICacheKeyPattern
{
    public string CompanyId { get; }

    public StaffDeletionJobsByCompanyKeyPattern(string companyId)
    {
        CompanyId = companyId;
    }

    public string GenerateKeyPattern()
    {
        return CacheHelper.BuildKeyNameFromPattern(
            new object[]
            {
                "CompanyStaffDeletionJobs",
                CompanyId
            });
    }

    public static StaffDeletionJobsByCompanyKeyPattern Create(string companyId)
    {
        return new StaffDeletionJobsByCompanyKeyPattern(companyId);
    }
}