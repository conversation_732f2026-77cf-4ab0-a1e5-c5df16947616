using System;
using Newtonsoft.Json;
using Travis_backend.CompanyDomain.Models;

namespace Travis_backend.CompanyDomain.ViewModels;

public class BusinessHourConfigViewModel
{
    public bool IsEnabled { get; set; }

    public WeeklyHours WeeklyHours { get; set; }

    public DateTime UpdatedAt { get; set; }

    [JsonConstructor]
    public BusinessHourConfigViewModel(bool isEnabled, WeeklyHours weeklyHours, DateTime updatedAt)
    {
        IsEnabled = isEnabled;
        WeeklyHours = weeklyHours;
        UpdatedAt = updatedAt;
    }

    public BusinessHourConfigViewModel(BusinessHourConfig businessHourConfig)
    {
        IsEnabled = businessHourConfig.IsEnabled;

        WeeklyHours = businessHourConfig.WeeklyHours;

        UpdatedAt = businessHourConfig.UpdatedAt;
    }
}