﻿using System.Security.Cryptography;
using System.Text;

namespace Sleekflow.Core.Infra.Helpers;

public static class AesHelper
{
    public static string Encrypt(string plainText, string cryptoKey)
    {
        var iv = GenerateIv();

        var aes = Aes.Create();
        var sha256 = SHA256.Create();
        aes.Key = sha256.ComputeHash(Encoding.UTF8.GetBytes(cryptoKey));
        aes.IV = Encoding.UTF8.GetBytes(iv);

        var dataByteArray = Encoding.UTF8.GetBytes(plainText);

        using var ms = new MemoryStream();
        using var cs = new CryptoStream(ms, aes.CreateEncryptor(), CryptoStreamMode.Write);

        cs.Write(dataByteArray, 0, dataByteArray.Length);
        cs.FlushFinalBlock();

        var result = Convert.ToBase64String(ms.ToArray()) + iv;

        return result;
    }

    public static string Decrypt(string cipher, string cryptoKey)
    {
        if (string.IsNullOrWhiteSpace(cipher))
        {
            return null;
        }

        string plaintext = null;

        try
        {
            var cipherWithoutIv = cipher.Substring(0, cipher.Length - 16);
            var iv = cipher.Substring(cipher.Length - 16, 16);

            using var aes = Aes.Create();
            var sha256 = SHA256.Create();
            aes.Key = sha256.ComputeHash(Encoding.UTF8.GetBytes(cryptoKey));
            aes.IV = Encoding.UTF8.GetBytes(iv);

            var decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

            // Create the streams used for decryption.
            using var msDecrypt = new MemoryStream(Convert.FromBase64String(cipherWithoutIv));
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var srDecrypt = new StreamReader(csDecrypt);

            plaintext = srDecrypt.ReadToEnd();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }

        return plaintext;
    }

    private static string GenerateIv()
    {
        var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789=";
        var stringChars = new char[16];

        for (int i = 0; i < stringChars.Length; i++)
        {
            var random = new Random();
            stringChars[i] = chars[random.Next(chars.Length)];
        }

        return new string(stringChars);
    }
}