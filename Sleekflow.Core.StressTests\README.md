# Sleekflow.Core.StressTests

This project contains stress tests for Sleekflow Core services using JMeter DSL with .NET integration.

## Overview

The stress tests are designed to validate the TravisBackend's performance under load using Abstracta's JMeter DSL for .NET, which provides a programmatic way to create JMeter test plans using C# & Nunit.

## Prerequisites

- .NET 8.0 SDK
- Abstracta.JmeterDsl (v0.7.0): https://abstracta.github.io/jmeter-dotnet-dsl/
- Java 8 runtime
- JMeter

## Project Structure

```
Sleekflow.Core.StressTests/
├── Logging/
│   └── LoggerConfig.cs      # Serilog configuration
├── Constants/               # Test constants and configurations
├── ...Domain/        # Domain-specific test implementations
└── *.cs                    # Test class files
```

## Writing Stress Tests

### Basic Structure

```csharp
[TestFixture]
public class YourStressTest
{
    private ILogger _logger;

    [OneTimeSetUp]
    public void Setup()
    {
        _logger = LoggerConfig.CreateLogger();
    }

    [Test]
    public void StressTest()
    {
        var testPlan = JMeter.TestPlan(
            JMeter.ThreadGroup(10, 60, // 10 threads for 60 seconds
                JMeter.HttpSampler("https://your-api-endpoint.com")
                    .Post()
                    .Body("{\"key\": \"value\"}")
            )
        );

        var stats = testPlan.Run();

        _logger.Information("Test Statistics: {Stats}", stats);

        Assert.That(stats.Overall.SampleTimePercentile99, Is.LessThan(TimeSpan.FromSeconds(1)));
    }
}
```

## Best Practices

1. **Isolation**: Each test should be independent and not rely on the state of other tests.
2. **Configuration**: Use environment variables or configuration files for test parameters.
3. **Logging**: Utilize structured logging to capture test execution details and metrics.
4. **Assertions**: Include meaningful assertions for response times, error rates, and throughput.
5. **Clean Up**: Implement proper cleanup in `[OneTimeTearDown]` or `[TearDown]` methods.

## Example Test Scenarios

1. **Load Testing**: Test system behavior under expected load
2. **Stress Testing**: Test system behavior under extreme load
3. **Spike Testing**: Test system behavior under sudden increases in load
4. **Endurance Testing**: Test system behavior over extended periods

## Troubleshooting

1. **Common Issues**
   - JMeter initialization failures
   - Network connectivity issues
   - Resource constraints

2. **Debugging**
   - Run tests in debug mode
   - Add as many logging statements as needed
   - Use breakpoints for debugging test code
   - Check console log
   - Review resource usage, google log and application insight during test execution

## Contributing

1. Follow the existing test structure
2. Include appropriate logging
3. Add meaningful assertions
4. Document any new test scenarios
5. Update this README as needed
