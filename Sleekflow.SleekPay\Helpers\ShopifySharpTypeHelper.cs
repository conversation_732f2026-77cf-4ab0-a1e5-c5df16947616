using System;
using System.Text.RegularExpressions;
using ShopifySharp;

namespace Sleekflow.SleekPay.Helpers;

/// <summary>
/// A helper class to map ShopifySharp GraphQL objects to ShopifySharp objects.
/// </summary>
/// <remarks>
/// To be backward compatible with the existing records, so we can't use the ShopiySharp GraphQL objects directly.
/// </remarks>
public static partial class ShopifySharpTypeHelper
{
    public static ProductVariant BuildShopifyProductVariantFromEdge(ShopifySharp.GraphQL.ProductVariantEdge productVariantEdge)
    {
        ArgumentNullException.ThrowIfNull(productVariantEdge);
        ArgumentNullException.ThrowIfNull(productVariantEdge.node);

        long productVariantIdAsLong;
        var productVariantIdMatch = ShopifyProductVariantIdRegex().Match(productVariantEdge.node.id);
        if (productVariantIdMatch.Success)
        {
            productVariantIdAsLong = long.Parse(productVariantIdMatch.Groups[1].Value);
        }
        else
        {
            throw new InvalidOperationException("Unable to match product variant Id");
        }

        var shopifyProductVariant = new ProductVariant()
        {
            Id = productVariantIdAsLong,
            Title = productVariantEdge.node.title,
            SKU = productVariantEdge.node.sku,
            Position = productVariantEdge.node.position,
            InventoryPolicy = productVariantEdge.node.inventoryPolicy?.ToString(),
            Price = productVariantEdge.node.price,
            CompareAtPrice = productVariantEdge.node.compareAtPrice,
            CreatedAt = productVariantEdge.node.createdAt,
            UpdatedAt = productVariantEdge.node.updatedAt,
            Taxable = productVariantEdge.node.taxable,
            TaxCode = productVariantEdge.node.taxCode,
            InventoryQuantity = productVariantEdge.node.inventoryQuantity
        };

        if (productVariantEdge.node.inventoryItem?.id != null)
        {
            long inventoryItemIdAsLong;
            var inventoryItemIdMatch = ShopifyInventoryItemIdRegex().Match(productVariantEdge.node.inventoryItem?.id);
            if (inventoryItemIdMatch.Success)
            {
                inventoryItemIdAsLong = long.Parse(inventoryItemIdMatch.Groups[1].Value);
            }
            else
            {
                throw new InvalidOperationException("Unable to match inventory item Id");
            }

            shopifyProductVariant.InventoryItemId = inventoryItemIdAsLong;
        }

        return shopifyProductVariant;
    }

    [GeneratedRegex(@"gid:\/\/shopify\/ProductVariant\/(\d+)")]
    private static partial Regex ShopifyProductVariantIdRegex();

    [GeneratedRegex(@"gid:\/\/shopify\/InventoryItem\/(\d+)")]
    private static partial Regex ShopifyInventoryItemIdRegex();
}