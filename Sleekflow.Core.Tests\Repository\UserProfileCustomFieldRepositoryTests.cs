using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Models;
using Travis_backend.Database;
using Travis_backend.Database.DataAccessLayer;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Z.EntityFramework.Extensions;

namespace Sleekflow.Core.Tests.Repository;

public class UserProfileCustomFieldRepositoryTests
{
    private ApplicationDbContext _appDbContext;
    private Mock<IDbContextService> _dbContextServiceMock;
    private IRepository<UserProfileCustomField> _userProfileCustomFieldRepository;
    private IRepository<CompanyCustomUserProfileField> _companyCustomUserProfileFieldRepository;

    [SetUp]
    public async Task Setup()
    {
        var dbName = Guid.NewGuid().ToString();

        // Initialize in-memory DbContexts with the same database name to share data
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: dbName)
            .Options;
        _appDbContext = new ApplicationDbContext(options);
        await SeedDataAsync();

        // Setup the DbContextService mock
        _dbContextServiceMock = new Mock<IDbContextService>();
        _dbContextServiceMock.Setup(s => s.GetDbContext()).Returns(_appDbContext);

        // Initialize the repository
        var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });

        _userProfileCustomFieldRepository = new Repository<UserProfileCustomField>(
            _dbContextServiceMock.Object,
            loggerFactory.CreateLogger<Repository<UserProfileCustomField>>());

        _companyCustomUserProfileFieldRepository = new Repository<CompanyCustomUserProfileField>(
            _dbContextServiceMock.Object,
            loggerFactory.CreateLogger<Repository<CompanyCustomUserProfileField>>());
    }

    [TearDown]
    public async Task TearDown()
    {
        // Dispose of the DbContext to free resources
        await _appDbContext.DisposeAsync();
    }

    #region Seed Data
    private async Task SeedDataAsync()
    {
        await _appDbContext.UserProfileCustomFields.AddRangeAsync(
            new List<UserProfileCustomField>()
            {
                new UserProfileCustomField
                {
                    Id = 1,
                    CompanyId = "companyId",
                    UserProfileId = "userprofileId1",
                    CompanyDefinedFieldId = "companyDefinedFieldId1",
                    Value = "value1"
                },
                new UserProfileCustomField
                {
                    Id = 2,
                    CompanyId = "companyId",
                    UserProfileId = "userprofileId2",
                    CompanyDefinedFieldId = "companyDefinedFieldId2",
                    Value = "value2"
                },
                new UserProfileCustomField
                {
                    Id = 3,
                    CompanyId = "companyId",
                    UserProfileId = "userprofileId3",
                    CompanyDefinedFieldId = "companyDefinedFieldId3",
                    Value = "value3"
                }
            });

        await _appDbContext.CompanyCustomUserProfileFields.AddRangeAsync(
            new List<CompanyCustomUserProfileField>()
            {
                new CompanyCustomUserProfileField
                {
                    Id = "companyDefinedFieldId1",
                    CompanyId = "companyId",
                    FieldName = "fieldName1",
                    Type = FieldDataType.SingleLineText,
                    Order = 1,
                    IsVisible = true,
                    IsEditable = true,
                    IsDefault = false,
                    IsDeletable = true,
                },
                new CompanyCustomUserProfileField
                {
                    Id = "companyDefinedFieldId2",
                    CompanyId = "companyId",
                    FieldName = "fieldName2",
                    Type = FieldDataType.Boolean,
                    Order = 2,
                    IsVisible = true,
                    IsEditable = true,
                    IsDefault = false,
                    IsDeletable = true,
                },
                new CompanyCustomUserProfileField
                {
                    Id = "companyDefinedFieldId3",
                    CompanyId = "companyId",
                    FieldName = "fieldName3",
                    Type = FieldDataType.MultiLineText,
                    Order = 3,
                    IsVisible = true,
                    IsEditable = true,
                    IsDefault = false,
                    IsDeletable = true
                }
            });
        await _appDbContext.SaveChangesAsync();
    }
    #endregion

    [TestCase(@"
    {
        ""customUserProfileFieldLinguals"": [
            {
                ""Id"": 0,
                ""CompanyCustomUserProfileFieldId"": ""ef93fb016d3c4fc49f07af9dc66d840a"",
                ""language"": ""en"",
                ""displayName"": ""DB_Custom_Field1""
            }
        ],
        ""customUserProfileFieldOptions"": [],
        ""type"": ""SingleLineText"",
        ""fieldName"": ""DB_Custom_Field1"",
        ""isVisible"": true,
        ""isEditable"": true,
        ""isDefault"": false,
        ""isDeletable"": true,
        ""fieldsCategory"": ""Custom"",
        ""order"": 999,
        ""companyId"": ""b6d7e442-38ae-4b9a-b100-2951729768bc"",
        ""id"": ""ef93fb016d3c4fc49f07af9dc66d840a""
    }
")]
    public async Task AddCompanyCustomUserProfileField(string companyCustomUserProfileFieldJson)
    {
        // Arrange
        var companyCustomUserProfileField = JsonConvert.DeserializeObject<CompanyCustomUserProfileField>(companyCustomUserProfileFieldJson);

        // Act
        await _companyCustomUserProfileFieldRepository.AddAsync(companyCustomUserProfileField);
        await _appDbContext.SaveChangesAsync();

        // Assert
        var result = await _companyCustomUserProfileFieldRepository.GetByIdAsync(companyCustomUserProfileField.Id);
        Assert.IsNotNull(result);
        Assert.That(result.Id, Is.EqualTo(companyCustomUserProfileField.Id));
    }

    [TestCase]
    public async Task DeleteUserProfileCustomFieldsByBatch()
    {
        Expression<Func<CompanyCustomUserProfileField, bool>> companyCustomUserProfileFieldsPredicate =
            x => x.CompanyId == "companyId";

        Expression<Func<UserProfileCustomField, bool>> predicate = x =>
            _companyCustomUserProfileFieldRepository.GetAll(companyCustomUserProfileFieldsPredicate)
                .Select(ccupf => ccupf.Id).Contains(x.CompanyDefinedFieldId);
        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.BatchSize = 500;
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        await _userProfileCustomFieldRepository.BulkDeleteByBatchAsync(
            predicate,
            x => x.Id,
            batchDeleteAction,
            batchSize: 500);

        var result = _userProfileCustomFieldRepository.GetAll()
            .Where(c => c.CompanyId == "companyId")
            .ToList();

        Assert.That(result.Count, Is.EqualTo(0));
    }
}