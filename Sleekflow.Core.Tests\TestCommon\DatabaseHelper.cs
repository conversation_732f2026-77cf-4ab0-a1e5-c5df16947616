using Microsoft.EntityFrameworkCore;
using Travis_backend.Database;
using Travis_backend.Database.Services;

namespace Sleekflow.Core.Tests.TestCommon;

public class DatabaseHelper
{
    public static DbContextOptions<T> GetDbContextOptions<T>(string connectionString)
        where T : DbContext
    {
        return new DbContextOptionsBuilder<T>()
            .UseSqlServer(connectionString + ";TrustServerCertificate=true;Encrypt=true")
            .Options;
    }

    public static T CreateDbContext<T>(string connectionString)
        where T : DbContext
    {
        var options = GetDbContextOptions<T>(connectionString);

        var ctor = typeof(T).GetConstructor([typeof(DbContextOptions<T>)]);
        if (ctor == null)
        {
            throw new InvalidOperationException(
                $"Type {typeof(T).Name} must have a constructor that accepts DbContextOptions<{typeof(T).Name}>."
            );
        }

        return (T)ctor.Invoke([options]);
    }

    public static DbContextService GetDbContextService(string connectionString)
    {
        return new DbContextService(
            new PersistenceContext(),
            new ApplicationDbContext(GetDbContextOptions<ApplicationDbContext>(connectionString)),
            new ApplicationReadDbContext(GetDbContextOptions<ApplicationReadDbContext>(connectionString)),
            new AnalyticDbContext(GetDbContextOptions<AnalyticDbContext>(connectionString)));
    }
}