using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Components.Configs.SleekflowCore;

public class ValuesConfig
{
    [JsonProperty("app_domain_name")]
    public string AppDomainName { get; set; }

    [JsonProperty("app_domain_name_v1")]
    public string AppDomainNameV1 { get; set; }

    [JsonProperty("share_link_function")]
    public string ShareLinkFunction { get; set; }

    [JsonProperty("sleekflow_api_gateway")]
    public string SleekflowApiGateway { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("sleekflow_public_api_url")]
    public string SleekflowPublicApiUrl { get; set; }

    [JsonProperty("sleekflow_public_api_key")]
    public string SleekflowPublicApiKey { get; set; }

    [JsonProperty("sleekflow_company_should_use_public_api")]
    public string SleekflowCompanyShouldUsePublicApi { get; set; }

    [Json<PERSON>roperty("sleekflow_pay_function")]
    public string SleekflowPayFunction { get; set; }

    [JsonProperty("sleekflow_core_azure_front_door_domain")]
    public string SleekflowCoreAzureFrontDoorDomain { get; set; }

    [JsonProperty("app_domain_name_v2")]
    public string AppDomainNameV2 { get; set; }

    public ValuesConfig(
        string appDomainName,
        string appDomainNameV1,
        string shareLinkFunction,
        string sleekflowApiGateway,
        string sleekflowCompanyId,
        string sleekflowPublicApiUrl,
        string sleekflowPublicApiKey,
        string sleekflowCompanyShouldUsePublicApi,
        string sleekflowPayFunction,
        string sleekflowCoreAzureFrontDoorDomain,
        string appDomainNameV2)
    {
        AppDomainName = appDomainName;
        AppDomainNameV1 = appDomainNameV1;
        ShareLinkFunction = shareLinkFunction;
        SleekflowApiGateway = sleekflowApiGateway;
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowPublicApiUrl = sleekflowPublicApiUrl;
        SleekflowPublicApiKey = sleekflowPublicApiKey;
        SleekflowCompanyShouldUsePublicApi = sleekflowCompanyShouldUsePublicApi;
        SleekflowPayFunction = sleekflowPayFunction;
        SleekflowCoreAzureFrontDoorDomain = sleekflowCoreAzureFrontDoorDomain;
        AppDomainNameV2 = appDomainNameV2;
    }
}