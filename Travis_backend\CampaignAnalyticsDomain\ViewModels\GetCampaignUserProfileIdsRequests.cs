﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Travis_backend.CampaignAnalyticsDomain.Constants;
using Travis_backend.CampaignAnalyticsDomain.Models;

namespace Travis_backend.CampaignAnalyticsDomain.ViewModels;

public abstract class GetCampaignUserProfileIdsRequest : IValidatableObject
{
    [Required]
    public string Status { get; set; }

    public ReplyWindow ReplyWindow { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (!CampaignMessageStatuses.All.Contains(Status))
        {
            yield return new ValidationResult(
                "Illegal message status.",
                new[] { nameof(Status) });
        }

        if (Status != CampaignMessageStatuses.Bounced && ReplyWindow is null)
        {
            yield return new ValidationResult(
                $"ReplyWindow is required for status: {Status}.",
                new[] { nameof(ReplyWindow) });
        }
    }
}

public class GetCampaignUserProfileIdsByBroadcastRequest : GetCampaignUserProfileIdsRequest
{
    [Required]
    public string BroadcastCampaignId { get; set; }
}

public class GetCampaignUserProfileIdsByAnalyticTagRequest : GetCampaignUserProfileIdsRequest
{
    [Required]
    [MinLength(1)]
    public string AnalyticTag { get; set; }
}
