openapi: 3.0.1
info:
  title: Sleekflow internal Api
  description: Sleekflow internal Api
  version: internal
paths:
  /FlowHubIntegrator/Internals/Functions/AuthenticateZapierApiKey:
    post:
      tags:
        - FlowHubIntegratorInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyOutput'
  /FlowHubIntegrator/Internals/Functions/GetSchemafulObjectZapierSample:
    post:
      tags:
        - FlowHubIntegratorInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleOutput'
  /FlowHubIntegrator/Internals/Functions/GetSchemaIdByUniqueName:
    post:
      tags:
        - FlowHubIntegratorInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameOutput'
  /FlowHub/Internals/Commands/AddInternalNoteToContact:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AddInternalNoteToContactInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AddInternalNoteToContactInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AddInternalNoteToContactInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AddInternalNoteToContactInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/v2/SendMessage:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInputV2'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInputV2'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInputV2'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInputV2'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/SendMessage:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactCollaboratorRelationships:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactCollaboratorRelationshipsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactCollaboratorRelationshipsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactCollaboratorRelationshipsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactCollaboratorRelationshipsInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactConversationStatus:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactConversationStatusInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactConversationStatusInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactConversationStatusInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactConversationStatusInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactLabelRelationships:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactLabelRelationshipsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactLabelRelationshipsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactLabelRelationshipsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactLabelRelationshipsInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactListRelationships:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactListRelationshipsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactListRelationshipsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactListRelationshipsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactListRelationshipsInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactOwnerRelationships:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactOwnerRelationshipsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactOwnerRelationshipsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactOwnerRelationshipsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactOwnerRelationshipsInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactProperties:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/CreateContact:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/CreateContactWithSalesforceUserMapping:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactWithSalesforceUserMappingInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactWithSalesforceUserMappingInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactWithSalesforceUserMappingInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactWithSalesforceUserMappingInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/GetConversionLastMessages:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationLastMessagesInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationLastMessagesInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationLastMessagesInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationLastMessagesInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactPropertiesByPropertyKey:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesByPropertyKeyInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesByPropertyKeyInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesByPropertyKeyInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesByPropertyKeyInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/CreateTicket:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateTicketInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateTicketInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateTicketInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateTicketInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/GetTicketExportData:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetTicketExportDataInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetTicketExportDataInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetTicketExportDataInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetTicketExportDataInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/GetContactProperties:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactPropertiesInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactPropertiesInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactPropertiesInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactPropertiesInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/GetContactPropertyValueByContactIds:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactPropertyValueByContactIdsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactPropertyValueByContactIdsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactPropertyValueByContactIdsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactPropertyValueByContactIdsInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/GetFacebookPageAccessToken:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetFacebookPageAccessTokenInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetFacebookPageAccessTokenInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetFacebookPageAccessTokenInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetFacebookPageAccessTokenInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/GetInstagramPageAccessToken:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetInstagramPageAccessTokenInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetInstagramPageAccessTokenInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetInstagramPageAccessTokenInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetInstagramPageAccessTokenInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/GetIntelligentHubUsageFilter:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetIntelligentHubUsageFilterInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetIntelligentHubUsageFilterInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetIntelligentHubUsageFilterInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetIntelligentHubUsageFilterInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/GetIsCompanyHasAiPocPlan:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetIsCompanyHasAiPocPlanInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetIsCompanyHasAiPocPlanInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetIsCompanyHasAiPocPlanInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetIsCompanyHasAiPocPlanInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Functions/GetContact:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactOutput'
  /FlowHub/Internals/Functions/GetContactDetail:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactDetailOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactDetailOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactDetailOutput'
  /FlowHub/Internals/Functions/GetContactsByBatch:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactsByBatchInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactsByBatchInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactsByBatchInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactsByBatchInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactsByBatchOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactsByBatchOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactsByBatchOutput'
  /FlowHub/Internals/Functions/GetStaff:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffOutput'
  /FlowHub/Internals/Functions/GetContactLists:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsOutput'
  /FlowHub/Internals/Functions/GetContactConversation:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationOutput'
  /FlowHub/Internals/Functions/GetContactIdByPhoneNumber:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByPhoneNumberInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByPhoneNumberInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByPhoneNumberInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByPhoneNumberInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
  /FlowHub/Internals/Functions/GetOrCreateContactIdByPhoneNumber:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByPhoneNumberInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByPhoneNumberInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByPhoneNumberInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByPhoneNumberInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
  /FlowHub/Internals/Functions/GetContactIdByEmail:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByEmailInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByEmailInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByEmailInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByEmailInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
  /FlowHub/Internals/Functions/GetOrCreateContactIdByEmail:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByEmailInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByEmailInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByEmailInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByEmailInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
  /FlowHub/Internals/Functions/GetConversationChannelLastMessage:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageOutput'
  /FlowHub/Internals/Functions/GetCompanyTimeZoneId:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetCompanyTimeZoneIdInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetCompanyTimeZoneIdInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetCompanyTimeZoneIdInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetCompanyTimeZoneIdInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                type: string
            application/json:
              schema:
                type: string
            text/json:
              schema:
                type: string
  /FlowHub/Internals/ServicesCall/EmailNotificationService/SendWorkflowInfiniteLoopEmail:
    post:
      tags:
        - FlowHubInternalsServicesCall
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendWorkflowInfiniteLoopEmailInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendWorkflowInfiniteLoopEmailInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendWorkflowInfiniteLoopEmailInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendWorkflowInfiniteLoopEmailInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/ServicesCall/EmailNotificationService/SendExecutionUsageReachedThresholdEmail:
    post:
      tags:
        - FlowHubInternalsServicesCall
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendExecutionUsageReachedThresholdEmailInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendExecutionUsageReachedThresholdEmailInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendExecutionUsageReachedThresholdEmailInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendExecutionUsageReachedThresholdEmailInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/ServicesCall/GetCompanyUsageCycle:
    post:
      tags:
        - FlowHubInternalsServicesCall
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetCompanyUsageCycleInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetCompanyUsageCycleInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetCompanyUsageCycleInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetCompanyUsageCycleInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/ServicesCall/ScaleWorkflowExecutionLimit:
    post:
      tags:
        - FlowHubInternalsServicesCall
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ScaleWorkflowExecutionLimitInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ScaleWorkflowExecutionLimitInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ScaleWorkflowExecutionLimitInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ScaleWorkflowExecutionLimitInput'
      responses:
        '200':
          description: OK
  /IntelligentHub/Internals/Commands/GetUserProfile:
    post:
      tags:
        - IntelligentHubInternalCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.GetUserProfileInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.GetUserProfileInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.GetUserProfileInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.GetUserProfileInput'
      responses:
        '200':
          description: OK
  /IntelligentHub/Internals/Commands/CreateUserProfile:
    post:
      tags:
        - IntelligentHubInternalCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.CreateUserProfileInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.CreateUserProfileInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.CreateUserProfileInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.CreateUserProfileInput'
      responses:
        '200':
          description: OK
  /MessagingHub/Internals/UpsertTikTokConfig:
    post:
      tags:
        - MessagingHubInternal
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Controllers.MessagingHubInternalController.UpsertTikTokConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Controllers.MessagingHubInternalController.UpsertTikTokConfigOutput'
  /MessagingHub/Internals/TikTokWebhook:
    post:
      tags:
        - MessagingHubInternal
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokWebhookPayload'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Controllers.MessagingHubInternalController.TikTokWebhookOutput'
  /StressTest/Internals/Messaging/SetupWhatsappCloudApiStressTestChannel:
    post:
      tags:
        - StressTestInternalMessaging
      responses:
        '200':
          description: OK
  /StressTest/Internals/Messaging/TeardownDeleteContacts:
    post:
      tags:
        - StressTestInternalMessaging
      responses:
        '200':
          description: OK
  /StressTest/Internals/Messaging/GetMessageCount:
    post:
      tags:
        - StressTestInternalMessaging
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.StressTestDomain.Messaging.StressTestInternalMessagingController.GetMessageCountInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.StressTestDomain.Messaging.StressTestInternalMessagingController.GetMessageCountInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.StressTestDomain.Messaging.StressTestInternalMessagingController.GetMessageCountInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.StressTestDomain.Messaging.StressTestInternalMessagingController.GetMessageCountInput'
      responses:
        '200':
          description: OK
  /TicketingHub/Internals/CreateTicketConversationIndicator:
    post:
      tags:
        - TicketingHubInternal
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.CreateTicketConversationIndicatorInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.CreateTicketConversationIndicatorOutput'
  /TicketingHub/Internals/GetTicketExportData:
    post:
      tags:
        - TicketingHubInternal
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.GetTicketExportDataInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.GetTicketExportDataOutput'
  /UserEventHub/Internals/Commands/GetUserProfileIds:
    post:
      tags:
        - UserEventHubInternalCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.UserEventAnalyticsHub.Model.GetUserProfileIdsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.UserEventAnalyticsHub.Model.GetUserProfileIdsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.UserEventAnalyticsHub.Model.GetUserProfileIdsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.UserEventAnalyticsHub.Model.GetUserProfileIdsInput'
      responses:
        '200':
          description: OK
components:
  schemas:
    Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyInput:
      type: object
      properties:
        api_key:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyOutput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameInput:
      type: object
      properties:
        schema_unique_name:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameOutput:
      type: object
      properties:
        schemaId:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleInput:
      type: object
      properties:
        schema_unique_name:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleOutput:
      type: object
      properties:
        schemafulObjectZapierViewModel:
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ActionMessageObject:
      type: object
      properties:
        flow_token:
          type: string
          nullable: true
        flow_action_data:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.AddInternalNoteToContactInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        content:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.AudioMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProvider'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ButtonReplyMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactConversation:
      type: object
      properties:
        conversation_status:
          type: string
          nullable: true
        conversation_id:
          type: string
          nullable: true
        last_message_channel:
          type: string
          nullable: true
        last_message_channel_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactDetail:
      type: object
      properties:
        contact:
          type: object
          additionalProperties: { }
          nullable: true
        contact_owner:
          type: object
          additionalProperties: { }
          nullable: true
        lists:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactList'
          nullable: true
        conversation:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactConversation'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactList:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        added_at:
          type: string
          format: date-time
        is_imported:
          type: boolean
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObject:
      type: object
      properties:
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectAddress'
          nullable: true
        birthday:
          type: string
          nullable: true
        emails:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectEmail'
          nullable: true
        name:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectName'
        org:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectOrg'
        ims:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectIm'
          nullable: true
        phones:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectPhone'
          nullable: true
        urls:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectUrl'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectAddress:
      type: object
      properties:
        street:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        state:
          type: string
          nullable: true
        zip:
          type: string
          nullable: true
        country:
          type: string
          nullable: true
        country_code:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectEmail:
      type: object
      properties:
        email:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectIm:
      type: object
      properties:
        service:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectName:
      type: object
      properties:
        formatted_name:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        middle_name:
          type: string
          nullable: true
        suffix:
          type: string
          nullable: true
        prefix:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectOrg:
      type: object
      properties:
        company:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        department:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectPhone:
      type: object
      properties:
        phone:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        wa_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectUrl:
      type: object
      properties:
        url:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ConversationChannelLastMessage:
      type: object
      properties:
        conversation_id:
          type: string
          nullable: true
        message_id:
          type: string
          nullable: true
        message_unique_id:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_type:
          type: string
          nullable: true
        message_delivery_type:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        channel_id:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.CreateContactInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        phone_number:
          type: string
          nullable: true
        contact_properties:
          type: object
          additionalProperties: { }
          nullable: true
        workflow_trigger_event_body_event_name:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.CreateContactWithSalesforceUserMappingInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        phone_number:
          type: string
          nullable: true
        salesforce_user_id_for_mapping:
          type: string
          nullable: true
        salesforce_connection_id:
          type: string
          nullable: true
        contact_properties:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.CreateTicketInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        message_origin_channel:
          type: string
          nullable: true
        channel_type:
          type: string
          nullable: true
        channel_identity_id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        due_date:
          type: string
          format: date-time
          nullable: true
        type_id:
          type: string
          nullable: true
        priority_id:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        is_unassigned:
          type: boolean
        assignment_strategy:
          type: string
          nullable: true
        team_id:
          type: string
          nullable: true
        staff_id:
          type: string
          nullable: true
        assignment_counter:
          type: integer
          format: int64
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.CurrencyMessageObject:
      type: object
      properties:
        fallback_value:
          type: string
          nullable: true
        code:
          type: string
          nullable: true
        amount_1000:
          type: integer
          format: int32
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.DateTimeMessageObject:
      type: object
      properties:
        fallback_value:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.DocumentMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProvider'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.FacebookMessengerMessageObject:
      type: object
      properties:
        message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerMessageObject'
        messaging_type:
          type: string
          nullable: true
        tag:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerAttachmentObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        payload:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerPayloadObject'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerMessageObject:
      type: object
      properties:
        attachment:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerAttachmentObject'
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerPayloadObject:
      type: object
      properties:
        url:
          type: string
          nullable: true
        is_reusable:
          type: boolean
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetCompanyTimeZoneIdInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetCompanyUsageCycleInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactConversationInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactConversationOutput:
      type: object
      properties:
        conversation:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactConversation'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactDetailOutput:
      type: object
      properties:
        contact:
          type: object
          additionalProperties: { }
          nullable: true
        contact_owner:
          type: object
          additionalProperties: { }
          nullable: true
        lists:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactList'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactIdByEmailInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
        state_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactIdByPhoneNumberInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
        state_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactIdOutput:
      type: object
      properties:
        contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactListsInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactListsOutput:
      type: object
      properties:
        lists:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactList'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactOutput:
      type: object
      properties:
        contact:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactPropertiesInput:
      type: object
      properties:
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactPropertyValueByContactIdsInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        contact_ids:
          type: array
          items:
            type: string
          nullable: true
        contact_property_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactsByBatchInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        last_contact_created_at:
          type: string
          format: date-time
          nullable: true
        last_contact_id:
          type: string
          nullable: true
        batch_size:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactsByBatchOutput:
      type: object
      properties:
        contacts:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactDetail'
          nullable: true
        next_batch:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.NextBatch'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        channel_id:
          type: string
          nullable: true
        conversation_id:
          type: string
          nullable: true
        is_sent_from_sleekflow:
          type: boolean
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageOutput:
      type: object
      properties:
        last_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ConversationChannelLastMessage'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetConversationLastMessagesInput:
      type: object
      properties:
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        target_channels:
          type: array
          items:
            type: string
          nullable: true
        target_channel_id:
          type: string
          nullable: true
        offset:
          type: integer
          format: int32
        limit:
          type: integer
          format: int32
        retrieval_window_timestamp:
          type: string
          format: date-time
          nullable: true
        agent_session_start_at_timestamp:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetFacebookPageAccessTokenInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        facebook_page_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetInstagramPageAccessTokenInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        instagram_page_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetIntelligentHubUsageFilterInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetIsCompanyHasAiPocPlanInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByEmailInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
        state_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByPhoneNumberInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
        state_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput:
      type: object
      properties:
        contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetStaffInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetStaffOutput:
      type: object
      properties:
        staff:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetTicketExportDataInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ImageMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProvider'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InstagramMessengerMessageObject:
      type: object
      properties:
        message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerMessageObject'
        messaging_type:
          type: string
          nullable: true
        tag:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerAttachmentDataObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        payload:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerPayloadObject'
        is_reusable:
          type: boolean
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerMessageObject:
      type: object
      properties:
        attachment:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerAttachmentDataObject'
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerPayloadObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        header:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectHeader'
        body:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectBody'
        footer:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectFooter'
        action:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectAction'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectAction:
      type: object
      properties:
        button:
          type: string
          nullable: true
        buttons:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionButton'
          nullable: true
        sections:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSection'
          nullable: true
        catalog_id:
          type: string
          nullable: true
        product_retailer_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionButton:
      type: object
      properties:
        type:
          type: string
          nullable: true
        reply:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionButtonReply'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionButtonReply:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSection:
      type: object
      properties:
        title:
          type: string
          nullable: true
        product_items:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSectionProductItem'
          nullable: true
        rows:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSectionRow'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSectionProductItem:
      type: object
      properties:
        product_retailer_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSectionRow:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectBody:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectFooter:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectHeader:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        video:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.VideoMessageObject'
        image:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ImageMessageObject'
        document:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.DocumentMessageObject'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveReplyMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        button_reply:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ButtonReplyMessageObject'
        list_reply:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ListReplyMessageObject'
        nfm_reply:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.NfmReplyMessageObject'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.LineMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ListReplyMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.LiveChatMessageObject:
      type: object
      properties:
        message:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.LocationMessageObject:
      type: object
      properties:
        latitude:
          type: number
          format: double
          nullable: true
        longitude:
          type: number
          format: double
          nullable: true
        name:
          type: string
          nullable: true
        address:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProvider:
      type: object
      properties:
        name:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        config:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfig'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfig:
      type: object
      properties:
        basic:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfigBasic'
        bearer:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfigBearer'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfigBasic:
      type: object
      properties:
        username:
          type: string
          nullable: true
        password:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfigBearer:
      type: object
      properties:
        bearer:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.MessageBody:
      type: object
      properties:
        audio_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AudioMessageObject'
        contacts_message:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObject'
          nullable: true
        currency_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CurrencyMessageObject'
        document_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.DocumentMessageObject'
        image_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ImageMessageObject'
        location_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.LocationMessageObject'
        reaction_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ReactionMessageObject'
        text_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.TextMessageObject'
        video_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.VideoMessageObject'
        date_time_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.DateTimeMessageObject'
        interactive_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObject'
        template_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.TemplateMessageObject'
        interactive_reply_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveReplyMessageObject'
        order_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.OrderMessageObject'
        facebook_messenger_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.FacebookMessengerMessageObject'
        instagram_messenger_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InstagramMessengerMessageObject'
        telegram_messenger_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.TelegramMessengerMessageObject'
        wechat_messenger_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.WeChatMessengerMessageObject'
        live_chat_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.LiveChatMessageObject'
        viber_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ViberMessageObject'
        line_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.LineMessageObject'
        sms_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SmsMessageObject'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.NextBatch:
      type: object
      properties:
        last_contact_created_at:
          type: string
          format: date-time
          nullable: true
        last_contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.NfmReplyMessageObject:
      type: object
      properties:
        name:
          type: string
          nullable: true
        body:
          type: string
          nullable: true
        response_json:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.OrderMessageObject:
      type: object
      properties:
        catalog_id:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        product_items:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.OrderMessageProductItem'
          nullable: true
        product_items_json:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.OrderMessageProductItem:
      type: object
      properties:
        product_retailer_id:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int32
          nullable: true
        item_price:
          type: number
          format: double
          nullable: true
        currency:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ReactionMessageObject:
      type: object
      properties:
        message_id:
          type: string
          nullable: true
        emoji:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ScaleWorkflowExecutionLimitInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SendExecutionUsageReachedThresholdEmailInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        threshold:
          type: number
          format: double
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SendMessageInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        channel:
          type: string
          nullable: true
        from_to:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInputFromTo'
        message_type:
          type: string
          nullable: true
        message_body:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MessageBody'
        delivery_type:
          type: integer
          format: int32
          nullable: true
        staff_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SendMessageInputFromTo:
      type: object
      properties:
        from_to_type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SendMessageInputFromToV2:
      type: object
      properties:
        channel_identity_id:
          type: string
          nullable: true
        to_contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SendMessageInputV2:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        channel:
          type: string
          nullable: true
        from_to:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInputFromToV2'
        message_type:
          type: string
          nullable: true
        message_body:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MessageBody'
        delivery_type:
          type: integer
          format: int32
          nullable: true
        staff_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SendWorkflowInfiniteLoopEmailInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SmsMessageObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.StateIdentity:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        object_id:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.TelegramMessengerMessageObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.TemplateMessageObject:
      type: object
      properties:
        template_name:
          type: string
          nullable: true
        language:
          type: string
          nullable: true
        components:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.TemplateMessageObjectComponent'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.TemplateMessageObjectComponent:
      type: object
      properties:
        type:
          type: string
          nullable: true
        sub_type:
          type: string
          nullable: true
        index:
          type: integer
          format: int32
        parameters:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.TemplateMessageObjectComponentParameter'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.TemplateMessageObjectComponentParameter:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        payload:
          type: string
          nullable: true
        image:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ImageMessageObject'
        audio:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AudioMessageObject'
        document:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.DocumentMessageObject'
        video:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.VideoMessageObject'
        location:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.LocationMessageObject'
        currency:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CurrencyMessageObject'
        date_time:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.DateTimeMessageObject'
        action:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ActionMessageObject'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.TextMessageObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactCollaboratorRelationshipsInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        add_staff_ids:
          type: array
          items:
            type: string
          nullable: true
        remove_staff_ids:
          type: array
          items:
            type: string
          nullable: true
        set_staff_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactConversationStatusInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactLabelRelationshipsInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        add_labels:
          type: array
          items:
            type: string
          nullable: true
        remove_labels:
          type: array
          items:
            type: string
          nullable: true
        set_labels:
          type: array
          items:
            type: string
          nullable: true
        is_create_if_not_exists:
          type: boolean
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactListRelationshipsInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        add_list_ids:
          type: array
          items:
            type: string
          nullable: true
        remove_list_ids:
          type: array
          items:
            type: string
          nullable: true
        set_list_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactOwnerRelationshipsInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        is_unassigned:
          type: boolean
        assignment_strategy:
          type: string
          nullable: true
        team_id:
          type: string
          nullable: true
        staff_id:
          type: string
          nullable: true
        assignment_counter:
          type: integer
          format: int64
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesByPropertyKeyInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_property_key_id:
          type: string
          nullable: true
        contact_property_key_value:
          type: string
          nullable: true
        properties_dict:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        properties_dict:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ViberMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.VideoMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProvider'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.WeChatMessengerMessageObject:
      type: object
      properties:
        msgtype:
          type: string
          nullable: true
        text:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.WeChatTextMessage'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.WeChatTextMessage:
      type: object
      properties:
        content:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.IntelligentHub.Model.CreateUserProfileInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.IntelligentHub.Model.GetUserProfileInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.MessagingHub.Model.TikTokConnectionDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        tik_tok_business_id:
          type: string
          nullable: true
        access_token:
          type: string
          nullable: true
        access_token_expiration:
          type: string
          format: date-time
        refresh_token:
          type: string
          nullable: true
        refresh_token_expiration:
          type: string
          format: date-time
        display_name:
          type: string
          nullable: true
        is_verified:
          type: boolean
        origin:
          type: string
          nullable: true
        username:
          type: string
          nullable: true
        profile_image:
          type: string
          nullable: true
        is_deleted:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.TicketingHub.Model.Channel:
      type: object
      properties:
        channel_type:
          type: string
          nullable: true
        channel_identity_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.TicketingHub.Model.MediaDto:
      type: object
      properties:
        filename:
          type: string
          nullable: true
        blob_name:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
        blob_type:
          type: string
          nullable: true
        download_url:
          type: string
          nullable: true
        expires_on:
          type: string
          format: date-time
          nullable: true
        content_type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.TicketingHub.Model.TicketDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        external_id:
          type: integer
          format: int64
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        status_id:
          type: string
          nullable: true
        priority_id:
          type: string
          nullable: true
        type_id:
          type: string
          nullable: true
        channel:
          $ref: '#/components/schemas/Sleekflow.Apis.TicketingHub.Model.Channel'
        medias:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.TicketingHub.Model.MediaDto'
          nullable: true
        due_date:
          type: string
          format: date-time
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
        assignee_id:
          type: string
          nullable: true
        assigned_team_ids:
          type: array
          items:
            type: string
          nullable: true
        associated_message_ids:
          type: array
          items:
            type: integer
            format: int64
          nullable: true
        url:
          type: string
          nullable: true
        resolution_time:
          type: string
          nullable: true
        resolved_at:
          type: string
          format: date-time
          nullable: true
        created_at:
          type: string
          format: date-time
        created_by:
          type: string
          nullable: true
        created_source:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.TicketingHub.Model.TicketUser:
      type: object
      properties:
        sleekflow_user_id:
          type: string
          nullable: true
        sleekflow_user_identity_id:
          type: string
          nullable: true
        sleekflow_role:
          type: string
          nullable: true
        sleekflow_team_ids:
          type: array
          items:
            type: string
          nullable: true
        company_rbac_enabled:
          type: boolean
      additionalProperties: false
    Sleekflow.Apis.UserEventAnalyticsHub.Model.BroadcastMessageStatus:
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
      type: integer
      format: int32
    Sleekflow.Apis.UserEventAnalyticsHub.Model.Condition:
      type: object
      properties:
        conditionOperator:
          $ref: '#/components/schemas/Sleekflow.Apis.UserEventAnalyticsHub.Model.SupportedOperator'
        timeValueType:
          $ref: '#/components/schemas/Sleekflow.Apis.UserEventAnalyticsHub.Model.TimeValueType'
        nextOperator:
          $ref: '#/components/schemas/Sleekflow.Apis.UserEventAnalyticsHub.Model.SupportedNextOperator'
        broadcastMessageStatus:
          $ref: '#/components/schemas/Sleekflow.Apis.UserEventAnalyticsHub.Model.BroadcastMessageStatus'
        containHashTag:
          type: string
          nullable: true
        fieldName:
          type: string
          nullable: true
        values:
          type: array
          items:
            type: string
          nullable: true
        companyMessageTemplateId:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.UserEventAnalyticsHub.Model.GetUserProfileIdsInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.UserEventAnalyticsHub.Model.Condition'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.UserEventAnalyticsHub.Model.SupportedNextOperator:
      enum:
        - 0
        - 1
      type: integer
      format: int32
    Sleekflow.Apis.UserEventAnalyticsHub.Model.SupportedOperator:
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        - 10
        - 11
        - 12
        - 13
        - 14
        - 15
        - 16
        - 17
        - 18
        - 19
        - 20
        - 21
        - 22
        - 23
        - 24
        - 25
        - 26
        - 500
        - 501
        - 502
        - 503
        - 504
        - 505
        - 506
        - 507
        - 509
        - 510
        - 511
        - 512
        - 513
        - 514
        - 515
        - 516
        - 517
        - 518
      type: integer
      format: int32
    Sleekflow.Apis.UserEventAnalyticsHub.Model.TimeValueType:
      enum:
        - 0
        - 1
        - 2
        - 3
      type: integer
      format: int32
    Travis_backend.MessagingHubDomain.Controllers.MessagingHubInternalController.TikTokWebhookOutput:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Controllers.MessagingHubInternalController.UpsertTikTokConfigInput:
      type: object
      properties:
        tik_tok_connection:
          $ref: '#/components/schemas/Sleekflow.Apis.MessagingHub.Model.TikTokConnectionDto'
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Controllers.MessagingHubInternalController.UpsertTikTokConfigOutput:
      type: object
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokAdReferral:
      type: object
      properties:
        advertiser_id:
          type: string
          nullable: true
        ad_id:
          type: string
          nullable: true
        timestamp:
          type: integer
          format: int64
        ad_name:
          type: string
          nullable: true
        embed_url:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokChatPrompt:
      type: object
      properties:
        title:
          type: string
          nullable: true
        content:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokImageContent:
      type: object
      properties:
        media_id:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokReadInfo:
      type: object
      properties:
        last_read_timestamp:
          type: integer
          format: int64
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokReferencedMessageInfo:
      type: object
      properties:
        referenced_message_id:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokReferral:
      type: object
      properties:
        source:
          type: string
          nullable: true
        ad:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokAdReferral'
        short_link:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokShortLinkReferral'
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokSharePostContent:
      type: object
      properties:
        embed_url:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokShortLinkReferral:
      type: object
      properties:
        ref:
          type: string
          nullable: true
        prefilled_message:
          type: string
          nullable: true
        prefilled_message_audit_status:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokSuggestedQuestion:
      type: object
      properties:
        question:
          type: string
          nullable: true
        answer:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokTextContent:
      type: object
      properties:
        body:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokUser:
      type: object
      properties:
        id:
          type: string
          nullable: true
        role:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokWebhookContent:
      type: object
      properties:
        timestamp:
          type: integer
          format: int64
        from:
          type: string
          nullable: true
        to:
          type: string
          nullable: true
        unique_identifier:
          type: string
          nullable: true
        from_user:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokUser'
        to_user:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokUser'
        conversation_id:
          type: string
          nullable: true
        message_id:
          type: string
          nullable: true
        referenced_message_info:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokReferencedMessageInfo'
        text:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokTextContent'
        image:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokImageContent'
        share_post:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokSharePostContent'
        type:
          type: string
          nullable: true
        scene_type:
          type: integer
          format: int32
          nullable: true
        is_follower:
          type: boolean
          nullable: true
        read:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokReadInfo'
        referral:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokReferral'
        auto_message_id:
          type: string
          nullable: true
        auto_message_type:
          type: string
          nullable: true
        audit_status:
          type: string
          nullable: true
        auto_message_action:
          type: string
          nullable: true
        welcome_message:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokWelcomeMessage'
        suggested_question:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokSuggestedQuestion'
        chat_prompt:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokChatPrompt'
      additionalProperties: false
      description: Unified TikTok webhook content model that handles all webhook types
    Travis_backend.MessagingHubDomain.Models.TikTokWebhookMessage:
      type: object
      properties:
        client_key:
          type: string
          nullable: true
        event:
          type: string
          nullable: true
        create_time:
          type: integer
          format: int64
        user_openid:
          type: string
          nullable: true
        content:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokWebhookPayload:
      type: object
      properties:
        event_type:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        tik_tok_business_id:
          type: string
          nullable: true
        webhook_message:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokWebhookMessage'
        content:
          $ref: '#/components/schemas/Travis_backend.MessagingHubDomain.Models.TikTokWebhookContent'
      additionalProperties: false
    Travis_backend.MessagingHubDomain.Models.TikTokWelcomeMessage:
      type: object
      properties:
        content:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.StressTestDomain.Messaging.StressTestInternalMessagingController.GetMessageCountInput:
      type: object
      properties:
        startDate:
          type: string
          format: date-time
        endDate:
          type: string
          format: date-time
      additionalProperties: false
    Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.CreateTicketConversationIndicatorInput:
      type: object
      properties:
        ticket:
          $ref: '#/components/schemas/Sleekflow.Apis.TicketingHub.Model.TicketDto'
        operation:
          type: string
          nullable: true
        ticket_user:
          $ref: '#/components/schemas/Sleekflow.Apis.TicketingHub.Model.TicketUser'
      additionalProperties: false
    Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.CreateTicketConversationIndicatorOutput:
      type: object
      additionalProperties: false
    Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.GetTicketExportDataInput:
      type: object
      properties:
        company_id:
          type: string
          nullable: true
        ticket_id:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.GetTicketExportDataOutput:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Travis_backend.TicketingHubDomain.Models.TicketsExport.TicketExportData'
      additionalProperties: false
    Travis_backend.TicketingHubDomain.Models.TicketsExport.TicketExportData:
      type: object
      properties:
        created_at:
          type: string
          format: date-time
        created_by:
          type: string
          nullable: true
        created_by_email:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        priority:
          type: string
          nullable: true
        due_date:
          type: string
          format: date-time
          nullable: true
        type:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        first_assignee:
          type: string
          nullable: true
        first_assignee_email:
          type: string
          nullable: true
        current_assignee:
          type: string
          nullable: true
        current_assignee_email:
          type: string
          nullable: true
        contact_name:
          type: string
          nullable: true
        contact_phone_number:
          type: string
          nullable: true
        contact_email:
          type: string
          nullable: true
        first_respondent:
          type: string
          nullable: true
        first_respondent_email:
          type: string
          nullable: true
        first_response_timestamp:
          type: string
          format: date-time
          nullable: true
        resolution_agent:
          type: string
          nullable: true
        resolution_agent_email:
          type: string
          nullable: true
        resolution_timestamp:
          type: string
          format: date-time
          nullable: true
        ticket_reassigned_before_resolution:
          type: boolean
      additionalProperties: false
  securitySchemes:
    Bearer:
      type: http
      description: Please insert JWT with Bearer into field
      scheme: bearer
      bearerFormat: Bearer _YourToken_
security:
  - Bearer: [ ]