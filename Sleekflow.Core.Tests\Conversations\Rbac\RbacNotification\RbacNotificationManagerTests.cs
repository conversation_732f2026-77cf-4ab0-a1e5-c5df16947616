using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationSettingsConstants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Rbac.RbacNotification
{
    [TestFixture]
    public class RbacNotificationManagerTests
    {
        private RbacNotificationManager _rbacNotificationManager;

        [SetUp]
        public void SetUp()
        {
            _rbacNotificationManager = new RbacNotificationManager();
        }

        #region Unassigned Conversation Tests

        [Test]
        public void staff_with_receive_unassigned_conversation_notification_setting_enabled_receives_notification_for_unassigned_conversation()
        {
            // Arrange
            var conversation = new Conversation();

            var role = new RbacRole
            {
                SleekflowRoleName = "Admin",
                SleekflowCompanyId = "sleekflow",
                RbacRolePermissions =
                [
                    RbacInboxSettings.UnassignedConversationNotifications
                ]
            };

            var staff = new StaffAccessControlAggregate
            {
                RbacRoles = [role]
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, staff);

            // Assert
            Assert.IsTrue(result);
        }

        [Test]
        public void staff_without_receive_unassigned_conversation_notification_setting_enabled_does_not_receive_notification_for_unassigned_conversation()
        {
            // Arrange
            var conversation = new Conversation();

            var role = new RbacRole
            {
                SleekflowRoleName = "Admin",
                SleekflowCompanyId = "sleekflow",
                RbacRolePermissions =
                []
            };

            var staff = new StaffAccessControlAggregate
            {
                RbacRoles = [role]
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, staff);

            // Assert
            Assert.IsFalse(result);
        }

        #endregion

        #region Contact Owner Tests

        [Test]
        public void contact_owner_receives_notification_for_assigned_conversation()
        {
            var staff = new StaffAccessControlAggregate
            {
                StaffId = 1
            };

            // Arrange
            var conversation = new Conversation
            {
                AssigneeId = staff.StaffId
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, staff);

            // Assert
            Assert.IsTrue(result);
        }

        [Test]
        public void collaborator_receives_notification_for_no_contact_owner_conversation()
        {
            var staff = new StaffAccessControlAggregate
            {
                StaffId = 1
            };

            var collaborator = new AdditionalAssignee
            {
                AssigneeId = staff.StaffId
            };

            // Arrange
            var conversation = new Conversation
            {
                AssigneeId = null,
                AdditionalAssignees = [collaborator]
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, staff);

            // Assert
            Assert.IsTrue(result);
        }

        #endregion

        #region Collaborator Tests

        [Test]
        public void collaborator_receives_notification_for_conversation_has_contact_owner()
        {
            var contactOwner = new StaffAccessControlAggregate
            {
                StaffId = 1
            };

            var collaborator = new StaffAccessControlAggregate
            {
                StaffId = 2
            };

            var collaborators = new List<AdditionalAssignee>
            {
                new ()
                {
                    AssigneeId = collaborator.StaffId
                }
            };

            // Arrange
            var conversation = new Conversation
            {
                AssigneeId = contactOwner.StaffId,
                AdditionalAssignees = collaborators
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, collaborator);

            // Assert

            Assert.IsTrue(result);
        }

        //staff_in_multiple_teams_receives_notification_when_conversation_assigned_to_their_team_but_has_no_contact_owner

        [Test]
        public void staff_does_not_receive_notification_when_conversation_assigned_to_their_team_but_someone_else_is_contact_owner()
        {
            var team = new TeamAccessControlAggregate
            {
                Id = 1
            };

            var contactOwner = new StaffAccessControlAggregate
            {
                StaffId = 1,
            };

            var staff = new StaffAccessControlAggregate
            {
                StaffId = 2,
                AssociatedTeams = [team]
            };

            // Arrange
            var conversation = new Conversation
            {
                AssigneeId = contactOwner.StaffId,
                AssignedTeamId = team.Id
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, staff);

            // Assert
            Assert.IsFalse(result);
        }

        #endregion

        #region Team Assignment Tests

        [Test]
        public void staff_receives_notification_when_conversation_assigned_to_their_team_but_has_no_contact_owner()
        {
            var team = new TeamAccessControlAggregate
            {
                Id = 1
            };

            var staff = new StaffAccessControlAggregate
            {
                StaffId = 2,
                AssociatedTeams = [team]
            };

            // Arrange
            var conversation = new Conversation
            {
                AssigneeId = null,
                AssignedTeamId = team.Id
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, staff);

            // Assert
            Assert.IsTrue(result);
        }

        [Test]
        public void staff_does_not_receive_notification_when_conversation_assigned_to_team_that_they_are_not_associated_to()
        {
            var team = new TeamAccessControlAggregate
            {
                Id = 1,
                TeamMemberStaffIds = [1, 2]
            };

            var staff = new StaffAccessControlAggregate
            {
                StaffId = 3,
            };

            // Arrange
            var conversation = new Conversation
            {
                AssigneeId = null,
                AssignedTeamId = team.Id
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, staff);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public void staff_with_receive_unassigned_conversation_notification_setting_enabled_does_not_receive_notification_for_conversation_with_contact_owner_but_without_team()
        {
            var role = new RbacRole
            {
                SleekflowRoleName = "Admin",
                SleekflowCompanyId = "sleekflow",
                RbacRolePermissions =
                    []
            };

            var staff = new StaffAccessControlAggregate
            {
                StaffId = 3,
                RbacRoles = [role]
            };

            var team = new TeamAccessControlAggregate
            {
                Id = 1
            };

            var contactOwner = new StaffAccessControlAggregate
            {
                StaffId = 1,
            };

            var teamMember = new StaffAccessControlAggregate
            {
                StaffId = 2,
                AssociatedTeams = [team]
            };

            // Arrange
            var conversation = new Conversation
            {
                AssigneeId = contactOwner.StaffId,
                AssignedTeamId = null
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, staff);

            // Assert
            Assert.IsFalse(result);
        }

        #endregion

        #region Mention Tests

        [Test]
        public void staff_receives_notification_for_mentioned_conversation()
        {
            var staff = new StaffAccessControlAggregate
            {
                StaffId = 1
            };

            // Arrange
            var conversation = new Conversation
            {
                Mentions =
                [
                    new Mention
                    {
                        MentionedStaffId = 1,
                        CreatedAt = DateTime.UtcNow
                    }
                ]
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, staff);

            // Assert
            Assert.IsTrue(result);
        }

        [Test]
        public void staff_does_not_receive_notification_when_mentioned_after_48_hours()
        {
            var staff = new StaffAccessControlAggregate
            {
                StaffId = 1
            };

            // Arrange
            var conversation = new Conversation
            {
                Mentions =
                [
                    new Mention
                    {
                        MentionedStaffId = 1,
                        CreatedAt = DateTime.UtcNow.AddDays(-2).AddSeconds(-1)
                    }
                ]
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, staff);

            // Assert
            Assert.IsFalse(result);
        }

        #endregion

        #region Edge Cases

        [Test]
        public void contact_owner_receives_notification_when_not_member_of_assigned_team()
        {
            var team = new TeamAccessControlAggregate
            {
                Id = 1
            };

            var contactOwner = new StaffAccessControlAggregate
            {
                StaffId = 1,
            };

            var teamMember = new StaffAccessControlAggregate
            {
                StaffId = 2,
                AssociatedTeams = [team]
            };

            // Arrange
            var conversation = new Conversation
            {
                AssigneeId = contactOwner.StaffId,
                AssignedTeamId = team.Id
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, teamMember);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public void collaborator_receives_notification_when_has_assigned_eam()
        {
            var team = new TeamAccessControlAggregate
            {
                Id = 1,
                TeamMemberStaffIds = [1, 2]
            };

            var collaboratorA = new StaffAccessControlAggregate
            {
                StaffId = 1
            };

            var collaboratorB = new StaffAccessControlAggregate
            {
                StaffId = 2
            };

            var collaborators = new List<AdditionalAssignee>
            {
                new ()
                {
                    AssigneeId = collaboratorA.StaffId
                },
                new ()
                {
                    AssigneeId = collaboratorB.StaffId
                }
            };

            // Arrange
            var conversation = new Conversation
            {
                AdditionalAssignees = collaborators,
                AssignedTeamId = team.Id
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, collaboratorA);

            // Assert

            Assert.IsTrue(result);
        }

        [Test]
        public void staff_in_multiple_teams_receives_notification_when_conversation_assigned_to_one_of_their_team_but_has_no_contact_owner()
        {
            // Arrange
            var teamA = new TeamAccessControlAggregate
            {
                Id = 1,
                TeamMemberStaffIds = [1]
            };

            var teamB = new TeamAccessControlAggregate
            {
                Id = 2,
                TeamMemberStaffIds = [1]
            };

            var teamC = new TeamAccessControlAggregate
            {
                Id = 3,
                TeamMemberStaffIds = [1]
            };

            var staff = new StaffAccessControlAggregate
            {
                StaffId = 1,
                AssociatedTeams = [teamA, teamB, teamC]
            };

            var conversation = new Conversation
            {
                AssigneeId = null,
                AssignedTeamId = teamC.Id
            };

            // Act
            var result = _rbacNotificationManager.CanReceive(conversation, staff);

            // Assert
            Assert.IsTrue(result);
        }

        // [Test]
        // public void staff_does_not_receive_notification_when_conversation_has_null_team_assignment()
        // {
        //     // Arrange
        //     // TODO: Create conversation with null team assignment
        //     var conversation = CreateConversationWithNullTeam();
        //
        //     // TODO: Create staff with team membership
        //     var staff = CreateStaffAsTeamMember(123L);
        //
        //     // Act
        //     var result = _rbacNotificationManager.CanReceive(conversation, staff);
        //
        //     // Assert
        //     Assert.IsFalse(result);
        // }

        #endregion
    }
}