using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Newtonsoft.Json;
using ShopifySharp;
using ShopifySharp.Filters;
using ShopifySharp.GraphQL;
using Sleekflow.SleekPay.Model;
using Sleekflow.SleekPay.RedisAccessor.Interface;
using Sleekflow.SleekPay.SQLDatabase;
using Sleekflow.SleekPay.SQLDatabase.Entity;
using Stripe;
using Stripe.Checkout;
using Sleekflow.SleekPay.Helpers;

namespace Sleekflow.SleekPay.Service;

public interface IStripePaymentService
{
    Session CreatePaymentIntent(GenerateStripPaymentRequest request, StripePaymentConfig stripePaymentConfig, out long applicationFeeAmount);
    Task ApplyRefundToShopifyOrder(ShopifyRefund shopifyRefund);
}

public class StripePaymentService : IStripePaymentService
{
    private readonly IRedisCacheAccessor _redisCacheAccessor;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IShareLinkService _shareLinkService;

    public StripePaymentService(IRedisCacheAccessor redisCacheAccessor,
        ApplicationDbContext appDbContext,
        IShareLinkService shareLinkService)
    {
        _redisCacheAccessor = redisCacheAccessor;
        _appDbContext = appDbContext;
        _shareLinkService = shareLinkService;
    }

    public Session CreatePaymentIntent(GenerateStripPaymentRequest request, StripePaymentConfig stripePaymentConfig, out long applicationFeeAmount)
    {
        var platformCountry = request.PlatformCountry;

        SetStripeApiKeyByPlatformCountry(platformCountry);

        if (request.ShopifyId.HasValue && request.IsReserveInventory)
        {
            var shopifyConfig = _appDbContext.ConfigShopifyConfigs.FirstOrDefault(x => x.Id == request.ShopifyId && x.CompanyId == request.CompanyId);

            var graphService = new GraphService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

            foreach (var lineItem in request.LineItems)
            {
                var variantIdString = lineItem.Metadata?["variantId"];

                if (long.TryParse(variantIdString, out var variantId))
                {
                    var query = @$"
                        {{
                            productVariants(first: 1, query: ""id:{variantId}"") {{
                                edges {{
                                    node {{
                                        id
                                        title
                                        sku
                                        position
                                        inventoryPolicy
                                        price
                                        compareAtPrice
                                        createdAt
                                        updatedAt
                                        taxable
                                        taxCode
                                        inventoryQuantity
                                        inventoryItem {{
                                            id
                                        }}
                                    }}
                                }}
                            }}
                        }}";

                    ProductVariantConnection productVariantConnection = null;

                    try
                    {
                        productVariantConnection = graphService.SendAsync<ProductVariantConnection>(query).Result;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Cannot get product variant: {ex.Message}");
                        continue;
                    }

                    ShopifySharp.ProductVariant productVariant;

                    if (productVariantConnection != null 
                        && productVariantConnection.edges != null
                        && productVariantConnection.edges.Any())
                    {
                        productVariant = ShopifySharpTypeHelper.BuildShopifyProductVariantFromEdge(productVariantConnection.edges.First());

                        if (productVariant.InventoryQuantity < lineItem.Quantity)
                            throw new Exception($"{lineItem.Name} is sold out");
                    }
                }
            }
        }

        var lineItems = new List<SessionLineItemOptions>();

        foreach (var lineItem in request.LineItems)
        {
            var item = new SessionLineItemOptions
            {
                Name = lineItem.Name,
                Amount = (long) ((lineItem.Amount - lineItem.TotalDiscount) * 100),
                Quantity = lineItem.Quantity,
                Currency = lineItem.Currency,
                Description = (string.IsNullOrEmpty(lineItem.Description)) ? null : lineItem.Description,
            };

            if (lineItem.ImageUrls != null && lineItem.ImageUrls.Any(x => x != null))
                item.Images = lineItem.ImageUrls.Where(x => x != null).ToList();
            lineItems.Add(item);
        }

        var total = lineItems.Sum(x => x.Amount * x.Quantity);

        // ReSharper disable once PossibleInvalidOperationException
        applicationFeeAmount = (long) (total * stripePaymentConfig.ApplicationFeeRate);

        var currency = lineItems.FirstOrDefault()?.Currency.ToLower();

        switch (currency)
        {
            case "usd":
                applicationFeeAmount += 30;
                break;
            case "hkd":
                applicationFeeAmount += 280;
                break;
            case "myr":
                applicationFeeAmount += 173;
                break;
            case "sgd":
                applicationFeeAmount += 58;
                break;
            case "gbp":
                applicationFeeAmount += 29;
                break;
        }

        var paymentResultKey = _shareLinkService.GenerateRandomKey(15);

        var options = new SessionCreateOptions
        {
            LineItems = lineItems,
            PaymentIntentData = new SessionPaymentIntentDataOptions
            {
                ApplicationFeeAmount = applicationFeeAmount,
                OnBehalfOf = stripePaymentConfig.AccountId,
                TransferData = new SessionPaymentIntentDataTransferDataOptions
                {
                    Destination = stripePaymentConfig.AccountId,
                },
            },
            Mode = "payment",
            SuccessUrl = $"{Environment.GetEnvironmentVariable("RedirectDomain")}/en-us/payment-result/success?key={paymentResultKey}",
            CancelUrl = $"{Environment.GetEnvironmentVariable("RedirectDomain")}/en-us/payment-result/cancel?key={paymentResultKey}",
            ExpiresAt = request.ExpiredAt
        };
        if (currency == "hkd")
        {
            options.PaymentMethodTypes = new List<string>
            {
                "card", "alipay", "wechat_pay"
            };
            options.PaymentMethodOptions = new SessionPaymentMethodOptionsOptions
            {
                WechatPay = new SessionPaymentMethodOptionsWechatPayOptions
                {
                    Client = "web"
                }
            };
        }

        if (stripePaymentConfig.IsShippingEnabled)
        {
            if (stripePaymentConfig.ShippingAllowedCountries != null && stripePaymentConfig.ShippingAllowedCountries.Any())
                options.ShippingAddressCollection = new SessionShippingAddressCollectionOptions
                {
                    AllowedCountries = stripePaymentConfig.ShippingAllowedCountries
                };
            else
            {
                options.ShippingAddressCollection = new SessionShippingAddressCollectionOptions
                {
                    AllowedCountries = GetSupportedStripeRegionIsoCodes()
                };
            }

            if (stripePaymentConfig.ShippingOptions != null && stripePaymentConfig.ShippingOptions.Any())
            {
                var matchedShippingOptions = new List<SessionShippingOptionOptions>();
                ;

                foreach (var shippingOption in stripePaymentConfig.ShippingOptions)
                {
                    var originalTotal = (decimal)(total / 100.0);

                    if (shippingOption.Condition.ShippingOptionConditionType == ShippingOptionConditionType.AlwaysShow ||
                        (originalTotal >= shippingOption.Condition.OrderPriceRangeLower && originalTotal <= shippingOption.Condition.OrderPriceRangeUpper))
                    {
                        matchedShippingOptions.Add(new SessionShippingOptionOptions
                        {
                            ShippingRateData = new SessionShippingOptionShippingRateDataOptions
                            {
                                Type = "fixed_amount",
                                FixedAmount = new SessionShippingOptionShippingRateDataFixedAmountOptions
                                {
                                    Amount = (long) (shippingOption.ShippingFee * 100),
                                    Currency = lineItems.FirstOrDefault()?.Currency
                                },
                                DisplayName = shippingOption.DisplayName,
                                DeliveryEstimate = new SessionShippingOptionShippingRateDataDeliveryEstimateOptions
                                {
                                    Minimum = new SessionShippingOptionShippingRateDataDeliveryEstimateMinimumOptions
                                    {
                                        Unit = shippingOption.DeliveryEstimateUnit.ToString(),
                                        Value = shippingOption.DeliveryEstimateMin
                                    },
                                    Maximum = new SessionShippingOptionShippingRateDataDeliveryEstimateMaximumOptions
                                    {
                                        Unit = shippingOption.DeliveryEstimateUnit.ToString(),
                                        Value = shippingOption.DeliveryEstimateMax
                                    }
                                }
                            }
                        });
                    }
                }

                if (matchedShippingOptions.Any())
                {
                    options.ShippingOptions = matchedShippingOptions;
                }
            }
        }

        if (!string.IsNullOrEmpty(request.CustomerId))
            options.Customer = request.CustomerId;
        else
            options.CustomerEmail = request.CustomerEmail;

        var service = new SessionService();
        Session session;
        try
        {
            session = service.Create(options);
            if (session.StripeResponse.StatusCode == HttpStatusCode.BadRequest)
            {
                options.PaymentMethodTypes = null;
                options.PaymentMethodOptions = null;
                session = service.Create(options);
            }
        }
        catch (Exception)
        {
            options.PaymentMethodTypes = null;
            options.PaymentMethodOptions = null;
            session = service.Create(options);
        }

        var paymentResultContent = JsonConvert.SerializeObject(new StripePaymentResult()
        {
            CompanyId = request.CompanyId,
            CompanyName = _appDbContext.CompanyCompanies.FirstOrDefault(x => x.Id == request.CompanyId)?.CompanyName,
            LineItems = request.LineItems,
            PaymentIntentId = session.PaymentIntentId,
        });
        _redisCacheAccessor.SaveToCache(paymentResultKey, paymentResultContent, TimeSpan.FromDays(180));

        if (request.ShopifyId.HasValue && request.IsReserveInventory)
        {
            var shopifyConfig = _appDbContext.ConfigShopifyConfigs.FirstOrDefault(x => x.Id == request.ShopifyId && x.CompanyId == request.CompanyId);
            var graphService = new GraphService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

            //Reserve inventory
            foreach (var lineItem in request.LineItems)
            {
                var variantIdString = lineItem.Metadata?["variantId"];

                if (long.TryParse(variantIdString, out var variantId))
                {
                    var query = @$"
                        {{
                            productVariants(first: 1, query: ""id:{variantId}"") {{
                                edges {{
                                    node {{
                                        id
                                        title
                                        sku
                                        position
                                        inventoryPolicy
                                        price
                                        compareAtPrice
                                        createdAt
                                        updatedAt
                                        taxable
                                        taxCode
                                        inventoryQuantity
                                        inventoryItem {{
                                            id
                                        }}
                                    }}
                                }}
                            }}
                        }}";

                    ProductVariantConnection productVariantConnection = null;

                    try
                    {
                        productVariantConnection = graphService.SendAsync<ProductVariantConnection>(query).Result;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Cannot get product variant: {ex.Message}");
                        continue;
                    }

                    ShopifySharp.ProductVariant productVariant = null;

                    if (productVariantConnection != null 
                        && productVariantConnection.edges != null
                        && productVariantConnection.edges.Any())
                    {
                        productVariant = ShopifySharpTypeHelper.BuildShopifyProductVariantFromEdge(productVariantConnection.edges.First());

                        if (productVariant.InventoryQuantity < lineItem.Quantity)
                            throw new Exception($"{lineItem.Name} is sold out");
                    }

                    if (productVariant is null 
                        || !productVariant.InventoryItemId.HasValue)
                    {
                        continue;
                    }
                        
                    var filter = new InventoryLevelListFilter()
                    {
                        InventoryItemIds = new List<long>()
                        {
                            productVariant.InventoryItemId.Value
                        }
                    };

                    var inventoryService = new InventoryLevelService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

                    var levels = inventoryService.ListAsync(filter).Result;
                    var level = levels.Items.First();

                    // Untracked inventory item returns null available
                    if (level.Available is not null)
                    {
                        level.Available -= lineItem.Quantity;
                        var inventoryLevel = inventoryService.SetAsync(level).Result;
                    }
                }
            }
        }

        return session;
    }

    public async Task ApplyRefundToShopifyOrder(ShopifyRefund shopifyRefund)
    {
        var shopifyConfig = _appDbContext.ConfigShopifyConfigs.FirstOrDefault(x => x.Id == shopifyRefund.ShopifyId);

        if (shopifyConfig != null)
        {
            var transactionService = new TransactionService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
            var transaction = new Transaction()
            {
                Kind = "refund",
                Amount = shopifyRefund.RefundAmount
            };

            await transactionService.CreateAsync(shopifyRefund.ShopifyOrderId.Value, transaction);

            var orderService = new ShopifySharp.OrderService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

            var order = await orderService.GetAsync(shopifyRefund.ShopifyOrderId.Value);

            string refundReason = null;
            switch (shopifyRefund.RefundReason)
            {
                case RefundReason.Duplicate:
                    refundReason = "Duplicate";
                    break;
                case RefundReason.Fraudulent:
                    refundReason = "Fraudulent";
                    break;
                case RefundReason.RequestedByCustomer:
                    refundReason = "Requested By Customer";
                    break;
                case RefundReason.Custom:
                    refundReason = shopifyRefund.CustomRefundReason;
                    break;
            }

            if (order.Note == null)
            {
                order.Note = "Refund Reason: " + refundReason;
            }
            else
            {
                order.Note = order.Note + " " + "Refund Reason: " + refundReason;
            }

            await orderService.UpdateAsync((long)order.Id, order);
        }
    }

    private static void SetStripeApiKeyByPlatformCountry(string platformCountry)
    {
        switch (platformCountry.ToLower())
        {
            case "hk":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_HK");
                break;
            case "sg":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_SG");
                break;
            case "my":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_MY");
                break;
            case "gb":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_GB");
                break;
        }
    }

    private static List<string> GetSupportedStripeRegionIsoCodes()
    {
        return new List<string>
        {
            "AC", "AD", "AE", "AF", "AG", "AI", "AL", "AM", "AO",
            "AQ", "AR", "AT", "AU", "AW", "AX", "AZ", "BA", "BB",
            "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BL", "BM",
            "BN", "BO", "BQ", "BR", "BS", "BT", "BV", "BW", "BY",
            "BZ", "CA", "CD", "CF", "CG", "CH", "CI", "CK", "CL",
            "CM", "CN", "CO", "CR", "CV", "CW", "CY", "CZ", "DE",
            "DJ", "DK", "DM", "DO", "DZ", "EC", "EE", "EG", "EH",
            "ER", "ES", "ET", "FI", "FJ", "FK", "FO", "FR", "GA",
            "GB", "GD", "GE", "GF", "GG", "GH", "GI", "GL", "GM",
            "GN", "GP", "GQ", "GR", "GS", "GT", "GU", "GW", "GY",
            "HK", "HN", "HR", "HT", "HU", "ID", "IE", "IL", "IM",
            "IN", "IO", "IQ", "IS", "IT", "JE", "JM", "JO", "JP",
            "KE", "KG", "KH", "KI", "KM", "KN", "KR", "KW", "KY",
            "KZ", "LA", "LB", "LC", "LI", "LK", "LR", "LS", "LT",
            "LU", "LV", "LY", "MA", "MC", "MD", "ME", "MF", "MG",
            "MK", "ML", "MM", "MN", "MO", "MQ", "MR", "MS", "MT",
            "MU", "MV", "MW", "MX", "MY", "MZ", "NA", "NC", "NE",
            "NG", "NI", "NL", "NO", "NP", "NR", "NU", "NZ", "OM",
            "PA", "PE", "PF", "PG", "PH", "PK", "PL", "PM", "PN",
            "PR", "PS", "PT", "PY", "QA", "RE", "RO", "RS", "RU",
            "RW", "SA", "SB", "SC", "SE", "SG", "SH", "SI", "SJ",
            "SK", "SL", "SM", "SN", "SO", "SR", "SS", "ST", "SV",
            "SX", "SZ", "TA", "TC", "TD", "TF", "TG", "TH", "TJ",
            "TK", "TL", "TM", "TN", "TO", "TR", "TT", "TV", "TW",
            "TZ", "UA", "UG", "US", "UY", "UZ", "VA", "VC", "VE",
            "VG", "VN", "VU", "WF", "WS", "XK", "YE", "YT", "ZA",
            "ZM", "ZW", "ZZ"
        };
    }
}