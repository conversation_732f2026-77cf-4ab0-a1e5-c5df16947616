﻿#nullable enable
using Newtonsoft.Json;

namespace Travis_backend.AnalyticsDomain.Models;

public class ConversationAnalyticsDimensionData
{
    [JsonProperty("is_by_working_hour")]
    public string? IsByWorkingHour { get; set; }

    [JsonProperty("asp_net_user_id")]
    public string? UserIdentityId { get; set; }

    [JsonProperty("channel_identity_id")]
    public string? ChannelIdentityId { get; set; }

    [JsonProperty("channel_type")]
    public string? ChannelType { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string? CompanyId { get; set; }

    [JsonProperty("user_profile_id")]
    public string? UserProfileId { get; set; }

    [JsonConstructor]
    public ConversationAnalyticsDimensionData(
        string? isByWorkingHour,
        string? userIdentityId,
        string? channelIdentityId,
        string? channelType,
        string? companyId,
        string? userProfileId)
    {
        IsByWorkingHour = isByWorkingHour;
        UserIdentityId = userIdentityId;
        ChannelIdentityId = channelIdentityId;
        ChannelType = channelType;
        CompanyId = companyId;
        UserProfileId = userProfileId;
    }
}