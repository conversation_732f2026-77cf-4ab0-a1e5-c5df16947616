using Travis_backend.ConversationDomain.ConversationSpecifications.RbacConversationSpecifications.QueryObjects;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Rbac.RbacQueryObjects;

[TestFixture]
public class MentionedConversationsQueryUnitTests
{
    private StaffAccessControlAggregate _staff;

    [SetUp]
    public void Setup()
    {
        _staff = new StaffAccessControlAggregate
        {
            StaffId = 15201,
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea"
        };
    }

    [Test]
    public void can_generate_valid_sql()
    {
        // Arrange
        var query = new MentionedConversationsQuery(_staff);

        // Act
        var sql = query.ToSql("C");

        // Assert
        Assert.That(sql, Does.Contain(_staff.CompanyId));
        Assert.That(sql, Does.Contain(_staff.StaffId.ToString()));

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(sql.Trim());
    }
}