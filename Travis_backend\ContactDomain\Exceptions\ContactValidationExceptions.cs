using System;
using System.Collections.Generic;
using System.Linq;

namespace Travis_backend.ContactDomain.Exceptions
{
    public class InvalidPhoneNumberFormatException : ArgumentException
    {
        public InvalidPhoneNumberFormatException(string phoneNumber)
            : base($"Invalid phone number format: '{phoneNumber}'. Please use international format (+1234567890)") { }
    }

    public class InvalidContactOwnerException : ArgumentException
    {
        public InvalidContactOwnerException(string contactOwner)
            : base($"Invalid contact owner: '{contactOwner}'.") { }
    }

    public class InvalidTeamAssignmentException : ArgumentException
    {
        public InvalidTeamAssignmentException(string teamId)
            : base($"Invalid team assignment: '{teamId}'") { }
    }
}