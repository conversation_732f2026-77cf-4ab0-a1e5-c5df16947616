### Rbac Login - (DEV only)

POST https://localhost:5000/auth0/account/getusertoken
content-type: application/json-patch+json

{
  "username": "",
  "password": ""
}

> {% client.global.set("token", response.body.accessToken); %}

### Rbac Conversation Unassignment
POST https://{{host}}/v2/conversation/assignee/{{conversation_Id}}
content-type: application/json
Authorization: Bearer {{token}}

{
  "assignmentType":"Unassigned"
}

### Rbac Conversation Assignment - Assign To Me Without Team
POST https://{{host}}/v2/conversation/assignee/{{conversation_Id}}
content-type: application/json
Authorization: Bearer {{token}}

{
  "assignmentType": "SpecificPerson",
  "staffId": "0631ebca-6f87-455e-b15d-55dc90c200dd"
}

### Rbac Conversation Assignment - Assign To Me With Team
POST https://{{host}}/v2/conversation/assignee/{{conversation_Id}}
content-type: application/json
Authorization: Bearer {{token}}

{
  "assignmentType": "SpecificGroup",
  "teamAssignmentType": "SpecificPerson",
  "teamId": 20325,
  "staffId": "11a3c118-bba2-4667-bbb2-3fb40e34e32b"
}

### Rbac Conversation Assignment - Assign Via Team - Assign to Team Inbox
POST https://{{host}}/v2/conversation/assignee/{{conversation_Id}}
content-type: application/json
Authorization: Bearer {{token}}

{
  "assignmentType": "SpecificGroup",
  "teamAssignmentType": "Unassigned",
  "teamId": 20325
}

### Rbac Conversation Assignment - Assign Via Team - Auto Assign By Queue
POST https://{{host}}/v2/conversation/assignee/{{conversation_Id}}
content-type: application/json
Authorization: Bearer {{token}}

{
  "assignmentType": "SpecificGroup",
  "teamAssignmentType": "QueueBased",
  "teamId": 20325
}

### Rbac Conversation Assignment - Assign to User without Team
POST https://{{host}}/v2/conversation/assignee/{{conversation_Id}}
content-type: application/json
Authorization: Bearer {{token}}

{
  "assignmentType": "SpecificPerson",
  "staffId": "11a3c118-bba2-4667-bbb2-3fb40e34e32b"
}

### Rbac Conversation Collaborator Assignment
POST https://{{host}}/v2/conversation/collaborator/{{conversation_Id}}
content-type: application/json
Authorization: Bearer {{token}}

{
  "additionalAssigneeIds": [
    "11a3c118-bba2-4667-bbb2-3fb40e34e32b",
    "0631ebca-6f87-455e-b15d-55dc90c200dd"
  ]
}

### Rbac Conversation Collaborator Unassignment
POST https://{{host}}/v2/conversation/collaborator/{{conversation_Id}}
content-type: application/json
Authorization: Bearer {{token}}

{
  "additionalAssigneeIds": []
}



