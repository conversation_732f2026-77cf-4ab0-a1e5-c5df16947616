﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class SleekFlowDatabaseMetric : IDashboardMetric
{
    private readonly Output<string>? _resourceId;
    private readonly Output<string>? _resourceName;

    public SleekFlowDatabaseMetric(Output<string>? resourceId, Output<string>? resourceName)
    {
        _resourceId = resourceId;
        _resourceName = resourceName;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs = new[]
                {
                    new Dictionary<string, object>()
                    {
                        {
                            "name", "options"
                        },
                        {
                            "isOptional", true
                        }
                    },
                    new Dictionary<string, object>()
                    {
                        {
                            "name", "sharedTimeRange"
                        },
                        {
                            "isOptional", true
                        }
                    }
                },
                Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                Settings =
                {
                    new Dictionary<string, object>()
                    {
                        {
                            "content", new Dictionary<string, object>()
                            {
                                {
                                    "options", new Dictionary<string, object>()
                                    {
                                        {
                                            "chart", new Dictionary<string, object>()
                                            {
                                                {
                                                    "metrics", new[]
                                                    {
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 3
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "CPU percentage"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "cpu_percent"
                                                            },
                                                            {
                                                                "namespace", "microsoft.sql/servers/databases"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 3
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "Data IO percentage"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "physical_data_read_percent"
                                                            },
                                                            {
                                                                "namespace", "microsoft.sql/servers/databases"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 4
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "CPU percentage"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "cpu_percent"
                                                            },
                                                            {
                                                                "namespace", "microsoft.sql/servers/databases"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 4
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "Data IO percentage"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "physical_data_read_percent"
                                                            },
                                                            {
                                                                "namespace", "microsoft.sql/servers/databases"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    "title", "SleekFlow Database"
                                                },
                                                {
                                                    "titleKind", 2
                                                },
                                                {
                                                    "visualization", new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "axisVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "x", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 2
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    "y", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 1
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "chartType", 2
                                                        },
                                                        {
                                                            "disablePinning", true
                                                        },
                                                        {
                                                            "legendVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "hideSubtitle", false
                                                                },
                                                                {
                                                                    "isVisible", true
                                                                },
                                                                {
                                                                    "position", 2
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}