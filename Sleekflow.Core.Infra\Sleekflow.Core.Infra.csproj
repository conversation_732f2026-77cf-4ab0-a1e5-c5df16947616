<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <OutputType>Exe</OutputType>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Search.Documents" Version="11.6.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Pulumi" Version="3.70.0" />
        <PackageReference Include="Pulumi.Auth0" Version="3.5.0" />
        <PackageReference Include="Pulumi.AzureNative" Version="2.74.0" />
        <PackageReference Include="Pulumi.Docker" Version="4.5.5" />
        <PackageReference Include="Pulumi.Random" Version="4.16.3" />
        <PackageReference Include="YamlDotNet" Version="16.3.0" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

</Project>
