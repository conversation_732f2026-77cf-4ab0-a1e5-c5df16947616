using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.Conversations.Rbac.RbacTestData;

public class CanViewTestData
{
    private static readonly string CompanyId = "sleekflow";
    private static readonly long StaffId = 1;
    private static readonly TeamAccessControlAggregate TeamA = new TeamAccessControlAggregate
    {
        Id = 1,
        TeamMemberStaffIds = new List<long> { 1, 2, 3 }
    };

    private static StaffAccessControlAggregate CreateStaff(List<string>? permissions = null)
    {
        permissions ??= [];

        return new StaffAccessControlAggregate
        {
            StaffId = StaffId,
            CompanyId = CompanyId,
            AssociatedTeams = new List<TeamAccessControlAggregate> { TeamA },
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = CompanyId,
                    RbacRolePermissions = permissions
                }
            }
        };
    }

    private static Conversation CreateConversation(
        string id,
        long? assigneeId = null,
        long? assignedTeamId = null,
        List<AdditionalAssignee>? additionalAssignees = null,
        List<Mention>? mentions = null)
    {
        return new Conversation
        {
            Id = id,
            CompanyId = CompanyId,
            AssigneeId = assigneeId,
            AssignedTeamId = assignedTeamId,
            AdditionalAssignees = additionalAssignees,
            Mentions = mentions
        };
    }

    public static IEnumerable<TestCaseData> GetNoViewConversationPermissionTestCases()
    {
        var staff = CreateStaff();

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), false).SetName(
            "Assigned_to_me_conversation_cannot_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), false)
            .SetName("Team_member_as_contact_owner_conversation_cannot_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_associated_team_conversation_cannot_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), false)
            .SetName("Assigned_to_associated_team_conversation_cannot_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "Unassigned_conversation_cannot_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            false).SetName("Assigned_as_collaborator_conversation_cannot_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_cannot_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_cannot_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_unassigned_conversation_cannot_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            false).SetName("Mentioned_conversation_cannot_view");
    }
    public static IEnumerable<TestCaseData> GetAllConversationViewConversationPermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacViewConversationsPermissions.AssignedToMe,
            RbacViewConversationsPermissions.AssignedToMyTeam,
            RbacViewConversationsPermissions.AllAssignedConversations,
            RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
            RbacViewConversationsPermissions.AllUnassignedConversations
        };

        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "Assigned_to_non-associated_team_user_conversation_can_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                true).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                true)
            .SetName("Assigned_to_associated_team_conversation_can_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            true).SetName(
            "Assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "Unassigned_conversation_can_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName(
            "Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_view");
    }
    public static IEnumerable<TestCaseData> GetAssignedToMeViewConversationPermissionTestCases()
    {
        var permissions = new List<string> {RbacViewConversationsPermissions.AssignedToMe};
        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), false)
            .SetName("Team_member_as_contact_owner_conversation_cannot_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_associated_team_conversation_cannot_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                false)
            .SetName("Assigned_to_associated_team_conversation_cannot_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "Unassigned_conversation_cannot_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName(
            "Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_cannot_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_cannot_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_unassigned_conversation_cannot_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_view");
    }
    public static IEnumerable<TestCaseData> GetAssignedToMeAndAssignedToMyTeamViewConversationPermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacViewConversationsPermissions.AssignedToMe, RbacViewConversationsPermissions.AssignedToMyTeam
        };
        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                true)
            .SetName("Assigned_to_associated_team_conversation_can_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "Unassigned_conversation_cannot_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName(
            "Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_view");
    }

    public static IEnumerable<TestCaseData>
        GetAssignedToMeAndAllAssignedConversationsViewConversationPermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacViewConversationsPermissions.AssignedToMe, RbacViewConversationsPermissions.AllAssignedConversations
        };
        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("team_member_as_contact_owner_conversation_can_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("team_member_as_contact_owner_with_associated_team_conversation_can_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("team_member_as_contact_owner_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "assigned_to_non-associated_team_user_conversation_can_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                true).SetName(
                "assigned_to_other_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                true)
            .SetName("assigned_to_associated_team_conversation_can_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            true).SetName(
            "assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "unassigned_conversation_cannot_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "assignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName(
            "team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("team_member_as_collaborator_assigned_to_associated_team_conversation_can_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("team_member_as_collaborator_unassigned_conversation_cannot_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }
                ),
            true).SetName("mentioned_conversation_can_view");
    }

    public static IEnumerable<TestCaseData>
        GetAssignedToMeAndUnassignedConversationsUnderMyTeamViewConversationPermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacViewConversationsPermissions.AssignedToMe, RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam
        };

        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), false)
            .SetName("Team_member_as_contact_owner_conversation_cannot_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_associated_team_conversation_cannot_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                true)
            .SetName("Assigned_to_associated_team_conversation_can_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "Unassigned_conversation_cannot_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName(
            "Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_cannot_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_unassigned_conversation_cannot_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_view");
    }

    public static IEnumerable<TestCaseData>
        GetAssignedToMeAndAllUnassignedConversationsViewConversationPermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacViewConversationsPermissions.AssignedToMe, RbacViewConversationsPermissions.AllUnassignedConversations
        };

        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), false)
            .SetName("Team_member_as_contact_owner_conversation_cannot_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_associated_team_conversation_cannot_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                false)
            .SetName("Assigned_to_associated_team_conversation_cannot_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "Unassigned_conversation_can_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName(
            "Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_cannot_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_cannot_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_view");
    }

    public static IEnumerable<TestCaseData>
        GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsAndUnassignedConversationsUnderMyTeamViewConversationPermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacViewConversationsPermissions.AssignedToMe,
            RbacViewConversationsPermissions.AssignedToMyTeam,
            RbacViewConversationsPermissions.AllAssignedConversations,
            RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
        };

        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "Assigned_to_non-associated_team_user_conversation_can_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                true).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                true)
            .SetName("Assigned_to_associated_team_conversation_can_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            true).SetName(
            "Assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "Unassigned_conversation_cannot_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName(
            "Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_view");
    }

    public static IEnumerable<TestCaseData>
        GetAssignedToMeAndAssignedToMyTeamAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsViewConversationPermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacViewConversationsPermissions.AssignedToMe,
            RbacViewConversationsPermissions.AssignedToMyTeam,
            RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
            RbacViewConversationsPermissions.AllUnassignedConversations,
        };

        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                true)
            .SetName("Assigned_to_associated_team_conversation_can_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "Unassigned_conversation_can_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName(
            "Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_view");
    }

    public static IEnumerable<TestCaseData>
        GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsAndAllUnassignedConversationsViewConversationPermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacViewConversationsPermissions.AssignedToMe,
            RbacViewConversationsPermissions.AssignedToMyTeam,
            RbacViewConversationsPermissions.AllAssignedConversations,
            RbacViewConversationsPermissions.AllUnassignedConversations,
        };

        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "Assigned_to_non-associated_team_user_conversation_can_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                true).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                true)
            .SetName("Assigned_to_associated_team_conversation_can_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            true).SetName(
            "Assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "Unassigned_conversation_can_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName(
            "Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_view");
    }

    public static IEnumerable<TestCaseData>
        GetAssignedToMeAndAllAssignedConversationsAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsViewPermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacViewConversationsPermissions.AssignedToMe,
            RbacViewConversationsPermissions.AllAssignedConversations,
            RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
            RbacViewConversationsPermissions.AllUnassignedConversations
        };

        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "Assigned_to_non-associated_team_user_conversation_can_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                true).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                true)
            .SetName("Assigned_to_associated_team_conversation_can_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            true).SetName(
            "Assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "Unassigned_conversation_can_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName(
            "Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_view");
    }

    public static IEnumerable<TestCaseData>
        GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsViewPermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacViewConversationsPermissions.AssignedToMe,
            RbacViewConversationsPermissions.AssignedToMyTeam,
            RbacViewConversationsPermissions.AllAssignedConversations,
        };

        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "Assigned_to_non-associated_team_user_conversation_can_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                true).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_can_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                true)
            .SetName("Assigned_to_associated_team_conversation_can_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            true).SetName(
            "Assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "Unassigned_conversation_cannot_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName(
            "Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_view");
    }

    public static IEnumerable<TestCaseData>
        GetAssignedToMeAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsViewPermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacViewConversationsPermissions.AssignedToMe,
            RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
            RbacViewConversationsPermissions.AllUnassignedConversations,
        };

        var staff = CreateStaff(permissions);

        // Can view convo with assign to me
        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_view");

        // Can view convo with assign to teammate (without team)
        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), false)
            .SetName("Team_member_as_contact_owner_conversation_cannot_view");

        // Can view convo with assign to teammate (with the same team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_associated_team_conversation_cannot_view");

        // Can view convo with assign to teammate (with the different team)
        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to user in different team (without team)
        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_view");

        // Can view convo with assign to user in different team (with team)
        yield return
            new TestCaseData(
                staff,
                CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100),
                false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_view");

        // Can view convo with assign to team with user is in
        yield return new TestCaseData(
                staff,
                CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id),
                true)
            .SetName("Assigned_to_associated_team_conversation_can_view");

        // Can view convo with assign to team with user is not in
        yield return new TestCaseData(
            staff,
            CreateConversation("assignedToNonAssociatedTeamConversation", null, 2),
            false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo
        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "Unassigned_conversation_can_view");

        // Can view convo with user as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_view");

        // Can view convo assigned to user (different team) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName(
            "Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_cannot_view");

        // Can view convo assigned to team (user is in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_view");

        // Can view convo assigned to team (user is not in) with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_cannot_view");

        // Can view unassigned convo with teammates as collaborator
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_view");

        // Can view convo with user is mentioned
        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = staff.StaffId, CreatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_view");
    }
}