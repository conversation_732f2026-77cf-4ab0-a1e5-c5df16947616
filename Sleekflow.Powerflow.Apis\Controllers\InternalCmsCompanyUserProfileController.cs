﻿using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Text;
using AutoMapper;
using CsvHelper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.VisualBasic.FileIO;
using Newtonsoft.Json;
using OfficeOpenXml;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Internal Cms APIs for creating demo plan and data.
/// </summary>
[Authorize(Roles = ApplicationUserRole.InternalCmsUser)] // Basic Role Requirement
[Route("/internal/userprofile/[action]")]
public class InternalCmsCompanyUserProfileController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IUserProfileService _userProfileService;

    public InternalCmsCompanyUserProfileController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<InternalCmsCompanyUserProfileController> logger,
        IUserProfileService userProfileService)
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _userProfileService = userProfileService;
    }

    public record ImportContactRequest(IFormFile File, string CompanyId);

    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsUser)]
    public async Task<ActionResult<ResponseViewModel>> BulkImportContact([FromForm] ImportContactRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsUser
                }) == null)
        {
            return Unauthorized();
        }

        if (request.File == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "File Not Found."
                });
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found."
                });
        }

        ImportSpreadsheet importSpreadsheet;

        try
        {
            importSpreadsheet = await SerializeCSV(
                company,
                new ImportSpreadsheetViewModel
                {
                    ImportName = "Bulk Import",
                    ColumnsMap = null,
                    files = new List<IFormFile>()
                    {
                        request.File
                    },
                    IsTriggerAutomation = false
                });

            if (importSpreadsheet.records.Count == 0)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "No record found."
                    });
            }
        }
        catch (Exception e)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Error on parsing csv."
                });
        }

        await _userProfileService.BulkImportNewUserProfiles(request.CompanyId, importSpreadsheet);

        return Ok(
            new ResponseViewModel()
            {
                message = $"Import Contact ({importSpreadsheet.records.Count} contact found in csv) job queued"
            });
    }

    public record GetImportSpreadsheetRequest(string CompanyId);

    [HttpPost]
    public async Task<IActionResult> GetImportContactTemplate([FromBody] GetImportSpreadsheetRequest request)
    {
        // before your loop
        var header = "FirstName,LastName";
        var sample = "SleekFlow,Team";

        // var propertyType = "Default contact property,Default contact property";
        var options = string.Empty;

        var companyCustomUserProfileFields = await _appDbContext.CompanyCustomUserProfileFields
            .Where(x => x.CompanyId == request.CompanyId)
            .Include(x => x.CustomUserProfileFieldOptions)
            .ToListAsync();

        var staffs = await _appDbContext.UserRoleStaffs.AsNoTracking().Where(x => x.CompanyId == request.CompanyId)
            .Include(x => x.Identity).ToListAsync();

        companyCustomUserProfileFields = companyCustomUserProfileFields.OrderBy(x => x.Order).ToList();

        companyCustomUserProfileFields.ForEach(
            x => x.CustomUserProfileFieldOptions = x.CustomUserProfileFieldOptions.OrderBy(z => z.Order)
                .ThenBy(z => z.Value).ToList());

        foreach (var customField in companyCustomUserProfileFields.Where(
                     x => x.IsEditable == true || x.FieldName == "LastChannel"))
        {
            header += $",{customField.FieldName}";
            sample += $",";

            // propertyType += $",";
            // propertyType += (customField.IsDefault.GetValueOrDefault()) ? "Default contact property" : "Custom property";
            switch (customField.Type)
            {
                case FieldDataType.Options:
                    options +=
                        $"\n{customField.FieldName}:,{string.Join(";", customField.CustomUserProfileFieldOptions.Select(x => x.Value))}";

                    if (customField.FieldName.ToLower() == "country")
                    {
                        sample += $"\"Hong Kong SAR\"";
                    }
                    else
                    {
                        sample += $"\"{customField.CustomUserProfileFieldOptions.FirstOrDefault()?.Value}\"";
                    }

                    break;
                case FieldDataType.Boolean:
                    sample += $"\"{true}\"";

                    break;
                case FieldDataType.PhoneNumber:
                    sample += "\"85239053590\"";

                    break;
                case FieldDataType.SingleLineText:
                    if (customField.FieldName.ToLower() == "companyname")
                    {
                        sample += "\"SleekFlow\"";
                    }

                    if (customField.FieldName.ToLower() == "jobtitle")
                    {
                        sample += "\"Developer\"";
                    }

                    break;
                case FieldDataType.Email:
                    sample += "\"<EMAIL>\"";

                    break;
                case FieldDataType.TravisUser:
                    sample += $"\"{staffs.FirstOrDefault().Identity.DisplayName}\"";

                    break;
                default:
                    break;
            }
        }

        // var samplecsv = $"{header}\n{sample}";
        var samplecsv = $"{header}\n{sample}\n\n\n\n\n" +
                        $"Index: (You can remove this in your import))\n" +
                        $"Contact owner:,{string.Join(";", staffs.Select(x => x.Identity.DisplayName))}" +
                        $"{options}";

        return File(Encoding.UTF8.GetBytes(samplecsv), "text/csv", "SleekFlow Example Imports - Contacts.csv");
    }

    private async Task<ImportSpreadsheet> SerializeCSV(
        Company company,
        ImportSpreadsheetViewModel importSpreadsheetViewModel)
    {
        ImportSpreadsheet importSpreadsheet = new ImportSpreadsheet()
        {
            ImportName = importSpreadsheetViewModel.ImportName
        };

        foreach (IFormFile _file in importSpreadsheetViewModel.files)
        {
            switch (_file.ContentType)
            {
                case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
                    var inputFile = TempFileHelper.GetTempFileFullPath("import_contact");

                    try
                    {
                        await using Stream fileStream = new FileStream(inputFile, FileMode.Create);

                        await _file.CopyToAsync(fileStream);

                        ExcelPackage excel = new ExcelPackage(fileStream);
                        var workSheet = excel.Workbook.Worksheets.First();

                        var startRowIndex = workSheet.Dimension.Start.Row;
                        var endRowIndex = workSheet.Dimension.End.Row;

                        var startColumn = workSheet.Dimension.Start.Column;
                        var endColumn = workSheet.Dimension.End.Column;

                        for (var currentRow = startRowIndex; currentRow <= endRowIndex; currentRow++)
                        {
                            ExcelRange range = workSheet.Cells[currentRow, startColumn, currentRow, endColumn];

                            if (currentRow == 1)
                            {
                                // Header
                                int columnNumber = 0;
                                var defaultHeaders = "ContactId,FirstName,LastName,Label";

                                for (int column = startColumn; column <= endColumn; column++)
                                {
                                    var header = workSheet.Cells[currentRow, column].Text;

                                    if (defaultHeaders.Contains(header))
                                    {
                                        importSpreadsheet.headers.Add(
                                            new ImportHeader
                                            {
                                                HeaderName = header,
                                                IsValid = true,
                                                CsvFileColumnNumber = columnNumber
                                            });
                                    }
                                    else
                                    {
                                        var fieldType = await _appDbContext.CompanyCustomUserProfileFields
                                            .Include(x => x.CustomUserProfileFieldOptions)
                                            .Include(X => X.CustomUserProfileFieldLinguals)
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.FieldName.ToLower() == header.ToLower()
                                                    && x.CompanyId == company.Id);

                                        if (fieldType != null)
                                        {
                                            importSpreadsheet.headers.Add(
                                                new ImportHeader
                                                {
                                                    HeaderName = header,
                                                    IsValid = true,
                                                    FieldType =
                                                        _mapper.Map<CompanyCustomUserProfileFieldViewModel>(
                                                            fieldType),
                                                    CsvFileColumnNumber = columnNumber
                                                });
                                        }
                                        else
                                        {
                                            importSpreadsheet.headers.Add(
                                                new ImportHeader
                                                {
                                                    HeaderName = header,
                                                    IsValid = false,
                                                    CsvFileColumnNumber = columnNumber
                                                });
                                        }
                                    }

                                    columnNumber++;
                                }
                            }
                            else
                            {
                                if (range.Any(c => !string.IsNullOrEmpty(c.Text)) == false)
                                {
                                    break;
                                }

                                try
                                {
                                    var fields = new List<string>();

                                    for (int column = startColumn; column <= endColumn; column++)
                                    {
                                        var field = workSheet.Cells[currentRow, column].Value;

                                        if (field == null)
                                        {
                                            fields.Add(string.Empty);
                                        }
                                        else
                                        {
                                            fields.Add(field.ToString());
                                        }
                                    }

                                    importSpreadsheet.records.Add(
                                        new ImportRecord
                                        {
                                            fields = fields.ToList()
                                        });
                                }
                                catch (Exception ex)
                                {
                                    throw new Exception($"{ex.Message}");
                                }
                            }
                        }

                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, "Error on parsing excel file");

                        throw;
                    }
                    finally
                    {
                        TempFileHelper.DeleteFileWithRetry(inputFile);
                    }

                    break;
                case "text/csv":
                default:
                    using (var reader = new StreamReader(_file.OpenReadStream()))
                    {
                        while (reader.Peek() >= 0)
                        {
                            if (importSpreadsheet.headers.Count == 0)
                            {
                                int columnNumber = 0;
                                var defaultHeaders = "ContactId,FirstName,LastName,Label";

                                var headers = (await reader.ReadLineAsync())
                                    .Split(",", StringSplitOptions.RemoveEmptyEntries)
                                    .ToList();

                                foreach (var header in headers)
                                {
                                    if (defaultHeaders.Contains(header))
                                    {
                                        importSpreadsheet.headers.Add(
                                            new ImportHeader
                                            {
                                                HeaderName = header, IsValid = true, CsvFileColumnNumber = columnNumber
                                            });
                                    }
                                    else
                                    {
                                        var fieldType = await _appDbContext.CompanyCustomUserProfileFields
                                            .Include(x => x.CustomUserProfileFieldOptions)
                                            .Include(X => X.CustomUserProfileFieldLinguals)
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.FieldName.ToLower() == header.ToLower()
                                                    && x.CompanyId == company.Id);

                                        if (fieldType != null)
                                        {
                                            importSpreadsheet.headers.Add(
                                                new ImportHeader
                                                {
                                                    HeaderName = header,
                                                    IsValid = true,
                                                    FieldType =
                                                        _mapper.Map<CompanyCustomUserProfileFieldViewModel>(
                                                            fieldType),
                                                    CsvFileColumnNumber = columnNumber
                                                });
                                        }
                                        else
                                        {
                                            importSpreadsheet.headers.Add(
                                                new ImportHeader
                                                {
                                                    HeaderName = header,
                                                    IsValid = false,
                                                    CsvFileColumnNumber = columnNumber
                                                });
                                        }
                                    }

                                    columnNumber++;
                                }
                            }
                            else
                            {
                                var line = await reader.ReadLineAsync();

                                var records = line
                                    .Split(",")
                                    .ToList();

                                var isNull = true;

                                foreach (var record in records)
                                {
                                    if (!string.IsNullOrEmpty(record))
                                    {
                                        isNull = false;

                                        break;
                                    }
                                }

                                if (string.IsNullOrEmpty(line) || isNull)
                                {
                                    break;
                                }

                                try
                                {
                                    TextFieldParser parser = new TextFieldParser(new StringReader(line));

                                    // You can also read from a file
                                    // TextFieldParser parser = new TextFieldParser("mycsvfile.csv");
                                    parser.HasFieldsEnclosedInQuotes = true;
                                    parser.SetDelimiters(",");

                                    string[] fields;

                                    while (!parser.EndOfData)
                                    {
                                        fields = parser.ReadFields();

                                        importSpreadsheet.records.Add(
                                            new ImportRecord
                                            {
                                                fields = fields.ToList()
                                            });
                                    }

                                    parser.Close();
                                }
                                catch (Exception ex)
                                {
                                    throw new Exception($"{ex.Message}, line: {line}");
                                }
                            }
                        }
                    }

                    break;
            }
        }

        return importSpreadsheet;
    }

    public record MergeContactRequest(
        string CompanyId,
        string UserProfileIdToKeep,
        string UserProfileIdToMerge);

    [HttpPost]
    public async Task<IActionResult> MergeConversation(
        [FromBody, Required]
        MergeContactRequest mergeContactRequest)
    {
        try
        {
            var conversationToMerge = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == mergeContactRequest.CompanyId
                        && x.UserProfileId == mergeContactRequest.UserProfileIdToMerge);

            var messagesToMerge = await _appDbContext.ConversationMessages
                .Where(
                    x =>
                        x.CompanyId == mergeContactRequest.CompanyId
                        && x.ConversationId == conversationToMerge.Id)
                .ToListAsync();

            var conversationIdToKeep = await _appDbContext.Conversations
                .Where(
                    x =>
                        x.CompanyId == mergeContactRequest.CompanyId
                        && x.UserProfileId == mergeContactRequest.UserProfileIdToKeep)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            var messagesToKeep = await _appDbContext.ConversationMessages
                .Where(
                    x =>
                        x.CompanyId == mergeContactRequest.CompanyId
                        && x.ConversationId == conversationIdToKeep)
                .ToListAsync();

            foreach (var msg in messagesToMerge)
            {
                // support five default channels and note
                if (msg.Channel == ChannelTypes.WhatsappTwilio
                    || msg.Channel == ChannelTypes.Whatsapp360Dialog
                    || msg.Channel == ChannelTypes.WhatsappCloudApi
                    || msg.Channel == ChannelTypes.Instagram
                    || msg.Channel == ChannelTypes.Facebook
                    || msg.Channel == ChannelTypes.Note)
                {
                    msg.ConversationId = conversationIdToKeep;

                    switch (msg.Channel)
                    {
                        case ChannelTypes.WhatsappTwilio:

                            var whatsappSenderId = messagesToKeep
                                .Where(
                                    x =>
                                        x.IsSentFromSleekflow
                                        && x.whatsappReceiverId != null)
                                .Select(x => x.whatsappReceiverId)
                                .FirstOrDefault();

                            if (msg.IsSentFromSleekflow)
                            {
                                msg.whatsappReceiverId = whatsappSenderId;
                            }
                            else
                            {
                                msg.whatsappSenderId = whatsappSenderId;
                            }

                            // only change the DynamicChannelSender when it is not null
                            if (msg.DynamicChannelSender != null)
                            {
                                var userIdentityId = await _appDbContext.UserProfiles
                                    .Where(
                                        x =>
                                            x.CompanyId == mergeContactRequest.CompanyId
                                            && x.Id == mergeContactRequest.UserProfileIdToKeep)
                                    .Select(x => x.PhoneNumber)
                                    .FirstOrDefaultAsync();

                                var whatsappDynamicChannelSender = messagesToKeep
                                    .Where(
                                        x =>
                                            x.DynamicChannelSender != null
                                            && x.DynamicChannelSender.UserIdentityId == userIdentityId)
                                    .Select(x => x.DynamicChannelSender)
                                    .FirstOrDefault();

                                msg.DynamicChannelSender = whatsappDynamicChannelSender;

                                _appDbContext.Entry(msg).Property(x => x.DynamicChannelSender).IsModified = true;
                            }

                            break;

                        case ChannelTypes.Whatsapp360Dialog:

                            var whatsapp360DialogSenderId = messagesToKeep
                                .Where(
                                    x =>
                                        x.IsSentFromSleekflow
                                        && x.Whatsapp360DialogReceiverId != null)
                                .Select(x => x.Whatsapp360DialogReceiverId)
                                .FirstOrDefault();

                            if (msg.IsSentFromSleekflow)
                            {
                                msg.Whatsapp360DialogReceiverId = whatsapp360DialogSenderId;
                            }
                            else
                            {
                                msg.Whatsapp360DialogSenderId = whatsapp360DialogSenderId;
                            }

                            if (msg.DynamicChannelSender != null)
                            {
                                var userIdentityId = await _appDbContext.UserProfiles
                                    .Where(
                                        x =>
                                            x.CompanyId == mergeContactRequest.CompanyId
                                            && x.Id == mergeContactRequest.UserProfileIdToKeep)
                                    .Select(x => x.PhoneNumber)
                                    .FirstOrDefaultAsync();

                                var whatsapp360DialogDynamicChannelSender = messagesToKeep
                                    .Where(
                                        x =>
                                            x.DynamicChannelSender != null
                                            && x.DynamicChannelSender.UserIdentityId == userIdentityId)
                                    .Select(x => x.DynamicChannelSender)
                                    .FirstOrDefault();

                                msg.DynamicChannelSender = whatsapp360DialogDynamicChannelSender;

                                _appDbContext.Entry(msg).Property(x => x.DynamicChannelSender).IsModified = true;
                            }

                            break;

                        case ChannelTypes.WhatsappCloudApi:

                            if (msg.DynamicChannelSender != null)
                            {
                                var userIdentityId = _appDbContext.UserProfiles
                                    .Where(
                                        x =>
                                            x.CompanyId == mergeContactRequest.CompanyId
                                            && x.Id == mergeContactRequest.UserProfileIdToKeep)
                                    .Select(x => x.PhoneNumber)
                                    .FirstOrDefault();

                                var whatsappCloudApiDynamicChannelSender = messagesToKeep
                                    .Where(
                                        x =>
                                            x.DynamicChannelSender != null
                                            && x.DynamicChannelSender.ChannelIdentityId == msg.ChannelIdentityId
                                            && x.DynamicChannelSender.UserIdentityId == userIdentityId)
                                    .Select(x => x.DynamicChannelSender)
                                    .FirstOrDefault();

                                msg.DynamicChannelSender = whatsappCloudApiDynamicChannelSender;

                                _appDbContext.Entry(msg).Property(x => x.DynamicChannelSender).IsModified = true;
                            }

                            break;

                        case ChannelTypes.Instagram:
                            var instagramSenderId = messagesToKeep
                                .Where(x => x.IsSentFromSleekflow && x.InstagramReceiverId != null)
                                .Select(x => x.InstagramReceiverId)
                                .FirstOrDefault();

                            if (msg.IsSentFromSleekflow)
                            {
                                msg.InstagramReceiverId = instagramSenderId;
                            }
                            else
                            {
                                msg.InstagramSenderId = instagramSenderId;
                            }

                            break;

                        case ChannelTypes.Facebook:
                            var facebookSenderId = messagesToKeep
                                .Where(x => x.IsSentFromSleekflow && x.facebookReceiverId != null)
                                .Select(x => x.facebookReceiverId)
                                .FirstOrDefault();

                            if (msg.IsSentFromSleekflow)
                            {
                                msg.facebookReceiverId = facebookSenderId;
                            }
                            else
                            {
                                msg.facebookSenderId = facebookSenderId;
                            }

                            break;
                    }

                    await _appDbContext.SaveChangesAsync();
                }
            }

            _logger.LogInformation(
                "[InternalCmsCompanyUserProfile {MethodName} endpoint] Company {CompanyId} is deleting user profile {UserProfileId}",
                nameof(MergeConversation),
                mergeContactRequest.CompanyId,
                conversationToMerge?.UserProfileId);

            await _userProfileService.DeleteUserProfileLinked(
                conversationToMerge.UserProfile,
                new UserProfileDeletionTriggerContext(
                    UpdateUserProfileTriggerSource.ConversationMerge,
                    null));
        }
        catch (Exception e)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Error on merging conversation."
                });
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "conversation merged successfully"
            });
    }

    public record DeleteUserProfilePhoneNumberRequest(string CompanyId, string UserProfileId);

    [HttpPost]
    public async Task<IActionResult> DeleteUserProfilePhoneNumber(
        [FromBody, Required]
        DeleteUserProfilePhoneNumberRequest request)
    {
        try
        {
            // setting customField PhoneNumber as null
            await SetCustomFieldValue(
                request.CompanyId,
                request.UserProfileId,
                "PhoneNumber",
                null);

            var userProfile = await _appDbContext.UserProfiles
                .Include(x => x.Conversation)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == request.CompanyId
                        && x.Id == request.UserProfileId);

            if (userProfile == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "userprofile not found"
                    });
            }

            // setting the PhoneNumber in UserProfile as null
            userProfile.PhoneNumber = null;

            var whatsappSenderId = userProfile.WhatsAppAccountId;
            var whatsapp360DialogSenderId = userProfile.WhatsApp360DialogUserId;
            var smsSenderId = userProfile.SMSUserId;

            userProfile.WhatsAppAccountId = null;
            userProfile.SMSUserId = null;
            userProfile.WhatsApp360DialogUserId = null;

            // channel: whatsapp
            if (whatsappSenderId.HasValue)
            {
                // setting Conversation.WhatsappUserId as null
                userProfile.Conversation.WhatsappUserId = null;

                // deleting conversation messages of the userProfileId
                await _appDbContext.ConversationMessages
                    .Where(
                        x =>
                            x.ConversationId == userProfile.Conversation.Id
                            && x.Channel == ChannelTypes.WhatsappTwilio)
                    .ExecuteDeleteAsync();

                // remove WhatsappSender
                _appDbContext.SenderWhatsappSenders.RemoveRange(
                    await _appDbContext.SenderWhatsappSenders
                        .Where(x => x.Id == whatsappSenderId)
                        .ToListAsync());
            }

            // channel: whatsapp360dialog
            if (whatsapp360DialogSenderId.HasValue)
            {
                userProfile.Conversation.WhatsApp360DialogUserId = null;

                await _appDbContext.ConversationMessages
                    .Where(
                        x =>
                            x.ConversationId == userProfile.Conversation.Id
                            && x.Channel == ChannelTypes.Whatsapp360Dialog)
                    .ExecuteDeleteAsync();

                _appDbContext.SenderWhatsApp360DialogSenders.RemoveRange(
                    await _appDbContext.SenderWhatsApp360DialogSenders
                        .Where(x => x.Id == whatsapp360DialogSenderId)
                        .ToListAsync());
            }

            // channel: sms
            if (smsSenderId.HasValue)
            {
                userProfile.Conversation.SMSUserId = null;

                await _appDbContext.ConversationMessages
                    .Where(
                        x =>
                            x.ConversationId == userProfile.Conversation.Id
                            && x.Channel == ChannelTypes.Sms)
                    .ExecuteDeleteAsync();

                _appDbContext.SenderSMSSenders.RemoveRange(
                    await _appDbContext.SenderSMSSenders
                        .Where(x => x.Id == smsSenderId)
                        .ToListAsync());
            }

            // channel: whatsappcloudapi
            if (await _appDbContext.WhatsappCloudApiSenders
                    .AnyAsync(x => x.UserProfileId == request.UserProfileId))
            {
                await _appDbContext.ConversationMessages
                    .Where(
                        x =>
                            x.ConversationId == userProfile.Conversation.Id
                            && x.Channel == ChannelTypes.WhatsappCloudApi)
                    .ExecuteDeleteAsync();

                _appDbContext.WhatsappCloudApiSenders.RemoveRange(
                    await _appDbContext.WhatsappCloudApiSenders
                        .Where(x => x.UserProfileId == request.UserProfileId)
                        .ToListAsync());
            }

            await _appDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName} endpoint] Payload: {RequestPayload} Error: {ExceptionMessage}",
                nameof(DeleteUserProfilePhoneNumber),
                JsonConvert.SerializeObject(request),
                ex.Message);

            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "deleted phone number successfully"
            });
    }

    private async Task SetCustomFieldValue(
        string companyId,
        string userProfileId,
        string fieldName,
        string fieldValue)
    {
        var customFieldId = await _appDbContext.CompanyCustomUserProfileFields
            .Where(
                x =>
                    x.FieldName == fieldName
                    && x.CompanyId == companyId)
            .Select(x => x.Id)
            .FirstOrDefaultAsync();

        if (customFieldId != null)
        {
            // if such fieldName exists and not been added value
            if (!await _appDbContext.UserProfileCustomFields
                    .AnyAsync(
                        x =>
                            x.UserProfileId == userProfileId
                            && x.CompanyDefinedFieldId == customFieldId
                            && x.Value != null))
            {
                await _appDbContext.UserProfileCustomFields.AddAsync(
                    new UserProfileCustomField
                    {
                        UserProfileId = userProfileId,
                        CompanyDefinedFieldId = customFieldId,
                        Value = fieldValue,
                        CompanyId = companyId
                    });
            }

            // such fieldName exists and update its value
            else
            {
                var userProfileCustomFieldEntity = await _appDbContext.UserProfileCustomFields
                    .FirstOrDefaultAsync(
                        x =>
                            x.UserProfileId == userProfileId
                            && x.CompanyDefinedFieldId == customFieldId);

                userProfileCustomFieldEntity.Value = fieldValue;
            }

            await _appDbContext.SaveChangesAsync();
        }
    }


    public class Csv
    {
        public string PhoneNumber { get; set; }
        public string FirstName { get; set; }

        public string LastName { get; set; }
        // public string customer_brand_pk { get; set; }
        // public string Recency_Segment { get; set; }
        // public string last_purchase_dt { get; set; }
        // public string trx_recency { get; set; }
        // public string cust_seq { get; set; }
    }

    public class CheckContactExistRequest
    {
        public IFormFile File { get; set; }
    }

    // L'Occitane Company Id
    // "dc4fac25-b55d-4d12-9f4d-1b25322e3055" - L'Occitane Saudi Arabia
    // "8494ce7f-f3be-400f-8167-9dab6b28dbaf" - L'Occitane UAE
    // "9b51debc-8cd9-4549-9ab7-40963a9a2f20" - L'Occitane Oman
    // "e676b366-de6f-46bf-bc38-81d076349cf2" - L'Occitane Bahrain
    // "7fcc040f-f714-4f14-9ca3-91255c187d10" - L'Occitane Jordan
    // "f7930fb0-1e2b-4034-a8cb-9fc702c3ce41" - L'Occitane Qatar
    // "a9fd2541-fb6c-424f-999a-0f1628b2f714" - L'Occitane Kuwait

#if DEBUG
    [AllowAnonymous]
#endif
    [HttpPost]
    public async Task<ActionResult> CheckContactExist(
        [FromForm]
        CheckContactExistRequest request,
        [FromQuery]
        string companyId)
    {
        var records = new List<Csv>();

        using (var csv = new CsvReader(new StreamReader(request.File.OpenReadStream()), CultureInfo.InvariantCulture))
        {
            await csv.ReadAsync();
            csv.ReadHeader();

            while (await csv.ReadAsync())
            {
                var record = new Csv
                {
                    PhoneNumber = csv.GetField("PhoneNumber"),
                    FirstName = csv.GetField("FirstName"),
                    LastName = csv.GetField("LastName"),
                    // customer_brand_pk = csv.GetField("customer_brand_pk"),
                    // Recency_Segment = csv.GetField("Recency_Segment"),
                    // last_purchase_dt = csv.GetField("last_purchase_dt"),
                    // trx_recency = csv.GetField("trx_recency"),
                    // cust_seq = csv.GetField("cust_seq"),
                };
                records.Add(record);
            }
        }

        records.ForEach(x => { x.PhoneNumber = PhoneNumberHelper.NormalizePhoneNumber(x.PhoneNumber); });

        records = records.Where(x => !string.IsNullOrEmpty(x.PhoneNumber)).DistinctBy(x => x.PhoneNumber).ToList();

        var newNumberList = new List<string>();

        var existingPhoneNumbers = await _appDbContext.UserProfiles
            .Where(
                x => x.CompanyId == companyId && x.ActiveStatus == ActiveStatus.Active)
            .OrderByDescending(x => x.CreatedAt)
            .Select(x => x.PhoneNumber)
            .ToListAsync();
        existingPhoneNumbers = existingPhoneNumbers.Where(x => x != null).ToList();

        foreach (var data in records.Chunk(10000))
        {
            var newPhoneNumbers = data.Select(c => c.PhoneNumber).ToList();
            newNumberList.AddRange(newPhoneNumbers.Except(existingPhoneNumbers).ToList());
        }

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(records.Where(x => newNumberList.Contains(x.PhoneNumber)));
            }
        }

        return File(ms.ToArray(), "text/csv", $"New Number Contacts.csv");
    }

#if DEBUG
    [AllowAnonymous]
#endif
    [HttpPost]
    public async Task<ActionResult> InsertContactsToListByPhoneNumber(
        [FromForm]
        CheckContactExistRequest request,
        [FromQuery]
        List<long> listIds,
        [FromQuery]
        string companyId,
        [FromQuery]
        string lookForCsvField = "PhoneNumber")
    {
        var lists = await _appDbContext.CompanyImportContactHistories
            .Where(x => listIds.Contains(x.Id) && x.CompanyId == companyId).ToListAsync();

        if (lists.Count != listIds.Count)
            return BadRequest("List not match");

        var records = new List<Csv>();

        using (var csv = new CsvReader(new StreamReader(request.File.OpenReadStream()), CultureInfo.InvariantCulture))
        {
            await csv.ReadAsync();
            csv.ReadHeader();

            while (await csv.ReadAsync())
            {
                var record = new Csv
                {
                    PhoneNumber = csv.GetField(lookForCsvField),
                };
                records.Add(record);
            }
        }

        records.ForEach(x => { x.PhoneNumber = PhoneNumberHelper.NormalizePhoneNumber(x.PhoneNumber); });

        records = records.Where(x => !string.IsNullOrEmpty(x.PhoneNumber)).DistinctBy(x => x.PhoneNumber).ToList();

        var userProfileList = new List<string>();

        var existingUserProfileIds = _appDbContext.UserProfiles
            .Where(
                x => x.CompanyId == companyId && x.ActiveStatus == ActiveStatus.Active)
            .OrderByDescending(x => x.CreatedAt)
            .Select(
                x => new
                {
                    x.Id, x.PhoneNumber
                })
            .AsEnumerable()
            .Where(x => x.PhoneNumber != null && x.Id != null)
            .ToList();

        var existingUserProfileIdsDict = new Dictionary<string, string>();
        existingUserProfileIds.ForEach(x => existingUserProfileIdsDict.Add(x.PhoneNumber, x.Id));

        records.Select(c => c.PhoneNumber).ToList().ForEach(
            x =>
            {
                if (existingUserProfileIdsDict.TryGetValue(x, out string userProfileId))
                {
                    userProfileList.Add(userProfileId);
                }
            });

        var i = 0;
        var chunk = userProfileList.Chunk((int) Math.Ceiling((double) userProfileList.Count / lists.Count));

        foreach (var userProfileListChuckByList in chunk)
        {
            var listId = lists[i].Id;
            BackgroundJob.Enqueue(() => InsertContactChuckToList(userProfileListChuckByList, listId));
            i++;
        }

        return Ok();
    }

    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task InsertContactChuckToList(string[] userProfileListChuckByList, long importContactHistoryId)
    {
        foreach (var smallerUserProfileListChuck in userProfileListChuckByList.Chunk(1000))
        {
            _appDbContext.CompanyImportedUserProfiles.AddRange(
                smallerUserProfileListChuck.Select(
                    x => new ImportedUserProfile
                    {
                        ImportContactHistoryId = importContactHistoryId, UserProfileId = x
                    }));

            await _appDbContext.SaveChangesAsync();
        }
    }
}