﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BroadcastDomain.Constants;
using Travis_backend.BroadcastDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FileDomain.Models;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using CreateDemoPlanRequest = Sleekflow.Powerflow.Apis.ViewModels.CreateDemoPlanRequest;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Internal Cms APIs for creating demo plan and data.
/// </summary>
[Authorize(Roles = ApplicationUserRole.InternalCmsUser)] // Basic Role Requirement
[Route("/internal/demo/[action]")]
public class InternalCmsDemoController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly ITwilioService _twilioService;
    private readonly IDistributedCache _cache;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;

    public InternalCmsDemoController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<InternalCmsDemoController> logger,
        ICompanyUsageService companyUsageService,
        ITwilioService twilioService,
        IDistributedCache cache,
        ICompanyInfoCacheService companyInfoCacheService)
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _companyUsageService = companyUsageService;
        _twilioService = twilioService;
        _cache = cache;
        _companyInfoCacheService = companyInfoCacheService;
    }

    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> CreateDemoPlan([FromBody] CreateDemoPlanRequest request)
    {
        // if (!ModelState.IsValid) return BadRequest(new ResponseViewModel { message = "Invalid Form" });
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found."
                });
        }

        var usage = await _companyUsageService.GetCompanyUsage(request.CompanyId);

        if (!ValidSubscriptionPlan.FreePlans.Contains(
                usage.billingPeriodUsages.FirstOrDefault()?.BillRecord.SubscriptionPlan.Id))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Company current Subscription Plan is not allowed to create demo."
                });
        }

        var transaction = await _appDbContext.Database.BeginTransactionAsync();

        try
        {
            await SetDemoPlan(company);

            var importDefaultStaffs = await ImportDefaultStaffsAsync(company);

            var staffs = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .OrderBy(x => x.Order)
                .ThenBy(x => x.Id)
                .Where(x => x.CompanyId == request.CompanyId)
                .ToListAsync();

            var teams = await _appDbContext.CompanyStaffTeams
                .OrderBy(x => x.TeamName)
                .Where(x => x.CompanyId == request.CompanyId)
                .ToListAsync();

            // Configure Owner
            var owner = staffs.First();

            // foreach (var team in teams)
            // {
            //     team.Members.Add(new TeamMember() { StaffId = owner.Id });
            // }
            await _appDbContext.SaveChangesAsync();

            var admin = staffs.Skip(1).First();

            // Company Configs
            var companyHashtags = await AddDemoCompanyDefinedHashtagsAsync(company.Id);
            var companyCustomUserProfileField = await AddDemoCompanyCustomUserProfileFields(company.Id);

            // User
            // var userProfiles = await AddDemoUserProfiles(company.Id);
            await AddDemoContactList(company.Id, admin);
            var contactList = await _appDbContext.CompanyImportContactHistories
                .Where(x => x.CompanyId == company.Id)
                .ToListAsync();

            var campaigns = await AddDemoCampaigns(company.Id, admin);

            var demoUser = await AddDemoUserProfileAndCustomFields(company.Id, teams.FirstOrDefault());

            if (demoUser != null)
            {
                var conversation = await AddConversation(company.Id, demoUser, admin);
                var conversationMessages = await AddConversationMessages(
                    company.Id,
                    conversation,
                    demoUser,
                    admin,
                    staffs.FirstOrDefault(x => x.Position != null && x.Position.Contains("Sales Rep 1")));
            }

            var assignmentRules = await AddAssignmentRule(
                company.Id,
                admin,
                teams.First(),
                teams.Skip(1)
                    .First(),
                contactList.FirstOrDefault(x => x.ImportName.Contains("[Demo] Campaign")),
                contactList.FirstOrDefault(x => x.ImportName.Contains("[Demo] Leads")));

            await transaction.CommitAsync();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await transaction.RollbackAsync();

            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Error! Demo plan and related data cannot be created!"
                });
        }

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        // await _cacheService.RemoveCompanyInfoCache(request.CompanyId, "AssignmentRuleInfo");
        return Ok(
            new ResponseViewModel()
            {
                message = "Demo plan and related data has created."
            });
    }

    #region Helper

    private async Task<bool> SetDemoPlan(Company company)
    {
        var newBill = new BillRecord()
        {
            SubscriptionPlanId = "sleekflow_demo",
            PayAmount = 0,
            CompanyId = company.Id,
            Status = BillStatus.Active,
            PaymentStatus = PaymentStatus.FreeOfCharge,
            PeriodStart = DateTime.UtcNow,
            PeriodEnd = DateTime.UtcNow.Add(TimeSpan.FromDays(30)),
        };

        var demoPlan = await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(x => x.Id == "sleekflow_demo");
        company.BillRecords = new List<BillRecord>();
        company.BillRecords.Add(newBill);
        company.MaximumAgents = demoPlan.IncludedAgents;
        company.MaximumWhatsappInstance = 0;
        company.MaximumContacts = demoPlan.MaximumContact;
        company.MaximumAutomations = demoPlan.MaximumAutomation;
        company.MaximumWhAutomatedMessages = demoPlan.MaximumCampaignSent;

        await _appDbContext.SaveChangesAsync();

        return true;
    }

    private async Task<bool> ImportDefaultStaffsAsync(Company company)
    {
        var userCompanyName = company.CompanyName.Replace(" ", string.Empty).ToLower();

        var demoStaffList = new List<StaffToImport>()
        {
            new ()
            {
                FirstName = "Admin",
                LastName = "1",
                Position = "[Demo] Manager",
                Role = StaffUserRole.Admin,
                Password = $"{userCompanyName}{DateTime.UtcNow:yyyy}",
                Username = $"{userCompanyName}admin1",
                Team = "Branch 3;Branch 1;Branch 2",
                Order = 1
            },
            new ()
            {
                FirstName = "Sales Manager",
                LastName = "1",
                Position = "[Demo] Sales Manager 1",
                Role = StaffUserRole.TeamAdmin,
                Password = $"{userCompanyName}{DateTime.UtcNow:yyyy}",
                Username = $"{userCompanyName}salesmanager1",
                Team = "Branch 1",
                Order = 2
            },
            new ()
            {
                FirstName = "Sales Manager",
                LastName = "2",
                Position = "[Demo] Sales Manager 2",
                Role = StaffUserRole.TeamAdmin,
                Password = $"{userCompanyName}{DateTime.UtcNow:yyyy}",
                Username = $"{userCompanyName}salesmanager2",
                Team = "Branch 2",
                Order = 3
            },
            new ()
            {
                FirstName = "Sales Manager",
                LastName = "3",
                Position = "[Demo] Sales Manager 3",
                Role = StaffUserRole.TeamAdmin,
                Password = $"{userCompanyName}{DateTime.UtcNow:yyyy}",
                Username = $"{userCompanyName}salesmanager3",
                Team = "Branch 3",
                Order = 4
            },
            new ()
            {
                FirstName = "Sales Rep",
                LastName = "1",
                Position = "[Demo] Sales Rep 1",
                Role = StaffUserRole.Staff,
                Password = $"{userCompanyName}{DateTime.UtcNow:yyyy}",
                Username = $"{userCompanyName}salesrep1",
                Team = "Branch 1",
                Order = 5
            },
            new ()
            {
                FirstName = "Sales Rep",
                LastName = "2",
                Position = "[Demo] Sales Rep 2",
                Role = StaffUserRole.Staff,
                Password = $"{userCompanyName}{DateTime.UtcNow:yyyy}",
                Username = $"{userCompanyName}salesrep2",
                Team = "Branch 1",
                Order = 6
            },
            new ()
            {
                FirstName = "Sales Rep",
                LastName = "3",
                Position = "[Demo] Sales Rep 3",
                Role = StaffUserRole.Staff,
                Password = $"{userCompanyName}{DateTime.UtcNow:yyyy}",
                Username = $"{userCompanyName}salesrep3",
                Team = "Branch 2",
                Order = 7
            },
            new ()
            {
                FirstName = "Sales Rep",
                LastName = "4",
                Position = "[Demo] Sales Rep 4",
                Role = StaffUserRole.Staff,
                Password = $"{userCompanyName}{DateTime.UtcNow:yyyy}",
                Username = $"{userCompanyName}salesrep4",
                Team = "Branch 2",
                Order = 8
            },
            new ()
            {
                FirstName = "Sales Rep",
                LastName = "5",
                Position = "[Demo] Sales Rep 5",
                Role = StaffUserRole.Staff,
                Password = $"{userCompanyName}{DateTime.UtcNow:yyyy}",
                Username = $"{userCompanyName}salesrep5",
                Team = "Branch 3",
                Order = 9
            },
            new ()
            {
                FirstName = "Sales Rep",
                LastName = "6",
                Position = "[Demo] Sales Rep 6",
                Role = StaffUserRole.Staff,
                Password = $"{userCompanyName}{DateTime.UtcNow:yyyy}",
                Username = $"{userCompanyName}salesrep6",
                Team = "Branch 3",
                Order = 10
            },
            new ()
            {
                FirstName = "Marketing",
                LastName = string.Empty,
                Position = "[Demo] Marketing",
                Role = StaffUserRole.Staff,
                Password = $"{userCompanyName}{DateTime.UtcNow:yyyy}",
                Username = $"{userCompanyName}marketing",
                Order = 11
            },
        };

        var currentStaffPos = await _appDbContext.UserRoleStaffs
            .Where(s => s.CompanyId == company.Id)
            .Select(
                x =>
                    x.Position)
            .ToListAsync();

        demoStaffList = demoStaffList.Where(x => !currentStaffPos.Contains(x.Position)).ToList();

        foreach (var staffToImport in demoStaffList)
        {
            try
            {
                var username = staffToImport.Username;
                var firstName = staffToImport.FirstName;
                var lastName = staffToImport.LastName;
                string email = null;
                var password = staffToImport.Password;
                var role = staffToImport.Role;

                var position = staffToImport.Position;
                var groupToAdd = staffToImport.Team;

                if (string.IsNullOrEmpty(username))
                {
                    username = email;
                }

                var identityResult = await _userManager.CreateAsync(
                    new ApplicationUser
                    {
                        UserName = username,
                        Email = email,
                        FirstName = firstName,
                        LastName = lastName,
                        DisplayName = $"{firstName} {lastName}",
                        EmailConfirmed = true
                    });

                if (identityResult.Succeeded)
                {
                    var user = await _userManager.FindByNameAsync(username);

                    await _userManager.AddPasswordAsync(user, password);

                    var staff = new Staff
                    {
                        CompanyId = company.Id,
                        IdentityId = user.Id,
                        Identity = user,
                        Locale = "en",
                        RoleType = role,
                        Position = position,
                        TimeZoneInfoId = company.TimeZoneInfoId,
                        NotificationSettingId = 1,
                        Order = staffToImport.Order
                    };
                    await _appDbContext.UserRoleStaffs.AddAsync(staff);

                    if (!string.IsNullOrEmpty(groupToAdd))
                    {
                        var groups = groupToAdd.Split(";", StringSplitOptions.RemoveEmptyEntries);

                        foreach (var group in groups)
                        {
                            var team = await _appDbContext.CompanyStaffTeams
                                .Where(x => x.CompanyId == company.Id && x.TeamName == group.Trim())
                                .Include(x => x.Members)
                                .FirstOrDefaultAsync();

                            if (team == null)
                            {
                                team = new CompanyTeam
                                {
                                    CompanyId = company.Id, TeamName = group.Trim()
                                };
                                _appDbContext.CompanyStaffTeams.Add(team);
                            }

                            team.Members.Add(
                                new TeamMember
                                {
                                    Staff = staff
                                });
                        }
                    }

                    await _appDbContext.SaveChangesAsync();
                }
                else if (!string.IsNullOrEmpty(groupToAdd))
                {
                    var user = await _userManager.FindByNameAsync(username);
                    var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                    await _userManager.ResetPasswordAsync(user, token, password);

                    var staff = await _appDbContext.UserRoleStaffs
                        .Where(x => x.CompanyId == company.Id && x.IdentityId == user.Id).FirstOrDefaultAsync();

                    if (staff == null)
                    {
                        staff = new Staff
                        {
                            CompanyId = company.Id,
                            IdentityId = user.Id,
                            Identity = user,
                            Locale = "en",
                            RoleType = role,
                            Position = position,
                            TimeZoneInfoId = company.TimeZoneInfoId,
                            NotificationSettingId = 1
                        };
                        await _appDbContext.UserRoleStaffs.AddAsync(staff);
                        await _appDbContext.SaveChangesAsync();
                    }
                    else
                    {
                        if (staff.RoleType != role)
                        {
                            staff.RoleType = role;
                            await _appDbContext.SaveChangesAsync();
                        }
                    }

                    if (!string.IsNullOrEmpty(groupToAdd))
                    {
                        var groups = groupToAdd.Split(";", StringSplitOptions.RemoveEmptyEntries);

                        foreach (var group in groups)
                        {
                            var team = await _appDbContext.CompanyStaffTeams.Include(x => x.Members)
                                .Where(x => x.CompanyId == company.Id && x.TeamName == group.Trim())
                                .FirstOrDefaultAsync();

                            if (team == null)
                            {
                                team = new CompanyTeam
                                {
                                    CompanyId = company.Id, TeamName = group.Trim()
                                };
                                _appDbContext.CompanyStaffTeams.Add(team);
                            }

                            if (!team.Members.Any(x => x.StaffId == staff.Id))
                            {
                                team.Members.Add(
                                    new TeamMember
                                    {
                                        Staff = staff
                                    });
                                await _appDbContext.SaveChangesAsync();
                            }
                        }
                    }
                }
                else
                {
                    continue;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} error. {ExceptionMessage}",
                    nameof(ImportDefaultStaffsAsync),
                    company.Id,
                    ex.Message);
            }
        }

        return true;
    }

    private async Task<List<CompanyHashtag>> AddDemoCompanyDefinedHashtagsAsync(string companyId)
    {
        var demoHashtags = new List<CompanyHashtag>()
        {
            // new() { CompanyId = companyId, Hashtag = "New order", HashTagColor = (HashTagColor)2, },
            // new() { CompanyId = companyId, Hashtag = "VIP", HashTagColor = (HashTagColor)0, },
            // new() { CompanyId = companyId, Hashtag = "New customer", HashTagColor = (HashTagColor)7, },
            // new() { CompanyId = companyId, Hashtag = "Pending payment", HashTagColor = (HashTagColor)1, },
            // new() { CompanyId = companyId, Hashtag = "Order complete", HashTagColor = (HashTagColor)3, },
            // new() { CompanyId = companyId, Hashtag = "Issue", HashTagColor = (HashTagColor)4, },
            // new() { CompanyId = companyId, Hashtag = "Paid", HashTagColor = (HashTagColor)5, },
            new ()
            {
                CompanyId = companyId, Hashtag = "Refunded", HashTagColor = (HashTagColor) 2,
            },
            new ()
            {
                CompanyId = companyId, Hashtag = "Confirmed", HashTagColor = (HashTagColor) 3,
            },
            new ()
            {
                CompanyId = companyId, Hashtag = "Cancelled", HashTagColor = (HashTagColor) 6,
            },
            new ()
            {
                CompanyId = companyId, Hashtag = "New", HashTagColor = (HashTagColor) 2,
            },
            new ()
            {
                CompanyId = companyId, Hashtag = "Completed", HashTagColor = (HashTagColor) 3,
            },
            new ()
            {
                CompanyId = companyId, Hashtag = "Unshipped", HashTagColor = (HashTagColor) 1,
            },
            new ()
            {
                CompanyId = companyId, Hashtag = "Shipping", HashTagColor = (HashTagColor) 0,
            },
            new ()
            {
                CompanyId = companyId, Hashtag = "Failed payment", HashTagColor = (HashTagColor) 4,
            },

            // new CompanyHashtag() {Hashtag = "", HashTagColor = (HashTagColor) 1, CompanyId = companyId},
        };

        var currentHashtags = await _appDbContext.CompanyDefinedHashtags
            .Where(s => s.CompanyId == companyId)
            .Select(
                x =>
                    x.Hashtag)
            .ToListAsync();

        _appDbContext.CompanyDefinedHashtags.AddRange(
            demoHashtags.Where(x => !currentHashtags.Contains(x.Hashtag)).ToList());

        await _appDbContext.SaveChangesAsync();

        return demoHashtags;
    }

    private async Task<List<CompanyCustomUserProfileField>> AddDemoCompanyCustomUserProfileFields(string companyId)
    {
        var demoUserProfileFields = new List<CompanyCustomUserProfileField>()
        {
            new ()
            {
                CompanyId = companyId,
                FieldName = "Labels",
                Type = (FieldDataType) 17,
                Order = 0,
                IsVisible = true,
                IsEditable = false,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "PositionOfContactOwner",
                Type = (FieldDataType) 16,
                Order = 99,
                IsVisible = false,
                IsEditable = false,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "AssignedTeam",
                Type = (FieldDataType) 6,
                Order = 99,
                IsVisible = true,
                IsEditable = false,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "Subscriber",
                Type = (FieldDataType) 7,
                Order = 10,
                IsVisible = true,
                IsEditable = true,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "LastChannel",
                Type = (FieldDataType) 10,
                Order = 11,
                IsVisible = true,
                IsEditable = false,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "LastContact",
                Type = (FieldDataType) 8,
                Order = 12,
                IsVisible = true,
                IsEditable = false,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "Order Status",
                Type = (FieldDataType) 6,
                Order = 17,
                IsVisible = true,
                IsEditable = true,
                IsDefault = false,
                IsDeletable = true
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "ContactOwner",
                Type = (FieldDataType) 9,
                Order = 5,
                IsVisible = true,
                IsEditable = true,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "LastContactFromCustomers",
                Type = (FieldDataType) 8,
                Order = 13,
                IsVisible = true,
                IsEditable = false,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "Fulfillment Status",
                Type = (FieldDataType) 6,
                Order = 18,
                IsVisible = true,
                IsEditable = true,
                IsDefault = false,
                IsDeletable = true
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "LeadStage",
                Type = (FieldDataType) 6,
                Order = 6,
                IsVisible = false,
                IsEditable = true,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "PhoneNumber",
                Type = (FieldDataType) 4,
                Order = 2,
                IsVisible = true,
                IsEditable = true,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "JobTitle",
                Type = (FieldDataType) 1,
                Order = 4,
                IsVisible = true,
                IsEditable = true,
                IsDefault = true,
                IsDeletable = true
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "CompanyName",
                Type = (FieldDataType) 1,
                Order = 3,
                IsVisible = true,
                IsEditable = true,
                IsDefault = true,
                IsDeletable = true
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "Country",
                Type = (FieldDataType) 6,
                Order = 9,
                IsVisible = true,
                IsEditable = false,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "Priority",
                Type = (FieldDataType) 6,
                Order = 8,
                IsVisible = true,
                IsEditable = true,
                IsDefault = true,
                IsDeletable = true
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "LeadSource",
                Type = (FieldDataType) 6,
                Order = 7,
                IsVisible = false,
                IsEditable = true,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "Email",
                Type = (FieldDataType) 5,
                Order = 1,
                IsVisible = true,
                IsEditable = true,
                IsDefault = true,
                IsDeletable = false
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "Abandoned Cart",
                Type = (FieldDataType) 7,
                Order = 17,
                IsVisible = true,
                IsEditable = true,
                IsDefault = false,
                IsDeletable = true
            },
            new ()
            {
                CompanyId = companyId,
                FieldName = "Financial Status",
                Type = (FieldDataType) 6,
                Order = 17,
                IsVisible = true,
                IsEditable = true,
                IsDefault = false,
                IsDeletable = true
            },
        };

        var currentUserProfileFields = await _appDbContext.CompanyCustomUserProfileFields
            .Where(s => s.CompanyId == companyId)
            .Select(
                x =>
                    x.FieldName)
            .ToListAsync();

        demoUserProfileFields = demoUserProfileFields.Where(x => !currentUserProfileFields.Contains(x.FieldName))
            .ToList();

        _appDbContext.CompanyCustomUserProfileFields.AddRange(demoUserProfileFields);

        await _appDbContext.SaveChangesAsync();

        return demoUserProfileFields;
    }

    private async Task<List<ImportContactHistory>> AddDemoContactList(string companyId, Staff staff)
    {
        var demoContactLists = new List<ImportContactHistory>()
        {
            new ()
            {
                CompanyId = companyId, ImportName = "[Demo] Campaign", ImportedFromId = staff.Id
            },
            new ()
            {
                CompanyId = companyId, ImportName = "[Demo] Leads", ImportedFromId = staff.Id
            },
        };

        var currentContactLists = await _appDbContext.CompanyImportContactHistories
            .Where(s => s.CompanyId == companyId)
            .Select(
                x =>
                    x.ImportName)
            .ToListAsync();

        _appDbContext.CompanyImportContactHistories.AddRange(
            demoContactLists.Where(x => !currentContactLists.Contains(x.ImportName)).ToList());
        await _appDbContext.SaveChangesAsync();

        return demoContactLists;
    }

    private async ValueTask<UserProfile> AddDemoUserProfileAndCustomFields(string companyId, CompanyTeam team)
    {
        if (await _appDbContext.UserProfiles
                .AnyAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.FirstName == "SleekFlow"
                        && x.LastName == "Team"))
        {
            return null;
        }

        var demoUser = new UserProfile
        {
            CompanyId = companyId,
            FirstName = "SleekFlow",
            LastName = "Team",
            ActiveStatus = ActiveStatus.Active,
            IsSandbox = false,
            WhatsAppAccount = new WhatsAppSender()
            {
                whatsAppId = "whatsapp:+***********",
                phone_number = "***********",
                CompanyId = companyId,
                name = "Sleekflow",
                locale = "en",
                InstanceId = "AC1fc368e79a37f6a9e87657dd3d75a2e5",
                isFetchProfileInfo = true,
                isVerified = false,
                InstaneSender = "whatsapp:+***********"
            },
            EmailAddress = new EmailSender()
            {
                Email = "<EMAIL>", locale = "en", CompanyId = companyId,
            },
            SMSUser = new SMSSender()
            {
                SMSId = "+***********",
                phone_number = "***********",
                name = "Anonymous",
                locale = "en",
                CompanyId = companyId,
            }
        };

        _appDbContext.UserProfiles.Add(demoUser);

        await _appDbContext.SaveChangesAsync();

        var companyCustomUserProfileField = await _appDbContext
            .CompanyCustomUserProfileFields
            .AsNoTracking()
            .Where(c => c.CompanyId == companyId)
            .ToListAsync();

        var demoUserCustomField = new List<UserProfileCustomField>();

        foreach (var field in companyCustomUserProfileField)
        {
            // if (field.FieldName == "Labels") ;
            // if (field.FieldName == "PositionOfContactOwner") ;
            if (field.FieldName == "AssignedTeam")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = team.Id.ToString(),
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "Subscriber")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "true",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "LastChannel")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = ChannelTypes.WhatsappTwilio,
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "LastContact")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "2021-09-04T06:48:06.5684979Z",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "Order Status")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "Cancelled",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "ContactOwner")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "f11b25e2-76cc-4287-8f24-29838b7f9fab",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "LastContactFromCustomers")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "2021-09-04T06:46:39.6155739Z",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "Fulfillment Status")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "Arrived",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "LeadStage")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "Prospect",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "PhoneNumber")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "***********",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "JobTitle")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "Developer",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "CompanyName")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "SleekFlow",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "Country")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "Hong Kong SAR",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "Priority")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "High",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "LeadSource")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "Organic search",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "Email")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "<EMAIL>",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "Abandoned Cart")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "true",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }

            if (field.FieldName == "Financial Status")
            {
                demoUserCustomField.Add(
                    new ()
                    {
                        UserProfileId = demoUser.Id,
                        Value = "Authorized",
                        CompanyDefinedFieldId = field.Id,
                        CompanyId = companyId
                    });
            }
        }

        _appDbContext.UserProfileCustomFields.AddRange(demoUserCustomField);

        await _appDbContext.SaveChangesAsync();

        return demoUser;
    }

    private async Task<List<CompanyMessageTemplate>> AddDemoCampaigns(string companyId, Staff staff)
    {
        var data = new List<CompanyMessageTemplate>()
        {
            new ()
            {
                CompanyId = companyId,
                TemplateName = "[Demo] Campaign Message",
                TemplateContent =
                    "Hello {{1}},\nThis is a testing message.\nFeel free to edit the content and add the parameters in the above bracket!\nYou can also pick the Channels on the left to send the message. \nPlease note that Official WhatsApp API does not support sending attachment in template message. After confirming the recipient list you can now click the blue button \nReview details and send.",
                TemplateParams = null,
                Status = BroadcastStatus.Draft,
                Conditions = new List<Condition>()
                {
                    new ()
                    {
                        FieldName = "importfrom",
                        ConditionOperator = (SupportedOperator) 3,
                        Values = new List<string>()
                        {
                            "35941"
                        },
                        NextOperator = (SupportedNextOperator) 0
                    }
                },
                SavedBy = staff,

                // TargetedChannelWithIds = new List<TargetedChannelModel>()
                // {
                //     new() { channel = ChannelTypes.WhatsappTwilio, ids = new List<string>() { "AC1fc368e79a37f6a9e87657dd3d75a2e5;whatsapp:+***********" } }
                // },
                StatisticsData = new StatisticsData()
                {
                    Sent = 0,
                    Failed = 0,
                    Delivered = 0,
                    Read = 0,
                    Replied = 0,
                    UpdatedAt = DateTime.UtcNow
                },
                BroadcastAsNote = false,
                IsBroadcastOn = true,
            }
        };

        var currentCampaigns = await _appDbContext
            .CompanyMessageTemplates
            .Where(x => x.CompanyId == companyId)
            .Select(x => x.TemplateName)
            .ToListAsync();

        _appDbContext.CompanyMessageTemplates.AddRange(data.Where(x => !currentCampaigns.Contains(x.TemplateName)));

        await _appDbContext.SaveChangesAsync();

        return data;
    }

    private async Task<Conversation> AddConversation(string companyId, UserProfile userProfile, Staff staff)
    {
        var data = new Conversation()
        {
            CompanyId = companyId,
            MessageGroupName = companyId,
            Status = "open",
            AssigneeId = staff.Id,
            WhatsappUser = userProfile.WhatsAppAccount,
            SMSUser = userProfile.SMSUser,
            UpdatedTime = DateTime.UtcNow,
            ModifiedAt = DateTime.UtcNow,
            IsSandbox = false,
            IsBookmarked = false,
            IsNewCreatedConversation = false,
            LastMessageChannel = ChannelTypes.WhatsappTwilio,
            CreatedAt = DateTime.UtcNow,
            LastMessageId = null,

            // ChatHistory = new List<ConversationMessage>()
            // {
            //
            // }
        };

        _appDbContext.Conversations.Add(data);

        await _appDbContext.SaveChangesAsync();

        return data;
    }

    private async Task<List<ConversationMessage>> AddConversationMessages(
        string companyId,
        Conversation conversation,
        UserProfile userProfile,
        Staff admin,
        Staff sale)
    {
        var conversationMessages = new List<ConversationMessage>
        {
            new ()
            {
                // Sent From Sleekflow
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                SenderId = admin.IdentityId,
                whatsappReceiverId = userProfile.WhatsAppAccountId,
                MessageContent = "Hello there! How can we help you today?",
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "text",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Read,
                CompanyId = companyId,
                IsSentFromSleekflow = true,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent from user
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                whatsappSenderId = userProfile.WhatsAppAccountId,
                MessageContent =
                    "👋 Hi there\nWelcome to SleekFlow - the #1 platform for selling and supporting your customers on social channels⚡\n*Want to find out more about us?*\n[1] I'd like to learn more\n[2] I'm a customer with a question\n[3] I have inquiries on partnership\nSimply type the number to proceed!\n_輸入Chi以切換語言至中文_",
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "text",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Received,
                CompanyId = companyId,
                IsSentFromSleekflow = false,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent From Sleekflow
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                SenderId = admin.IdentityId,
                whatsappReceiverId = userProfile.WhatsAppAccountId,
                MessageContent = "1",
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "text",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Read,
                CompanyId = companyId,
                IsSentFromSleekflow = true,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent from user
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                whatsappSenderId = userProfile.WhatsAppAccountId,
                MessageContent = "What's your company team size?\n[1] 1-10\n[2] 11-50\n[3] >50",
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "text",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Received,
                CompanyId = companyId,
                IsSentFromSleekflow = false,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent From Sleekflow
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                SenderId = admin.IdentityId,
                whatsappReceiverId = userProfile.WhatsAppAccountId,
                MessageContent = "3",
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "text",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Read,
                CompanyId = companyId,
                IsSentFromSleekflow = true,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent from user
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                whatsappSenderId = userProfile.WhatsAppAccountId,
                MessageContent =
                    "Thanks for your reply!\nOur solution consultant, *Ronald*, will be in touch shortly💡",
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "text",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Received,
                CompanyId = companyId,
                IsSentFromSleekflow = false,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent from user
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                whatsappSenderId = userProfile.WhatsAppAccountId,
                MessageContent =
                    "To learn more about industry best practice and use case, feel free to book a 1-to-1 demo session with us:\nhttps://sleekflow.io/demo-ry",
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "text",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Received,
                CompanyId = companyId,
                IsSentFromSleekflow = false,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent from user
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                whatsappSenderId = userProfile.WhatsAppAccountId,
                MessageContent = "*Please feel free to send message to this testing account*",
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "text",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Received,
                CompanyId = companyId,
                IsSentFromSleekflow = false,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent from user
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                whatsappSenderId = userProfile.WhatsAppAccountId,
                MessageContent = "Hi! Sleekflow!",
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "text",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Received,
                CompanyId = companyId,
                IsSentFromSleekflow = false,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent From Sleekflow
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                SenderId = admin.IdentityId,
                MessageContent = "Please reply this",
                Channel = ChannelTypes.Note,
                Visibility = "private",
                MessageType = "text",
                MessageAssigneeId = sale.Id,
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Read,
                CompanyId = companyId,
                IsSentFromSleekflow = true,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent From Sleekflow
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                SenderId = sale.IdentityId,
                whatsappReceiverId = userProfile.WhatsAppAccountId,
                MessageContent = "Hello how can I help you?",
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "text",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Read,
                CompanyId = companyId,
                IsSentFromSleekflow = true,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent from user
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                whatsappSenderId = userProfile.WhatsAppAccountId,
                MessageContent =
                    "Hi there👋 \nOur team is currently off for the weekend!\nExpect a reply on Monday🚀\nMeanwhile, feel free to search from our help centre:\nhttps://docs.sleekflow.io/using-the-platform?q=",
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "text",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Received,
                CompanyId = companyId,
                IsSentFromSleekflow = false,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            },
            new ()
            {
                // Sent From Sleekflow
                ConversationId = conversation.Id,
                MessageChecksum = Guid.NewGuid().ToString(),
                SenderId = sale.IdentityId,
                whatsappReceiverId = userProfile.WhatsAppAccountId,
                Channel = ChannelTypes.WhatsappTwilio,
                Visibility = "public",
                MessageType = "file",
                DeliveryType = DeliveryType.Normal,
                Status = MessageStatus.Read,
                CompanyId = companyId,
                IsSentFromSleekflow = true,
                IsSandbox = false,
                FrondendTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                UploadedFiles = new List<UploadedFile>
                {
                    new ()
                    {
                        SenderId = sale.IdentityId,
                        BlobContainer = "f751e66d-f2fb-4147-98ba-6396b6b9dfef",
                        Filename =
                            "Conversation/5f97ba5c-209a-481b-9f3a-9495a25ff009/9/4/2021 6:48:06 AM/flutter_audio_recorder_1630738086492.mp3",
                        MIMEType = "audio/mpeg",
                        FileSize = 5596,
                    }
                }
            },
        };

        _appDbContext.ConversationMessages.AddRange(conversationMessages);

        await _appDbContext.SaveChangesAsync();

        return conversationMessages;
    }

    private async Task<List<AssignmentRule>> AddAssignmentRule(
        string companyId,
        Staff user,
        CompanyTeam team1,
        CompanyTeam team2,
        ImportContactHistory campaignList,
        ImportContactHistory leadsList)
    {
        var demoAssignmentRule = new List<AssignmentRule>
        {
            new ()
            {
                // 14205
                CompanyId = companyId,
                AssignmentRuleName = "[Demo] Off-hour (Monday - Friday) - HK",
                AssignmentType = AssignmentType.Unassigned,
                Order = 7,
                AutomationType = (AutomationType) 2,
                Status = (AutomationStatus) 0,
                SavedBy = user,
                Conditions = JsonConvert.DeserializeObject<List<Condition>>(
                    @"[{""FieldName"":""LastContactFromCustomers"",""ConditionOperator"":13,""Values"":[""1"",""2"",""3"",""4"",""5""],""NextOperator"":0},{""FieldName"":""LastContact"",""ConditionOperator"":7,""Values"":[""1""],""TimeValueType"":2,""NextOperator"":0},{""FieldName"":""LastContactFromCustomers"",""ConditionOperator"":12,""Values"":[""2021-08-26T11:00:00.000Z"",""2021-08-26T01:00:00.000Z""],""NextOperator"":0},{""FieldName"":""ContactOwner"",""ConditionOperator"":4,""Values"":[],""NextOperator"":0},{""FieldName"":""Language"",""ConditionOperator"":3,""Values"":[""zh_CHS"",""zh_CHT""],""NextOperator"":0}]"),
                TargetedChannelWithIds = new List<TargetedChannelModel>(),
                AutomationActions = new List<AutomationAction>()
                {
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 1,
                        AssignmentType = (AssignmentType) 0,
                        Order = 0,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>(),
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>(),
                        MessageContent = "你好 唔好意思\n我哋辦公時間為9:00am-7:00pm\n我哋會喺聽日早上盡快回覆你🚀",
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>()
                    }
                }
            },
            new ()
            {
                // 14215
                CompanyId = companyId,
                AssignmentRuleName = "[Demo] Off-hour (Sat-Sun) - HK",
                AssignmentType = AssignmentType.Unassigned,
                Order = 8,
                AutomationType = (AutomationType) 2,
                Status = (AutomationStatus) 0,
                SavedBy = user,
                Conditions = JsonConvert.DeserializeObject<List<Condition>>(
                    @"[{""FieldName"":""LastContactFromCustomers"",""ConditionOperator"":13,""Values"":[""6"",""7""],""NextOperator"":0},{""FieldName"":""LastContact"",""ConditionOperator"":7,""Values"":[""1""],""TimeValueType"":2,""NextOperator"":0},{""FieldName"":""ContactOwner"",""ConditionOperator"":4,""Values"":[],""NextOperator"":0},{""FieldName"":""Language"",""ConditionOperator"":3,""Values"":[""zh_CHS"",""zh_CHT""],""NextOperator"":0}]"),
                TargetedChannelWithIds = new List<TargetedChannelModel>(),
                AutomationActions = new List<AutomationAction>()
                {
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 1,
                        AssignmentType = (AssignmentType) 0,
                        Order = 0,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>(),
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>(),
                        MessageContent = "Hello👋\n唔好意思 因為週末關係\n我會喺星期一回覆你🚀",
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>()
                    }
                }
            },
            new ()
            {
                // 14405
                CompanyId = companyId,
                AssignmentRuleName = "[Demo] Order Confirmation",
                AssignmentType = AssignmentType.Unassigned,
                Order = 9,
                AutomationType = (AutomationType) 1,
                Status = (AutomationStatus) 0,
                SavedBy = user,
                Conditions =
                    JsonConvert.DeserializeObject<List<Condition>>(
                        @"[{""FieldName"":""Order Status"",""ConditionOperator"":20,""Values"":[""Confirmed""],""NextOperator"":0}]"),
                TargetedChannelWithIds = new List<TargetedChannelModel>(),
                AutomationActions = new List<AutomationAction>()
                {
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 1,
                        AssignmentType = (AssignmentType) 0,
                        Order = 0,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>(),
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>()
                        {
                            "@firstname"
                        },
                        MessageContent =
                            "Hello {0},\nYour Order has been confirmed! We will keep you updated with the delivery status! Thank you! 😊",
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>()
                    },
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 6,
                        AssignmentType = (AssignmentType) 3,
                        Order = 1,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>()
                        {
                        },
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>()
                        {
                        },
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>(),
                        TeamAssignmentType = (AssignmentType) 1,
                        AssignedTeamId = team1.Id
                    },
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 4,
                        AssignmentType = (AssignmentType) 0,
                        Order = 2,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>()
                        {
                        },
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>()
                        {
                            new ()
                            {
                                Hashtag = "Confirmed"
                            }
                        },
                    }
                }
            },
            new ()
            {
                // 14406
                CompanyId = companyId,
                AssignmentRuleName = "[Demo] Chase Payment",
                AssignmentType = AssignmentType.Unassigned,
                Order = 10,
                AutomationType = (AutomationType) 1,
                Status = (AutomationStatus) 0,
                SavedBy = user,
                Conditions =
                    JsonConvert.DeserializeObject<List<Condition>>(
                        @"[{""FieldName"":""Financial Status"",""ConditionOperator"":20,""Values"":[""Failed"",""Expired"",""Unpaid""],""NextOperator"":0}]"),
                TargetedChannelWithIds = new List<TargetedChannelModel>(),
                AutomationActions = new List<AutomationAction>()
                {
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 1,
                        AssignmentType = (AssignmentType) 0,
                        Order = 0,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>(),
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>()
                        {
                            "@firstname", "@Financial Status"
                        },
                        MessageContent =
                            "Hello {0},\nYour payment is {1}, please complete the payment to proceed with the order. We will hold the order for 3 more work days.",
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>()
                    },
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 4,
                        AssignmentType = (AssignmentType) 0,
                        Order = 1,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>()
                        {
                            new ()
                            {
                                Hashtag = "Pending payment"
                            }
                        },
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>()
                        {
                        },
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>()
                    },
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 6,
                        AssignmentType = (AssignmentType) 3,
                        Order = 2,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>()
                        {
                        },
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>()
                        {
                        },
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>(),
                        AssignedTeamId = team2.Id, // TODO
                        TeamAssignmentType = (AssignmentType) 0,
                    }
                }
            },
            new ()
            {
                // 14407
                CompanyId = companyId,
                AssignmentRuleName = "[Demo] Shipped Order",
                AssignmentType = AssignmentType.Unassigned,
                Order = 11,
                AutomationType = (AutomationType) 1,
                Status = (AutomationStatus) 0,
                SavedBy = user,
                Conditions = JsonConvert.DeserializeObject<List<Condition>>(
                    @"[{""FieldName"":""Fulfillment Status"",""ConditionOperator"":20,""Values"":[""Shipped""],""NextOperator"":0},{""FieldName"":""Financial Status"",""ConditionOperator"":20,""Values"":[""Completed""],""NextOperator"":0}]"),
                TargetedChannelWithIds = new List<TargetedChannelModel>(),
                AutomationActions = new List<AutomationAction>()
                {
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 1,
                        AssignmentType = (AssignmentType) 0,
                        Order = 0,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>(),
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>()
                        {
                            "@firstname"
                        },
                        MessageContent = "Hello {0}\n\nYour order is on its way! We have shipped your order!",
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>()
                    },
                }
            },
            new ()
            {
                // 14492
                CompanyId = companyId,
                AssignmentRuleName = "[Demo] Assignment by Queue (New Customers - WhatsApp)",
                AssignmentType = AssignmentType.Unassigned,
                Order = 3,
                AutomationType = (AutomationType) 2,
                Status = (AutomationStatus) 1,
                SavedBy = user,
                Conditions = JsonConvert.DeserializeObject<List<Condition>>(
                    @"[{""FieldName"":""LastChannel"",""ConditionOperator"":3,""Values"":[],""NextOperator"":0},{""FieldName"":""ContactOwner"",""ConditionOperator"":5,""Values"":[],""NextOperator"":0}]"),
                TargetedChannelWithIds = new List<TargetedChannelModel>(),
                AutomationActions = new List<AutomationAction>()
                {
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 6,
                        AssignmentType = (AssignmentType) 1,
                        Order = 0,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>()
                        {
                        },
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>()
                        {
                        },
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>(),
                    },
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 1,
                        AssignmentType = (AssignmentType) 0,
                        Order = 1,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>()
                        {
                        },
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>()
                        {
                            "@ContactOwner"
                        },
                        MessageContent = "Thank you for your enquiry!\nYou're now connected to {0}⚡",
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>(),
                    },
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 4,
                        AssignmentType = (AssignmentType) 0,
                        Order = 2,
                        ActionAddedToGroupIds = new List<long>(),
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>()
                        {
                            new ()
                            {
                                Hashtag = "Confirmed"
                            }
                        },
                    }
                }
            },
            new ()
            {
                // 14498
                CompanyId = companyId,
                AssignmentRuleName = "[Demo] Auto-Reply when Customers Reply a Campaign",
                AssignmentType = AssignmentType.Unassigned,
                Order = 4,
                AutomationType = (AutomationType) 2,
                Status = (AutomationStatus) 0,
                SavedBy = user,
                Conditions = JsonConvert.DeserializeObject<List<Condition>>(
                    @"[{""FieldName"":""Message"",""ConditionOperator"":3,""Values"":[""1""],""NextOperator"":0},{""FieldName"":""importfrom"",""ConditionOperator"":22,""Values"":[""35941""],""NextOperator"":0}]"),
                TargetedChannelWithIds = new List<TargetedChannelModel>(),
                AutomationActions = new List<AutomationAction>()
                {
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 1,
                        AssignmentType = (AssignmentType) 0,
                        Order = 0,
                        MessageParams = new List<string>()
                        {
                        },
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        MessageContent = "Hi there! Glad that you have chose our first option!",
                        AddAdditionalAssigneeIds = new List<string>(),
                    },
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 7,
                        AssignmentType = (AssignmentType) 0,
                        Order = 1,
                        ActionAddedToGroupIds = new List<long>()
                        {
                        },
                        ActionRemoveFromGroupIds = new ()
                        {
                            campaignList.Id
                        },
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>()
                        {
                        },
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>()
                        {
                        },
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>(),
                    },
                    new ()
                    {
                        CompanyId = companyId,
                        AutomatedTriggerType = (AutomatedTriggerType) 2,
                        AssignmentType = (AssignmentType) 0,
                        Order = 2,
                        ActionAddedToGroupIds = new List<long>()
                        {
                            leadsList.Id
                        },
                        ActionUpdateCustomFields = new List<AddCustomFieldsViewModel>(),
                        ActionAddConversationHashtags = new List<ConversationHashtagViewModel>()
                        {
                        },
                        ActionAddConversationRemarks = new List<RemarkViewModel>(),
                        MessageParams = new List<string>()
                        {
                        },
                        ActionRemoveFromGroupIds = new (),
                        TargetedChannelWithIds = new List<TargetedChannelModel>(),
                        AddAdditionalAssigneeIds = new List<string>(),
                    },
                }
            },
        };

        var currentAssignmentRule = await _appDbContext.CompanyAssignmentRules
            .Where(x => x.CompanyId == companyId)
            .Select(x => x.AssignmentRuleName)
            .ToListAsync();

        demoAssignmentRule = demoAssignmentRule.Where(x => !currentAssignmentRule.Contains(x.AssignmentRuleName))
            .ToList();

        foreach (var assignmentRule in demoAssignmentRule)
        {
            _appDbContext.CompanyAssignmentRules.Add(assignmentRule);

            await _appDbContext.SaveChangesAsync();
        }

        return demoAssignmentRule;
    }

    private class StaffToImport
    {
        public string FirstName { get; set; }

        public string LastName { get; set; }

        // public string Email { get; set; }
        public string Password { get; set; }

        public string Username { get; set; }

        public StaffUserRole Role { get; set; }

        public string Team { get; set; }

        public string Position { get; set; }

        public int Order { get; set; }
    }

    private string GenerateRandomUsernamePostfix()
    {
        var random = new Random();
        const string chars = "abcdefghijklmnopqrstuvwxyz0123456789";

        return new string(Enumerable.Repeat(chars, 8).Select(s => s[random.Next(s.Length)]).ToArray());
    }

    #endregion
}