using Newtonsoft.Json;
using Travis_backend.CompanyDomain.Models;

namespace Travis_backend.CompanyDomain.ViewModels;

public class UpdateBusinessHourConfigModel
{
    public bool IsEnabled { get; set; }

    public WeeklyHours WeeklyHours { get; set; }

    [JsonConstructor]
    public UpdateBusinessHourConfigModel(bool isEnabled, WeeklyHours weeklyHours)
    {
        IsEnabled = isEnabled;
        WeeklyHours = weeklyHours;
    }

    public UpdateBusinessHourConfigModel(BusinessHourConfig businessHourConfig)
    {
        IsEnabled = businessHourConfig.IsEnabled;

        WeeklyHours = businessHourConfig.WeeklyHours;
    }
}