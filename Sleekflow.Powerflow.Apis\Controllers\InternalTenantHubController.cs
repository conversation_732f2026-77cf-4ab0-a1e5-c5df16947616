using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.Apis.TenantHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.Enums;
using Travis_backend.Telemetries;
using Travis_backend.Telemetries.Constants;
using Travis_backend.TenantHubDomain.Services;
using Travis_backend.TenantHubDomain.ViewModels;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Route("/internal/tenant-hub/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
public class InternalTenantHubController : InternalControllerBase
{
    private readonly IEnabledFeaturesService _enabledFeaturesService;
    private readonly ILogger<InternalTenantHubController> _logger;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

    public InternalTenantHubController(
        UserManager<ApplicationUser> userManager,
        IEnabledFeaturesService enabledFeaturesService,
        ILogger<InternalTenantHubController> logger,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
        : base(userManager)
    {
        _enabledFeaturesService = enabledFeaturesService;
        _logger = logger;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
    }

    public record FeatureToggleRequest(string companyId, string featureName);

    public record UpdateSubscriptionTierDefaultFeatureFlagsRequest(int SubscriptionTier, List<string> FeatureFlags);


    // Enable feature for company
    [HttpPost]
    public async Task<ActionResult<EnabledFeature>> EnableFeatureForCompany([FromBody] FeatureToggleRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user is null)
        {
            return Unauthorized();
        }

        var result = await _enabledFeaturesService.EnableFeatureForCompany(request.companyId, request.featureName, user.Id);
        _applicationInsightsTelemetryTracer.TraceEvent(
            TraceEventNames.FeatureFlagEnabled,
            new Dictionary<string, string>
            {
                { "feature_name", request.featureName },
                { "sleekflow_company_id", request.companyId },
                { "operator_user_id", user.Id},
                { "operator_user_email", user.Email },
                { "success", result.Success.ToString() }
            });
        return Ok(result.Data.EnabledFeature);
    }

    // Disable feature for company
    [HttpPost]
    public async Task<ActionResult<EnabledFeature>> DisableFeatureForCompany([FromBody] FeatureToggleRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user is null)
        {
            return Unauthorized();
        }

        var result = await _enabledFeaturesService.DisableFeatureForCompany(request.companyId, request.featureName, user.Id);
        _applicationInsightsTelemetryTracer.TraceEvent(
            TraceEventNames.FeatureFlagDisabled,
            new Dictionary<string, string>
            {
                { "feature_name", request.featureName },
                { "sleekflow_company_id", request.companyId },
                { "operator_user_id", user.Id},
                { "operator_user_email", user.Email },
                { "success", result.Success.ToString() }
            });
        return Ok(result);
    }

    [HttpPost]
    public async Task<ActionResult<bool>> SyncAllFeatureFlagsBasedOnPlan()
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user is null)
        {
            return Unauthorized();
        }

        var result = await _enabledFeaturesService.SyncAllFeatureFlagsBasedOnPlan();
        return Ok(result);
    }

    [HttpPost]
    public async Task<ActionResult<GetAllSubscriptionTierDefaultFeatureFlagsResponse>>
        GetAllSubscriptionTierDefaultFeatureFlags()
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            var allDefaultFeatureFlags = await _enabledFeaturesService.GetAllSubscriptionTierDefaultFeatureFlags();

            var response = new GetAllSubscriptionTierDefaultFeatureFlagsResponse
            {
                SubscriptionTierFeatureFlags = allDefaultFeatureFlags.Select(
                    x => new SubscriptionTierFeatureFlags
                    {
                        SubscriptionTierId = x.SubscriptionTier,
                        SubscriptionTierName = Enum.GetName(typeof(SubscriptionTier), x.SubscriptionTier),
                        FeatureFlags = x.DefaultFeatureFlags
                    }).ToList()
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all subscription tier default feature flags");
            return BadRequest(new ResponseViewModel("Error retrieving all subscription tier default feature flags."));
        }
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdateSubscriptionTierDefaultFeatureFlags(
        [FromBody]
        UpdateSubscriptionTierDefaultFeatureFlagsRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            var result = await _enabledFeaturesService.UpdateSubscriptionTierDefaultFeatureFlags(
                request.SubscriptionTier,
                request.FeatureFlags);

            if (!result)
            {
                return BadRequest(new ResponseViewModel("Failed to update subscription tier default feature flags."));
            }

            return Ok(
                new ResponseViewModel
                {
                    message = "Subscription tier default feature flags updated successfully."
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error updating subscription tier default feature flags for tier: {SubscriptionTier}",
                request.SubscriptionTier);
            return BadRequest(new ResponseViewModel("Error updating subscription tier default feature flags."));
        }
    }
}