﻿using System;

namespace Travis_backend.Cache.Models.CacheKeyPatterns;

public class TopicAnalyticsMetricsCacheKeyPattern : ICacheKeyPattern
{
    private const string MetricType = "Topic";

    public string CompanyId { get; set; }

    public DateOnly Date { get; set; }

    public string TopicId { get; set; }


    public TopicAnalyticsMetricsCacheKeyPattern(
        string companyId,
        string topicId,
        DateOnly date)
    {
        CompanyId = companyId;
        Date = date;
        TopicId = topicId;
    }

    public string GenerateKeyPattern()
    {
        var keyName = CacheHelper.BuildKeyNameFromPattern(
            new object[]
            {
                CompanyId,
                Date,
                MetricType,
                TopicId,
            });
        return keyName;
    }
}