﻿using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Components.Configs;

public class VNetConfig
{
    [Json<PERSON>roperty("default_address_space")]
    public string DefaultAddressSpace { get; set; }

    [JsonProperty("default_subnet_address_prefix")]
    public string DefaultSubnetAddressPrefix { get; set; }

    [JsonProperty("sleekflow_core_db_address_prefix")]
    public string SleekflowCoreDbAddressPrefix { get; set; }

    [JsonProperty("sleekflow_core_address_prefix")]
    public string SleekflowCoreAddressPrefix { get; set; }

    [Json<PERSON>roperty("sleekflow_core_worker_address_prefix")]
    public string SleekflowCoreWorkerAddressPrefix { get; set; }

    [JsonProperty("sleekflow_powerflow_address_prefix")]
    public string SleekflowPowerflowAddressPrefix { get; set; }

    [JsonProperty("sleekflow_sleek_pay_address_prefix")]
    public string SleekflowSleekPayAddressPrefix { get; set; }

    public VNetConfig(
        string defaultAddressSpace,
        string defaultSubnetAddressPrefix,
        string sleekflowCoreDbAddressPrefix,
        string sleekflowCoreAddressPrefix,
        string sleekflowCoreWorkerAddressPrefix,
        string sleekflowPowerflowAddressPrefix,
        string sleekflowSleekPayAddressPrefix)
    {
        DefaultAddressSpace = defaultAddressSpace;
        DefaultSubnetAddressPrefix = defaultSubnetAddressPrefix;
        SleekflowCoreDbAddressPrefix = sleekflowCoreDbAddressPrefix;
        SleekflowCoreAddressPrefix = sleekflowCoreAddressPrefix;
        SleekflowCoreWorkerAddressPrefix = sleekflowCoreWorkerAddressPrefix;
        SleekflowPowerflowAddressPrefix = sleekflowPowerflowAddressPrefix;
        SleekflowSleekPayAddressPrefix = sleekflowSleekPayAddressPrefix;
    }
}