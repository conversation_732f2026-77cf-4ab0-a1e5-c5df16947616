namespace Travis_backend.CompanyDomain.Services;

using System;

/// <inheritdoc/>
public class UsageCycleCalculator : IUsageCycleCalculator
{
    /// <summary>
    /// TimeProvider.
    /// </summary>
    private readonly TimeProvider _timeProvider;

    /// <summary>
    /// Initializes a new instance of the <see cref="UsageCycleCalculator"/> class.
    /// </summary>
    /// <param name="timeProvider">TimeProvider.</param>
    public UsageCycleCalculator(TimeProvider timeProvider)
    {
        _timeProvider = timeProvider;
    }

    /// <inheritdoc/>
    public (DateTime From, DateTime To) GetUsageCycle(DateTime periodStart, DateTime periodEnd)
    {
        var (from, to) = GetUsageCycle(periodStart, periodEnd, TimeSpan.Zero);
        return (from.DateTime, to.DateTime);
    }

    /// <inheritdoc/>
    public (DateTimeOffset From, DateTimeOffset To) GetUsageCycle(DateTimeOffset periodStart, DateTimeOffset periodEnd, TimeSpan offset = default)
    {
        DateTimeOffset now = _timeProvider.GetUtcNow();

        var passedMonthsOffset = GetPassMonthOffset(periodStart, now);
        var passedMonths = now.Month - periodStart.Month + (12 * (now.Year - periodStart.Year)) + passedMonthsOffset;

        var usageCycleFrom = periodStart.AddMonths(passedMonths);
        var usageCycleTo = periodStart.AddMonths(passedMonths + 1) < periodEnd ? periodStart.AddMonths(passedMonths + 1) : periodEnd;

        return (new DateTimeOffset(usageCycleFrom.DateTime, offset), new DateTimeOffset(usageCycleTo.DateTime, offset));
    }

    /// <summary>
    /// Determine if current time is passed a month and return the offset.
    /// </summary>
    /// <param name="periodStart">Period Start of BillRecord.</param>
    /// <param name="now">Current DateTime.</param>
    /// <returns>Return 0 if passed the month, else return -1.</returns>
    private int GetPassMonthOffset(DateTimeOffset periodStart, DateTimeOffset now)
    {
        var daysInCurrentMonth = DateTime.DaysInMonth(now.Year, now.Month);

        return
            (
                periodStart.Day > daysInCurrentMonth
                && !(periodStart is { Month: 2, Day: 29 } && daysInCurrentMonth == 28)
            )
            || now.Day >= periodStart.Day ? 0 : -1;
    }
}