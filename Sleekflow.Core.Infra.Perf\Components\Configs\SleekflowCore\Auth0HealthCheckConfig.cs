using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Perf.Components.Configs.SleekflowCore;

public class Auth0HealthCheckConfig
{
    [JsonProperty("is_enabled")]
    public string IsEnabled { get; set; }

    [JsonProperty("client_id")]
    public string ClientId { get; set; }

    [JsonProperty("client_secret")]
    public string ClientSecret { get; set; }

    [JsonProperty("username")]
    public string Username { get; set; }

    [JsonProperty("password")]
    public string Password { get; set; }

    public Auth0HealthCheckConfig(
        string isEnabled,
        string clientId,
        string clientSecret,
        string username,
        string password)
    {
        IsEnabled = isEnabled;
        ClientId = clientId;
        ClientSecret = clientSecret;
        Username = username;
        Password = password;
    }
}