namespace Sleekflow.Core.Infra.Components.Configs;

public class MyConfig
{
    public string Name { get; }

    public bool IsCi { get; }

    public string BuildTime { get; }

    public MyConfig()
    {
        IsCi = Environment.GetEnvironmentVariable("CI", EnvironmentVariableTarget.Process) == "true";

        var config = new Pulumi.Config("sleekflow");
        Name = config.Require("name");

        // Use IMAGE_TAG from environment if available (from CI), otherwise fallback to timestamp
        var imageTag = Environment.GetEnvironmentVariable("IMAGE_TAG");
        BuildTime = !string.IsNullOrEmpty(imageTag) ? imageTag : DateTimeOffset.UtcNow.ToString("yyyyMMddHHmmss");
    }
}