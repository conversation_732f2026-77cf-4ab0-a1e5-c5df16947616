using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.InternalDomain.Services;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.ResellerDomain.ViewModels;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Authorize(Roles = ApplicationUserRole.InternalCmsUser)]
[Route("/internal/partnerstack/[action]")]
public class InternalPartnerStackController : InternalControllerBase
{
    private readonly IInternalPartnerStackService _internalPartnerStackService;

    public InternalPartnerStackController(
        UserManager<ApplicationUser> userManager,
        IInternalPartnerStackService internalPartnerStackService)
        : base(userManager)
    {
        _internalPartnerStackService = internalPartnerStackService;
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdatePartnerStackCustomerKey(
        [FromBody]
        UpdatePartnerStackCustomerKeyRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
        [
            ApplicationUserRole.InternalCmsSuperUser,
            ApplicationUserRole.InternalCmsAdmin,
            ApplicationUserRole.InternalCmsCustomerSuccessUser,
            ApplicationUserRole.InternalCmsSalesUser,
            ApplicationUserRole.InternalCmsUser
        ]);

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper =
            await _internalPartnerStackService.CreateOrUpdatePartnerStackCustomerMapping(
                request.CompanyId,
                request.CustomerKey);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (CmsPartnerStackCustomerMapDto) responseWrapper.Data;

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> SyncMrrToPartnerStack(
        [FromBody]
        SyncMrrToPartnerStackRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
        [
            ApplicationUserRole.InternalCmsSuperUser,
            ApplicationUserRole.InternalCmsAdmin,
            ApplicationUserRole.InternalCmsCustomerSuccessUser,
            ApplicationUserRole.InternalCmsSalesUser,
            ApplicationUserRole.InternalCmsUser
        ]);

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper = await _internalPartnerStackService.PowerflowSyncMrrToPartnerStack(request.CompanyId);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = responseWrapper.Data.ToString();

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdatePartnerStackPartnerKey(
        [FromBody]
        UpdatePartnerStackPartnerKeyRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
        [
            ApplicationUserRole.InternalCmsSuperUser,
            ApplicationUserRole.InternalCmsAdmin,
            ApplicationUserRole.InternalCmsCustomerSuccessUser,
            ApplicationUserRole.InternalCmsSalesUser,
            ApplicationUserRole.InternalCmsUser
        ]);

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper =
            await _internalPartnerStackService.UpdatePartnerStackPartnerKey(
                request.CompanyId,
                request.PartnerKey);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (ResellerProfileInformation) responseWrapper.Data;

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdatePartnerStackIndividualCommissionConfig(
        [FromBody]
        UpdatePartnerStackCustomerCommissionConfigRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
        [
            ApplicationUserRole.InternalCmsSuperUser,
            ApplicationUserRole.InternalCmsAdmin,
            ApplicationUserRole.InternalCmsCustomerSuccessUser,
            ApplicationUserRole.InternalCmsSalesUser,
            ApplicationUserRole.InternalCmsUser
        ]);

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper = await _internalPartnerStackService.PowerflowUpdatePartnerStackIndividualCommissionConfig(
            request.CompanyId,
            request.SyncType,
            request.IndividualCommissionRate,
            request.CommissionEndDate);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (CmsPartnerStackCustomerMapDto) responseWrapper.Data;

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> SyncPartnerStackInformation(
        [FromBody]
        SyncPartnerStackInformationRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
        [
            ApplicationUserRole.InternalCmsSuperUser,
            ApplicationUserRole.InternalCmsAdmin,
            ApplicationUserRole.InternalCmsCustomerSuccessUser,
            ApplicationUserRole.InternalCmsSalesUser,
            ApplicationUserRole.InternalCmsUser
        ]);

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper = await _internalPartnerStackService.SyncPartnerStackInformation(request.CompanyId);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (CmsPartnerStackCustomerMapDto) responseWrapper.Data;

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult> GetCmsPartnerStackTransactions(
        [FromBody]
        GetCmsPartnerStackTransactionsRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
        [
            ApplicationUserRole.InternalCmsSuperUser,
            ApplicationUserRole.InternalCmsAdmin,
            ApplicationUserRole.InternalCmsCustomerSuccessUser,
            ApplicationUserRole.InternalCmsSalesUser,
            ApplicationUserRole.InternalCmsUser
        ]);

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper = await _internalPartnerStackService.GetCmsPartnerStackTransactions(request.CompanyId);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (List<CmsPartnerStackTransactionDto>) responseWrapper.Data;

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult> GetPartnerStackCompanies(
        [FromBody]
        GetPartnerStackCompaniesRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
        [
            ApplicationUserRole.InternalCmsSuperUser,
            ApplicationUserRole.InternalCmsAdmin,
            ApplicationUserRole.InternalCmsCustomerSuccessUser,
            ApplicationUserRole.InternalCmsSalesUser,
            ApplicationUserRole.InternalCmsUser
        ]);

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper = await _internalPartnerStackService.GetPartnerStackCompanies(request);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (List<CmsPartnerStackCompanyListItemView>) responseWrapper.Data;

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> GetPartnerStackSelections()
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
        [
            ApplicationUserRole.InternalCmsSuperUser,
            ApplicationUserRole.InternalCmsAdmin,
            ApplicationUserRole.InternalCmsCustomerSuccessUser,
            ApplicationUserRole.InternalCmsSalesUser,
            ApplicationUserRole.InternalCmsUser
        ]);

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper = await _internalPartnerStackService.GetPartnerStackSelections();

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = responseWrapper.Data;

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> SyncAllPartnerStackInformation()
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
        [
            ApplicationUserRole.InternalCmsSuperUser,
            ApplicationUserRole.InternalCmsAdmin,
            ApplicationUserRole.InternalCmsCustomerSuccessUser,
            ApplicationUserRole.InternalCmsSalesUser,
            ApplicationUserRole.InternalCmsUser
        ]);

        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            _internalPartnerStackService.SyncAllPartnerStackInformation();

            return Ok(
                new ResponseViewModel
                {
                    message = "Partner Stack information sync has been initiated in the background"
                });
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Error: Failed to initiate Partner Stack information sync. {ex.Message}"
                });
        }
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdatePartnerCountry(
        [FromBody]
        UpdatePartnerCountryRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
        [
            ApplicationUserRole.InternalCmsAdmin,
        ]);

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper = await _internalPartnerStackService.UpdatePartnerCountry(
            request.CompanyId,
            request.Country);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (CmsPartnerStackCustomerMapDto) responseWrapper.Data;

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdatePartnershipDealOwnerId(
        [FromBody]
        UpdatePartnershipDealOwnerIdRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
        [
            ApplicationUserRole.InternalCmsAdmin,
        ]);

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper = await _internalPartnerStackService.UpdatePartnershipDealOwnerId(
            request.CompanyId,
            request.PartnershipDealOwnerId);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (CmsPartnerStackCustomerMapDto) responseWrapper.Data;

        return Ok(response);
    }
}