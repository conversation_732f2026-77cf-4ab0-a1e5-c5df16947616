﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Stripe.Checkout;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.InternalDomain.Services;
using Travis_backend.InternalIntegrationHubDomain.Services;
using Twilio;
using Twilio.Exceptions;
using Twilio.Rest.Api.V2010;
using Twilio.Rest.Lookups.V2;

namespace Travis_backend.ConversationServices
{
    public interface ITwilioService
    {
        Task<CreateTemplateWithContentApiResponse> GetTemplateByContentApiAsync(
            string sid,
            string secret,
            string contentSid);

        Task<GetContentResponse> GetTemplateByContentApiAsync(string sid, string secret, int pageSize, int page);

        Task<CreateTemplateWithContentApiResponse> CreateTemplateByContentApiAsync(
            string sid,
            string secret,
            ContentApiTemplateRequest createTemplateViewModel);

        Task<bool> DeleteTemplateByContentApiAsync(string accountSid, string secret, string contentSid);

        Task<WhatsappTemplate> AddNewTemplate(
            string sid,
            string secret,
            CreateTemplateViewModel createTemplateViewModel);

        Task<bool> RemovesNewTemplate(string accountSid, string secret, string templateSid);

        Task Topup(
            string twilioAccountSid,
            decimal topupAmount,
            TwilioTopUpMethod topupMethod,
            Session stripeCheckoutSession = null,
            string internalUserId = null,
            bool isInternalTestingUse = false,
            string internalInvoiceId = null);

        Task<bool> ArePhoneNumbersEquivalentInTwilioAsync(
            string firstPhoneNumber,
            string secondPhoneNumber,
            string companyId);
    }

    public class TwilioService : ITwilioService
    {
        private readonly ApplicationDbContext _appDbContext;

        private readonly ILogger<TwilioService> _logger;

        // private readonly ICacheService _cacheService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly IHttpClientFactory _httpClientFactory;

        public TwilioService(
            ILogger<TwilioService> logger,
            ApplicationDbContext appDbContext,
            ICacheManagerService cacheManagerService,
            IHttpClientFactory httpClientFactory)
        {
            _logger = logger;
            _appDbContext = appDbContext;
            _cacheManagerService = cacheManagerService;
            _httpClientFactory = httpClientFactory;
        }

        public async Task<CreateTemplateWithContentApiResponse> GetTemplateByContentApiAsync(
            string sid,
            string secret,
            string contentSid)
        {
            var getContentResourceUrl = $"https://content.twilio.com/v1/Content/{contentSid}";
            try
            {
                var cachedTwilioContentObject = await _cacheManagerService.GetAndSaveCacheWithConstantKeyAsync(
                    getContentResourceUrl,
                    async () =>
                    {
                        var client = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                            "Basic",
                            Convert.ToBase64String(Encoding.ASCII.GetBytes($"{sid}:{secret}")));

                        var response = await client.GetAsync(getContentResourceUrl);

                        if (response.IsSuccessStatusCode)
                        {
                            var result =
                                JsonConvert.DeserializeObject<CreateTemplateWithContentApiResponse>(
                                    await response.Content.ReadAsStringAsync());

                            return result;
                        }

                        return null;
                    },
                    TimeSpan.FromDays(1));

                if (cachedTwilioContentObject != null)
                {
                    return cachedTwilioContentObject;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error for account sid {TwilioAccountSid}, content sid {ContentSid}",
                    nameof(GetTemplateByContentApiAsync),
                    sid,
                    contentSid);
            }

            return null;
        }

        public async Task<GetContentResponse> GetTemplateByContentApiAsync(
            string sid,
            string secret,
            int pageSize,
            int page)
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                "Basic",
                Convert.ToBase64String(Encoding.ASCII.GetBytes($"{sid}:{secret}")));

            var response = await httpClient.GetAsync(
                $"https://content.twilio.com/v1/ContentAndApprovals?PageSize={pageSize}&Page={page}");
            var str = await response.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<GetContentResponse>(await response.Content.ReadAsStringAsync());

            var bookmarkedTemplates = await _appDbContext.TwilioTemplateBookmarkRecords
                .Where(x => x.AccountSID == sid)
                .ToListAsync();

            if (bookmarkedTemplates.Count <= 0)
            {
                return result;
            }

            {
                foreach (var template in result.Contents.Where(
                             x => bookmarkedTemplates.Select(y => y.TemplateId).Contains(x.Sid)))
                {
                    var bookmarkObject = bookmarkedTemplates
                        .FirstOrDefault(x => x.TemplateId == template.Sid);

                    template.BookmarkId = bookmarkObject.Id;
                    template.BookmarkedAt = bookmarkObject.CreatedAt;
                    template.IsBookmarked = true;
                }

                result.Contents = result.Contents
                    .OrderByDescending(x => x.IsBookmarked)
                    .ThenByDescending(x => x.BookmarkedAt)
                    .ToArray();
            }

            return result;
        }

        public async Task<CreateTemplateWithContentApiResponse> CreateTemplateByContentApiAsync(
            string sid,
            string secret,
            ContentApiTemplateRequest createTemplateViewModel)
        {
            try
            {
                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                    "Basic",
                    Convert.ToBase64String(Encoding.ASCII.GetBytes($"{sid}:{secret}")));

                var response = await httpClient.PostAsJsonAsync(
                    "https://content.twilio.com/v1/Content",
                    createTemplateViewModel);
                var result =
                    JsonConvert.DeserializeObject<CreateTemplateWithContentApiResponse>(
                        await response.Content.ReadAsStringAsync());

                var submitToWhatsApp = new SubmitWhatsAppApproval
                {
                    Name = createTemplateViewModel.FriendlyName, Category = createTemplateViewModel.Category
                };

                await httpClient.PostAsJsonAsync(result.Links.ApprovalCreate, submitToWhatsApp);

                return JsonConvert.DeserializeObject<CreateTemplateWithContentApiResponse>(
                    await response.Content.ReadAsStringAsync());
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Add New Template Error: {TwilioAccountSid}:{TwilioSecret} {CreateTemplateViewModel}, Exception: {ExceptionString}",
                    sid,
                    secret,
                    JsonConvert.SerializeObject(createTemplateViewModel),
                    ex.ToString());

                throw;
            }
        }

        public async Task<bool> DeleteTemplateByContentApiAsync(string accountSid, string secret, string contentSid)
        {
            try
            {
                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                    "Basic",
                    Convert.ToBase64String(Encoding.ASCII.GetBytes($"{accountSid}:{secret}")));

                var response = await httpClient.DeleteAsync($"https://content.twilio.com/v1/Content/{contentSid}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unable to delete Content Error: {TwilioAccountSid}:{TwilioSecret} {TwilioContentSid}",
                    accountSid,
                    secret,
                    contentSid);

                throw;
            }
        }

        public async Task<WhatsappTemplate> AddNewTemplate(
            string sid,
            string secret,
            CreateTemplateViewModel createTemplateViewModel)
        {
            try
            {
                var client = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue(
                    "Basic",
                    Convert.ToBase64String(System.Text.ASCIIEncoding.ASCII.GetBytes($"{sid}:{secret}")));

                var formContent = new FormUrlEncodedContent(
                    new[]
                    {
                        new KeyValuePair<string, string>("AccountSID", sid),
                        new KeyValuePair<string, string>("Category", createTemplateViewModel.Category),
                        new KeyValuePair<string, string>(
                            "Languages",
                            JsonConvert.SerializeObject(createTemplateViewModel.Languages)),
                        new KeyValuePair<string, string>("Name", createTemplateViewModel.Name.ToLower())
                    });

                var templates = await client.PostAsync(
                    "https://messaging.twilio.com/v1/Channels/WhatsApp/Templates",
                    formContent);

                if (templates.IsSuccessStatusCode)
                {
                    var templatesResponse =
                        JsonConvert.DeserializeObject<WhatsappTemplate>(await templates.Content.ReadAsStringAsync());

                    return templatesResponse;
                }
                else
                {
                    throw new Exception(await templates.Content.ReadAsStringAsync());
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Add New Template Error: {TwilioAccountSid}:{TwilioSecret} {CreateTemplateViewModel}, Exception: {ExceptionString}",
                    sid,
                    secret,
                    JsonConvert.SerializeObject(createTemplateViewModel),
                    ex.ToString());

                throw;
            }
        }

        public async Task<bool> RemovesNewTemplate(string accountSid, string secret, string templateSid)
        {
            try
            {
                var client = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue(
                    "Basic",
                    Convert.ToBase64String(System.Text.ASCIIEncoding.ASCII.GetBytes($"{accountSid}:{secret}")));

                var templates = await client.DeleteAsync(
                    $"https://messaging.twilio.com/v1/Channels/WhatsApp/Templates/{templateSid}");

                return templates.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Remove New Template Error: {TwilioTemplateSid}:{TwilioSecret}, Exception: {ExceptionString}",
                    templateSid,
                    secret,
                    ex.ToString());

                throw;
            }
        }

        public async Task Topup(
            string twilioAccountSid,
            decimal topupAmount,
            TwilioTopUpMethod topupMethod,
            Session stripeCheckoutSession = null,
            string internalUserId = null,
            bool isInternalTestingUse = false,
            string internalInvoiceId = null)
        {
            var twilioRecords = await _appDbContext.CompanyTwilioUsageRecords
                .Where(x => x.TwilioAccountId == twilioAccountSid)
                .ToListAsync();

            if (twilioRecords.Count > 0)
            {
                try
                {
                    var coreTwilio = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();

                    TwilioClient.Init(coreTwilio.AccountSID, coreTwilio.AccountSecret);

                    var account = await AccountResource.UpdateAsync(
                        status: AccountResource.StatusEnum.Active,
                        pathSid: twilioAccountSid);

                    await _appDbContext.ConfigWhatsAppConfigs
                        .Where(config => config.TwilioAccountId == twilioAccountSid)
                        .ExecuteUpdateAsync(
                            config =>
                                config.SetProperty(c => c.IsRequireTopup, false));
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Twilio top-up error: {ExceptionString}",
                        ex.ToString());
                }

                twilioRecords.ForEach(
                    twilioRecord =>
                    {
                        twilioRecord.TotalCreditValue ??= 0;
                        twilioRecord.TotalPrice ??= 0;
                    });

                var maxTopupValue = twilioRecords.Max(x => x.TotalCreditValue.Value);
                var newTotalCreditValue = maxTopupValue + topupAmount;

                foreach (var twilioRecord in twilioRecords)
                {
                    twilioRecord.TotalCreditValue = newTotalCreditValue;

                    // Update Whatsapp Application Record
                    var cmsWhatsappApplication = await _appDbContext.CmsWhatsappApplications
                        .FirstOrDefaultAsync(
                            x =>
                                x.TwilioAccountSid == twilioAccountSid
                                && (x.HasToppedUp == false
                                    || x.HasToppedUp == null));

                    if (cmsWhatsappApplication != null)
                    {
                        cmsWhatsappApplication.HasToppedUp = true;

                        if (cmsWhatsappApplication.HubSpotTicketId != null)
                        {
                            // Add HubSpot Ticket
                            BackgroundJob.Enqueue<IInternalHubSpotService>(
                                x => x.UpdateWhatsAppApplicationTicket(
                                    cmsWhatsappApplication.HubSpotTicketId,
                                    null,
                                    null,
                                    true));
                        }
                    }
                }

                var twilioRecordFirst = twilioRecords.First();

                _appDbContext.TwilioTopUpLogs.Add(
                    new TwilioTopUpLog()
                    {
                        TwilioUsageRecordId = twilioRecordFirst.Id,
                        CompanyId = twilioRecordFirst.CompanyId,
                        TwilioAccountSid = twilioRecordFirst.TwilioAccountId,
                        TopUpAmount = topupAmount,
                        Method = topupMethod,
                        InternalUserId = internalUserId,
                        InvoiceId =
                            topupMethod == TwilioTopUpMethod.Stripe ? stripeCheckoutSession?.Id : internalInvoiceId,
                        IsInternalTestingUse = isInternalTestingUse,
                        CustomerEmail = stripeCheckoutSession?.CustomerEmail,
                        CustomerId = stripeCheckoutSession?.CustomerId,
                        AmountTotal = stripeCheckoutSession?.AmountTotal,
                        Currency = stripeCheckoutSession?.Currency ?? "usd",
                    });

                await _appDbContext.SaveChangesAsync();

                // NetSuite create invoice in background job
                var invoiceExternalId = $"TWILIO-TOPUP-{twilioRecordFirst.CompanyId}-{twilioRecordFirst.Id}";
                var currency = stripeCheckoutSession?.Currency ?? "usd";

                var jobId = BackgroundJob.Enqueue<IInternalIntegrationService>(x => x.CreateTwilioTopUpInvoiceAsync(
                    invoiceExternalId,
                    twilioRecordFirst.CompanyId,
                    topupAmount,
                    currency,
                    DateTime.UtcNow));

                _logger.LogInformation(
                    "Successfully queued Twilio top-up invoice {InvoiceExternalId}. Company: {CompanyId}, Amount: {Amount} {Currency} as background job. JobId: {JobId}",
                    invoiceExternalId,
                    twilioRecordFirst.CompanyId,
                    topupAmount,
                    currency,
                    jobId);
            }
        }

        public async Task<bool> ArePhoneNumbersEquivalentInTwilioAsync(string firstPhoneNumber, string secondPhoneNumber, string companyId)
        {
            if (string.IsNullOrEmpty(firstPhoneNumber) || string.IsNullOrEmpty(secondPhoneNumber))
            {
                return false;
            }

            var smsChannel = await _appDbContext.ConfigSMSConfigs
                .Where(x => x.CompanyId == companyId).FirstOrDefaultAsync();

            if (smsChannel is null)
            {
                return false;
            }

            TwilioClient.Init(smsChannel.TwilioAccountId, smsChannel.TwilioSecret);

            firstPhoneNumber = firstPhoneNumber.Replace("+", string.Empty);
            secondPhoneNumber = secondPhoneNumber.Replace("+", string.Empty);

            try
            {
                var firstPhoneNumberResource = await PhoneNumberResource.FetchAsync(pathPhoneNumber: $"+{firstPhoneNumber}");
                var secondPhoneNumberResource = await PhoneNumberResource.FetchAsync(pathPhoneNumber: $"+{secondPhoneNumber}");

                if (firstPhoneNumberResource != null && secondPhoneNumberResource != null)
                {
                    var isFirstPhoneNumberValid = firstPhoneNumberResource.Valid ?? false;
                    var isSecondPhoneNumberValid = secondPhoneNumberResource.Valid ?? false;

                    if (isFirstPhoneNumberValid && isSecondPhoneNumberValid)
                    {
                        if (!(firstPhoneNumberResource.CountryCode == secondPhoneNumberResource.CountryCode
                            && firstPhoneNumberResource.NationalFormat == secondPhoneNumberResource.NationalFormat
                            && string.Equals(firstPhoneNumberResource.PhoneNumber.ToString(), secondPhoneNumberResource.ToString())))
                        {
                            _logger.LogInformation(
                                "[{MethodName}] Both numbers from Twilio are not equivalent companyId=[{CompanyId}], firstPhoneNumber=[{FirstPhoneNumber}], secondPhoneNumber=[{SecondPhoneNumber}]",
                                nameof(ArePhoneNumbersEquivalentInTwilioAsync),
                                companyId,
                                JsonConvert.SerializeObject(firstPhoneNumberResource),
                                JsonConvert.SerializeObject(secondPhoneNumberResource));
                        }

                        return firstPhoneNumberResource.CountryCode == secondPhoneNumberResource.CountryCode
                               && firstPhoneNumberResource.NationalFormat == secondPhoneNumberResource.NationalFormat
                               && string.Equals(firstPhoneNumberResource.PhoneNumber.ToString(), secondPhoneNumberResource.ToString());
                    }
                }
            }
            catch (ApiException ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Failed to fetch number from Twilio companyId=[{CompanyId}], firstPhoneNumber=[{FirstPhoneNumber}], secondPhoneNumber=[{SecondPhoneNumber}]",
                    nameof(ArePhoneNumbersEquivalentInTwilioAsync),
                    companyId,
                    firstPhoneNumber,
                    secondPhoneNumber);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Failed to fetch number from Twilio companyId=[{CompanyId}], firstPhoneNumber=[{FirstPhoneNumber}], secondPhoneNumber=[{SecondPhoneNumber}]",
                    nameof(ArePhoneNumbersEquivalentInTwilioAsync),
                    companyId,
                    firstPhoneNumber,
                    secondPhoneNumber);
                return false;
            }

            return false;
        }
    }
}