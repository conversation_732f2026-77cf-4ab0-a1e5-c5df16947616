using Microsoft.EntityFrameworkCore;
using ShopifySharp;
using Sleekflow.Apis.MessagingHub.Model;
using Stripe;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.MessageDomain.Models;
using Charge = Stripe.Charge;
using Customer = Stripe.Customer;

namespace Sleekflow.Core.Tests.Conversations;

public class ConversationMessageDbContext : DbContext
{
     public DbSet<ConversationMessage> ConversationMessages { get; set; }

    public ConversationMessageDbContext(DbContextOptions<ConversationMessageDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ConversationMessage>();

        var entityTypes =
            modelBuilder.Model.GetEntityTypes()
                .ToList(); // Use ToList() to avoid modifying the collection while iterating

        foreach (var entityType in entityTypes)
        {
            // Skip entities that are already configured as keyless
            if (entityType.IsKeyless)
            {
                continue;
            }

            // Check if the entity has a primary key defined
            var primaryKey = entityType.FindPrimaryKey();

            if (primaryKey == null)
            {
                // Ignore the entity
                modelBuilder.Ignore(entityType.ClrType);
            }
        }


        modelBuilder.Entity<Conversation>()
            .Ignore(c => c.Metadata);

        modelBuilder.Entity<ConversationMessage>()
            .HasOne(cm => cm.Conversation)
            .WithMany(c => c.ChatHistory)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<ConversationMessage>()
            .Ignore(c => c.Metadata);

        modelBuilder.Ignore<WabaDto>();
        modelBuilder.Ignore<WabaPhoneNumberDto>();
        modelBuilder.Ignore<WhatsappCloudApiActionMessageParameterObject>();
        modelBuilder.Ignore<WhatsappCloudApiActionObject>();
        modelBuilder.Ignore<WhatsappCloudApiConfigObject>();
        modelBuilder.Ignore<WhatsappCloudApiContactObject>();
        modelBuilder.Ignore<WhatsappCloudApiHeaderObject>();
        modelBuilder.Ignore<WhatsappCloudApiInteractiveObject>();
        modelBuilder.Ignore<WhatsappCloudApiMediaObject>();
        modelBuilder.Ignore<WhatsappCloudApiParameterActionObject>();
        modelBuilder.Ignore<WhatsappCloudApiParameterObject>();
        modelBuilder.Ignore<WhatsappCloudApiTemplateMessageCardComponentObject>();
        modelBuilder.Ignore<WhatsappCloudApiTemplateMessageCardObject>();
        modelBuilder.Ignore<WhatsappCloudApiTemplateMessageComponentObject>();
        modelBuilder.Ignore<WhatsappCloudApiWebhookInteractiveReplyObject>();
        modelBuilder.Ignore<WhatsappCloudApiWebhookOrderObject>();
        modelBuilder.Ignore<WhatsappCloudApiLocationObject>();
        modelBuilder.Ignore<WhatsappCloudApiWebhookLocationObject>();
        modelBuilder.Ignore<WhatsappCloudApiWebhookReferralObject>();
        modelBuilder.Ignore<WhatsappCloudApiReactionObject>();
        modelBuilder.Ignore<WhatsappCloudApiWebhookTemplateButtonReplyObject>();
        modelBuilder.Ignore<ContactReplyMessage>();
        modelBuilder.Ignore<CronJobObject>();
        modelBuilder.Ignore<DefaultAction>();
        modelBuilder.Ignore<Element>();
        modelBuilder.Ignore<StripeResponse>();
        modelBuilder.Ignore<WhatsappCloudApiOrderDetailObject>();
        modelBuilder.Ignore<Fulfillment>();
        modelBuilder.Ignore<MetaField>();
        modelBuilder.Ignore<Account>();
        modelBuilder.Ignore<ApplicationFee>();
        modelBuilder.Ignore<BalanceTransaction>();
        modelBuilder.Ignore<CashBalance>();
        modelBuilder.Ignore<Charge>();
        modelBuilder.Ignore<Coupon>();
        modelBuilder.Ignore<Customer>();
        modelBuilder.Ignore<Invoice>();
        modelBuilder.Ignore<PaymentIntent>();
        modelBuilder.Ignore<CreateTemplateWithContentApiResponse>();
        modelBuilder.Ignore<BillRecord>();
        modelBuilder.Ignore<BroadcastHistory>();
        modelBuilder.Ignore<Company>();
        modelBuilder.Ignore<UserProfile>();
        modelBuilder.Ignore<WhatsappTwilioContentApiObject>();

        base.OnModelCreating(modelBuilder);
    }
}