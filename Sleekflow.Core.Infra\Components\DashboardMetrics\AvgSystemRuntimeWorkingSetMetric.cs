﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class AvgSystemRuntimeWorkingSetMetric : IDashboardMetric
{
    private readonly Output<string>? _sleekflowCoreName;
    private readonly Output<string>? _sleekflowCoreAppInsightName;
    private readonly Output<string>? _sleekflowCoreAppInsightResourceId;

    public AvgSystemRuntimeWorkingSetMetric(
        Output<string>? sleekflowCoreName,
        Output<string>? sleekflowCoreAppInsightName,
        Output<string>? sleekflowCoreAppInsightResourceId)
    {
        _sleekflowCoreName = sleekflowCoreName;
        _sleekflowCoreAppInsightName = sleekflowCoreAppInsightName;
        _sleekflowCoreAppInsightResourceId = sleekflowCoreAppInsightResourceId;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs =
                    new[]
                    {
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "options"
                            },
                            {
                                "value", new Dictionary<string, object>()
                                {
                                    {
                                        "chart", new Dictionary<string, object>()
                                        {
                                            {
                                                "filterCollection", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "filters", new[]
                                                        {
                                                            new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "key", "cloud/roleName"
                                                                },
                                                                {
                                                                    "operator", 0
                                                                },
                                                                {
                                                                    "values", new[]
                                                                    {
                                                                        _sleekflowCoreName!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            {
                                                "grouping", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "dimension", "cloud/roleInstance"
                                                    },
                                                    {
                                                        "sort", 2
                                                    },
                                                    {
                                                        "top", 10
                                                    }
                                                }
                                            },
                                            {
                                                "metrics", new[]
                                                {
                                                    new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "aggregationType", 4
                                                        },
                                                        {
                                                            "metricVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "displayName", "System.Runtime|Allocation Rate"
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "name", "customMetrics/System.Runtime|Allocation Rate"
                                                        },
                                                        {
                                                            "namespace", "microsoft.insights/components/kusto"
                                                        },
                                                        {
                                                            "resourceMetadata", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "id", _sleekflowCoreAppInsightResourceId!
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            {
                                                "timespan", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "grain", 1
                                                    },
                                                    {
                                                        "relative", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "duration", 86400000
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "showUTCTime", false
                                                    }
                                                }
                                            },
                                            {
                                                "title", Output.Tuple(
                                                        _sleekflowCoreName!,
                                                        _sleekflowCoreAppInsightName!)
                                                    .Apply(
                                                        o =>
                                                            $"Avg System.Runtime|Allocation Rate for {o.Item2} by Cloud role instance where Cloud role name = '{o.Item1}'")
                                            },
                                            {
                                                "titleKind", 1
                                            },
                                            {
                                                "visualization", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "axisVisualization", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "x", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "axisType", 2
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "y", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "axisType", 1
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "chartType", 2
                                                    },
                                                    {
                                                        "legendVisualization", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "hideSubtitle", false
                                                            },
                                                            {
                                                                "isVisible", true
                                                            },
                                                            {
                                                                "position", 2
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "sharedTimeRange"
                            },
                            {
                                "isOptional", true
                            }
                        }
                    },
                Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                Settings =
                {
                    new Dictionary<string, object>()
                    {
                        {
                            "content", new Dictionary<string, object>()
                            {
                                {
                                    "options", new Dictionary<string, object>()
                                    {
                                        {
                                            "chart", new Dictionary<string, object>()
                                            {
                                                {
                                                    "grouping", new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "dimension", "cloud/roleInstance"
                                                        },
                                                        {
                                                            "sort", 2
                                                        },
                                                        {
                                                            "top", 10
                                                        }
                                                    }
                                                },
                                                {
                                                    "metrics", new[]
                                                    {
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 4
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "System.Runtime|Working Set"
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "customMetrics/System.Runtime|Working Set"
                                                            },
                                                            {
                                                                "namespace", "microsoft.insights/components/kusto"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreAppInsightResourceId!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    "title", Output.Tuple(
                                                            _sleekflowCoreName!,
                                                            _sleekflowCoreAppInsightName!)
                                                        .Apply(
                                                            o =>
                                                                $"Avg System.Runtime|Working Set for {o.Item2} by cloud/roleInstance where cloud/roleName = '{o.Item1}'")
                                                },
                                                {
                                                    "titleKind", 1
                                                },
                                                {
                                                    "visualization", new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "axisVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "x", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 2
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    "y", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 1
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "chartType", 2
                                                        },
                                                        {
                                                            "disablePinning", true
                                                        },
                                                        {
                                                            "legendVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "hideSubtitle", false
                                                                },
                                                                {
                                                                    "isVisible", true
                                                                },
                                                                {
                                                                    "position", 2
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}