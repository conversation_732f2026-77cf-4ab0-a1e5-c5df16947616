﻿using Travis_backend.CampaignAnalyticsDomain.Models;

namespace Travis_backend.CampaignAnalyticsDomain.ViewModels;

public class GetCampaignCommonMetricsResponse
{
    public int Sent { get; set; }

    public int Delivered { get; set; }

    public int Read { get; set; }

    public int Replied { get; set; }

    public int Bounced { get; set; }

    public GetCampaignCommonMetricsResponse(
        int sent,
        int delivered,
        int read,
        int replied,
        int bounced)
    {
        Sent = sent;
        Delivered = delivered;
        Read = read;
        Replied = replied;
        Bounced = bounced;
    }

    public GetCampaignCommonMetricsResponse(CampaignCommonMetrics campaignCommonMetrics)
    {
        Sent = campaignCommonMetrics.Sent;
        Delivered = campaignCommonMetrics.Delivered;
        Read = campaignCommonMetrics.Read;
        Replied = campaignCommonMetrics.Replied;
        Bounced = campaignCommonMetrics.Bounced;
    }
}