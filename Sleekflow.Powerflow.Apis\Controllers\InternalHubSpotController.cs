﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.Database;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.Services;
using Travis_backend.InternalDomain.ViewModels;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Manage HubSpot Integration.
/// </summary>
[Route("/internal/hubspot/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)] // Basic Role Requirement
public class InternalHubSpotController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IInternalHubspotRepository _internalHubspotRepository;
    private readonly IInternalHubSpotService _internalHubSpotService;

    public InternalHubSpotController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        ILogger<InternalHubSpotController> logger,
        IInternalHubspotRepository internalHubspotRepository,
        IInternalHubSpotService internalHubSpotService)
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _logger = logger;
        _internalHubspotRepository = internalHubspotRepository;
        _internalHubSpotService = internalHubSpotService;
    }

    /// <summary>
    /// Sync Company Owner From Hubspot.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> SyncCompanyOwnerFromHubspot(
        [FromBody]
        SyncCompanyOwnerFromHubspotRequest request)
    {
        foreach (var companyId in request.CompanyIds)
        {
            try
            {
                await _internalHubSpotService.SyncCompanyOwnerFromHubspot(companyId);
            }
            catch (Exception e)
            {
                _logger.LogError(
                    e,
                    "Error syncing company owner from Hubspot: {CompanyId}, {Error}",
                    companyId,
                    e.Message);
            }
        }

        return Ok(
            new ResponseViewModel
            {
                message = $"ok"
            });
    }

    /// <summary>
    /// Sync Company.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> SyncCompanies([FromBody] List<string> request)
    {
        foreach (var companyId in request)
        {
            try
            {
                await _internalHubSpotService.SyncCompany(companyId);
                await _internalHubSpotService.SyncCompanyStaffs(companyId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error syncing company: {CompanyId}, {Error}", companyId, e.Message);
            }
        }

        return Ok(
            new ResponseViewModel
            {
                message = $"ok"
            });
    }

    /// <summary>
    /// Sync Company.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> BulkUpdateCompanies()
    {
        try
        {
            await _internalHubSpotService.UpdateAndSyncAllCompanies();

            return Ok(
                new ResponseViewModel
                {
                    message = $"ok"
                });
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error updating and syncing all companies: {Error}", e.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Error updating and syncing all companies: {e.Message}"
                });
        }
    }

    /// <summary>
    /// Sync Company.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> SyncCompany([FromBody] SyncHubSpotCompanyRequest request)
    {
        try
        {
            await _internalHubSpotService.SyncCompany(request.CompanyId);

            return Ok(
                new ResponseViewModel
                {
                    message = $"ok"
                });
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error syncing company: {CompanyId}, {Error}", request.CompanyId, e.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Error syncing company: {request.CompanyId}, {e.Message}"
                });
        }
    }

    /// <summary>
    /// Sync Company Staffs.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> SyncCompanyStaffs(
        [FromBody]
        SyncHubSpotCompanyRequest request)
    {
        try
        {
            await _internalHubSpotService.SyncCompanyStaffs(request.CompanyId);

            return Ok(
                new ResponseViewModel
                {
                    message = $"ok"
                });
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error syncing company staffs: {CompanyId}, {Error}", request.CompanyId, e.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Error syncing company staffs: {request.CompanyId}, {e.Message}"
                });
        }
    }

    /// <summary>
    /// Update And Sync All Companies.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdateAndSyncAllCompanies()
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        try
        {
            await _internalHubSpotService.UpdateAndSyncAllCompanies();

            return Ok(
                new ResponseViewModel
                {
                    message = $"ok"
                });
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error updating and syncing all companies: {Error}", e.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Error updating and syncing all companies: {e.Message}"
                });
        }
    }

    /// <summary>
    /// Sync All Company and Staffs.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> SyncAllCompaniesAndStaffs()
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        try
        {
            await _internalHubSpotService.SyncAllCompaniesAndStaffs();

            return Ok(
                new ResponseViewModel
                {
                    message = $"ok"
                });
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error syncing all companies and staffs: {Error}", e.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Error syncing all companies and staffs: {e.Message}"
                });
        }
    }

    /// <summary>
    /// Sync HubSpot Company and Staff Sync History.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<List<CmsHubSpotCompanySyncHistory>>> GetHubSpotSyncHistory()
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        try
        {
            var list = await _appDbContext.CmsHubSpotCompanySyncHistories.ToListAsync();

            return Ok(list);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error getting hubspot sync history: {Error}", e.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Error getting hubspot sync history"
                });
        }
    }

    /// <summary>
    /// Sync HubSpot Contact Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<List<CmsHubSpotContactOwnerMap>>> SyncContactOwner()
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        try
        {
            var result = await _internalHubSpotService.SyncInternalContactOwnerMapping();

            return Ok(result);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error syncing contact owner: {Error}", e.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Error syncing contact owner"
                });
        }
    }

    /// <summary>
    /// Update HubSpot Company Mapping.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<CmsHubSpotCompanyMapDto>> UpdateHubSpotCompanyMapping(
        UpdateHubSpotCompanyMappingRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        try
        {
            var mapping =
                await _appDbContext.CmsHubSpotCompanyMaps.FirstOrDefaultAsync(x => x.CompanyId == request.CompanyId);

            if (mapping == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Mapping not found."
                    });
            }

            var company = await _internalHubspotRepository.GetCompanyObjectAsync(request.HubSpotCompanyHubSpotId);

            if (company == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "HubSpot Company not found."
                    });
            }

            mapping.HubSpotCompanyObjectId = request.HubSpotCompanyHubSpotId;
            mapping.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            var result = _mapper.Map<CmsHubSpotCompanyMapDto>(mapping);

            return Ok(result);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error updating hubspot company mapping: {Error}", e.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Error updating hubspot company mapping"
                });
        }
    }

    /// <summary>
    /// Get Hub Spot Api Usage.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<IntegrationLimitResponse>> GetHubSpotApiUsage()
    {
        try
        {
            var result = await _internalHubspotRepository.GetHubSpotApiUsageAsync();

            return Ok(result);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error getting hubspot api usage: {Error}", e.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Error getting hubspot api usage"
                });
        }
    }

    /// <summary>
    /// Update Last Month Company Breakdown Status.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> UpdateLastMonthCompanyBreakdownStatuses(
        [FromBody]
        List<string> companyIds = null)
    {
        try
        {
            await _internalHubSpotService.UpdateLastMonthCompanyBreakdownStatuses(companyIds);

            return Ok();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error updating last month company breakdown statuses: {Error}", e.Message);

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Error updating last month company breakdown statuses"
                });
        }
    }
}