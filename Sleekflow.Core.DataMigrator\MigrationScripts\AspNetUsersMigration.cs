using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sleekflow.Core.DataMigrator.Migrations;
using Sleekflow.Core.DataMigrator.Serializations;
using Travis_backend.CompanyDomain.Utils;

namespace Sleekflow.Core.DataMigrator.MigrationScripts;

public class AspNetUsersMigration : BaseMigration
{
    public AspNetUsersMigration(Configurations configurations)
        : base(configurations)
    {
    }

    public override async Task<int> ExecuteAsync(string companyId)
    {
        var status = 0;

        try
        {
            var staffList =
                await OriginalContext.UserRoleStaffs.Where(u => u.CompanyId == companyId).ToListAsync();

            var migratedItems = MigrationContext.Users.Select(x => x.Id)
                .ToHashSet();

            var csmUsers = OriginalContext.Users
                .FromSqlRaw(
                    @"select Users.* from AspNetUsers Users where Users.Id in (select UserId from AspNetUserRoles);")
                .ToList();

            foreach (var user in csmUsers)
            {
                if (!migratedItems.Contains(user.Id))
                {
                    await MigrationContext.Users.AddAsync(user);
                }
            }

            var users = new List<string?>();

            var settings = new JsonSerializerSettings();
            settings.Converters.Add(new DateTimeOffsetConverter());


            foreach (var userRoleStaff in staffList)
            {
                var aspNetUser = await OriginalContext.Users.Where(u => u.Id == userRoleStaff.IdentityId)
                    .FirstOrDefaultAsync();
                if (aspNetUser == null)
                {
                    continue;
                }

                if (!migratedItems.Contains(aspNetUser.Id))
                {
                    await MigrationContext.Users.AddAsync(aspNetUser);
                }

                var user = JsonConvert.SerializeObject(aspNetUser);
                var userWithStaffId = JsonConvert.DeserializeObject<Dictionary<string, Object>>(user);
                if (userWithStaffId == null)
                {
                    continue;
                }

                userWithStaffId["StaffId"] = userRoleStaff.Id;
                userWithStaffId["RoleType"] = RoleTypeConverter.ConvertToString(userRoleStaff.RoleType);
                string? serializedUser = null;
                try
                {
                    serializedUser = JsonConvert.SerializeObject(userWithStaffId, settings);
                }
                catch (JsonWriterException e)
                {
                    Console.WriteLine(e.Message);
                }

                users.Add(serializedUser);
            }

            var filePath = SerializeEntityToJson.GetPath("AspNetUser", "SerializedDocuments");
            await SerializeEntityToJson.SerializeToJson(users, filePath);

            var entityEntries = MigrationContext.ChangeTracker.Entries().ToList();
            foreach (var entityEntry in entityEntries.Where(
                         entityEntry =>
                             entityEntry.Entity.GetType() != OriginalContext.Users.FirstOrDefault()?.GetType()))
            {
                entityEntry.State = EntityState.Unchanged;
            }

            status = await MigrationContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
            if (e.InnerException != null)
            {
                Console.WriteLine(e.InnerException);
            }
        }

        return status;
    }
}