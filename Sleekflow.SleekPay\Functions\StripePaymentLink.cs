using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ShopifySharp;
using ShopifySharp.Filters;
using Sleekflow.SleekPay.Model;
using Sleekflow.SleekPay.RedisAccessor.Interface;
using Sleekflow.SleekPay.Service;
using Sleekflow.SleekPay.SQLDatabase;
using Sleekflow.SleekPay.SQLDatabase.Entity;
using Stripe;
using RefundService = Stripe.RefundService;
using IShopifyService = Sleekflow.SleekPay.Service.IShopifyService;
using ShopifySharp.GraphQL;
using Sleekflow.SleekPay.Helpers;

namespace Sleekflow.SleekPay.Functions;

public class StripePaymentLink
{
    private readonly IRedisCacheAccessor _redisCacheAccessor;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IShareLinkService _shareLinkService;
    private readonly IStripePaymentService _stripePaymentService;
    private readonly IShopifyService _shopifyService;

    public StripePaymentLink(IRedisCacheAccessor redisCacheAccessor,
        ApplicationDbContext appDbContext,
        IShareLinkService shareLinkService,
        IStripePaymentService stripePaymentService,
        IShopifyService shopifyService)
    {
        _redisCacheAccessor = redisCacheAccessor;
        _appDbContext = appDbContext;
        _shareLinkService = shareLinkService;
        _stripePaymentService = stripePaymentService;
        _shopifyService = shopifyService;
    }

    // [FunctionName("GeneratePaymentLink")]
    //
    //
    [FunctionName("GenerateStripePaymentLink")]
    public async Task<IActionResult> RunStripePaymentLink([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
    {
        try
        {
            var content = await new StreamReader(req.Body).ReadToEndAsync();
            var request = JsonConvert.DeserializeObject<GenerateStripPaymentRequest>(content);

            SetStripeApiKeyByPlatformCountry(request.PlatformCountry);

            var stripePaymentRecord = new StripePaymentRecord
            {
                CompanyId = request.CompanyId,
                StaffId = request.SharedStaffId,
                LineItems = request.LineItems,
                CustomerId = request.CustomerId,
                CustomerEmail = request.CustomerEmail,
                UserProfileId = request.UserProfileId,
                TeamId = request.SharedTeamId,
                SharedType = request.SharedType,
                PlatformCountry = request.PlatformCountry,
            };

            var stripePaymentConfig = await _appDbContext.ConfigStripePaymentConfigs.FirstOrDefaultAsync(x => x.CompanyId == request.CompanyId && x.Status == StripePaymentRegistrationStatus.Registered && x.Country == request.PlatformCountry);

            if (stripePaymentConfig == null)
                return new BadRequestObjectResult("stripe payment config error");

            if (request.ShopifyId.HasValue)
            {
                var shopifyConfig = await _appDbContext.ConfigShopifyConfigs.FirstOrDefaultAsync(x => x.Id == request.ShopifyId && x.CompanyId == request.CompanyId);
                
                var graphService = new GraphService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

                var shopifyDraftLineItems = new List<DraftLineItem>();

                foreach (var lineItem in request.LineItems)
                {
                    var variantIdString = lineItem.Metadata?["variantId"];

                    if (!long.TryParse(variantIdString, out var variantId))
                    {
                        var draftLineItem = new DraftLineItem()
                        {
                            Title = lineItem.Name,
                            Price = lineItem.Amount,
                            Quantity = lineItem.Quantity,
                            AppliedDiscount = new AppliedDiscount()
                            {
                                Title = "Discount",
                                ValueType = "fixed_amount",
                                Value = lineItem.TotalDiscount.ToString("."),
                                Amount = lineItem.TotalDiscount
                            }
                        };
                        shopifyDraftLineItems.Add(draftLineItem);
                    }
                    else
                    {
                        var query = @$"
                            {{
                                productVariants(first: 1, query: ""id:{variantId}"") {{
                                    edges {{
                                        node {{
                                            id
                                            title
                                            sku
                                            position
                                            inventoryPolicy
                                            price
                                            compareAtPrice
                                            createdAt
                                            updatedAt
                                            taxable
                                            taxCode
                                            inventoryQuantity
                                            inventoryItem {{
                                                id
                                            }}
                                        }}
                                    }}
                                }}
                            }}";

                        ProductVariantConnection productVariantConnection = null;

                        try
                        {
                            productVariantConnection = await graphService.SendAsync<ProductVariantConnection>(query);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Cannot get product variant: {ex.Message}");
                            continue;
                        }

                        ShopifySharp.ProductVariant productVariant;

                        if (productVariantConnection != null 
                            && productVariantConnection.edges != null
                            && productVariantConnection.edges.Any())
                        {
                            productVariant = ShopifySharpTypeHelper.BuildShopifyProductVariantFromEdge(productVariantConnection.edges.First());
                            
                            if (request.IsReserveInventory && productVariant.InventoryQuantity < lineItem.Quantity)
                                throw new Exception($"{lineItem.Name} is sold out");

                            var draftLineItem = new DraftLineItem()
                            {
                                VariantId = productVariant.Id,
                                Quantity = lineItem.Quantity,
                                AppliedDiscount = new AppliedDiscount()
                                {
                                    Title = "Discount",
                                    ValueType = "fixed_amount",
                                    Value = lineItem.TotalDiscount.ToString("."),
                                    Amount = lineItem.TotalDiscount
                                }
                            };

                            shopifyDraftLineItems.Add(draftLineItem);
                        }
                    }
                }

                var customerService = new ShopifySharp.CustomerService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
                
                if (!string.IsNullOrEmpty(request.ShopifyCustomerPhone))
                {
                    var customerExists = true;
                    var customers = await customerService.SearchAsync(new CustomerSearchListFilter()
                    {
                        Query = $"{request?.ShopifyCustomerPhone}"
                    });

                    if (!customers.Items.Any() && !string.IsNullOrEmpty(request?.ShopifyCustomerEmail))
                    {
                        customers = await customerService.SearchAsync(new CustomerSearchListFilter()
                        {
                            Query = $"email:{request?.ShopifyCustomerEmail}"
                        });
                        
                        if (customers.Items.MinBy(e=>e.Email)?.Email != request.ShopifyCustomerEmail)
                            customerExists = false;
                    }
                    else if (!customers.Items.Any())
                    {
                        customerExists = false;
                    }
                    var userProfile = await _appDbContext.UserProfiles.FirstOrDefaultAsync(x => x.CompanyId == request.CompanyId && x.Id == request.UserProfileId);

                    try
                    {
                        if (customerExists)
                        {
                            request.ShopifyCustomerId = customers.Items.FirstOrDefault()?.Id;

                            if (string.IsNullOrEmpty(customers.Items.FirstOrDefault()?.Phone) &&
                                !string.IsNullOrEmpty(request.ShopifyCustomerPhone) &&
                                customers.Items.FirstOrDefault()?.Phone != request.ShopifyCustomerPhone)
                            {
                                //update phone number
                                await customerService.UpdateAsync(request.ShopifyCustomerId.Value, new ShopifySharp.Customer()
                                {
                                    Phone = "+" + request.ShopifyCustomerPhone
                                });
                            }

                            if (string.IsNullOrEmpty(customers.Items.FirstOrDefault()?.Email) &&
                                !string.IsNullOrEmpty(request.ShopifyCustomerEmail) &&
                                customers.Items.FirstOrDefault()?.Email != request.ShopifyCustomerEmail)
                            {
                                //update email
                                await customerService.UpdateAsync(request.ShopifyCustomerId.Value, new ShopifySharp.Customer()
                                {
                                    Email = request.ShopifyCustomerEmail
                                });
                            }

                            if (userProfile != null)
                            {
                                if (userProfile.ShopifyCustomerId != request.ShopifyCustomerId)
                                {
                                    userProfile.ShopifyCustomerId = request.ShopifyCustomerId;
                                    await _appDbContext.SaveChangesAsync();
                                }
                            }
                        }
                        else
                        {
                            request.ShopifyCustomerId = null;

                            var customer = new ShopifySharp.Customer()
                            {
                                FirstName = request?.ShopifyCustomerFirstname,
                                LastName = request?.ShopifyCustomerLastname,
                                Phone = "+" + request?.ShopifyCustomerPhone,
                                Email = request?.ShopifyCustomerEmail,
                                VerifiedEmail = true,
                                Note = "SleekFlow created customer",
                                State = "enabled",
                            };

                            customer = await customerService.CreateAsync(customer);

                            request.ShopifyCustomerId = customer?.Id;

                            if (userProfile.ShopifyCustomerId != request.ShopifyCustomerId)
                            {
                                userProfile.ShopifyCustomerId = request.ShopifyCustomerId;
                                await _appDbContext.SaveChangesAsync();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Cannot create customer: {ex.Message}");
                    }
                }

                var shopifyOrderTags = request.ShopifyOrderTags == null ? new List<string>() : request.ShopifyOrderTags;
                shopifyOrderTags.Add("SleekFlow");
                if (request.SharedType == StripePaymentLinkSharedType.Campaign)
                {
                    shopifyOrderTags.Add("Campaign");
                }

                var shopifyGraphQlAdminApiVersion = Environment.GetEnvironmentVariable("ShopifyGraphQlAdminApiVersion");

                //Create draft order
                try
                {
                    var draftOrderCreateResult = await _shopifyService.DraftOrderCreate(
                        request.CompanyId,
                        request.SharedStaffId,
                        request.SharedStaffName,
                        request.SharedStaffUsername,
                        request.SharedStaffEmail,
                        request.ShopifyCustomerId.Value,
                        request.ShopifyCustomerEmail,
                        "+" + request.ShopifyCustomerPhone,
                        request.LineItems.FirstOrDefault()?.Currency,
                        request.PlatformCountry,
                        shopifyDraftLineItems,
                        string.Join(", ", shopifyOrderTags.ToArray()),
                        shopifyConfig.UsersMyShopifyUrl,
                        shopifyConfig.AccessToken,
                        shopifyGraphQlAdminApiVersion);

                    var shopifyDraftOrderFullId = draftOrderCreateResult.DraftOrderCreate.DraftOrder.Id;
                    stripePaymentRecord.ShopifyDraftOrderId = long.Parse(shopifyDraftOrderFullId.Substring(
                        shopifyDraftOrderFullId.LastIndexOf("/") + 1,
                        shopifyDraftOrderFullId.Length - (shopifyDraftOrderFullId.LastIndexOf("/") + 1)));
                    stripePaymentRecord.ShopifyInvoiceUrl = draftOrderCreateResult.DraftOrderCreate.DraftOrder.InvoiceUrl;

                    if (request.CompanyId == "3de2da2a-3b56-4479-aabf-1345c5a924ae")
                    {
                        var draftOrderService = new DraftOrderService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

                        var draftOrder =
                            await draftOrderService.GetAsync(stripePaymentRecord.ShopifyDraftOrderId.Value);
                        draftOrder.Note = $"Created by SleekFlow\n" +
                                          $"Staff name: {request.SharedStaffName}\n" +
                                          $"Staff email: {request.SharedStaffEmail}\n" +
                                          $"APM-Monaco Id: {request.SharedStaffUsername}\n" +
                                          $"StaffId: {request.SharedStaffUsername}";

                        await draftOrderService.UpdateAsync(draftOrder.Id.Value, draftOrder);
                    }
                }
                catch (Exception ex)
                {
                    var draftOrder = new ShopifySharp.DraftOrder
                    {
                        Email = request.CustomerEmail,
                        LineItems = shopifyDraftLineItems,
                        Note = $"Created by SleekFlow\n" +
                               $"Staff name: {request.SharedStaffName}\n"+
                               $"Staff email: {request.SharedStaffEmail}\n"+
                               (request.CompanyId == "3de2da2a-3b56-4479-aabf-1345c5a924ae" ? $"APM-Monaco Id: {request.SharedStaffName}\n" : "") +
                               $"StaffId: {(request.CompanyId == "3de2da2a-3b56-4479-aabf-1345c5a924ae" ? request.SharedStaffUsername : request.SharedStaffId)}",
                        Tags = string.Join(", ", shopifyOrderTags.ToArray()),
                        Customer = new ShopifySharp.Customer()
                        {
                            Id = request?.ShopifyCustomerId,
                            FirstName = request?.ShopifyCustomerFirstname,
                            LastName = request?.ShopifyCustomerLastname,
                            Phone = request?.ShopifyCustomerPhone,
                            Email = request?.ShopifyCustomerEmail
                        }
                    };

                    var draftOrderService = new DraftOrderService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
                    var draftOrderCreateResult = await draftOrderService.CreateAsync(draftOrder);
                    stripePaymentRecord.ShopifyDraftOrderId = draftOrderCreateResult.Id;
                    stripePaymentRecord.ShopifyInvoiceUrl = draftOrder.InvoiceUrl;
                }

                stripePaymentRecord.ShopifyId = shopifyConfig.Id;
            }

            if (request.ExpiredAt == null && stripePaymentConfig.PaymentLinkExpirationOption != null)
            {
                request.ExpiredAt = stripePaymentConfig.PaymentLinkExpirationOption.PaymentLinkExpirationType == PaymentLinkExpirationType.ExpireSometimeAfterCreation ? DateTime.Now.AddDays(stripePaymentConfig.PaymentLinkExpirationOption.ExpireNumberOfDaysAfter) : stripePaymentConfig.PaymentLinkExpirationOption.ExpireAt;
            }

            var session = _stripePaymentService.CreatePaymentIntent(request, stripePaymentConfig, out var applicationFeeAmount);

            stripePaymentRecord.ShopifyId = request.ShopifyId;
            stripePaymentRecord.CustomerId = session.CustomerId;
            stripePaymentRecord.StripePaymentIntentId = session.PaymentIntentId;
            stripePaymentRecord.RequestedApplicationFeeAmount = (decimal) applicationFeeAmount / 100;

            stripePaymentRecord.ExpiredAt = request.ExpiredAt;
            if (request.ExpiredAt.HasValue && request.ExpiredAt <= DateTime.UtcNow)
            {
                stripePaymentRecord.Status = StripePaymentStatus.Canceled;
            }

            await _appDbContext.StripePaymentRecords.AddAsync(stripePaymentRecord);

            var shareLinkObject = await _shareLinkService.GenerateShareLinkRecord(new CreateShareLinkTracking()
            {
                CompanyId = request.CompanyId,
                ShareLinkType = ShareLinkType.StripePaymentLink,
                SharedStaffId = request.SharedStaffId,
                Title = request.Title,
                UserProfileId = request.UserProfileId,
                Domain = Environment.GetEnvironmentVariable("AzureFunctionDomain"),
                Url = session.Url,
                ExpiredAt = request.ExpiredAt,
                PlatformCountry = request.PlatformCountry,
            });

            var createShareLinkTracking = JsonConvert.DeserializeObject<CreateShareLinkTracking>(shareLinkObject);

            stripePaymentRecord.PaymentTrackingUrl = createShareLinkTracking?.TrackingUrl;
            stripePaymentRecord.PaymentUrl = createShareLinkTracking?.Url;
            stripePaymentRecord.TrackingId = createShareLinkTracking?.TrackingId;
            await _appDbContext.SaveChangesAsync();

            dynamic response = new JObject();
            response.stripePaymentRecordId = stripePaymentRecord.Id;
            response.paymentIntentId = stripePaymentRecord.StripePaymentIntentId;
            response.trackingUrl = createShareLinkTracking?.TrackingUrl;
            response.url = createShareLinkTracking?.Url;
            response.title = createShareLinkTracking?.Title;

            return new OkObjectResult(response);
        }
        catch (Exception ex)
        {
            log.LogError(ex.ToString());

            return new BadRequestObjectResult(ex);
        }
    }

    [FunctionName("Refund")]
    public async Task<IActionResult> RefundStripePayment([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
    {
        try
        {
            var content = await new StreamReader(req.Body).ReadToEndAsync();
            var request = JsonConvert.DeserializeObject<RefundPaymentRequest>(content);

            var stripePaymentRecord = await _appDbContext.StripePaymentRecords.FirstOrDefaultAsync(x => x.CompanyId == request.CompanyId && x.StripePaymentIntentId == request.PaymentIntentId);

            SetStripeApiKeyByPlatformCountry(stripePaymentRecord.PlatformCountry);

            long? refundAmount = null;

            if (request != null && request.IsPartialRefund && request.RefundAmount.HasValue)
                refundAmount = (long) (request.RefundAmount * 100);

            var options = new RefundCreateOptions
            {
                PaymentIntent = stripePaymentRecord?.StripePaymentIntentId,
                Amount = refundAmount,
                ReverseTransfer = true,
                RefundApplicationFee = false,
            };
            if (request.Reason != RefundReason.Custom)
            {
                stripePaymentRecord.RefundReason = request.Reason.ToString();

                switch (request.Reason)
                {
                    case RefundReason.Duplicate:
                        options.Reason = "duplicate";
                        break;
                    case RefundReason.Fraudulent:
                        options.Reason = "fraudulent";
                        break;
                    case RefundReason.RequestedByCustomer:
                        options.Reason = "requested_by_customer";
                        break;
                }
            }
            else
            {
                stripePaymentRecord.RefundReason = request.CustomReason;
            }
            var service = new RefundService();
            var refundResult = await service.CreateAsync(options);

            decimal? refundedAmount = null;

            var refundStatus = refundResult.Status;

            if (refundStatus == "succeeded")
            {
                refundedAmount = request.RefundAmount;

                if (stripePaymentRecord.ShopifyOrderId.HasValue)
                {
                    //Update the refund to Shopify Order
                    var shopifyConfig = _appDbContext.ConfigShopifyConfigs.FirstOrDefault(x => x.Id == stripePaymentRecord.ShopifyId);

                    if (shopifyConfig != null)
                    {
                        var orderService = new ShopifySharp.OrderService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
                        
                        var order = await orderService.GetAsync(stripePaymentRecord.ShopifyOrderId.Value);

                        var maximumShopifyRefundAmount = order.CurrentTotalPrice;
                        if (order.Refunds?.ToList() != null && order.Refunds?.ToList().Count > 0)
                        {
                            foreach (var refund in order.Refunds.ToList())
                            {
                                if (refund.Transactions?.ToList() != null && refund.Transactions?.ToList().Count > 0)
                                {
                                    foreach (var refundTransaction in refund.Transactions?.ToList())
                                    {
                                        maximumShopifyRefundAmount -= refundTransaction.Amount;
                                    }
                                }
                            }
                        }

                        var transactionService = new TransactionService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
                        
                        if (maximumShopifyRefundAmount > 0)
                        {
                            var stripeRefundAmount = request.IsPartialRefund
                                ? request.RefundAmount
                                : stripePaymentRecord.PayAmount;

                            var transaction = new Transaction()
                            {
                                Kind = "refund",
                                Amount = stripeRefundAmount > maximumShopifyRefundAmount 
                                    ? maximumShopifyRefundAmount
                                    : stripeRefundAmount,
                                Currency = stripePaymentRecord.Currency.ToUpper()
                            };

                            await transactionService.CreateAsync(stripePaymentRecord.ShopifyOrderId.Value, transaction);

                            string refundReason = null;
                            switch (request.Reason)
                            {
                                case RefundReason.Duplicate:
                                    refundReason = "Duplicate";
                                    break;
                                case RefundReason.Fraudulent:
                                    refundReason = "Fraudulent";
                                    break;
                                case RefundReason.RequestedByCustomer:
                                    refundReason = "Requested By Customer";
                                    break;
                                case RefundReason.Custom:
                                    refundReason = request.CustomReason;
                                    break;
                            }

                            if (order.Note == null)
                            {
                                order.Note = "Refund Reason: " + refundReason;
                            }
                            else
                            {
                                order.Note = order.Note + " " + "Refund Reason: " + refundReason;
                            }

                            await orderService.UpdateAsync((long)order.Id, order);   
                        }
                    }
                }

                stripePaymentRecord.Status = (stripePaymentRecord.RefundedAmount.GetValueOrDefault(0m) + request.RefundAmount.GetValueOrDefault(0m) < stripePaymentRecord.PayAmount) ?
                                              StripePaymentStatus.PartialRefund : StripePaymentStatus.Refunded;

                if (stripePaymentRecord.RefundedAmount.HasValue)
                    request.RefundAmount += stripePaymentRecord.RefundedAmount;

                switch (stripePaymentRecord.Status)
                {
                    case StripePaymentStatus.PartialRefund:
                        stripePaymentRecord.RefundedAmount = request.RefundAmount;
                        break;
                    case StripePaymentStatus.Refunded:
                        stripePaymentRecord.RefundedAmount = stripePaymentRecord.PayAmount;
                        break;
                }
            }
            else
            {
                switch (refundStatus)
                {
                    case "pending":
                        stripePaymentRecord.Status = StripePaymentStatus.RefundPending;
                        break;
                    case "failed":
                        stripePaymentRecord.Status = StripePaymentStatus.RefundFailed;
                        break;
                }
            }

            stripePaymentRecord.RefundId = refundResult.Id;
          
            await _appDbContext.SaveChangesAsync();

            dynamic response = new JObject();
            response.stripePaymentRecordId = stripePaymentRecord.Id;
            response.refundedAmount = refundedAmount;
            response.paymentIntentId = stripePaymentRecord.StripePaymentIntentId;
            response.status = stripePaymentRecord.Status.ToString();
            response.refundId = stripePaymentRecord.RefundId;

            return new OkObjectResult(response);
        }
        catch (Exception ex)
        {
            log.LogError(ex.ToString());

            return new BadRequestObjectResult(ex);
        }
    }

    
    [FunctionName("Result")]
    public async Task<IActionResult> RunAsync([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "result/{resultId}")] HttpRequest req, string resultId, ILogger log)
    {
        var content = _redisCacheAccessor.GetFromCache(resultId);

        if (content == null)
            return new BadRequestResult();

        var result = JsonConvert.DeserializeObject<StripePaymentResult>(content);

        var stripePaymentRecord = await _appDbContext.StripePaymentRecords.FirstOrDefaultAsync(r => r.StripePaymentIntentId == result.PaymentIntentId);
        result.PayAmount = stripePaymentRecord.PayAmount;

        return new OkObjectResult(result);
    }

    [FunctionName("UpdateStripePaymentLinkBranding")]
    public async Task<IActionResult> UpdateStripePaymentLinkBranding([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
    {
        try
        {
            var companyId = req.Query["companyId"].ToString();
            var accountId = req.Query["accountId"].ToString();

            var stripePaymentConfig = await _appDbContext.ConfigStripePaymentConfigs.FirstOrDefaultAsync(x => x.CompanyId == companyId && x.AccountId == accountId);

            if (stripePaymentConfig == null)
            {
                return new BadRequestObjectResult("stripe payment config error");
            }

            SetStripeApiKeyByPlatformCountry(stripePaymentConfig.Country);

            var supportEmail = req.Query["supportEmail"].ToString();
            var supportPhoneNumber = req.Query["supportPhoneNumber"].ToString();
            var statementDescriptor = req.Query["statementDescriptor"].ToString();

            var primaryColor = "#" + req.Query["primaryColor"].ToString();
            var secondaryColor = "#" + req.Query["secondaryColor"].ToString();

            var acctInfo = stripePaymentConfig.AccountInfo;

            acctInfo.Settings.Payments.StatementDescriptor = statementDescriptor;

            acctInfo.Settings.Branding.PrimaryColor = primaryColor;
            acctInfo.Settings.Branding.SecondaryColor = secondaryColor;

            var acctUpdateOptions = new AccountUpdateOptions();

            if (!string.IsNullOrEmpty(supportEmail) || !string.IsNullOrEmpty(supportPhoneNumber))
            {
                acctUpdateOptions.BusinessProfile = new AccountBusinessProfileOptions
                {
                    SupportEmail = string.IsNullOrEmpty(supportEmail) ? null : supportEmail,
                    SupportPhone = string.IsNullOrEmpty(supportPhoneNumber) ? null : supportPhoneNumber
                };

                if (!string.IsNullOrEmpty(supportEmail))
                {
                    acctInfo.BusinessProfile.SupportEmail = supportEmail;
                }
                if (!string.IsNullOrEmpty(supportPhoneNumber))
                {
                    acctInfo.BusinessProfile.SupportPhone = supportPhoneNumber;
                }
            }

            if (!string.IsNullOrEmpty(statementDescriptor) || !string.IsNullOrEmpty(primaryColor) || !string.IsNullOrEmpty(secondaryColor))
            {
                acctUpdateOptions.Settings = new AccountSettingsOptions
                {
                    Payments = new AccountSettingsPaymentsOptions()
                    {
                        StatementDescriptor = statementDescriptor
                    },
                    Branding = new AccountSettingsBrandingOptions
                    {
                        PrimaryColor = primaryColor,
                        SecondaryColor = secondaryColor
                    }
                };
            }

            if (req.HasFormContentType)
            {
                if (req.Form != null)
                {
                    foreach (var file in req.Form.Files)
                    {
                        if (file.Name == "companyLogo")
                        {
                            using (Stream stream = file.OpenReadStream())
                            {
                                var fileService = new FileService();

                                var fileCreateOptions = new FileCreateOptions
                                {
                                    File = stream,
                                    Purpose = FilePurpose.BusinessLogo,
                                };
                                Stripe.File uploadedFile = fileService.Create(fileCreateOptions);

                                if (acctUpdateOptions.Settings == null)
                                {
                                    acctUpdateOptions.Settings = new AccountSettingsOptions
                                    {
                                        Branding = new AccountSettingsBrandingOptions
                                        {
                                            Logo = uploadedFile.Id
                                        }
                                    };
                                }
                                else
                                {
                                    acctUpdateOptions.Settings.Branding.Logo = uploadedFile.Id;
                                }

                                acctInfo.Settings.Branding.LogoId = uploadedFile.Id;
                            }
                        }
                    }
                }
            }

            if (acctUpdateOptions.BusinessProfile != null || acctUpdateOptions.Settings != null)
            {
                var acctService = new AccountService();
                acctService.Update(stripePaymentConfig.AccountId, acctUpdateOptions);
            }

            stripePaymentConfig.AccountInfo = acctInfo;

            _appDbContext.Update(stripePaymentConfig);
            await _appDbContext.SaveChangesAsync();

            return new OkResult();
        }
        catch (Exception ex)
        {
            return new BadRequestObjectResult(ex);
        }
    }

    private static void SetStripeApiKeyByPlatformCountry(string platformCountry)
    {
        switch (platformCountry.ToLower())
        {
            case "hk":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_HK");
                break;
            case "sg":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_SG");
                break;
            case "my":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_MY");
                break;
            case "gb":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_GB");
                break;
        }
    }
}