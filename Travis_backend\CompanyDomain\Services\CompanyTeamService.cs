﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.FlowHubs;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.CompanyDomain.Services
{
    public interface ICompanyTeamService
    {
        Task<List<CompanyTeam>> GetTeamsAsync(Staff companyUser);

        Task<CompanyTeam> GetCompanyTeamAsync(string companyId, long teamId);

        Task AddOrRemoveTeam(string companyId, long staffId, List<long> teamIds);

        Task<CompanyTeam> AddToTeam(string companyId, long teamId, CreateNewTeamViewModel createNewTeamViewModel);

        Task<CompanyTeam> RemoveFromTeam(string companyId, long teamId, CreateNewTeamViewModel createNewTeamViewModel);

        Task<List<CompanyTeam>> GetCompanyteamFromStaffId(string companyId, string staffId);

        Task<bool> IsTeamExist(string companyId, long teamId);

        Task<CompanyTeam> GetCompanyTeamFromTeamId(string companyId, long teamId);

        Task<List<Staff>> GetActiveTeamMembersAsync(long teamId);

        Task<List<Staff>> GetAllTeamMembersAsync(long teamId);

        Task<(List<string> TeamWhatsappTwilioDefaultChannelIds,
            List<string> TeamWhatsapp360DialogDefaultChannelIds,
            List<string> TeamWhatsappCloudDefaultChannelIds,
            List<string> TeamFacebookDefaultChannelIds,
            List<string> TeamInstagramDefaultChannelIds)> GetTeamsDefaultChannelIdentityIdsAsync(
            List<CompanyTeam> companyTeams);

        bool IsMessageBelongsToTeamDefaultChannel(
            ConversationMessage message,
            List<string> teamWhatsappTwilioDefaultChannelIds,
            List<string> teamWhatsapp360DialogDefaultChannelIds,
            List<string> teamWhatsappCloudDefaultChannelIds,
            List<string> teamFacebookDefaultChannelIds,
            List<string> teamInstagramDefaultChannelIds);
    }

    public class CompanyTeamService : ICompanyTeamService
    {
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly ILogger _logger;
        private readonly IDbContextService _dbContextService;
        private readonly IStaffHooks _staffHooks;
        private readonly IAccessControlAggregationService _aggregationService;

        public CompanyTeamService(
            IMapper mapper,
            IConfiguration configuration,
            ILogger<CompanyTeamService> logger,
            IDbContextService dbContextService,
            IStaffHooks staffHooks,
            IAccessControlAggregationService aggregationService)
        {
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _dbContextService = dbContextService;
            _staffHooks = staffHooks;
            _aggregationService = aggregationService;
        }

        public async Task<List<CompanyTeam>> GetTeamsAsync(Staff companyUser)
        {
            var appDbContext = _dbContextService.GetDbContext();

            var teams = await appDbContext.CompanyStaffTeams
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .OrderBy(x => x.TeamName)
                .ToListAsync();

            var staff = await _aggregationService.GetStaffAccessControlAggregateAsync(companyUser);

            if (staff.HasPermissionToViewAllTeams())
            {
                return teams;
            }

            if (staff.HasPermissionToViewAssociatedTeams())
            {
                var associatedTeamIds = staff.AssociatedTeams.Select(t => t.Id).ToList();
                teams = teams.Where(x => associatedTeamIds.Contains(x.Id)).ToList();
                return teams;
            }

            return[];
        }

        public Task<CompanyTeam> GetCompanyTeamAsync(string companyId, long teamId)
        {
            var appDbContext = _dbContextService.GetDbContext();
            return appDbContext.CompanyStaffTeams
                .FirstOrDefaultAsync(
                    team =>
                        team.CompanyId == companyId
                        && team.Id == teamId);
        }

        public async Task AddOrRemoveTeam(string companyId, long staffId, List<long> teamIds)
        {
            var appDbContext = _dbContextService.GetDbContext();
            if (teamIds != null)
            {
                var staff = await appDbContext.UserRoleStaffs
                    .Where(x => x.Id == staffId)
                    .FirstOrDefaultAsync();

                var joinedTeam = await appDbContext.CompanyStaffTeams
                    .Where(x => x.Members.Any(x => x.StaffId == staffId))
                    .ToListAsync();

                var toRemoves = joinedTeam
                    .Where(y => !teamIds.Contains(y.Id))
                    .ToList();

                foreach (var toRemove in toRemoves)
                {
                    var team = await RemoveFromTeam(
                        companyId,
                        toRemove.Id,
                        new CreateNewTeamViewModel
                        {
                            StaffIds = new List<string>
                            {
                                staff.IdentityId
                            }
                        });
                }

                var addNewTeams = teamIds
                    .Where(y => !joinedTeam
                        .Select(x => x.Id)
                        .Contains(y))
                    .ToList();

                foreach (var add in addNewTeams)
                {
                    var team = await AddToTeam(
                        companyId,
                        add,
                        new CreateNewTeamViewModel
                        {
                            StaffIds = new List<string>
                            {
                                staff.IdentityId
                            }
                        });
                }
            }
        }

        public async Task<CompanyTeam> AddToTeam(
            string companyId,
            long teamId,
            CreateNewTeamViewModel createNewTeamViewModel)
        {
            var appDbContext = _dbContextService.GetDbContext();
            var team = await appDbContext.CompanyStaffTeams
                .Include(x => x.Members)
                    .ThenInclude(x => x.Staff.Identity)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == teamId);

            foreach (var staffId in createNewTeamViewModel.StaffIds)
            {
                if (!team.Members
                        .Select(x => x.Staff.IdentityId)
                        .Contains(staffId))
                {
                    var staff = await appDbContext.UserRoleStaffs
                        .Include(x => x.Identity)
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == companyId
                                && x.IdentityId == staffId);
                    if (staff == null)
                    {
                        continue;
                    }

                    team.Members.Add(
                        new TeamMember
                        {
                            Staff = staff
                        });

                    await _staffHooks.OnStaffAddedToTeamsAsync(
                        team.CompanyId,
                        staff.Id,
                        new List<CompanyTeam>()
                        {
                            team
                        });
                }
            }

            team.LastUpdated = DateTime.UtcNow;
            await appDbContext.SaveChangesAsync();

            BackgroundJob.Enqueue<IConversationAssigneeService>(
                x =>
                    x.SetAssigneeNewAssignedTeamId(
                        companyId,
                        teamId,
                        createNewTeamViewModel));

            return team;
        }

        public async Task<CompanyTeam> RemoveFromTeam(
            string companyId,
            long teamId,
            CreateNewTeamViewModel createNewTeamViewModel)
        {
            var appDbContext = _dbContextService.GetDbContext();
            var team = await appDbContext.CompanyStaffTeams
                .Include(x => x.Members)
                .ThenInclude(x => x.Staff.Identity)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == teamId);

            var membersTobeRemove = team.Members
                .Where(x => createNewTeamViewModel.StaffIds.Contains(x.Staff.IdentityId))
                .ToList();

            foreach (var tobeRemove in membersTobeRemove)
            {
                team.Members.Remove(tobeRemove);

                await _staffHooks.OnStaffRemovedFromTeamsAsync(
                    companyId,
                    tobeRemove.StaffId,
                    new List<CompanyTeam>()
                    {
                        team
                    });
            }

            team.LastUpdated = DateTime.UtcNow;
            await appDbContext.SaveChangesAsync();

            BackgroundJob.Enqueue<IConversationAssigneeService>(
                x =>
                    x.RemoveAssigneeAssignedTeamId(
                        companyId,
                        teamId,
                        createNewTeamViewModel));

            return team;
        }

        public async Task<List<CompanyTeam>> GetCompanyteamFromStaffId(string companyId, string staffId)
        {
            var appDbContext = _dbContextService.GetDbContext();
            var teams = await appDbContext.CompanyStaffTeams
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Members.Any(y => y.Staff.IdentityId == staffId))
                .ToListAsync();

            return teams;
        }

        public Task<bool> IsTeamExist(string companyId, long teamId)
        {
            var appDbContext = _dbContextService.GetDbContext();
            return appDbContext.CompanyStaffTeams
                .AnyAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == teamId);
        }

        public async Task<CompanyTeam> GetCompanyTeamFromTeamId(string companyId, long teamId)
        {
            if (!await IsTeamExist(companyId, teamId))
            {
                throw new ArgumentException($"Team with Id {teamId} does not exist");
            }
            var appDbContext = _dbContextService.GetDbContext();
            return await appDbContext.CompanyStaffTeams.FirstAsync(x => x.Id == teamId);
        }

        public Task<List<Staff>> GetActiveTeamMembersAsync(long teamId)
        {
            var appDbContext = _dbContextService.GetDbContext();
            return appDbContext.CompanyTeamMembers
                .Where(
                    member =>
                        member.CompanyTeamId == teamId
                        && member.Staff.Status == StaffStatus.Active
                        && member.Staff.Id != 1)
                .Select(member => member.Staff)
                .OrderBy(staff => staff.Order)
                .ThenBy(x => x.Id)
                .ToListAsync();
        }

        public Task<List<Staff>> GetAllTeamMembersAsync(long teamId)
        {
            var appDbContext = _dbContextService.GetDbContext();
            return appDbContext.CompanyTeamMembers
                .Where(
                    member =>
                        member.CompanyTeamId == teamId
                        && member.Staff.Id != 1)
                .Select(member => member.Staff)
                .OrderBy(staff => staff.Order)
                .ThenBy(x => x.Id)
                .ToListAsync();
        }

        public async Task<(List<string> TeamWhatsappTwilioDefaultChannelIds,
            List<string> TeamWhatsapp360DialogDefaultChannelIds,
            List<string> TeamWhatsappCloudDefaultChannelIds,
            List<string> TeamFacebookDefaultChannelIds,
            List<string> TeamInstagramDefaultChannelIds)> GetTeamsDefaultChannelIdentityIdsAsync(List<CompanyTeam> companyTeams)
        {
            var teamWhatsappTwilioDefaultChannelIds = new List<string>();
            var teamWhatsappCloudDefaultChannelIds = new List<string>();
            var teamWhatsapp360dialogDefaultChannelTableIds = new List<long>();
            var teamWhatsapp360dialogDefaultChannelIds = new List<string>();
            var teamFacebookDefaultChannelIds = new List<string>();
            var teamInstagramDefaultChannelIds = new List<string>();

            foreach (var companyTeam in companyTeams)
            {
                if (companyTeam.DefaultChannels != null && companyTeam.DefaultChannels.Any())
                {
                    foreach (var defaultChannel in companyTeam.DefaultChannels)
                    {
                        switch (defaultChannel.channel)
                        {
                            case ChannelTypes.WhatsappTwilio:
                                defaultChannel.ids.ForEach(
                                    channelId =>
                                    {
                                        var twilioInstance = channelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                        var channelIdentityId = twilioInstance[1].Replace("whatsapp:+", string.Empty);
                                        teamWhatsappTwilioDefaultChannelIds.Add(channelIdentityId);
                                    });

                                break;
                            case ChannelTypes.Whatsapp360Dialog:
                                defaultChannel.ids.ForEach(
                                    channelId =>
                                    {
                                        var validLong = long.TryParse(
                                            channelId,
                                            out var whatsapp360dialogChannelId);

                                        if (validLong)
                                        {
                                            teamWhatsapp360dialogDefaultChannelTableIds.Add(
                                                whatsapp360dialogChannelId);
                                        }
                                    });
                                break;
                            case ChannelTypes.WhatsappCloudApi:
                                teamWhatsappCloudDefaultChannelIds.AddRange(defaultChannel.ids);
                                break;
                            case ChannelTypes.Facebook:
                                teamFacebookDefaultChannelIds.AddRange(defaultChannel.ids);
                                break;
                            case ChannelTypes.Instagram:
                                teamInstagramDefaultChannelIds.AddRange(defaultChannel.ids);
                                break;
                        }
                    }
                }
            }

            var dbContext = _dbContextService.GetDbContext();

            teamWhatsapp360dialogDefaultChannelIds = await dbContext.ConfigWhatsApp360DialogConfigs
                .Where(x => teamWhatsapp360dialogDefaultChannelTableIds.Contains(x.Id)).Select(x => x.ChannelIdentityId)
                .ToListAsync();

            return (teamWhatsappTwilioDefaultChannelIds, teamWhatsapp360dialogDefaultChannelIds,
                teamWhatsappCloudDefaultChannelIds, teamFacebookDefaultChannelIds, teamInstagramDefaultChannelIds);
        }

        public bool IsMessageBelongsToTeamDefaultChannel(
            ConversationMessage message,
            List<string> teamWhatsappTwilioDefaultChannelIds,
            List<string> teamWhatsapp360DialogDefaultChannelIds,
            List<string> teamWhatsappCloudDefaultChannelIds,
            List<string> teamFacebookDefaultChannelIds,
            List<string> teamInstagramDefaultChannelIds)
        {
            return message.Channel switch
            {
                ChannelTypes.WhatsappTwilio => teamWhatsappTwilioDefaultChannelIds.Contains(message.ChannelIdentityId),
                ChannelTypes.Whatsapp360Dialog => teamWhatsapp360DialogDefaultChannelIds.Contains(
                    message.ChannelIdentityId),
                ChannelTypes.WhatsappCloudApi => teamWhatsappCloudDefaultChannelIds.Contains(message.ChannelIdentityId),
                ChannelTypes.Facebook => teamFacebookDefaultChannelIds.Contains(message.ChannelIdentityId),
                ChannelTypes.Instagram => teamInstagramDefaultChannelIds.Contains(message.ChannelIdentityId),
                _ => false
            };
        }
    }
}