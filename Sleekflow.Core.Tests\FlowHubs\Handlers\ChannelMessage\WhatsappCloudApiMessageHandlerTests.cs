using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.FlowHubs.Handlers.ChannelMessage;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.FlowHubs.Handlers.ChannelMessage;

[TestFixture]
public class WhatsappCloudApiMessageHandlerTests
{
  private WhatsappCloudApiMessageHandler _handler;
  private ILogger<WhatsappCloudApiMessageHandler> _mockLogger;
  private IUserProfileService _mockUserProfileService;

  [SetUp]
  public void SetUp()
  {
    _mockLogger = Substitute.For<ILogger<WhatsappCloudApiMessageHandler>>();
    _mockUserProfileService = Substitute.For<IUserProfileService>();

    _handler = new WhatsappCloudApiMessageHandler(
        null,
        _mockLogger);
  }

  [Test]
  public void ChannelType_ShouldReturnWhatsappCloudApi()
  {
    // Act
    var result = _handler.ChannelType;

    // Assert
    Assert.That(result, Is.EqualTo(ChannelTypes.WhatsappCloudApi));
  }

  [Test]
  public async Task PrepareConversationMessageAsync_WithTextMessage_ShouldSetCorrectProperties()
  {
    // Arrange
    var companyId = "test-company-id";
    var channelIdentityId = "test-channel-id";
    var messageType = "text";
    var messageText = "Hello, this is a test message";

    var messageBody = new MessageBody
    {
      TextMessage = new TextMessageObject
      {
        Text = messageText
      }
    };

    var conversation = new Conversation
    {
      Id = "test-conversation-id",
      WhatsappCloudApiUser = new WhatsappCloudApiSender
      {
        WhatsappId = "test-whatsapp-user-id",
      }
    };

    var files = new List<IFormFile>();

    // Act
    var result = await _handler.PrepareConversationMessageAsync(
        companyId, channelIdentityId, messageType, messageBody, conversation, files);

    // Assert
    Assert.Multiple(() =>
    {
      Assert.That(result.Channel, Is.EqualTo(ChannelTypes.WhatsappCloudApi));
      Assert.That(result.ConversationId, Is.EqualTo(conversation.Id));
      Assert.That(result.IsSentFromSleekflow, Is.True);
      Assert.That(result.MessageType, Is.EqualTo(messageType));
      Assert.That(result.MessageContent, Is.EqualTo(messageText));
      Assert.That(result.WhatsappCloudApiReceiver, Is.Not.Null);
      Assert.That(result.DeliveryType, Is.EqualTo(DeliveryType.FlowHubAction));
      Assert.That(result.AnalyticTags, Is.Null);
      Assert.That(files, Is.Empty);
    });
  }

  [Test]
  public async Task PrepareConversationMessageAsync_WithInteractiveMessage_ShouldSetExtendedMessagePayload()
  {
    // Arrange
    var companyId = "test-company-id";
    var channelIdentityId = "test-channel-id";
    var messageType = "interactive";

    var messageBody = new MessageBody();
    var conversation = new Conversation
    {
      Id = "test-conversation-id",
      WhatsappCloudApiUser = new WhatsappCloudApiSender
      {
        WhatsappId = "test-whatsapp-user-id",
      }
    };

    var files = new List<IFormFile>();

    // Act
    var result = await _handler.PrepareConversationMessageAsync(
        companyId, channelIdentityId, messageType, messageBody, conversation, files);

    // Assert
    Assert.Multiple(() =>
    {
      Assert.That(result.Channel, Is.EqualTo(ChannelTypes.WhatsappCloudApi));
      Assert.That(result.ConversationId, Is.EqualTo(conversation.Id));
      Assert.That(result.IsSentFromSleekflow, Is.True);
      Assert.That(result.MessageType, Is.EqualTo(messageType));
      Assert.That(result.ExtendedMessagePayload, Is.Not.Null);
      Assert.That(result.ExtendedMessagePayload.Channel, Is.EqualTo(ChannelTypes.WhatsappCloudApi));
      Assert.That(result.WhatsappCloudApiReceiver, Is.Not.Null);
      Assert.That(result.DeliveryType, Is.EqualTo(DeliveryType.FlowHubAction));
      Assert.That(result.AnalyticTags, Is.Null);
      Assert.That(files, Is.Empty);
    });
  }

  [Test]
  public async Task PrepareConversationMessageAsync_WithNullWhatsappUser_ShouldCallSwitchChannel()
  {
    // Arrange
    var companyId = "test-company-id";
    var channelIdentityId = "test-channel-id";
    var messageType = "text";
    var userProfileId = "test-user-profile-id";

    var messageBody = new MessageBody
    {
      TextMessage = new TextMessageObject
      {
        Text = "Test message"
      }
    };

    var conversation = new Conversation
    {
      Id = "test-conversation-id",
      UserProfileId = userProfileId,
      WhatsappCloudApiUser = null
    };

    var files = new List<IFormFile>();

    // Act
    await _handler.PrepareConversationMessageAsync(
        companyId, channelIdentityId, messageType, messageBody, conversation, files);

    // Assert
    await _mockUserProfileService.Received(1).SwitchWhatsappCloudApiChannel(
        companyId, userProfileId, channelIdentityId);
  }

  [Test]
  public void PrepareConversationMessageAsync_WithUnsupportedMessageType_ShouldThrowNotImplementedException()
  {
    // Arrange
    var companyId = "test-company-id";
    var channelIdentityId = "test-channel-id";
    var messageType = "unsupported";
    var messageBody = new MessageBody();
    var conversation = new Conversation { Id = "test-conversation-id" };
    var files = new List<IFormFile>();

    // Act & Assert
    var exception = Assert.ThrowsAsync<NotImplementedException>(async () =>
        await _handler.PrepareConversationMessageAsync(
            companyId, channelIdentityId, messageType, messageBody, conversation, files));

    Assert.That(exception.Message, Is.EqualTo($"Message type {messageType} not implemented for {ChannelTypes.WhatsappCloudApi}"));
  }
}