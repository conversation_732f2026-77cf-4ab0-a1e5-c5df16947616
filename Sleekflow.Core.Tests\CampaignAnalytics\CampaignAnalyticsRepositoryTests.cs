﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.CampaignAnalyticsDomain.Constants;
using Travis_backend.CampaignAnalyticsDomain.Models;
using Travis_backend.CampaignAnalyticsDomain.Services;
using Travis_backend.Database;
using Travis_backend.Exceptions;

namespace Sleekflow.Core.Tests.CampaignAnalytics;

public class CampaignAnalyticsRepositoryTests
{
    private const string MockCompanyId = "mock-company-id";
    private const string MockCampaignId = "mock-campaign-id";
    private const string MockAnalyticTag = "mock-analytic-tag";
    private static readonly ReplyWindow MockReplyWindow = new ReplyWindow("day", 3);

    [Test]
    public async Task AllQueries_WithCorrectInputs_ShouldNotThrowException()
    {
        // Arrange, assign a new db context instance to each task to make it parallelizable
        var getCountsPerMessageStatusByBroadcastTask = NewCampaignAnalyticsRepositoryInstance()
            .GetCountsPerMessageStatusByBroadcastAsync(MockCompanyId, MockCampaignId);

        var getRepliedCountByBroadcastTask = NewCampaignAnalyticsRepositoryInstance()
            .GetRepliedCountByBroadcastAsync(MockCompanyId, MockCampaignId, MockReplyWindow);

        var countsPerMessageStatusByAnalyticTagTask = NewCampaignAnalyticsRepositoryInstance()
            .GetCountsPerMessageStatusByAnalyticTagAsync(MockCompanyId, MockAnalyticTag);

        var repliedCountByAnalyticTagTask = NewCampaignAnalyticsRepositoryInstance()
            .GetRepliedCountByAnalyticTagAsync(MockCompanyId, MockAnalyticTag, MockReplyWindow);

        var repliedMessageOverviewByBroadcastTask = NewCampaignAnalyticsRepositoryInstance()
            .GetRepliedMessageOverviewByBroadcastAsync(
                MockCompanyId,
                MockCampaignId,
                MockReplyWindow,
                "CreatedAt",
                "asc",
                0,
                10);

        var messageOverviewByBroadcastTask = NewCampaignAnalyticsRepositoryInstance()
            .GetMessageOverviewByBroadcastAsync(
                MockCompanyId,
                MockCampaignId,
                CampaignMessageStatuses.Bounced,
                "CreatedAt",
                "asc",
                0,
                10);

        var userProfileIdsOfRepliedByBroadcastTask = NewCampaignAnalyticsRepositoryInstance()
            .GetUserProfileIdsOfRepliedByBroadcastAsync(MockCompanyId, MockCampaignId, MockReplyWindow);

        var userProfileIdsOfCommonStatusesByBroadcastTask = NewCampaignAnalyticsRepositoryInstance()
            .GetUserProfileIdsOfCommonStatusesByBroadcastAsync(
                MockCompanyId,
                MockCampaignId,
                CampaignMessageStatuses.Sent);

        var userProfileIdsOfRepliedByAnalyticTagTask = NewCampaignAnalyticsRepositoryInstance()
            .GetUserProfileIdsOfRepliedByAnalyticTagAsync(MockCompanyId, MockAnalyticTag, MockReplyWindow);

        var userProfileIdsOfCommonStatusesByAnalyticTagTask = NewCampaignAnalyticsRepositoryInstance()
            .GetUserProfileIdsOfCommonStatusesByAnalyticTagAsync(
                MockCompanyId,
                MockAnalyticTag,
                CampaignMessageStatuses.Sent);

        var repliedMessageOverviewByAnalyticTagTask = NewCampaignAnalyticsRepositoryInstance()
            .GetRepliedMessageOverviewByAnalyticTagAsync(
                MockCompanyId,
                MockAnalyticTag,
                MockReplyWindow,
                "CreatedAt",
                "asc",
                0,
                10);

        var messageOverviewByAnalyticTagTask = NewCampaignAnalyticsRepositoryInstance()
            .GetMessageOverviewByAnalyticTagAsync(
                MockCompanyId,
                MockAnalyticTag,
                CampaignMessageStatuses.Bounced,
                "CreatedAt",
                "asc",
                0,
                10);

        var repliedMessageOverviewsTask = NewCampaignAnalyticsRepositoryInstance()
            .GetRepliedMessageOverviewsAsync(
                MockCompanyId,
                new List<long>
                {
                    1, 2, 3, 4
                },
                MockReplyWindow);

        // Act
        var tasks = new List<Task>
        {
            getCountsPerMessageStatusByBroadcastTask,
            getRepliedCountByBroadcastTask,
            countsPerMessageStatusByAnalyticTagTask,
            repliedCountByAnalyticTagTask,
            repliedMessageOverviewByBroadcastTask,
            messageOverviewByBroadcastTask,
            userProfileIdsOfRepliedByBroadcastTask,
            userProfileIdsOfCommonStatusesByBroadcastTask,
            userProfileIdsOfRepliedByAnalyticTagTask,
            userProfileIdsOfCommonStatusesByAnalyticTagTask,
            repliedMessageOverviewByAnalyticTagTask,
            messageOverviewByAnalyticTagTask,
            repliedMessageOverviewsTask
        };

        // Assert
        await Parallel.ForEachAsync(
            tasks,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 3
            },
            (task, _) =>
            {
                Assert.DoesNotThrowAsync(async () => await task);

                return ValueTask.CompletedTask;
            });
    }

    [Test]
    public async Task SampleQuery_WithIncorrectInputs_ShouldThrowException()
    {
        // Arrange
        var query = "SELECT * FROM [dbo].[NonExistedTable];";

        var qd = new CampaignAnalyticsQueryDefinition(
            query,
            new List<SqlParameter>());

        // Act & Assert
        Assert.ThrowsAsync<CampaignAnalyticsDbQueryException>(
            async () =>
            {
                await NewCampaignAnalyticsRepositoryInstance()
                    .GetObjectsAsync<int>(qd);
            });
    }

    private ICampaignAnalyticsRepository NewCampaignAnalyticsRepositoryInstance()
    {
        var connectionString =
            "Server=tcp:sleekflow-core-sql-server-eas-dev928ea268.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=s81604a6f;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=100;";
        var options = new DbContextOptionsBuilder<ApplicationReadDbContext>()
            .UseSqlServer(connectionString)
            .Options;
        var dbContext = new ApplicationReadDbContext(options);

        return new CampaignAnalyticsRepository(
            dbContext,
            new Logger<CampaignAnalyticsRepository>(new LoggerFactory()));
    }
}