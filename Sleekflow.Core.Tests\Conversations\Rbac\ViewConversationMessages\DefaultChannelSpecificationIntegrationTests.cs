using Microsoft.EntityFrameworkCore;
using NUnit.Framework;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationSettingsConstants;
using Travis_backend.ConversationDomain.ConversationSpecifications.RbacConversationSpecifications.
    ConversationMessageSpecifications;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.Conversations.Rbac.ViewConversationMessages
{
    public class DefaultChannelSpecificationIntegrationTests
    {
        private DbContextOptions<ConversationMessageDbContext> _options;
        private StaffAccessControlAggregate _staff;
        private TeamAccessControlAggregate _team;
        private string _companyId;
        private const string FacebookChannelOne = "FacebookChannelOne";
        private const string FacebookChannelTwo = "FacebookChannelTwo";
        private const string WhatsappCloudApiChannelOne = "WhatsappCloudApiChannelOne";
        private const string WhatsappCloudApiChannelTwo = "WhatsappCloudApiChannelTwo";
        private const string WhatsappTwilioChannelOne = "WhatsappTwilioChannelOne";
        private const string WhatsappTwilioChannelTwo = "WhatsappTwilioChannelTwo";


        [SetUp]
        public void Setup()
        {
            _options = new DbContextOptionsBuilder<ConversationMessageDbContext>()
                .UseInMemoryDatabase("TestConversationMessageDb")
                .Options;

            _companyId = "sleekflow";

            _team = new TeamAccessControlAggregate
            {
                Id = 1,
                TeamMemberStaffIds = new List<long>
                {
                    1, 2, 3
                },
                TeamDefaultChannels = new TeamDefaultChannels
                {
                    ChannelTypeToChannelIdentityIds = new Dictionary<string, List<string>>
                    {
                        {
                            ChannelTypes.WhatsappTwilio, new List<string>
                            {
                                WhatsappTwilioChannelOne
                            }
                        },
                        {
                            ChannelTypes.WhatsappCloudApi, new List<string>
                            {
                                WhatsappCloudApiChannelOne
                            }
                        },
                        {
                            ChannelTypes.Facebook, new List<string>
                            {
                                FacebookChannelOne
                            }
                        }
                    }
                }
            };

            _staff = new StaffAccessControlAggregate
            {
                StaffId = 1,
                RoleType = StaffUserRole.Staff,
                CompanyId = _companyId,
                RbacRoles = new List<RbacRole>
                {
                    new RbacRole
                    {
                        RbacRolePermissions = new List<string>
                        {
                            RbacInboxSettings.ViewDefaultChannelMessagesOnly,
                        }
                    }
                },
                AssociatedTeams = new List<TeamAccessControlAggregate>
                {
                    _team
                }
            };
        }

        [TearDown]
        public void TearDown()
        {
            using (var context = new ConversationMessageDbContext(_options))
            {
                context.Database.EnsureDeleted();
            }
        }

        [TestCaseSource(nameof(GetDefaultChannelSuccessTestCases))]
        public void SuccessTestCases(ConversationMessage message)
        {
            var specification = new DefaultChannelSpecification(_staff);

            using (var context = new ConversationMessageDbContext(_options))
            {
                context.ConversationMessages.Add(message);
                context.SaveChanges();
            }

            using (var context = new ConversationMessageDbContext(_options))
            {
                var result = context.ConversationMessages
                    .Where(specification.ToExpression())
                    .ToList();

                Assert.That(
                    result.Count,
                    Is.EqualTo(1),
                    $"Expected 1 conversation message to match the specification for message Id: {message.Id}");
            }
        }

        [TestCaseSource(nameof(GetDefaultChannelFailureTestCases))]
        public void FailureTestCases(ConversationMessage message)
        {
            var specification = new DefaultChannelSpecification(_staff);

            using (var context = new ConversationMessageDbContext(_options))
            {
                context.ConversationMessages.Add(message);
                context.SaveChanges();
            }

            using (var context = new ConversationMessageDbContext(_options))
            {
                var result = context.ConversationMessages
                    .Where(specification.ToExpression())
                    .ToList();

                Assert.That(
                    result.Count,
                    Is.EqualTo(0),
                    $"Expected 0 conversation message to match the specification for message Id: {message.Id}");
            }
        }

        private static IEnumerable<TestCaseData> GetDefaultChannelSuccessTestCases()
        {
            var companyId = "sleekflow";

            yield return new TestCaseData(
                new ConversationMessage
                {
                    Id = 1, CompanyId = companyId, ChannelIdentityId = WhatsappTwilioChannelOne, Channel = ChannelTypes.WhatsappTwilio
                }).SetName("Message from WhatsappTwilio default channel");

            yield return new TestCaseData(
                new ConversationMessage
                {
                    Id = 2, CompanyId = companyId, ChannelIdentityId = FacebookChannelOne, Channel = ChannelTypes.Facebook
                }).SetName("Message from Facebook default channel");

            yield return new TestCaseData(
                new ConversationMessage
                {
                    Id = 3, CompanyId = companyId, ChannelIdentityId = ChannelTypes.Viber, Channel = ChannelTypes.Viber
                }).SetName("Message from non default channel channel Viber");
        }

        private static IEnumerable<TestCaseData> GetDefaultChannelFailureTestCases()
        {
            var companyId = "sleekflow";

            yield return new TestCaseData(
                new ConversationMessage
                {
                    Id = 4, CompanyId = companyId, ChannelIdentityId = WhatsappTwilioChannelTwo, Channel = ChannelTypes.WhatsappTwilio
                }).SetName("Message from non-default channel WhatsappTwilio");

            yield return new TestCaseData(
                new ConversationMessage
                {
                    Id = 5, CompanyId = companyId, ChannelIdentityId = FacebookChannelTwo, Channel = ChannelTypes.Facebook
                }).SetName("Message from non-default channel Facebook");

            yield return new TestCaseData(
                new ConversationMessage
                {
                    Id = 6, CompanyId = companyId, ChannelIdentityId = WhatsappCloudApiChannelTwo, Channel = ChannelTypes.WhatsappCloudApi
                }).SetName("Message from non-default channel WhatsappCloudApi");
        }
    }
}