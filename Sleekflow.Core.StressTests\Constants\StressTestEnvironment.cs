namespace Sleekflow.Core.StressTests.Constants;

public static class StressTestEnvironment
{
    private static readonly string EnvironmentName;
    static StressTestEnvironment()
    {
        EnvironmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
    }

    public static bool IsDevelopment() => EnvironmentName == "Development";
    public static bool IsStaging() => EnvironmentName == "Staging";
    public static bool IsProduction() => EnvironmentName == "Production";
    public static bool IsPerformance() => EnvironmentName == "Performance";

    public static string GetSleekflowCoreApiServerPath()
    {
        if (IsStaging())
        {
            return "https://sleekflow-core-app-eas-staging.azurewebsites.net";
        }
        if (IsPerformance())
        {
            return "https://sleekflow-core-app-eas-performance.azurewebsites.net";
        }
        if (IsProduction())
        {
            return "https://api.sleekflow.io";
        }

        // Default to Development
        return "https://sleekflow-core-app-eas-dev.azurewebsites.net";
    }

    public static string GetSleekflowGatewayServerPath()
    {
        if (IsStaging())
        {
            return "https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net";
        }
        if (IsPerformance())
        {
            return "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net";
        }
        if (IsProduction())
        {
            return "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net";
        }

        // Default to Development
        return "https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net";
    }
}