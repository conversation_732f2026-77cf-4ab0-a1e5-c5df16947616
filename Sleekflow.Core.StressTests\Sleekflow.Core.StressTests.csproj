<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <DefineConstants>TRACE;DEBUG</DefineConstants>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DefineConstants>TRACE;RELEASE</DefineConstants>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Environment)' == 'Development'">
        <EnvironmentName>Development</EnvironmentName>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Environment)' == 'Staging'">
        <EnvironmentName>Staging</EnvironmentName>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Environment)' == 'Production'">
        <EnvironmentName>Production</EnvironmentName>
    </PropertyGroup>
    
    <PropertyGroup Condition="'$(Environment)' == 'Performance'">
        <EnvironmentName>Performance</EnvironmentName>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Abstracta.JmeterDsl" Version="0.7.0" />
        <PackageReference Include="coverlet.collector" Version="6.0.0"/>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0"/>
        <PackageReference Include="NUnit" Version="3.14.0"/>
        <PackageReference Include="NUnit.Analyzers" Version="3.9.0"/>
        <PackageReference Include="NUnit3TestAdapter" Version="4.5.0"/>
        <PackageReference Include="Polly" Version="8.4.1" />
        <PackageReference Include="Serilog" Version="3.1.1" />
        <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    </ItemGroup>

    <ItemGroup>
        <Using Include="NUnit.Framework"/>
    </ItemGroup>

</Project>
