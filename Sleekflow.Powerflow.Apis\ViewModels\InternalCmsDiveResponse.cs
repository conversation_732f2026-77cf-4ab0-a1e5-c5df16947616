namespace Sleekflow.Powerflow.Apis.ViewModels;

public class DiveActivityLogs
{
    public string UserName { get; set; }

    public string UserEmail { get; set; }

    public string CompanyName { get; set; }

    public string LoginAsName { get; set; }

    public string LoginAsEmail { get; set; }

    public DateTime? CreatedAt { get; set; }

    public bool IsAdminDive { get; set; }
}

public class GetDiveActivityLogsResponse
{
    public List<DiveActivityLogs> DiveActivityLogs { get; set; }

    public int Count { get; set; }
}