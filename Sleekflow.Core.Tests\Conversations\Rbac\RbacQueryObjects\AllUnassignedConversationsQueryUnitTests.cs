using System.Text.RegularExpressions;
using Travis_backend.ConversationDomain.ConversationSpecifications.RbacConversationSpecifications.QueryObjects.Rbac;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Rbac.RbacQueryObjects;

public class AllUnassignedConversationsQueryUnitTests
{
    private StaffAccessControlAggregate _staff;

    [SetUp]
    public void Setup()
    {
        _staff = new StaffAccessControlAggregate
        {
            StaffId = 15201,
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea"
        };
    }

    [Test]
    public void can_generate_valid_sql()
    {
        // Arrange
        var query = new AllUnassignedConversationsQuery(_staff);
        var alias = "C";
        var expectedSql = $@"
            SELECT {alias}.*
            FROM Conversations {alias}
            WHERE {alias}.AssignedTeamId IS NULL
            AND {alias}.AssigneeId IS NULL
	        AND {alias}.CompanyId = '{_staff.CompanyId}'
        ";

        // Act
        var actualSql = query.ToSql(alias);
        Assert.That(
            Regex.Replace(actualSql.ToLower().Trim(), @"\s+", ""),
            Is.EqualTo(Regex.Replace(expectedSql.ToLower().Trim(), @"\s+", "")));

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(actualSql.Trim());
        TestContext.WriteLine("Expected SQL Query:");
        TestContext.WriteLine(expectedSql.Trim());
    }
}