using System.Collections.Generic;
using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Utils;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;

namespace Travis_backend.CompanyDomain.Services;

/// <summary>
/// Company Staff Service for API Controllers.
/// </summary>
public interface ICompanyStaffControllerService
{
    /// <summary>
    /// Get company staff for given search conditions.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="companyType">Company type.</param>
    /// <param name="requestQuery">Search condition.</param>
    /// <param name="offset">Offset.</param>
    /// <param name="limit">Limit.</param>
    /// <returns>Collection of StaffOverviewResponse.</returns>
    Task<(IEnumerable<StaffOverviewResponse> Results, int Count)>
        GetCompanyStaffAsync(string companyId, CompanyType companyType, GetCompanyStaffRequestQuery requestQuery, int? offset, int? limit);

    /// <summary>
    /// Count number of company staff for given search conditions.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="companyType">Company type.</param>
    /// <param name="requestQuery">Search condition.</param>
    /// <returns>Number of staffs.</returns>
    Task<int> CountCompanyStaffAsync(string companyId, CompanyType companyType, GetCompanyStaffRequestQuery requestQuery);

    /// <summary>
    /// Check whether a user is company owner.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="identityId">IdentityId.</param>
    /// <returns>Boolean indicate whether a user is company owner.</returns>
    Task<bool> IsCompanyOwnerAsync(string companyId, string identityId);

    /// <summary>
    /// Update the role of a staff member in the company.
    /// </summary>
    /// <param name="companyId">The ID of the company.</param>
    /// <param name="staffId">The ID of the staff member whose role is to be updated.</param>
    /// <param name="targetRole">The new role to be assigned to the staff member.</param>
    /// <returns>
    /// A `Result` object containing the updated `StaffUserRole` if successful, or an error message if the update fails.
    /// </returns>
    Task<Result<StaffUserRole, string>> UpdateStaffRoleAsync(
        string companyId,
        string staffId,
        string targetRole);
}