using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationSpecifications.RbacConversationSpecifications.SpecificationBuilders;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Rbac.RbacSpecificationBuilders;

[TestFixture]
public class RbacViewConversationsSqlSpecificationBuilderUnitTests
{
    private StaffAccessControlAggregate _staffWithAssignedToMePermission;
    private StaffAccessControlAggregate _staffWithAssignedToMeAndAssignedToMyTeamPermission;
    private StaffAccessControlAggregate _staffWithoutPermission;
    private const string Alias = "C";

    [SetUp]
    public void Setup()
    {
        _staffWithAssignedToMeAndAssignedToMyTeamPermission = new StaffAccessControlAggregate
        {
            StaffId = 15201,
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                new ()
                {
                    Id = 1
                },
                new ()
                {
                    Id = 2
                },
                new ()
                {
                    Id = 3
                }
            },
            RbacRoles = new List<RbacRole>()
            {
                new ()
                {
                    SleekflowRoleName = "Admin",
                    SleekflowCompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe,
                        RbacViewConversationsPermissions.AssignedToMyTeam
                    ]
                }
            }
        };

        _staffWithAssignedToMePermission = new StaffAccessControlAggregate
        {
            StaffId = 15201,
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            RbacRoles = new List<RbacRole>()
            {
                new ()
                {
                    SleekflowRoleName = "Admin",
                    SleekflowCompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe
                    ]
                }
            }
        };

        _staffWithoutPermission = new StaffAccessControlAggregate
        {
            StaffId = 15201,
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            RbacRoles = new List<RbacRole>()
            {
                new ()
                {
                    SleekflowRoleName = "PoorStaff",
                    SleekflowCompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
                    RbacRolePermissions = []
                }
            }
        };
    }

    [Test]
    public void can_generate_valid_sql_specification_for_staff_with_no_permission()
    {
        // Arrange
        var builder = new RbacViewConversationsSqlBuilder(_staffWithoutPermission, Alias);
        var expectedSql = @$"
            SELECT {Alias}.*
            FROM Conversations  {Alias}
            WHERE 1=0
        ";

        // Act
        var actualSql = builder.Build().ToString();

        // Assert
        // Assert.That(actualSql.Trim(), Is.EqualTo(expectedSql.Trim()));

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(actualSql.Trim());
    }

    [Test]
    public void can_generate_valid_sql_specification_for_staff_with_full_access()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 15201,
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                new ()
                {
                    Id = 1
                },
                new ()
                {
                    Id = 2
                },
                new ()
                {
                    Id = 3
                }
            },
            RbacRoles = new List<RbacRole>()
            {
                new ()
                {
                    SleekflowRoleName = "Admin",
                    SleekflowCompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe,
                        RbacViewConversationsPermissions.AssignedToMyTeam,
                        RbacViewConversationsPermissions.AllAssignedConversations,
                        RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                        RbacViewConversationsPermissions.AllUnassignedConversations
                    ]
                }
            }
        };

        var builder = new RbacViewConversationsSqlBuilder(staff, Alias);
        var expectedSql = @$"
            SELECT {Alias}.*
            FROM Conversations {Alias}
            WHERE {Alias}.CompanyId = '{staff.CompanyId}'
        ";

        // Act
        var actualSql = builder.Build().ToString();

        // Assert
        Assert.That(actualSql.Trim(), Is.EqualTo(expectedSql.Trim()));

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(actualSql.Trim());
    }

    [Test]
    public void can_generate_valid_sql_specification_for_staff_with_assigned_to_me_permission()
    {
        // Arrange
        var builder = new RbacViewConversationsSqlBuilder(_staffWithAssignedToMePermission, Alias);

        // Act
        var actualSql = builder.Build();

        // Assert
        // This test case used to generate the SQL query

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(actualSql.Trim());
    }


    [Test]
    public void can_generate_valid_sql_specification_for_staff_with_assigned_to_me_and_assigned_to_my_team_permission()
    {
        // Arrange
        var builder = new RbacViewConversationsSqlBuilder(_staffWithAssignedToMeAndAssignedToMyTeamPermission, Alias);

        // Act
        var actualSql = builder.Build();

        // Assert
        // This test case used to generate the SQL query

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(actualSql.Trim());
    }


    [Test]
    public void can_generate_valid_sql_specification_for_staff_with_assigned_to_me_and_assigned_to_my_team_and_all_assigned_permission()
    {
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 15201,
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                new ()
                {
                    Id = 1
                },
                new ()
                {
                    Id = 2
                },
                new ()
                {
                    Id = 3
                }
            },
            RbacRoles = new List<RbacRole>()
            {
                new ()
                {
                    SleekflowRoleName = "Admin",
                    SleekflowCompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe,
                        RbacViewConversationsPermissions.AssignedToMyTeam,
                        RbacViewConversationsPermissions.AllAssignedConversations
                    ]
                }
            }
        };


        // Arrange
        var builder = new RbacViewConversationsSqlBuilder(staff, Alias);

        // Act
        var actualSql = builder.Build();

        // Assert
        // This test case used to generate the SQL query

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(actualSql.Trim());
    }

    [Test]
    public void can_generate_valid_sql_specification_for_staff_with_assigned_to_me_and_assigned_to_my_team_and_all_assigned_conversations_all_unassigned_conversations_permission()
    {
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 15201,
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                new ()
                {
                    Id = 1
                },
                new ()
                {
                    Id = 2
                },
                new ()
                {
                    Id = 3
                }
            },
            RbacRoles = new List<RbacRole>()
            {
                new ()
                {
                    SleekflowRoleName = "Admin",
                    SleekflowCompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe,
                        RbacViewConversationsPermissions.AssignedToMyTeam,
                        RbacViewConversationsPermissions.AllAssignedConversations,
                        RbacViewConversationsPermissions.AllUnassignedConversations
                    ]
                }
            }
        };


        // Arrange
        var builder = new RbacViewConversationsSqlBuilder(staff, Alias);

        // Act
        var actualSql = builder.Build();

        // Assert
        // This test case used to generate the SQL query

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(actualSql.Trim());
    }


    [Test]
    public void can_generate_valid_sql_specification_for_staff_with_assigned_to_me_and_all_unassigned_conversations_and_unassigned_conversations_under_my_team_permission()
    {
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 15201,
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                new ()
                {
                    Id = 1
                },
                new ()
                {
                    Id = 2
                },
                new ()
                {
                    Id = 3
                }
            },
            RbacRoles = new List<RbacRole>()
            {
                new ()
                {
                    SleekflowRoleName = "Admin",
                    SleekflowCompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
                    RbacRolePermissions =
                    [
                        RbacViewConversationsPermissions.AssignedToMe,
                        RbacViewConversationsPermissions.AllUnassignedConversations,
                        RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam
                    ]
                }
            }
        };


        // Arrange
        var builder = new RbacViewConversationsSqlBuilder(staff, Alias);

        // Act
        var actualSql = builder.Build();

        // Assert
        // This test case used to generate the SQL query

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(actualSql.Trim());
    }
}