using Sleekflow.Core.Tests.Conversations.Rbac.RbacTestData;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.Conversations.Rbac.ViewConversations;

// reference https://docs.google.com/spreadsheets/d/1CL1c8744GMhn4uup1cdP98jFV3rWzQnE6yL90JvtM8k/edit?gid=1322051058#gid=1322051058
[TestFixture]
public class CanViewUnitTests
{
    private IRbacConversationPermissionManager _rbacConversationPermissionManager;

    private StaffAccessControlAggregate _adminA;
    private StaffAccessControlAggregate _teamAdminA;
    private StaffAccessControlAggregate _staffA;
    private StaffAccessControlAggregate _adminB;
    private StaffAccessControlAggregate _teamAdminB;
    private StaffAccessControlAggregate _staffB;
    private StaffAccessControlAggregate _adminC;
    private StaffAccessControlAggregate _adminInAnotherCompany;
    private StaffAccessControlAggregate _staffC;
    private StaffAccessControlAggregate _staffD;
    private TeamAccessControlAggregate _teamA;
    private TeamAccessControlAggregate _teamB;
    private RbacRole _admin;
    private RbacRole _teamAdmin;
    private RbacRole _staff;
    private string _companyId;
    private string _anotherCompanyId;


    [SetUp]
    public void Setup()
    {
        _rbacConversationPermissionManager = new RbacConversationPermissionManager(new RbacDefaultChannelPermissionManager());

        _companyId = "sleekflow";
        _anotherCompanyId = "another_companyId";

        // Teams
        _teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long>
            {
                1, 2, 3
            }
        };
        _teamB = new TeamAccessControlAggregate
        {
            Id = 2,
            TeamMemberStaffIds = new List<long>
            {
                4, 5, 6
            }
        };

        _admin = new RbacRole
        {
            SleekflowRoleName = "Admin",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllAssignedConversations,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations
            ]
        };

        // Conversation access rules for a team admin:
        // - Assigned to Associated Teams: Must be linked to at least one team in `associatedTeamIds`.
        // - Team Member as Contact Owner: Must have a contact owner from `teamMemberStaffIds`.
        // - Team Member as Collaborator: Must include a collaborator from `teamMemberStaffIds`.
        _teamAdmin = new RbacRole
        {
            SleekflowRoleName = "TeamAdmin",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations
            ]
        };

        _staff = new RbacRole
        {
            SleekflowRoleName = "Staff",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations
            ]
        };

        #region Team A members

        _adminA = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = [_admin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };

        _teamAdminA = new StaffAccessControlAggregate
        {
            StaffId = 2,
            RbacRoles = [_teamAdmin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };


        _staffA = new StaffAccessControlAggregate
        {
            StaffId = 3,
            CompanyId = _companyId,
            RbacRoles = [_staff],
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };

        #endregion

        #region Team B members

        _adminB = new StaffAccessControlAggregate
        {
            StaffId = 4,
            RbacRoles = [_admin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _teamAdminB = new StaffAccessControlAggregate
        {
            StaffId = 5,
            RbacRoles = [_teamAdmin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _staffB = new StaffAccessControlAggregate
        {
            StaffId = 6,
            RbacRoles = [_staff],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };

        #endregion


        // Others
        _adminC = new StaffAccessControlAggregate
        {
            StaffId = 7, RbacRoles = [_admin], CompanyId = _companyId
        };

        _staffC = new StaffAccessControlAggregate
        {
            StaffId = 8, RbacRoles = [_staff], CompanyId = _companyId
        };

        _adminInAnotherCompany = new StaffAccessControlAggregate
        {
            StaffId = 9, RbacRoles = [_admin], CompanyId = _anotherCompanyId
        };

        _staffD = new StaffAccessControlAggregate
        {
            StaffId = 10, RbacRoles = [_staff], CompanyId = _companyId
        };
    }

    #region Conversation is assigned to contact owner only

    [Test]
    public void HasAccess_AccessConversationAssignedToContactOwnerWithoutTeam_ReturnsExpectedResult()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId, AssigneeId = _staffC.StaffId
        };

        // Team A members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminA, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_teamAdminA, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_teamAdminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffB, conversation));

        // Others
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminC, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffC, conversation));
    }

    #endregion

    #region Conversation is assigned to contact owner and team

    [Test]
    public void HasAccess_AccessConversationAssignedToContactOwnerWithTeam_ReturnsExpectedResult()
    {
        var conversation = new Conversation
        {
            Id = "conversationB", CompanyId = _companyId, AssignedTeamId = _teamB.Id, AssigneeId = _staffB.StaffId
        };

        // Team A members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminA, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_teamAdminA, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminB, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminB, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffB, conversation));

        // Others
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminC, conversation));
    }

    [Test]
    public void HasAccess_AccessConversationAssignedToContactOwnerWithDifferentTeam_ReturnsExpectedResult()
    {
        var conversation = new Conversation
        {
            Id = "conversationB", CompanyId = _companyId, AssignedTeamId = _teamA.Id, AssigneeId = _staffB.StaffId
        };

        // Team A members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminA, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminB, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminB, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffB, conversation));

        // Others
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminC, conversation));
    }

    #endregion

    #region Conversation is assigned to team only

    [Test]
    public void HasAccess_AccessConversationAssignedToTeamOnly_ReturnsExpectedResult()
    {
        var conversation = new Conversation
        {
            Id = "conversationA", CompanyId = _companyId, AssignedTeamId = _teamA.Id
        };

        // Team A members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_teamAdminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffB, conversation));

        // Others
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminC, conversation));
    }

    #endregion

    #region Conversation has collaborators

    [Test]
    public void HasAccess_AccessConversationHasCollaborator_ReturnsExpectedResult()
    {
        var collaborator = new AdditionalAssignee
        {
            Id = 1, CompanyId = _companyId, AssigneeId = _staffC.StaffId
        };
        var conversation = new Conversation
        {
            Id = "conversationC",
            CompanyId = _companyId,
            AssigneeId = _staffA.StaffId,
            AdditionalAssignees = new List<AdditionalAssignee>
            {
                collaborator
            }
        };

        // Team A members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_teamAdminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffB, conversation));

        // Others
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminC, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffC, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffD, conversation));
    }

    #endregion

    #region Conversation has mentioned staffs

    [Test]
    public void HasAccess_AccessConversationHasMentionedStaff_ReturnsExpectedResult()
    {
        var conversation = new Conversation
        {
            Id = "conversationC",
            CompanyId = _companyId,
            AssigneeId = _staffA.StaffId,
            Mentions = new List<Mention>
            {
                new ()
                {
                    MentionedStaffId = _staffC.StaffId, CreatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1)
                },
                new ()
                {
                    MentionedStaffId = _staffD.StaffId, CreatedAt = DateTime.UtcNow.AddDays(-2).AddHours(-1)
                }
            }
        };

        // Team A members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_teamAdminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffB, conversation));

        // Others
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminC, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffC, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffD, conversation));
    }

    #endregion

    #region Conversation is assigned to contact owner only who is a team admin

    [Test]
    public void HasAccess_AccessConversationAssignedToContactOwnerOnlyWhoIsTeamAdmin_ReturnsExpectedResult()
    {
        var conversation = new Conversation
        {
            Id = "conversationD", CompanyId = _companyId, AssigneeId = _teamAdminA.StaffId
        };

        // Team A members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminA, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_teamAdminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffB, conversation));

        // Others
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminC, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffC, conversation));
    }

    #endregion

    #region Conversation has collaborator who is a team member

    [Test]
    public void HasAccess_AccessConversationHasCollaboratorWhoIsTeamMember_ReturnsExpectedResult()
    {
        var collaborator = new AdditionalAssignee
        {
            Id = 1, CompanyId = _companyId, AssigneeId = _staffA.StaffId
        };
        var conversation = new Conversation
        {
            Id = "conversationE",
            CompanyId = _companyId,
            AssigneeId = _staffC.StaffId,
            AdditionalAssignees = new List<AdditionalAssignee>
            {
                collaborator
            }
        };

        // Team A members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_teamAdminB, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffB, conversation));

        // Others
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminC, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffC, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffD, conversation));
    }

    #endregion

    #region Conversation assigned to team with multiple collaborators

    [Test]
    public void HasAccess_AccessConversationAssignedToTeamWithMultipleCollaborators_ReturnsExpectedResult()
    {
        var collaborators = new List<AdditionalAssignee>
        {
            new ()
            {
                Id = 1, CompanyId = _companyId, AssigneeId = _staffC.StaffId
            },
            new ()
            {
                Id = 3, CompanyId = _companyId, AssigneeId = _staffB.StaffId
            }
        };
        var conversation = new Conversation
        {
            Id = "conversationF",
            CompanyId = _companyId,
            AssignedTeamId = _teamA.Id,
            AdditionalAssignees = collaborators
        };

        // Team A members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminB, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminB, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffB, conversation));

        // Others
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminC, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffC, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffD, conversation));
    }

    #endregion

    #region Conversation with mixed mentioned staffs and collaborators

    [Test]
    public void HasAccess_AccessConversationWithMixedMentionedStaffsAndCollaborators_ReturnsExpectedResult()
    {
        var collaborator = new AdditionalAssignee
        {
            Id = 1, CompanyId = _companyId, AssigneeId = _staffB.StaffId
        };
        var conversation = new Conversation
        {
            Id = "conversationG",
            CompanyId = _companyId,
            AssigneeId = _staffA.StaffId,
            AdditionalAssignees = new List<AdditionalAssignee>
            {
                collaborator
            },
            Mentions = new List<Mention>
            {
                new ()
                {
                    MentionedStaffId = _staffC.StaffId, CreatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1)
                },
                new ()
                {
                    MentionedStaffId = _staffD.StaffId, CreatedAt = DateTime.UtcNow.AddDays(-2).AddHours(-1)
                }
            }
        };

        // Team A members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminB, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminB, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffB, conversation));

        // Others
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminC, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffC, conversation));
        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_staffD, conversation));
    }

    #endregion

    #region Access attempt from staff in another company

    [Test]
    public void HasAccess_AccessAttemptFromStaffInAnotherCompany_ReturnsFalse()
    {
        var conversation = new Conversation
        {
            Id = "conversationH", CompanyId = _companyId, AssignedTeamId = _teamA.Id
        };

        Assert.IsFalse(_rbacConversationPermissionManager.CanView(_adminInAnotherCompany, conversation));
    }

    #endregion

    #region Conversation is unassigned

    [Test]
    public void HasAccess_AccessConversationWithNullAssignedTeamIdAndAssigneeId_ReturnsExpectedResult()
    {
        var conversation = new Conversation
        {
            Id = "conversationI", CompanyId = _companyId, AssignedTeamId = null, AssigneeId = null
        };

        // All staff should have access to unassigned conversations
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffA, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminB, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_teamAdminB, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffB, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_adminC, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffC, conversation));
        Assert.IsTrue(_rbacConversationPermissionManager.CanView(_staffD, conversation));
    }

    #endregion

    #region Rbac Can View Test cases

    [Test, Order(1), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetNoViewConversationPermissionTestCases))]
    public void staff_with_no_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(2), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAllConversationViewConversationPermissionTestCases))]
    public void Staff_with_all_conversations_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(3), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAssignedToMeViewConversationPermissionTestCases))]
    public void Staff_with_assigned_to_me_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(4), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAssignedToMeAndAssignedToMyTeamViewConversationPermissionTestCases))]
    public void Staff_with_assigned_to_me_and_assigned_to_my_team_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(5), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAssignedToMeAndAllAssignedConversationsViewConversationPermissionTestCases))]
    public void Staff_with_assigned_to_me_and_all_assigned_conversations_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(6), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAssignedToMeAndUnassignedConversationsUnderMyTeamViewConversationPermissionTestCases))]
    public void Staff_with_assigned_to_me_and_unassigned_conversations_under_my_team_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(7), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAssignedToMeAndAllUnassignedConversationsViewConversationPermissionTestCases))]
    public void Staff_with_assigned_to_me_and_all_unassigned_conversations_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(8), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsAndUnassignedConversationsUnderMyTeamViewConversationPermissionTestCases))]
    public void Staff_with_assigned_to_me_and_assigned_to_my_team_and_all_assigned_conversations_and_unassigned_conversations_under_my_team_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(9), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAssignedToMeAndAssignedToMyTeamAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsViewConversationPermissionTestCases))]
    public void Staff_with_assigned_to_me_and_assigned_to_my_team_and_unassigned_conversations_under_my_team_and_all_unassigned_conversations_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(10), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsAndAllUnassignedConversationsViewConversationPermissionTestCases))]
    public void Staff_with_assigned_to_me_and_assigned_to_my_team_and_all_assigned_conversations_and_all_unassigned_conversations_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(11), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAssignedToMeAndAllAssignedConversationsAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsViewPermissionTestCases))]
    public void Staff_with_assigned_to_me_and_all_assigned_conversations_and_unassigned_conversations_under_my_team_and_all_unassigned_conversations_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(12), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsViewPermissionTestCases))]
    public void Staff_with_assigned_to_me_and_assigned_to_my_team_and_all_assigned_conversations_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, Order(13), TestCaseSource(typeof(CanViewTestData), nameof(CanViewTestData.GetAssignedToMeAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsViewPermissionTestCases))]
    public void Staff_with_assigned_to_me_and_unassigned_conversations_under_my_team_and_all_unassigned_conversations_view_conversation_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    #endregion

    #region Rbac Inbox View Test cases

    [Test, TestCaseSource(typeof(InboxViewTestData), nameof(InboxViewTestData.GetAllInboxViewConversationTestCases))]
    public void all_inbox_view_conversations(StaffAccessControlAggregate staff, Conversation conversation, InboxView inboxView, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation, inboxView);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, TestCaseSource(typeof(InboxViewTestData), nameof(InboxViewTestData.GetAssignedToMeInboxViewConversationTestCases))]
    public void assigned_to_me_inbox_view_conversations(StaffAccessControlAggregate staff, Conversation conversation, InboxView inboxView, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation, inboxView);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }


    [Test, TestCaseSource(typeof(InboxViewTestData), nameof(InboxViewTestData.GetCollaborationsInboxViewConversationTestCases))]
    public void collaborations_inbox_view_conversations(StaffAccessControlAggregate staff, Conversation conversation, InboxView inboxView, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation, inboxView);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, TestCaseSource(typeof(InboxViewTestData), nameof(InboxViewTestData.GetMentionsInboxViewConversationTestCases))]
    public void mentions_inbox_view_conversations(
        StaffAccessControlAggregate staff,
        Conversation conversation,
        InboxView inboxView,
        bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation, inboxView);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test, TestCaseSource(typeof(InboxViewTestData), nameof(InboxViewTestData.GetTeamInboxViewConversationTestCases))]
    public void team_inbox_view_conversations(StaffAccessControlAggregate staff, Conversation conversation, InboxView inboxView, bool expectedResult)
    {
        // Act
        var result = _rbacConversationPermissionManager.CanView(staff, conversation, inboxView);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    #endregion
}