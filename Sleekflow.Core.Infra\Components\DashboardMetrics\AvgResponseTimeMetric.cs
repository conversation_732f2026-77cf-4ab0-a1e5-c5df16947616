﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class AvgResponseTimeMetric : IDashboardMetric
{
    private readonly Output<string>? _resourceId;
    private readonly Output<string>? _resourceName;

    public AvgResponseTimeMetric(Output<string>? resourceId, Output<string>? resourceName)
    {
        _resourceId = resourceId;
        _resourceName = resourceName;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs =
                    new[]
                    {
                        new Dictionary<string, object>
                        {
                            {
                                "name", "options"
                            },
                            {
                                "value", new Dictionary<string, object>
                                {
                                    {
                                        "chart", new Dictionary<string, object>
                                        {
                                            {
                                                "metrics", new[]
                                                {
                                                    new Dictionary<string, object>
                                                    {
                                                        {
                                                            "aggregationType", 4
                                                        },
                                                        {
                                                            "metricVisualization", new Dictionary<string, object>
                                                            {
                                                                {
                                                                    "displayName", "Response Time"
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "name", "HttpResponseTime"
                                                        },
                                                        {
                                                            "namespace", "microsoft.web/sites"
                                                        },
                                                        {
                                                            "resourceMetadata", new Dictionary<string, object>
                                                            {
                                                                {
                                                                    "id", _resourceId!
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            {
                                                "timespan", new Dictionary<string, object>
                                                {
                                                    {
                                                        "grain", 2
                                                    },
                                                    {
                                                        "relative", new Dictionary<string, object>
                                                        {
                                                            {
                                                                "duration", 86400000
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "showUTCTime", false
                                                    }
                                                }
                                            },
                                            {
                                                "title", _resourceName?.Apply(a => $"Avg Response Time for {a}")
                                            },
                                            {
                                                "titleKind", 1
                                            },
                                            {
                                                "visualization", new Dictionary<string, object>
                                                {
                                                    {
                                                        "axisVisualization", new Dictionary<string, object>
                                                        {
                                                            {
                                                                "x", new Dictionary<string, object>
                                                                {
                                                                    {
                                                                        "axisType", 2
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "y", new Dictionary<string, object>
                                                                {
                                                                    {
                                                                        "axisType", 1
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "chartType", 2
                                                    },
                                                    {
                                                        "legendVisualization", new Dictionary<string, object>
                                                        {
                                                            {
                                                                "hideSubtitle", false
                                                            },
                                                            {
                                                                "isVisible", true
                                                            },
                                                            {
                                                                "position", 2
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>
                        {
                            {
                                "name", "sharedTimeRange"
                            },
                            {
                                "isOptional", true
                            }
                        }
                    },
                Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                Settings =
                {
                    new Dictionary<string, object>()
                    {
                        {
                            "content", new Dictionary<string, object>
                            {
                                {
                                    "options", new Dictionary<string, object>
                                    {
                                        {
                                            "chart", new Dictionary<string, object>
                                            {
                                                {
                                                    "metrics", new[]
                                                    {
                                                        new Dictionary<string, object>
                                                        {
                                                            {
                                                                "aggregationType", 4
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>
                                                                {
                                                                    {
                                                                        "displayName", "Response Time"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "HttpResponseTime"
                                                            },
                                                            {
                                                                "namespace", "microsoft.web/sites"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    "title", _resourceName?.Apply(a => $"Avg Response Time for {a}")
                                                },
                                                {
                                                    "titleKind", 1
                                                },
                                                {
                                                    "visualization", new Dictionary<string, object>
                                                    {
                                                        {
                                                            "axisVisualization", new Dictionary<string, object>
                                                            {
                                                                {
                                                                    "x", new Dictionary<string, object>
                                                                    {
                                                                        {
                                                                            "axisType", 2
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    "y", new Dictionary<string, object>
                                                                    {
                                                                        {
                                                                            "axisType", 1
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "chartType", 2
                                                        },
                                                        {
                                                            "disablePinning", true
                                                        },
                                                        {
                                                            "legendVisualization", new Dictionary<string, object>
                                                            {
                                                                {
                                                                    "hideSubtitle", false
                                                                },
                                                                {
                                                                    "isVisible", true
                                                                },
                                                                {
                                                                    "position", 2
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}