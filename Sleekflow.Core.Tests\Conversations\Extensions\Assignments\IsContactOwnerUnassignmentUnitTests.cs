using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Extensions.Assignments;

[TestFixture]
public class IsContactOwnerUnassignmentUnitTests
{
    [Test]
    public void IsContactOwnerUnassignment_ShouldReturnFalse_WhenConversationIsNull()
    {
        Conversation conversation = null;
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 1 };

        bool result = conversation.IsContactOwnerUnassignment(newContactOwner);

        Assert.IsFalse(result);
    }

    [Test]
    public void IsContactOwnerUnassignment_ShouldReturnTrue_WhenNewContactOwnerIsNull()
    {
        var conversation = new Conversation { AssigneeId = 1 };
        StaffAccessControlAggregate newContactOwner = null;

        bool result = conversation.IsContactOwnerUnassignment(newContactOwner);

        Assert.IsTrue(result);
    }

    [Test]
    public void IsContactOwnerUnassignment_ShouldReturnFalse_WhenNewContactOwnerIsNotNull()
    {
        var conversation = new Conversation { AssigneeId = 1 };
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 2 };

        bool result = conversation.IsContactOwnerUnassignment(newContactOwner);

        Assert.IsFalse(result);
    }
}