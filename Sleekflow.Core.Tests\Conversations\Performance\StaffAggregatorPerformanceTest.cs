using System.Diagnostics;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.Core.Tests.Constants;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationQueryables;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.TenantHubDomain.Services;
using RbacRole = Travis_backend.ConversationDomain.ViewModels.RbacRole;

namespace Sleekflow.Core.Tests.Conversations.Performance;

public class StaffAggregatorPerformanceTest
{
    private ApplicationDbContext _appDbContext;

    private RbacRole _admin;
    private RbacRole _teamAdmin;
    private RbacRole _staff;
    private List<StaffAccessControlAggregate> _staffAccessControlAggregates;
    private IRbacConversationPermissionManager _rbacConversationPermissionManager;
    private IAccessControlAggregationService _accessControlAggregationService;
    private string _companyId;
    private long _teamId;
    private Mock<IRbacService> _mockRbacService;
    private Mock<ILogger<AccessControlAggregationService>> _mockILoggerService;
    private Mock<AccessControlAggregationService> _mockAccessControlAggregationService;
    private Mock<IWebHostEnvironment> _mockWebHostEnvironment;
    private Mock<IDbContextService> _mockDbContextService;
    private Mock<IMentionQueryableResolver> _mockMentionQueryableResolver;

    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseSqlServer(DbConnectionStrings.Dev)
            .Options;
        _appDbContext = new ApplicationDbContext(options);
        _rbacConversationPermissionManager =
            new RbacConversationPermissionManager(new RbacDefaultChannelPermissionManager());
        _staffAccessControlAggregates = [];
        _mockRbacService = new Mock<IRbacService>();
        _mockAccessControlAggregationService = new Mock<AccessControlAggregationService>();
        _mockWebHostEnvironment = new Mock<IWebHostEnvironment>();
        var serviceProvider = new Mock<IServiceProvider>();
        serviceProvider.Setup(sp => sp.GetService(typeof(IRbacService))).Returns(_mockRbacService.Object);
        // Use the service provider in your test
        _mockRbacService.Setup(x => x.IsRbacEnabled(It.IsAny<string>())).ReturnsAsync(true);
        _mockMentionQueryableResolver = new Mock<IMentionQueryableResolver>();

        var mockRbacRoleResult = new Dictionary<long, List<RbacRole>>
        {
            {
                1, new List<RbacRole>
                {
                    new RbacRole
                    {
                        SleekflowRoleId = "role1", SleekflowRoleName = "Role 1"
                    }
                }
            },
            {
                2, new List<RbacRole>
                {
                    new RbacRole
                    {
                        SleekflowRoleId = "role2", SleekflowRoleName = "Role 2"
                    }
                }
            }
        };

        _mockAccessControlAggregationService
            .Setup(x => x.GetRolesByStaffIdsAsync(It.IsAny<List<long>>(), It.IsAny<string>()))
            .ReturnsAsync(mockRbacRoleResult);

        _accessControlAggregationService = new AccessControlAggregationService(
            _mockDbContextService.Object,
            serviceProvider.Object,
            _mockILoggerService.Object,
            _mockMentionQueryableResolver.Object
            );
        var companyWithMostStaff = _appDbContext.UserRoleStaffs
            .GroupBy(x => x.CompanyId)
            .OrderByDescending(g => g.Count())
            .Select(
                g => new
                {
                    CompanyId = g.Key, Staffs = g.ToList()
                })
            .FirstOrDefault();

        if (companyWithMostStaff == null)
        {
            throw new Exception("No staffs found in this env");
        }

        _teamId = _appDbContext.CompanyStaffTeams
            .AsEnumerable()
            .Where(x => x.DefaultChannels.Count != 0)
            .Select(x => x.Id)
            .FirstOrDefault();

        _companyId = companyWithMostStaff.CompanyId;

        _admin = new RbacRole
        {
            SleekflowRoleName = "Admin",
            SleekflowCompanyId = _companyId,
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllAssignedConversations,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations,
                RbacAssignConversationsPermissions.AssignToAnyUser
            ]
        };

        _teamAdmin = new RbacRole
        {
            SleekflowRoleName = "TeamAdmin",
            SleekflowCompanyId = _companyId,
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations
            ]
        };

        _staff = new RbacRole
        {
            SleekflowRoleName = "Staff",
            SleekflowCompanyId = _companyId,
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations
            ]
        };

        _staffAccessControlAggregates = companyWithMostStaff.Staffs.Select(
            x =>
            {
                return x.RoleType switch
                {
                    StaffUserRole.Staff => new StaffAccessControlAggregate()
                    {
                        RbacRoles = [_staff], CompanyId = x.CompanyId, StaffId = x.Id
                    },
                    StaffUserRole.TeamAdmin => new StaffAccessControlAggregate()
                    {
                        RbacRoles = [_teamAdmin], CompanyId = x.CompanyId, StaffId = x.Id
                    },
                    StaffUserRole.Admin => new StaffAccessControlAggregate()
                    {
                        RbacRoles = [_admin], CompanyId = x.CompanyId
                    },
                    _ => new StaffAccessControlAggregate()
                    {
                        RbacRoles = [_staff], CompanyId = x.CompanyId, StaffId = x.Id
                    }
                };
            }).ToList();
    }

    // [Test]
    public async Task Performance_Measure_StaffAggregator()
    {
        var conversation = new Conversation
        {
            Id = "conversationC",
            CompanyId = _companyId,
            AssigneeId = _staffAccessControlAggregates.FirstOrDefault()?.StaffId,
            AssignedTeamId = _teamId
        };

        var stopwatch = new Stopwatch();
        stopwatch.Start();

        await _accessControlAggregationService.GetStaffAccessControlAggregatesAsync(
            _staffAccessControlAggregates.Select(x => x.StaffId).ToList(),
            companyId: _companyId);

        stopwatch.Stop();
        Console.WriteLine(
            $"Time taken: {stopwatch.ElapsedMilliseconds} ms for ${_staffAccessControlAggregates.Count} staffs");

        stopwatch = new Stopwatch();
        stopwatch.Start();
        foreach (var staffAccessControlAggregate in _staffAccessControlAggregates)
        {
            _rbacConversationPermissionManager
                .CanView(staffAccessControlAggregate, conversation);
        }

        stopwatch.Stop();
        Console.WriteLine(
            $"Time taken: {stopwatch.ElapsedMilliseconds} ms for ${_staffAccessControlAggregates.Count} staffs");
    }
}