using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Travis_backend.AnalyticsDomain.Models;

[Table("mrr_by_company_by_day")]
public class MrrByCompanyByDay
{
    [Column("id")]
    public string Id { get; set; }

    [Column("date")]
    public DateOnly Date { get; set; }

    [Column("sleekflow_company_id")]
    public string CompanyId { get; set; }

    [Column("subscription_plan_id")]
    public string SubscriptionPlanId { get; set; }

    [Column("subscription_monthly_recurring_revenue_usd")]
    public decimal SubscriptionMonthlyRecurringRevenueUsd { get; set; }

    [Column("add_on_monthly_recurring_revenue_usd")]
    public decimal AddOnMonthlyRecurringRevenueUsd { get; set; }

    [Column("monthly_recurring_revenue_usd")]
    public decimal MonthlyRecurringRevenueUsd { get; set; }

    [Column("to_be_deleted")]
    public byte ToBeDeleted { get; set; }
}