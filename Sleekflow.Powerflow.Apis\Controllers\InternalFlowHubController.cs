using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Filters;

namespace Sleekflow.Powerflow.Apis.Controllers;

[TypeFilter(typeof(FlowHubExceptionFilter))]
[Route("/internal/flow-hub/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
public class InternalFlowHubController : InternalControllerBase
{
    private readonly IFlowHubConfigsApi _flowHubConfigsApi;
    private readonly IFlowHubService _flowHubService;
    private readonly IWorkflowsApi _workflowsApi;
    private readonly IExecutionsApi _executionsApi;

    public InternalFlowHubController(
        UserManager<ApplicationUser> userManager,
        IFlowHubConfigsApi flowHubConfigsApi,
        IFlowHubService flowHubService,
        IWorkflowsApi workflowsApi,
        IExecutionsApi executionsApi)
        : base(userManager)
    {
        _flowHubConfigsApi = flowHubConfigsApi;
        _flowHubService = flowHubService;
        _workflowsApi = workflowsApi;
        _executionsApi = executionsApi;
    }

    public record GetFlowHubConfigRequest(string SleekflowCompanyId);

    [HttpPost]
    public async Task<ActionResult<GetFlowHubConfigOutputOutput>> GetFlowHubConfig(
        [FromBody]
        GetFlowHubConfigRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        var getFlowHubConfigOutputOutput = await _flowHubConfigsApi.FlowHubConfigsGetFlowHubConfigPostAsync(
            getFlowHubConfigInput: new GetFlowHubConfigInput(
                request.SleekflowCompanyId));

        return Ok(getFlowHubConfigOutputOutput);
    }

    public record CountWorkFlowsRequest(string SleekflowCompanyId, string WorkFlowType);

    [HttpPost]
    public async Task<ActionResult<CountWorkflowsOutputOutput>> CountWorkFlows(
        [FromBody]
        CountWorkFlowsRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        var countWorkflowsOutputOutput = await _workflowsApi.WorkflowsCountWorkflowsPostAsync(
            countWorkflowsInput: new CountWorkflowsInput(request.SleekflowCompanyId, request.WorkFlowType));

        return Ok(countWorkflowsOutputOutput);
    }

    public record GetWorkflowExecutionStatisticsRequest(
        string SleekflowCompanyId,
        DateTime FromDateTime,
        DateTime ToDateTime);

    [HttpPost]
    public async Task<ActionResult<GetMonetizedWorkflowExecutionStatisticsOutputOutput>> GetWorkflowExecutionStatistics(
        [FromBody]
        GetWorkflowExecutionStatisticsRequest request)
    {
        var normalWorkflowExecutionStatistics =
            await _executionsApi.ExecutionsGetMonetizedWorkflowExecutionStatisticsPostAsync(
                getMonetizedWorkflowExecutionStatisticsInput: new GetMonetizedWorkflowExecutionStatisticsInput(
                    request.SleekflowCompanyId,
                    new GetWorkflowExecutionStatisticsInputFilters(
                        fromDateTime: new DateTimeOffset(request.FromDateTime, TimeSpan.Zero),
                        toDateTime: new DateTimeOffset(request.ToDateTime, TimeSpan.Zero),
                        workflowType: "normal")));

        var aiWorkflowExecutionStatistics =
            await _executionsApi.ExecutionsGetMonetizedWorkflowExecutionStatisticsPostAsync(
                getMonetizedWorkflowExecutionStatisticsInput: new GetMonetizedWorkflowExecutionStatisticsInput(
                    request.SleekflowCompanyId,
                    new GetWorkflowExecutionStatisticsInputFilters(
                        fromDateTime: new DateTimeOffset(request.FromDateTime, TimeSpan.Zero),
                        toDateTime: new DateTimeOffset(request.ToDateTime, TimeSpan.Zero),
                        workflowType: "ai_workflow")));

        var output = new GetMonetizedWorkflowExecutionStatisticsOutputOutput(
            data: new GetMonetizedWorkflowExecutionStatisticsOutput(
                normalWorkflowExecutionStatistics.Data.NumOfStartedWorkflows + aiWorkflowExecutionStatistics.Data.NumOfStartedWorkflows,
                normalWorkflowExecutionStatistics.Data.NumOfCompleteWorkflows + aiWorkflowExecutionStatistics.Data.NumOfCompleteWorkflows,
                normalWorkflowExecutionStatistics.Data.NumOfCancelledWorkflows + aiWorkflowExecutionStatistics.Data.NumOfCancelledWorkflows,
                normalWorkflowExecutionStatistics.Data.NumOfBlockedWorkflows + aiWorkflowExecutionStatistics.Data.NumOfBlockedWorkflows,
                normalWorkflowExecutionStatistics.Data.NumOfFailedWorkflows + aiWorkflowExecutionStatistics.Data.NumOfFailedWorkflows,
                normalWorkflowExecutionStatistics.Data.NumOfRestrictedWorkflows + aiWorkflowExecutionStatistics.Data.NumOfRestrictedWorkflows
            )
        );

        return Ok(output);
    }

    public record ToggleFlowHubConfigUsageLimitRequest(bool IsUsageLimitEnabled, string SleekflowCompanyId);

    [HttpPost]
    public async Task<ActionResult<ToggleFlowHubUsageLimitOutputOutput>> ToggleFlowHubConfigUsageLimit(
        [FromBody]
        ToggleFlowHubConfigUsageLimitRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user is null)
        {
            return Unauthorized();
        }

        var toggleWorkflowEnrolmentOutputOutput =
            await _flowHubConfigsApi.FlowHubConfigsToggleFlowHubUsageLimitPostAsync(
                toggleFlowHubUsageLimitInput: new ToggleFlowHubUsageLimitInput(
                    request.SleekflowCompanyId,
                    request.IsUsageLimitEnabled));

        return Ok(toggleWorkflowEnrolmentOutputOutput);
    }

    public record GetUpdateFlowHubConfig(string SleekflowCompanyId);

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdateFlowHubConfig(
        [FromBody]
        GetUpdateFlowHubConfig request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user is null)
        {
            return Unauthorized();
        }

        await _flowHubService.UpdateFlowHubConfigAsync(
            request.SleekflowCompanyId,
            null
        );

        return Ok(
            new ResponseViewModel()
            {
                message = "success"
            });
    }

    public record UpdateFlowHubConfigUsageLimitOffsetRequest(
        string SleekflowCompanyId,
        int MaximumNumOfActiveWorkflowsOffset,
        int MaximumNumOfNodesPerWorkflowOffset,
        int MaximumNumOfMonthlyWorkflowExecutionsOffset);

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdateFlowHubConfigUsageLimitOffset(
        [FromBody]
        UpdateFlowHubConfigUsageLimitOffsetRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user is null)
        {
            return Unauthorized();
        }

        try
        {
            var getFlowHubConfigOutputOutput = await _flowHubConfigsApi.FlowHubConfigsGetFlowHubConfigPostAsync(
                getFlowHubConfigInput: new GetFlowHubConfigInput(
                    request.SleekflowCompanyId));
            var flowHubConfig = getFlowHubConfigOutputOutput.Data.FlowHubConfig;
            var usageLimit = flowHubConfig.UsageLimit;

            if (usageLimit.MaximumNumOfActiveWorkflows + request.MaximumNumOfActiveWorkflowsOffset < 0 ||
                usageLimit.MaximumNumOfActiveWorkflows + request.MaximumNumOfActiveWorkflowsOffset > 100)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message =
                            "Invalid usage limit offset, total maximum number of active workflows must be between 0 and 100. " +
                            $"Company Id: {request.SleekflowCompanyId}"
                    });
            }

            if (usageLimit.MaximumNumOfNodesPerWorkflow + request.MaximumNumOfNodesPerWorkflowOffset < 0 ||
                usageLimit.MaximumNumOfNodesPerWorkflow + request.MaximumNumOfNodesPerWorkflowOffset > 500)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message =
                            "Invalid usage limit offset, total maximum number of nodes per workflow must be between 0 and 500. " +
                            $"Company Id: {request.SleekflowCompanyId}"
                    });
            }

            if (usageLimit.MaximumNumOfMonthlyWorkflowExecutions + request.MaximumNumOfMonthlyWorkflowExecutionsOffset <
                0)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message =
                            "Invalid usage limit offset, total maximum number of monthly workflow executions must be greater than or equal to 0. " +
                            $"Company Id: {request.SleekflowCompanyId}"
                    });
            }

            await _flowHubConfigsApi.FlowHubConfigsUpdateFlowHubConfigPostAsync(
                updateFlowHubConfigInput: new UpdateFlowHubConfigInput(
                    request.SleekflowCompanyId,
                    null,
                    null,
                    flowHubConfig.UsageLimit,
                    new UsageLimitOffset(
                        request.MaximumNumOfActiveWorkflowsOffset,
                        request.MaximumNumOfNodesPerWorkflowOffset,
                        request.MaximumNumOfMonthlyWorkflowExecutionsOffset)));
            return Ok(
                new ResponseViewModel
                {
                    message =
                        $"Flow builder usage limit offset updated. CompanyId: {request.SleekflowCompanyId}"
                });
        }
        catch (Exception e)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Failed to update flow builder usage limit offset. " +
                              $"CompanyId: {request.SleekflowCompanyId}. " +
                              $"Error: {e.Message}"
                });
        }
    }

    // toggle free trial
    public record ToggleFreeTrialRequest(string SleekflowCompanyId, bool IsFreeTrialEnabled, DateTimeOffset StartDate, DateTimeOffset EndDate, string SleekflowStaffId);

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> ToggleFreeTrial(
        [FromBody]
        ToggleFreeTrialRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user is null)
        {
            return Unauthorized();
        }

        try
        {
            await _flowHubService.ToggleFreeTrialAsync(
                request.SleekflowCompanyId,
                request.IsFreeTrialEnabled,
                request.StartDate,
                request.EndDate,
                user.Id);

            return Ok(new ResponseViewModel { message = "success" });
        }
        catch (Exception e)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Failed to toggle free trial. " +
                              $"CompanyId: {request.SleekflowCompanyId}. " +
                              $"Error: {e.Message}"
                });
        }
    }
}