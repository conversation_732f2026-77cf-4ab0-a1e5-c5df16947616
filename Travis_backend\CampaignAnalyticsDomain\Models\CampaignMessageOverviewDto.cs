﻿using System;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.CampaignAnalyticsDomain.Models;

public class CampaignMessageOverviewDto
{
    public long MessageId { get; set; }

    public DateTime CreatedAt { get; set; }

    public string ChannelStatusMessage { get; set; }

    public MessageStatus MessageStatus { get; set; }

    public string UserProfileId { get; set; }

    public string ConversationId { get; set; }

    public string FirstName { get; set; }

    public string LastName { get; set; }

    public string FullName { get; set; }

    public string PhoneNumber { get; set; }

    public CampaignMessageOverviewDto(
        long messageId,
        DateTime createdAt,
        string channelStatusMessage,
        MessageStatus messageStatus,
        string userProfileId,
        string conversationId,
        string firstName,
        string lastName,
        string fullName,
        string phoneNumber)
    {
        MessageId = messageId;
        CreatedAt = createdAt;
        ChannelStatusMessage = channelStatusMessage;
        MessageStatus = messageStatus;
        UserProfileId = userProfileId;
        ConversationId = conversationId;
        FirstName = firstName;
        LastName = lastName;
        FullName = fullName;
        PhoneNumber = phoneNumber;
    }
}