﻿using System;
using Travis_backend.CampaignAnalyticsDomain.Models;
using Travis_backend.CampaignAnalyticsDomain.Utils;

namespace Travis_backend.CampaignAnalyticsDomain.ViewModels;

public class CampaignMessageOverviewViewModel
{
    public long MessageId { get; set; }

    public DateTime CreatedAt { get; set; }

    public string FailedReason { get; set; }

    public string Status { get; set; }

    public RecipientOverviewViewModel RecipientOverview { get; set; }

    public CampaignMessageOverviewViewModel(
        long messageId,
        DateTime createdAt,
        string failedReason,
        string status,
        RecipientOverviewViewModel recipientOverview)
    {
        MessageId = messageId;
        CreatedAt = createdAt;
        FailedReason = failedReason;
        Status = status;
        RecipientOverview = recipientOverview;
    }

    public CampaignMessageOverviewViewModel(CampaignMessageOverviewDto campaignMessageOverviewDto)
    {
        MessageId = campaignMessageOverviewDto.MessageId;
        CreatedAt = campaignMessageOverviewDto.CreatedAt;
        FailedReason = campaignMessageOverviewDto.ChannelStatusMessage;
        Status = CampaignMessageStatusMapper.Map(campaignMessageOverviewDto.MessageStatus);
        RecipientOverview = new RecipientOverviewViewModel(
            campaignMessageOverviewDto.UserProfileId,
            campaignMessageOverviewDto.ConversationId,
            campaignMessageOverviewDto.FirstName,
            campaignMessageOverviewDto.LastName,
            campaignMessageOverviewDto.FullName,
            campaignMessageOverviewDto.PhoneNumber);
    }
}