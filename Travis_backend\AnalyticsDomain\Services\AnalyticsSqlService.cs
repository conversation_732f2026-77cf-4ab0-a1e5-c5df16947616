using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.Utils;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Services;

namespace Travis_backend.AnalyticsDomain.Services;

public interface IAnalyticsSqlService
{
    Task<List<AnalyticsRecord>> GetDataForCompanyAsync(
        string companyId,
        string staffId,
        long? teamId,
        CompanyTeam team,
        DateTime fromDate,
        DateTime toDate,
        string timeZoneId,
        List<Condition> conditions,
        string searchConditionHash,
        List<UserProfile> conditionalUserProfiles,
        List<string> conditionalConversationIds,
        Func<int, ValueTask> onComplete = null);

    Task<List<UserProfile>> GetConditionalUserProfilesAsync(
        string companyId,
        string staffId = null,
        long? teamId = null,
        List<Condition> conditions = null,
        Func<int, ValueTask> onComplete = null);
}

public class AnalyticsSqlService : IAnalyticsSqlService
{
    private readonly ILogger _logger;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IDbContextService _dbContextService;
    private readonly IUserProfileSqlService _userProfileSqlService;
    private readonly IConversationSqlService _conversationSqlService;
    private readonly IConversationMessageSqlService _conversationMessageSqlService;

    public AnalyticsSqlService(
        ICacheManagerService cacheManagerService,
        IDbContextService dbContextService,
        ILogger<AnalyticsSqlService> logger,
        IUserProfileSqlService userProfileSqlService,
        IConversationSqlService conversationSqlService,
        IConversationMessageSqlService conversationMessageSqlService)
    {
        _logger = logger;
        _cacheManagerService = cacheManagerService;
        _dbContextService = dbContextService;
        _userProfileSqlService = userProfileSqlService;
        _conversationSqlService = conversationSqlService;
        _conversationMessageSqlService = conversationMessageSqlService;
    }

    public async Task<List<AnalyticsRecord>> GetDataForCompanyAsync(
        string companyId,
        string staffId,
        long? teamId,
        CompanyTeam team,
        DateTime fromDate,
        DateTime toDate,
        string timeZoneId,
        List<Condition> conditions,
        string searchConditionHash,
        List<UserProfile> conditionalUserProfiles,
        List<string> conditionalConversationIds,
        Func<int, ValueTask> onComplete = null)
    {
        var completed = 0;
        var result = new List<AnalyticsRecord>();

        // Can increase the limit once the performance is improved
        for (var date = fromDate.Date; date <= toDate.Date; date = date.AddDays(1))
        {
            try
            {
                _logger.LogInformation(
                    "Retrieving analytics data for company: {AnalyticsFromDate} {CompanyId}, staffId {StaffId}, teamId: {TeamId}",
                    fromDate.ToString("d"),
                    companyId,
                    staffId,
                    teamId);
                var fromWithTimeZone = date.ConvertSpecificTimeZoneDateTimeToUtcDateTime(timeZoneId);
                var toWithTimeZone = date.AddDays(1).ConvertSpecificTimeZoneDateTimeToUtcDateTime(timeZoneId);

                var recordKey =
                    $"v5_AnalyticsRecord_{companyId}_{staffId}_{teamId}_{fromWithTimeZone}_{toWithTimeZone}_{searchConditionHash}";
                // var recordData = await _cacheManagerService.GetCacheWithConstantKeyAsync(recordKey);
                // if (!string.IsNullOrEmpty(recordData))
                // {
                //     result.Add(JsonConvert.DeserializeObject<AnalyticsRecord>(recordData));
                //     continue;
                // }

                // Check if the staffId and teamId are not null or empty
                if ((string.IsNullOrEmpty(staffId) && !teamId.HasValue)
                    || (teamId.HasValue && string.IsNullOrEmpty(staffId))
                    || !string.IsNullOrEmpty(staffId))
                {
                    var data = await GetAnalyticsDataAsync(
                        companyId,
                        timeZoneId,
                        fromWithTimeZone,
                        toWithTimeZone,
                        staffId ?? null,
                        conditionalUserProfiles?
                            .Where(
                                x =>
                                    x.CreatedAt >= fromWithTimeZone
                                    && x.CreatedAt <= toWithTimeZone)
                            .Select(x => x.Id)
                            .ToList(),
                        conditionalConversationIds);

                    if (data is not null)
                    {
                        var record = new AnalyticsRecord
                        {
                            CompanyId = companyId,
                            TeamId = team?.Id,
                            StaffId = staffId,
                            ConditionsHash = searchConditionHash,
                            DateTime = date,
                            Data = data
                        };

                        if (toWithTimeZone.Date < DateTime.UtcNow.Date)
                        {
                            // await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                            //     recordKey,
                            //     JsonConvert.SerializeObject(record),
                            //     TimeSpan.FromDays(30));
                        }

                        result.Add(record);
                    }
                    else
                    {
                        _logger.LogWarning(
                            "No analytics record found for: {CompanyId} {FromWithTimeZone} {ToWithTimeZone} {Conditions}",
                            companyId,
                            fromWithTimeZone,
                            toWithTimeZone,
                            JsonConvert.SerializeObject(conditions));
                    }
                }

                if (onComplete == null)
                {
                    continue;
                }

                completed++;
                await onComplete(completed);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Analytic {MethodName}] error: {ExceptionMessage}",
                    nameof(GetDataForCompanyAsync),
                    ex.Message);

                return result;
            }
        }

        return result;
    }

    public async Task<List<UserProfile>> GetConditionalUserProfilesAsync(
        string companyId,
        string staffId = null,
        long? teamId = null,
        List<Condition> conditions = null,
        Func<int, ValueTask> onComplete = null)
    {
        var dbContext = _dbContextService.GetDbContext();
        long? staffIdLong = null;
        var (channelList, channelIdList) = ExtractChannelsAndChannelIds(conditions);

        if (string.IsNullOrEmpty(staffId))
        {
            staffIdLong = dbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyId && x.IdentityId == staffId).Select(
                    x => new
                    {
                        x.Id
                    }).FirstOrDefault()?.Id;
        }

        List<UserProfile> userProfiles;
        var allContacts = new List<UserProfile>();
        IQueryable<long> teamMembersQueryable = null;

        if (teamId.HasValue && string.IsNullOrEmpty(staffId))
        {
            teamMembersQueryable = dbContext.CompanyTeamMembers
                .Where(t => t.CompanyTeamId == teamId.Value)
                .Select(t => t.StaffId);
        }

        if (conditions != null)
        {
            var (userProfileResult, count, query) = await _userProfileSqlService.GetUserProfilesAsync(
                companyId,
                conditions,
                null,
                null,
                OrderCondition.CreatedAt,
                OrderCondition.DESC,
                staffIdLong,
                channelList,
                channelIdList,
                StaffUserRole.Admin,
                getUserProfileIdOnly: true);

            allContacts.AddRange(userProfileResult);

            if (teamId.HasValue && string.IsNullOrEmpty(staffId) && teamMembersQueryable is not null)
            {
                var userProfileIdsAssignedToTeam = await dbContext.Conversations
                    .Where(
                        conversation =>
                            conversation.CompanyId == companyId
                            && (conversation.AssignedTeamId == teamId.Value
                                || teamMembersQueryable.Contains(conversation.AssigneeId.Value)))
                    .Select(conversation => conversation.UserProfileId)
                    .ToListAsync();

                allContacts = allContacts
                    .Where(profile => userProfileIdsAssignedToTeam.Contains(profile.Id))
                    .ToList();
            }

            if (!string.IsNullOrEmpty(staffId))
            {
                if (staffId == "unassigned")
                {
                    var companyConversationDtos = await dbContext.Conversations
                        .Where(conversation => conversation.CompanyId == companyId)
                        .Select(
                            conversation => new
                            {
                                conversation.UserProfileId, conversation.AssigneeId
                            })
                        .ToListAsync();

                    allContacts = allContacts
                        .Where(
                            profile =>
                                companyConversationDtos
                                    .Where(conversation => !conversation.AssigneeId.HasValue)
                                    .Select(conversation => conversation!.UserProfileId)
                                    .Contains(profile.Id) ||
                                companyConversationDtos
                                    .All(conversation => !conversation.UserProfileId.Equals(profile.Id)))
                        .ToList();
                }
                else
                {
                    // Update to raw sql
                    var userProfileIdsAssignedOrCollaborateToStaff = await dbContext.Conversations
                        .Where(
                            conversation =>
                                conversation.CompanyId == companyId
                                && (conversation.Assignee.IdentityId == staffId
                                    || conversation.AdditionalAssignees.Any(
                                        collaborator =>
                                            collaborator.Assignee.IdentityId == staffId &&
                                            collaborator.CompanyId == companyId)))
                        .Select(conversation => conversation.UserProfileId)
                        .ToListAsync();

                    allContacts = allContacts
                        .Where(
                            profile =>
                                userProfileIdsAssignedOrCollaborateToStaff.Contains(profile.Id))
                        .ToList();
                }
            }

            userProfiles = allContacts;
        }
        else
        {
            var userProfilesQ = dbContext.UserProfiles
                .Where(
                    profile =>
                        profile.CompanyId == companyId
                        && profile.ActiveStatus == ActiveStatus.Active);

            if (teamId.HasValue && string.IsNullOrEmpty(staffId) && teamMembersQueryable is not null)
            {
                userProfilesQ = userProfilesQ
                    .Where(
                        profile =>
                            profile.Conversation.AssignedTeamId == teamId.Value ||
                            teamMembersQueryable.Contains(profile.Conversation.AssigneeId.Value));
            }

            if (!string.IsNullOrEmpty(staffId))
            {
                if (staffId == "unassigned")
                {
                    userProfilesQ = userProfilesQ
                        .Where(
                            profile =>
                                !profile.Conversation.AssigneeId.HasValue ||
                                !dbContext.Conversations.Any(
                                    conversation =>
                                        conversation.CompanyId == companyId
                                        && conversation.UserProfileId.Equals(profile.Id)));
                }
                else
                {
                    userProfilesQ = userProfilesQ
                        .Where(
                            profile =>
                                profile.Conversation.Assignee.IdentityId == staffId ||
                                profile.Conversation.AdditionalAssignees
                                    .Any(
                                        collaborator =>
                                            collaborator.Assignee.IdentityId == staffId));
                }
            }

            userProfiles = await userProfilesQ.ToListAsync();
        }

        return userProfiles;
    }

    private async Task<AnalyticsData> GetAnalyticsDataAsync(
        string companyId,
        string timeZoneId,
        DateTime fromWithTimeZone,
        DateTime toWithTimeZone,
        string staffUserId = null,
        List<string> newUserProfileIds = null,
        List<string> conditionalConversationIds = null)
    {
        var conversationMessagesQuery =
            _conversationMessageSqlService.GetConversationMessagesHistoryAsync(
                companyId,
                staffUserId,
                null,
                null,
                fromWithTimeZone,
                toWithTimeZone,
                conditionalConversationIds: conditionalConversationIds);

        // The filter and logic is been applied in the getConversationMessagesHistory method
        // x.CompanyId == companyId && x.CreatedAt >= fromWithTimeZone && x.CreatedAt < toWithTimeZone
        var conversationMessagesEnum = await conversationMessagesQuery.ToListAsync();

        // Reduce the load of the data fetch from the database by filter the conversation messages in memory
        var conversationMessagesRespTimeEnum = conversationMessagesEnum
            .Where(
                c =>
                    c.Channel != ChannelTypes.Note
                    && c.MessageType != MessageTypes.System
                    && c.DeliveryType != DeliveryType.AutomatedMessage
                    && c.DeliveryType != DeliveryType.FlowHubAction
                    && c.DeliveryType != DeliveryType.AiAgentAction).ToList();

        var newCustomersConversationsEnum = await _conversationSqlService.GetNewCustomersConversationsAsync(
            companyId,
            fromWithTimeZone,
            toWithTimeZone,
            conditionalConversationIds);

        var newUserProfileIdsEnum = await _userProfileSqlService.GetNewUserProfilesAsync(
            companyId,
            fromWithTimeZone,
            toWithTimeZone,
            newUserProfileIds);

        return AnalyticsUtils.ConstructingAnalyticsData(
            conversationMessagesEnum,
            newCustomersConversationsEnum,
            newUserProfileIdsEnum,
            conversationMessagesRespTimeEnum,
            fromWithTimeZone,
            timeZoneId);
    }

    private static (List<string> Channels, List<string> ChannelIds)
        ExtractChannelsAndChannelIds(List<Condition> conditions)
    {
        var channels = new List<string>();
        var channelIds = new List<string>();

        if (!(conditions?.Count > 0))
        {
            return (channels, channelIds);
        }

        var condLastChannel = conditions
            .FirstOrDefault(
                x =>
                    !string.IsNullOrEmpty(x.FieldName)
                    && x.FieldName.ToLower() == "lastchannel"
                    && x.ConditionOperator == SupportedOperator.Contains);

        if (condLastChannel == null)
        {
            return (channels, channelIds);
        }

        foreach (var value in condLastChannel.Values)
        {
            var set = value.Split(":");

            if (set.Length <= 1)
            {
                continue;
            }

            if (!channels.Contains(set[0]))
            {
                channels.Add(set[0]);
            }

            channelIds.Add(value[(value.IndexOf(":", StringComparison.Ordinal) + 1)..]);
        }

        return (channels, channelIds);
    }
}