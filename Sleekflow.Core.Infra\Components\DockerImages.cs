using Pulumi;
using Sleekflow.Core.Infra.Components.Models;
using Docker = Pulumi.Docker;

namespace Sleekflow.Core.Infra.Components;

public class DockerImages
{
    private readonly string _prefixShortName;
    private readonly Output<string> _imageName;
    private readonly ContainerRegistryOutput _containerRegistryOutput;
    private readonly string _imageTag;

    public DockerImages(string prefixShortName, Output<string> imageName, ContainerRegistryOutput containerRegistryOutput, string imageTag)
    {
        _prefixShortName = prefixShortName;
        _imageName = imageName;
        _containerRegistryOutput = containerRegistryOutput;
        _imageTag = imageTag;
    }

    public Docker.Image InitDockerImages()
    {
        return new Docker.Image(
            _prefixShortName,
            new Docker.ImageArgs
            {
                ImageName = _imageName,
                Build = new Docker.Inputs.DockerBuildArgs
                {
                    Dockerfile = "Dockerfile",
                    CacheFrom = new Docker.Inputs.CacheFromArgs
                    {
                        Images = new List<string>
                        {
                            "mcr.microsoft.com/dotnet/aspnet:7.0.5-bullseye-slim-amd64"
                        }
                    },
                    Args = new InputMap<string>
                    {
                        {
                            "SRC_IMG", $"{_prefixShortName}:{_imageTag}"
                        }
                    }
                },
                Registry = new Docker.Inputs.RegistryArgs
                {
                    Server = _containerRegistryOutput.Registry.LoginServer,
                    Username = _containerRegistryOutput.AdminUsername,
                    Password = _containerRegistryOutput.AdminPassword
                },
            },
            new CustomResourceOptions
            {
                Parent = _containerRegistryOutput.Registry
            });
    }
}