using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using Moq;
using NUnit.Framework;
using Travis_backend.Enums;
using Travis_backend.Extensions;

namespace Sleekflow.Core.Tests.Rbac
{
    [TestFixture]
    public class StaffUserRoleExtensionsTests
    {
        private Mock<HttpContext> _mockHttpContext = null!;
        private Dictionary<object, object> _httpContextItems = null!;

        [SetUp]
        public void Setup()
        {
            _mockHttpContext = new Mock<HttpContext>();
            _httpContextItems = new Dictionary<object, object>();
            _mockHttpContext.Setup(x => x.Items).Returns(_httpContextItems);
        }

        [Test]
        public void RbacAwareRole_Equals_ShouldWorkCorrectly()
        {
            // Arrange
            var role1 = new RbacAwareRole(StaffUserRole.Admin);
            var role2 = new RbacAwareRole(StaffUserRole.Admin);
            var role3 = new RbacAwareRole(StaffUserRole.Staff);

            // Act & Assert
            Assert.That(role1.Equals(role2), Is.True);
            Assert.That(role1.Equals(role3), <PERSON>.False);
            Assert.That(role1.Equals(StaffUserRole.Admin), Is.True);
            Assert.That(role1.Equals(StaffUserRole.Staff), Is.False);
            Assert.That(role1.Equals("not a role"), Is.False);
        }

        [Test]
        public void RbacAwareRole_EqualityOperator_WhenRbacIsDisabled_ShouldCompareRolesNormally()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            var role1 = new RbacAwareRole(StaffUserRole.Admin);
            var role2 = new RbacAwareRole(StaffUserRole.Staff);

            // Act & Assert
            Assert.That(role1 == StaffUserRole.Admin, Is.True);
            Assert.That(role1 == StaffUserRole.Staff, Is.False);
            Assert.That(StaffUserRole.Admin == role1, Is.True);
            Assert.That(StaffUserRole.Staff == role1, Is.False);
            Assert.That(role1 != StaffUserRole.Staff, Is.True);
            Assert.That(StaffUserRole.Staff != role1, Is.True);
        }

        [Test]
        public void RbacAwareRole_EqualityOperator_WhenRbacIsEnabled_ShouldReturnTrue()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = true;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            var role1 = new RbacAwareRole(StaffUserRole.Admin);
            var role2 = new RbacAwareRole(StaffUserRole.Staff);

            // Act & Assert
            Assert.That(role1 == StaffUserRole.Admin, Is.True);
            Assert.That(role1 == StaffUserRole.Staff, Is.True);
            Assert.That(StaffUserRole.Admin == role1, Is.True);
            Assert.That(StaffUserRole.Staff == role1, Is.True);
            Assert.That(role1 != StaffUserRole.Staff, Is.False);
            Assert.That(StaffUserRole.Staff != role1, Is.False);
        }

        [Test]
        public void AsRbacAware_ShouldReturnRbacAwareRole()
        {
            // Arrange
            var role = StaffUserRole.Admin;

            // Act
            var rbacAwareRole = role.AsRbacAware();

            // Assert
            Assert.That(rbacAwareRole.Role, Is.EqualTo(role));
        }

        [Test]
        public void ImplicitConversion_ShouldWorkCorrectly()
        {
            // Arrange
            StaffUserRole role = StaffUserRole.Admin;
            
            // Act
            RbacAwareRole rbacAwareRole = role;
            StaffUserRole convertedBack = rbacAwareRole;
            
            // Assert
            Assert.That(rbacAwareRole.Role, Is.EqualTo(role));
            Assert.That(convertedBack, Is.EqualTo(role));
        }

        [Test]
        public void RbacAwareRole_EqualityOperator_SuperAdmin_Should_Have_Admin_Permissions_When_RbacIsDisabled()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            var superAdminRole = new RbacAwareRole(StaffUserRole.SuperAdmin);
            var adminRole = new RbacAwareRole(StaffUserRole.Admin);

            // Act & Assert
            Assert.That(superAdminRole == StaffUserRole.Admin, Is.True);
            Assert.That(StaffUserRole.SuperAdmin == adminRole, Is.False);
            
            Assert.That(superAdminRole != StaffUserRole.Staff, Is.True);
            Assert.That(StaffUserRole.Staff != superAdminRole, Is.True);
        }

        [Test]
        public void RbacAwareRole_Equals_SuperAdmin_Should_Have_Admin_Permissions()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            var superAdminRole = new RbacAwareRole(StaffUserRole.SuperAdmin);
            var adminRole = new RbacAwareRole(StaffUserRole.Admin);

            // Act & Assert
            Assert.That(superAdminRole.Equals(StaffUserRole.Admin), Is.True);
            Assert.That(adminRole.Equals(StaffUserRole.SuperAdmin), Is.False);
            
            // Test RbacAwareRole to RbacAwareRole equality - one-way permission model
            // SuperAdmin can equal Admin, but Admin doesn't equal SuperAdmin
            Assert.That(superAdminRole.Equals(adminRole), Is.False, 
                "SuperAdmin role should not equal Admin role object directly (one-way permission model)");
            Assert.That(adminRole.Equals(superAdminRole), Is.False);
        }

        [Test]
        public void RbacAwareRole_Staff_EqualityOperator_WhenRbacIsDisabled()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            var staffRole = new RbacAwareRole(StaffUserRole.Staff);
            var adminRole = new RbacAwareRole(StaffUserRole.Admin);
            var superAdminRole = new RbacAwareRole(StaffUserRole.SuperAdmin);

            // Act & Assert
            Assert.That(staffRole == StaffUserRole.Staff, Is.True);
            Assert.That(staffRole == StaffUserRole.Admin, Is.False);
            Assert.That(staffRole == StaffUserRole.SuperAdmin, Is.False);
            
            Assert.That(StaffUserRole.Staff == staffRole, Is.True);
            Assert.That(StaffUserRole.Admin == staffRole, Is.False);
            Assert.That(StaffUserRole.SuperAdmin == staffRole, Is.False);
            
            // Staff should not have Admin or SuperAdmin permissions
            Assert.That(staffRole.Equals(adminRole), Is.False);
            Assert.That(staffRole.Equals(superAdminRole), Is.False);
        }

        [Test]
        public void RbacAwareRole_TeamAdmin_EqualityOperator_WhenRbacIsDisabled()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            var teamAdminRole = new RbacAwareRole(StaffUserRole.TeamAdmin);
            var adminRole = new RbacAwareRole(StaffUserRole.Admin);
            var staffRole = new RbacAwareRole(StaffUserRole.Staff);
            var superAdminRole = new RbacAwareRole(StaffUserRole.SuperAdmin);

            // Act & Assert
            Assert.That(teamAdminRole == StaffUserRole.TeamAdmin, Is.True);
            Assert.That(teamAdminRole == StaffUserRole.Admin, Is.False);
            Assert.That(teamAdminRole == StaffUserRole.Staff, Is.False);
            Assert.That(teamAdminRole == StaffUserRole.SuperAdmin, Is.False);
            
            Assert.That(StaffUserRole.TeamAdmin == teamAdminRole, Is.True);
            Assert.That(StaffUserRole.Admin == teamAdminRole, Is.False);
            Assert.That(StaffUserRole.Staff == teamAdminRole, Is.False);
            Assert.That(StaffUserRole.SuperAdmin == teamAdminRole, Is.False);
            
            // TeamAdmin should not have Admin or SuperAdmin permissions
            Assert.That(teamAdminRole.Equals(adminRole), Is.False);
            Assert.That(teamAdminRole.Equals(superAdminRole), Is.False);
            
            // TeamAdmin and Staff are different roles
            Assert.That(teamAdminRole.Equals(staffRole), Is.False);
        }

        [Test]
        public void RbacAwareRole_Staff_And_TeamAdmin_WhenRbacIsEnabled()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = true;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            var staffRole = new RbacAwareRole(StaffUserRole.Staff);
            var teamAdminRole = new RbacAwareRole(StaffUserRole.TeamAdmin);
            var adminRole = new RbacAwareRole(StaffUserRole.Admin);
            var superAdminRole = new RbacAwareRole(StaffUserRole.SuperAdmin);

            // Act & Assert - With RBAC enabled, all roles should be considered equal
            Assert.That(staffRole == StaffUserRole.Staff, Is.True);
            Assert.That(staffRole == StaffUserRole.Admin, Is.True);
            Assert.That(staffRole == StaffUserRole.TeamAdmin, Is.True);
            Assert.That(staffRole == StaffUserRole.SuperAdmin, Is.True);
            
            Assert.That(teamAdminRole == StaffUserRole.Staff, Is.True);
            Assert.That(teamAdminRole == StaffUserRole.Admin, Is.True);
            Assert.That(teamAdminRole == StaffUserRole.TeamAdmin, Is.True);
            Assert.That(teamAdminRole == StaffUserRole.SuperAdmin, Is.True);
            
            // When RBAC is enabled, any role should equal any other role
            Assert.That(staffRole.Equals(adminRole), Is.True);
            Assert.That(staffRole.Equals(teamAdminRole), Is.True);
            Assert.That(staffRole.Equals(superAdminRole), Is.True);
            
            Assert.That(teamAdminRole.Equals(adminRole), Is.True);
            Assert.That(teamAdminRole.Equals(staffRole), Is.True);
            Assert.That(teamAdminRole.Equals(superAdminRole), Is.True);
        }
    }
}
