using Hangfire;
using Hangfire.MemoryStorage;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using Sleekflow.Core.Tests.Constants;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Database;
using Travis_backend.**********************;
using Travis_backend.FlowHubs;

namespace Sleekflow.Core.Tests.CompanyDomain.Services;

public class BusinessHourConfigTests
{
    private static readonly string MockCompanyId = $"aaaabbbb-cccc-dddd-eeee-{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString()[..12]}";

    private ApplicationDbContext _dbContext;
    private BusinessHourConfigService _businessHourConfigService;

    [SetUp]
    public void Setup()
    {
        // Initialize Hangfire with In-Memory storage
        GlobalConfiguration.Configuration.UseMemoryStorage();

        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseSqlServer(DbConnectionStrings.Dev)
            .Options;
        _dbContext = new ApplicationDbContext(options);
        var businessHourConfigRepository = new BusinessHourConfigRepository(_dbContext);

        var distributedInvocationContextService = new DistributedInvocationContextService(
            NullLogger<DistributedInvocationContextService>.Instance);

        var analyticsHooks = new AnalyticsHooks(distributedInvocationContextService);

        _businessHourConfigService = new BusinessHourConfigService(
            businessHourConfigRepository,
            analyticsHooks);
    }

    [TearDown]
    public async Task TearDown()
    {
        await _dbContext.BusinessHourConfigs.DeleteByKeyAsync(MockCompanyId);
    }

    [Test]
    public async Task BusinessHourConfig_LifeCycle_Test()
    {
        // Test get Non-initialized BusinessHourConfig
        // Should return null
        {
            var businessHourConfig = await _businessHourConfigService.GetAsync(MockCompanyId);

            Assert.That(businessHourConfig, Is.Null);
        }

        // Test create BusinessHourConfig
        // Should return new entity
        {
            var weeklyHours = GenerateWeeklyHours();
            var businessHourConfig = await _businessHourConfigService.CreateAndGetConfigAsync(MockCompanyId, true, weeklyHours);

            Assert.That(businessHourConfig, Is.Not.Null);
            Assert.Multiple(() =>
            {
                Assert.That(businessHourConfig.IsEnabled, Is.True);
                Assert.That(businessHourConfig.WeeklyHours.Version, Is.EqualTo("v1"));
                Assert.That(businessHourConfig.WeeklyHours.Monday[0].Start.CompareTo(TimeOnly.Parse("10:00:00")), Is.Zero);
                Assert.That(businessHourConfig.WeeklyHours.Monday[0].End.CompareTo(TimeOnly.Parse("18:00:00")), Is.Zero);
                Assert.That(businessHourConfig.WeeklyHours.Saturday, Is.Empty);
            });
        }

        // Test duplicate creation
        // Should throw error
        {
            var weeklyHours = GenerateWeeklyHours();
            Assert.ThrowsAsync<InvalidOperationException>(
                async () =>
                {
                    await _businessHourConfigService.CreateAndGetConfigAsync(MockCompanyId, true, weeklyHours);
                });
        }

        // Test update
        // Should update correctly
        {
            var weeklyHours = GenerateWeeklyHours();
            weeklyHours.Wednesday[0].Start = TimeOnly.Parse("14:00:00");

            var businessHourConfig = await _businessHourConfigService.UpdateAndGetAsync(MockCompanyId, false, weeklyHours);

            Assert.That(businessHourConfig, Is.Not.Null);
            Assert.Multiple(() =>
            {
                Assert.That(businessHourConfig.IsEnabled, Is.False);
                Assert.That(businessHourConfig.WeeklyHours.Monday[0].Start.CompareTo(TimeOnly.Parse("10:00:00")), Is.Zero);
                Assert.That(businessHourConfig.WeeklyHours.Wednesday[0].Start.CompareTo(TimeOnly.Parse("14:00:00")), Is.Zero);
                Assert.That(businessHourConfig.WeeklyHours.Saturday, Is.Empty);
            });
        }
    }

    private static WeeklyHours GenerateWeeklyHours()
    {
        return new WeeklyHours(
            "v1",
            [new Period(TimeOnly.Parse("10:00:00"), TimeOnly.Parse("18:00:00"), false)],
            [new Period(TimeOnly.Parse("10:00:00"), TimeOnly.Parse("18:00:00"), false)],
            [new Period(TimeOnly.Parse("10:00:00"), TimeOnly.Parse("18:00:00"), false)],
            [new Period(TimeOnly.Parse("10:00:00"), TimeOnly.Parse("18:00:00"), false)],
            [new Period(TimeOnly.Parse("10:00:00"), TimeOnly.Parse("18:00:00"), false)],
            [],
            []);
    }
}