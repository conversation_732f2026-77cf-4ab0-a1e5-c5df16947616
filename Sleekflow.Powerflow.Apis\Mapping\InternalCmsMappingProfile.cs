﻿using AutoMapper;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.MessageDomain.Models;
using Travis_backend.ResellerDomain.ViewModels;
using CmsCompanyResponse = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyResponse;
using CmsContactOwnerAssignLogDto = Sleekflow.Powerflow.Apis.ViewModels.CmsContactOwnerAssignLogDto;
using CmsUserDto = Sleekflow.Powerflow.Apis.ViewModels.CmsUserDto;
using CmsWebClientSenderDto = Sleekflow.Powerflow.Apis.ViewModels.CmsWebClientSenderDto;
using CmsWhatsApp360DialogUsageRecordViewModel = Sleekflow.Powerflow.Apis.ViewModels.CmsWhatsApp360DialogUsageRecordViewModel;
using CmsWhatsappApplicationDto = Sleekflow.Powerflow.Apis.ViewModels.CmsWhatsappApplicationDto;
using DivingUserInfoResponse = Sleekflow.Powerflow.Apis.ViewModels.DivingUserInfoResponse;
using WhatsApp360DialogUsageTransactionLogDto = Sleekflow.Powerflow.Apis.ViewModels.WhatsApp360DialogUsageTransactionLogDto;

namespace Sleekflow.Powerflow.Apis.Mapping;

public class InternalCmsMappingProfile : Profile
{
    public InternalCmsMappingProfile()
    {
        CreateMap<Company, CmsCompanyResponse>();

        CreateMap<BillRecord, CmsBillRecordDto>()
            .ForMember(
                dest =>
                    dest.PurchaseStaffName,
                opt =>
                    opt.MapFrom(src => src.PurchaseStaff.Identity.DisplayName))
            .ForMember(
                dest =>
                    dest.SubscriptionPlanName,
                opt =>
                    opt.MapFrom(src => src.SubscriptionPlan.SubscriptionName));

        CreateMap<BillRecord, CmsBillRecordLiteDto>();
        CreateMap<CmsSalesPaymentRecord, CmsSalesPaymentRecordDto>();
        CreateMap<CmsSalesPaymentRecord, CmsSalesPaymentRecordAnalyticDto>();
        CreateMap<CmsSalesPaymentRecordDto, CmsSalesPaymentRecordAnalyticDto>();
        CreateMap<CmsSalesPaymentRecordFile, CmsSalesPaymentRecordFileDto>();
        CreateMap<CmsSalesPaymentRecord, CmsSalesPaymentRecordSnapshotData>();
        CreateMap<WebClientSender, CmsWebClientSenderDto>();

        CreateMap<ApplicationUser, DivingUserInfoResponse>();
        CreateMap<ApplicationUser, CmsUserDto>();

        CreateMap<BillRecord, CmsAnalyticBillRecordDto>();
        CreateMap<CmsSalesPaymentRecord, CmsAnalyticSalesPaymentRecordDto>();
        CreateMap<CmsContactOwnerAssignLog, CmsContactOwnerAssignLogDto>()
            .ForMember(
                dest =>
                    dest.FromContactOwnerName,
                opt =>
                    opt.MapFrom(src => src.FromContactOwner.DisplayName))
            .ForMember(
                dest =>
                    dest.ToContactOwnerName,
                opt =>
                    opt.MapFrom(src => src.ToContactOwner.DisplayName))
            .ForMember(
                dest =>
                    dest.AssignedByUserName,
                opt =>
                    opt.MapFrom(src => src.AssignedByUser.DisplayName));

        CreateMap<Company, CmsCompanyAnalyticDto>();

        CreateMap<CmsWhatsappApplication, CmsWhatsappApplicationDto>()
            .ForMember(
                dest =>
                    dest.CmsCompanyName,
                opt =>
                    opt.MapFrom(src => src.Company.CompanyName))
            .ForMember(
                dest =>
                    dest.Country,
                opt =>
                    opt.MapFrom(src => src.Country ?? src.Company.CompanyCountry))
            .ForMember(
                dest =>
                    dest.ContactOwnerName,
                opt =>
                    opt.MapFrom(src => src.ContactOwner.DisplayName ?? null))
            .ForMember(
                dest =>
                    dest.CreatedByUserName,
                opt =>
                    opt.MapFrom(src => src.CreatedByUser.DisplayName ?? null))
            .ForMember(
                dest =>
                    dest.UpdatedByUserName,
                opt =>
                    opt.MapFrom(src => src.UpdatedByUser.DisplayName ?? null));

        CreateMap<WhatsApp360DialogUsageRecord, CmsWhatsApp360DialogUsageRecordViewModel>();
        CreateMap<WhatsApp360DialogUsageTransactionLog, WhatsApp360DialogUsageTransactionLogDto>();

        CreateMap<CmsBillRecordDto, CmsAnalyticBillRecordDto>();

        CreateMap<CmsCompanyAdditionalInfo, CmsCompanyAdditionalInfoViewModel>();
        CreateMap<BillRecord, AllBillRecordsDto>().ForMember(
            response => response.SubscriptionPlan,
            src => src.MapFrom(x => x.SubscriptionPlan));
    }
}