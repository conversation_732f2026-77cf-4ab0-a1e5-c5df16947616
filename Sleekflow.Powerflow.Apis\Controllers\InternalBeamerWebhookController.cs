using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Database;
using Travis_backend.InternalDomain.Services;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Route("internal/beamer/NPS/webhook/[action]")]
[AllowAnonymous]
public class InternalBeamerWebhookController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<InternalBeamerWebhookController> _logger;

    public InternalBeamerWebhookController(
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        IInternalHubspotRepository internalHubspotRepository,
        ILogger<InternalBeamerWebhookController> logger,
        UserManager<ApplicationUser> userManager
    )
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _configuration = configuration;
        _logger = logger;
    }

    [HttpPost]
    public async Task<IActionResult> SyncBeamerNPS(
        [FromBody]
        BeamerNPSResponse request,
        [FromQuery(Name = "key")]
        string key
    )
    {
        var verifyKey = _configuration["Beamer:WebhookVerifyKey"];
        if (string.IsNullOrEmpty(verifyKey))
        {
            return StatusCode(500);
        }

        if (request == null || key != verifyKey)
        {
            return BadRequest();
        }

        _logger.LogInformation(
            "SyncBeamerNPS requested, payload: {Payload}",
            JsonConvert.SerializeObject(request));

        // Get User
        var staff = await _appDbContext.UserRoleStaffs.Include(staff => staff.Company)
            .FirstOrDefaultAsync(x => x.Identity.Email == request.UserEmail);

        if (staff != null)
        {
            // Sync to DB
            staff.Company.NPSScore = request.Score;
            await _appDbContext.SaveChangesAsync();
        }
        else
        {
            _logger.LogInformation("SyncBeamerNPS, Sync to DB Fail: User not found!");
        }

        _logger.LogInformation("SyncBeamerNPS, Success");

        return Ok("Sync Successfully");
    }
}