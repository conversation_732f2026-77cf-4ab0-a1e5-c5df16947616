﻿using System.Globalization;
using AutoMapper;
using CsvHelper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.Database;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.Services;
using GetCompaniesDateRangeSnapShotRequest = Sleekflow.Powerflow.Apis.ViewModels.GetCompaniesDateRangeSnapShotRequest;
using GetCompaniesDetailSnapShotRequest = Sleekflow.Powerflow.Apis.ViewModels.GetCompaniesDetailSnapShotRequest;
using GetCompaniesDetailSnapShotResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCompaniesDetailSnapShotResponse;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Manage HubSpot Integration.
/// </summary>
[Route("/internal/snapshot/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsAdmin)] // Basic Role Requirement
public class InternalCmsSnapshotController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IInternalCompanyDataRepository _internalCompanyDataRepository;
    private readonly IInternalDataSnapshotService _internalDataSnapshotService;
    private readonly IInternalSquadMetricsRepository _internalSquadMetricsRepository;

    public InternalCmsSnapshotController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<InternalCmsSnapshotController> logger,
        IInternalDataSnapshotService internalDataSnapshotService,
        IInternalCompanyDataRepository internalCompanyDataRepository,
        IInternalSquadMetricsRepository internalSquadMetricsRepository)
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _internalDataSnapshotService = internalDataSnapshotService;
        _internalCompanyDataRepository = internalCompanyDataRepository;
        _internalSquadMetricsRepository = internalSquadMetricsRepository;
    }

    /// <summary>
    /// Get Latest Snapshot Companies Detail In CSV.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> GetLatestSnapshotCompaniesDetail([FromQuery] string companyId = null)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsAdmin
                }) == null)
        {
            return Unauthorized();
        }

        var companySnapshotInPastDay =
            await _internalCompanyDataRepository.GetCompanySnapshotDataAsync(
                DateTime.UtcNow.Date.AddDays(-1).AddHours(8));

        var detail = await _internalCompanyDataRepository.GetCmsCompanyDetails(
            companyId != null
                ? new List<string>()
                {
                    companyId
                }
                : null,
            companySnapshotInPastDay);

        var snapshotData = detail.Select(CmsCompanyDetailConverter.ConvertToCompanySnapshotData).ToList();
        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(snapshotData);
            }
        }

        return File(ms.ToArray(), "text/csv", $"CompanyDetails-{DateTime.UtcNow.AddHours(8):yyyyMMdd}.csv");
    }

    /// <summary>
    /// Get Trim Latest Snapshot Companies Detail In CSV(For Lewis).
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> GetLatestTrimSnapshotCompaniesDetail([FromQuery] string companyId = null)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsAdmin
                }) == null)
        {
            return Unauthorized();
        }

        var companySnapshotInPastDay =
            await _internalCompanyDataRepository.GetCompanySnapshotDataAsync(
                DateTime.UtcNow.Date.AddDays(-1).AddHours(8));

        var detail = await _internalCompanyDataRepository.GetCmsCompanyDetails(
            companyId != null
                ? new List<string>()
                {
                    companyId
                }
                : null,
            companySnapshotInPastDay);

        var snapshotData = detail.Select(CmsCompanyDetailConverter.ConvertToCompanySnapshotData).ToList();
        var snapshotTrimData = _mapper.Map<List<CompanySnapshotTrimData>>(snapshotData);
        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(snapshotTrimData);
            }
        }

        return File(ms.ToArray(), "text/csv", $"CompanyDetails-{DateTime.UtcNow.AddHours(8):yyyyMMdd}.csv");
    }

    /// <summary>
    /// Get Latest Snapshot Bill Records In CSV.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> GetLatestBillRecords([FromQuery] string companyId = null)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsAdmin
                }) == null)
        {
            return Unauthorized();
        }

        var billRecords = await _internalCompanyDataRepository.GetCmsCompanyBillRecords(
            companyId != null
                ? new List<string>()
                {
                    companyId
                }
                : null);

        var billRecordSnapshotData =
            billRecords.Select(CmsCompanyDetailConverter.ConvertToCompanyBillRecordSnapshotData).ToList();

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(billRecordSnapshotData);
            }
        }

        return File(ms.ToArray(), "text/csv", $"BillRecords-{DateTime.UtcNow.AddHours(8):yyyyMMdd}.csv");
    }

    /// <summary>
    /// Manually Create Company Detail SnapShot.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> CreateCompaniesDetailSnapshot()
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsAdmin
                }) == null)
        {
            return Unauthorized();
        }

        var detail = await _internalCompanyDataRepository.GetCmsCompanyDetails();

        var snapshotData = detail.Select(CmsCompanyDetailConverter.ConvertToCompanySnapshotData).ToList();

        await _internalCompanyDataRepository.SaveCompanySnapshotDataAsync(snapshotData, DateTime.UtcNow);

        return Ok(
            new ResponseViewModel()
            {
                message = "Created"
            });
    }

    /// <summary>
    /// Get Company Detail SnapShot by Date.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> GetCompaniesDetailSnapShot([FromBody] GetCompaniesDetailSnapShotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsAdmin
                }) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var companySnapshotData = await _internalCompanyDataRepository.GetCompanySnapshotDataAsync(request.Date);

        return Ok(
            new GetCompaniesDetailSnapShotResponse()
            {
                CompanySnapshotDataList = companySnapshotData
            });
    }

    /// <summary>
    /// Create By Month Daily Whatsapp Cloud Api Conversation Usage Analytic Snapshot.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> CreateByMonthDailyWhatsappCloudApiConversationUsageAnalyticSnapshot(
        [FromBody]
        GetCompaniesDetailSnapShotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsAdmin
                }) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        await _internalDataSnapshotService.CreateByMonthDailyWhatsappCloudApiConversationUsageAnalyticsSnapshot(
            request.Date.StartOfMonth());

        return Ok();
    }

    /// <summary>
    /// Create Internal By Month Daily Whatsapp Cloud Api Conversation Usage Analytic Snapshot.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> CreateInternalByMonthDailyWhatsappCloudApiConversationUsageAnalyticsSnapshot(
        [FromBody]
        GetCompaniesDetailSnapShotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                [ApplicationUserRole.InternalCmsAdmin]) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        _internalDataSnapshotService
            .CreateInternalByMonthDailyWhatsappCloudApiConversationUsageAnalyticsSnapshotInBackgroundJob(
                request.Date.StartOfMonth());

        return Ok(
            new ResponseViewModel
            {
                message =
                    "Internal WhatsApp Cloud API Conversation Usage Analytics Snapshot job has been queued successfully"
            });
    }

    /// <summary>
    /// Get AllTime Daily Whatsapp CloudApi Conversation Usage Analytic.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [AllowAnonymous]
    public async Task<ActionResult> GetSleekPayReportData()
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsAdmin
                }) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var data = await _internalCompanyDataRepository.GetCmsSleekPayReportData();

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(data);
            }
        }

        return File(
            ms.ToArray(),
            "text/csv",
            $"GetSleekPayReportData-{DateTime.UtcNow.AddHours(8):yyyyMMdd}.csv");
    }

    /// <summary>
    /// Get AllTime Daily Whatsapp CloudApi Conversation Usage Analytic.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> GetAllTimeDailyWhatsappCloudApiConversationUsageAnalytic(
        [FromBody]
        GetCompaniesDateRangeSnapShotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsAdmin
                }) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var data = await _internalCompanyDataRepository.GetDailyWhatsappCloudApiConversationUsageAnalytic(
            request.Start,
            request.End);

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(data);
            }
        }

        return File(
            ms.ToArray(),
            "text/csv",
            $"WhatsappCloudApiConversation-{request.Start:yyyyMMdd}-{request.End:yyyyMMdd}.csv");
    }

    /// <summary>
    /// Get Message Volume By Company (BugBq Metrics)
    /// </summary>
    [HttpPost]
    public async Task<ActionResult> GetMessageVolumeByCompany(
        [FromBody]
        GetCompaniesDateRangeSnapShotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsAdmin
                }) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var data = await _internalSquadMetricsRepository.GetMessageVolumeByCompany(
            request.Start.ToString("yyyy-MM-dd"),
            request.Start,
            request.End);

        var failedMessageVolumeByCompany = await _internalSquadMetricsRepository.GetFailedMessageVolumeByCompany(
            request.Start.ToString("yyyy-MM-dd"),
            request.Start,
            request.End);

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(data);
            }
        }

        return File(
            ms.ToArray(),
            "text/csv",
            $"GetMessageVolumeByCompany-{request.Start:yyyyMMdd}-{request.End:yyyyMMdd}.csv");
    }

    /// <summary>
    /// Get Failed Message Volume By Company (BugBq Metrics)
    /// </summary>
    [HttpPost]
    public async Task<ActionResult> GetFailedMessageVolumeByCompany(
        [FromBody]
        GetCompaniesDateRangeSnapShotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsAdmin
                }) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var data = await _internalSquadMetricsRepository.GetFailedMessageVolumeByCompany(
            request.Start.ToString("yyyy-MM-dd"),
            request.Start,
            request.End);

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(data);
            }
        }

        return File(
            ms.ToArray(),
            "text/csv",
            $"GetFailedMessageVolumeByCompany-{request.Start:yyyyMMdd}-{request.End:yyyyMMdd}.csv");
    }

    /// <summary>
    /// Get Message Volume By Company Summary (BugBq Metrics)
    /// </summary>
    [HttpPost]
    [Authorize]
    public async Task<ActionResult> GetMessageVolumeByCompanySummary(
        [FromBody]
        GetCompaniesDateRangeSnapShotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsAdmin
                }) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var data  = await _internalSquadMetricsRepository.GetMessageVolumeByCompanySummary(
            request.Start.ToString("yyyy-MM-dd"),
            request.Start,
            request.End);

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(data);
            }
        }

        return File(
            ms.ToArray(),
            "text/csv",
            $"GetMessageVolumeByCompanySummary-{request.Start:yyyyMMdd}-{request.End:yyyyMMdd}.csv");
    }

    /// <summary>
    /// Get By Business Daily Whatsapp Cloud Api Conversation Usage Analytic Monthly Snapshot.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> GetByBusinessDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(
        [FromBody]
        GetCompaniesDetailSnapShotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                [ApplicationUserRole.InternalCmsAdmin]) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var data = await _internalCompanyDataRepository
            .GetByBusinessDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(
                request.Date.StartOfMonth());

        if (data == null || data.Count == 0)
        {
            return NotFound(
                new ResponseViewModel
                {
                    message = $"No data found for month {request.Date:yyyy-MM}"
                });
        }

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(data);
            }
        }

        return File(
            ms.ToArray(),
            "text/csv",
            $"ByBusinessWhatsappCloudApiConversationUsage-{request.Date:yyyyMM}.csv");
    }

    /// <summary>
    /// Get By Waba Daily Whatsapp Cloud Api Conversation Usage Analytic Monthly Snapshot.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> GetByWabaDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(
        [FromBody]
        GetCompaniesDetailSnapShotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                [ApplicationUserRole.InternalCmsAdmin]) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var data = await _internalCompanyDataRepository
            .GetByWabaDailyWhatsappCloudApiConversationUsageAnalyticMonthlySnapshot(
                request.Date.StartOfMonth());

        if (data == null || data.Count == 0)
        {
            return NotFound(
                new ResponseViewModel
                {
                    message = $"No data found for month {request.Date:yyyy-MM}"
                });
        }

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(data);
            }
        }

        return File(
            ms.ToArray(),
            "text/csv",
            $"ByWabaWhatsappCloudApiConversationUsage-{request.Date:yyyyMM}.csv");
    }
}