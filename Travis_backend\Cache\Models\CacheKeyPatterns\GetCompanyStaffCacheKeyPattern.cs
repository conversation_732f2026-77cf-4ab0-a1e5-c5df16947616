using System.Collections.Generic;
using System.Linq;

namespace Travis_backend.Cache.Models.CacheKeyPatterns;

public class GetCompanyStaffCacheKeyPattern : ICacheKeyPattern
{
    public string IdentityId { get; set; }

    public GetCompanyStaffCacheKeyPattern(string identityId)
    {
        IdentityId = identityId;
    }

    public string GenerateKeyPattern()
    {
        var keyComponents = new List<object> { IdentityId };

        return CacheHelper.BuildKeyNameFromPattern(keyComponents.ToArray());
    }
}