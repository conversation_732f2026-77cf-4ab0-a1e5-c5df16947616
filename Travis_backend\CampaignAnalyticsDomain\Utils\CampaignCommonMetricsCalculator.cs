﻿using System.Collections.Generic;
using System.Linq;
using Travis_backend.CampaignAnalyticsDomain.Constants;
using Travis_backend.CampaignAnalyticsDomain.Models;

namespace Travis_backend.CampaignAnalyticsDomain.Utils;

public static class CampaignCommonMetricsCalculator
{
    public static CampaignCommonMetrics Calculate(
        List<MetricDataPointDto> metricDataPoints,
        int repliedCount)
    {
        var sent = metricDataPoints.Sum(
            x => MessageStatusGroups.Sent.Contains(x.MessageStatus)
                ? x.Count
                : 0);

        var delivered = metricDataPoints.Sum(
            x => MessageStatusGroups.Delivered.Contains(x.MessageStatus)
                ? x.Count
                : 0);

        var read = metricDataPoints.Sum(
            x => MessageStatusGroups.Read.Contains(x.MessageStatus)
                ? x.Count
                : 0);

        var bounced = metricDataPoints.Sum(
            x => MessageStatusGroups.Bounced.Contains(x.MessageStatus)
                ? x.Count
                : 0);

        return new CampaignCommonMetrics(
            sent,
            delivered,
            read,
            repliedCount,
            bounced);
    }
}