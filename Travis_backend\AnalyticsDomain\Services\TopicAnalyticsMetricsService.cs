using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.Utils;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;

namespace Travis_backend.AnalyticsDomain.Services;

public interface ITopicAnalyticsMetricsService
{
    Task<List<TopicAnalyticsMetricDto>> GetTopicMetricDailyLogsAsync(
        string companyId,
        DateOnly from,
        DateOnly to,
        string topicId,
        bool usingCache,
        CancellationToken cancellationToken);

    TopicAnalyticsMetricDto CalculateSummedLog(
        DateOnly? date,
        IReadOnlyCollection<TopicAnalyticsMetricDto> metrics);

    Task<TopicAnalyticsMetricDto> GetCommonMetricDailyLogAsync(
        string companyId,
        DateOnly date,
        string topicId,
        CancellationToken cancellationToken);

    Task<List<TopicAnalyticsMetric>> GetFilteredTopicAnalyticsMetricsAsync(
        string companyId,
        DateOnly date,
        string topicId,
        CancellationToken cancellationToken);

    Task<DateTime?> GetLastUpdateTime();
}

public class TopicAnalyticsMetricsService
    : ITopicAnalyticsMetricsService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ICacheManagerService _cacheManagerService;

    public TopicAnalyticsMetricsService(
        IServiceProvider serviceProvider,
        ICacheManagerService cacheManagerService)
    {
        _serviceProvider = serviceProvider;
        _cacheManagerService = cacheManagerService;
    }

    public async Task<List<TopicAnalyticsMetricDto>> GetTopicMetricDailyLogsAsync(
        string companyId,
        DateOnly from,
        DateOnly to,
        string topicId,
        bool usingCache,
        CancellationToken cancellationToken)
    {
        var dateRange = new List<DateOnly>();
        for (var date = from; date <= to; date = date.AddDays(1))
        {
            dateRange.Add(date);
        }

        var dailyLogs = new ConcurrentBag<TopicAnalyticsMetricDto>();
        await Parallel.ForEachAsync(
            dateRange,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 6
            },
            async (thisDate, thisCancellationToken) =>
            {
                // todo remove the code block after performance testing
                if (!usingCache)
                {
                    dailyLogs.Add(
                        await GetCommonMetricDailyLogAsync(
                            companyId,
                            thisDate,
                            topicId,
                            thisCancellationToken)
                    );

                    return;
                }

                var cachedKeyPattern = new TopicAnalyticsMetricsCacheKeyPattern(
                    companyId,
                    topicId,
                    thisDate);

                var dailyLog = await _cacheManagerService.GetAndSaveCacheAsync(
                    cachedKeyPattern,
                    async () => await GetCommonMetricDailyLogAsync(
                        companyId,
                        thisDate,
                        topicId,
                        thisCancellationToken));

                dailyLogs.Add(dailyLog);
            });

        return dailyLogs.OrderBy(d => d.Date).ToList();
    }

    public TopicAnalyticsMetricDto CalculateSummedLog(
        DateOnly? date,
        IReadOnlyCollection<TopicAnalyticsMetricDto> metrics)
    {
        return TopicAnalyticsMetricUtils.Aggregate(date, metrics);
    }

    public async Task<TopicAnalyticsMetricDto> GetCommonMetricDailyLogAsync(
        string companyId,
        DateOnly date,
        string topicId,
        CancellationToken cancellationToken)
    {
        var topicAnalyticsMetrics = await GetFilteredTopicAnalyticsMetricsAsync(
            companyId,
            date,
            topicId,
            cancellationToken);

        // Business hour dimension only contains time related data points,
        // so we need to fetch other data points from it's base dimension

        var dailyLog = TopicAnalyticsMetricUtils.AggregateToDto(date, topicAnalyticsMetrics);

        return dailyLog;
    }


    public async Task<List<TopicAnalyticsMetric>> GetFilteredTopicAnalyticsMetricsAsync(
        string companyId,
        DateOnly date,
        string topicId,
        CancellationToken cancellationToken)
    {
        // This function is designed to be called in parallel, thus we assign a new scope for each call.
        // Analytic DB is readonly so guaranteed to be thread-safe
        using var scope = _serviceProvider.CreateScope();
        var topicAnalyticsMetricsRepository =
            scope.ServiceProvider.GetRequiredService<ITopicAnalyticsMetricsRepository>();

        var topicAnalyticsMetrics = new List<TopicAnalyticsMetric>();

        topicAnalyticsMetrics = await topicAnalyticsMetricsRepository.GetDailyDataAsync(
            companyId,
            date,
            topicId,
            cancellationToken);

        TopicAnalyticsMetricUtils.Sanitize(ref topicAnalyticsMetrics);

        return topicAnalyticsMetrics;
    }

    public async Task<DateTime?> GetLastUpdateTime()
    {
        using var scope = _serviceProvider.CreateScope();
        var topicAnalyticsMetricsRepository =
            scope.ServiceProvider.GetRequiredService<ITopicAnalyticsMetricsRepository>();

        return await topicAnalyticsMetricsRepository.GetLastUpdateTime();
    }


}