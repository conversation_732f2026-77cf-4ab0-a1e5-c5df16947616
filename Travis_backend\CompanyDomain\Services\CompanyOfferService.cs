#nullable enable
using System;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Enums;
using Travis_backend.Infrastructures.Options;
using Travis_backend.SubscriptionPlanDomain.Constants;

namespace Travis_backend.CompanyDomain.Services;

/// <inheritdoc />
public class CompanyOfferService : ICompanyOfferService
{
    #region Dependencies & Constructor

    /// <summary>
    /// ICompanyService.
    /// </summary>
    private readonly ICompanyService _companyService;

    /// <summary>
    /// ICompanyBillRecordService.
    /// </summary>
    private readonly ICompanyBillRecordService _companyBillRecordService;

    /// <summary>
    /// FlowBuilderFlowEnrollmentsIncentivesOptions.
    /// </summary>
    private readonly FlowBuilderFlowEnrollmentsIncentivesOptions _flowBuilderFlowEnrollmentsIncentivesOptions;

    /// <summary>
    /// ILogger.
    /// </summary>
    private readonly ILogger<CompanyOfferService> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="CompanyOfferService"/> class.
    /// </summary>
    /// <param name="companyService">ICompanyService.</param>
    /// <param name="companyBillRecordService">ICompanyBillRecordService.</param>
    /// <param name="flowBuilderFlowEnrollmentsIncentivesOptions">FlowBuilderFlowEnrollmentsIncentivesOptions.</param>
    /// <param name="logger">ILogger.</param>
    public CompanyOfferService(
        ICompanyService companyService,
        ICompanyBillRecordService companyBillRecordService,
        IOptions<FlowBuilderFlowEnrollmentsIncentivesOptions> flowBuilderFlowEnrollmentsIncentivesOptions,
        ILogger<CompanyOfferService> logger)
    {
        _companyService = companyService;
        _companyBillRecordService = companyBillRecordService;
        _flowBuilderFlowEnrollmentsIncentivesOptions = flowBuilderFlowEnrollmentsIncentivesOptions.Value;
        _logger = logger;
    }

    #endregion

    /// <inheritdoc />
    public async Task<bool> IsEntitleToFlowEnrollmentAddOnPurchaseIncentiveAsync(string companyId, string? stripeInvoiceLineItemId = null)
    {
        _logger.LogInformation(
            "Determine if company entitle to flow enrollment incentive. " +
            "CompanyId: {CompanyId}, " +
            "StripeInvoiceLineItemId: {StripeInvoiceLineItemId}",
            companyId,
            stripeInvoiceLineItemId);

        if (!_flowBuilderFlowEnrollmentsIncentivesOptions.IsValidIncentivesPeriod)
        {
            _logger.LogInformation(
                "Current time is not in the incentive period. Now: {Now}, From: {From}, To: {To}",
                DateTime.UtcNow,
                _flowBuilderFlowEnrollmentsIncentivesOptions.PeriodStart,
                _flowBuilderFlowEnrollmentsIncentivesOptions.PeriodEnd);

            return false;
        }

        var subscriptionInfo = await _companyService.GetCompanySubscriptionInfo(companyId);

        if (subscriptionInfo.SubscriptionTier == SubscriptionTier.Free)
        {
            _logger.LogInformation(
                "Current company is not on paid subscription. CompanyId: {CompanyId}, SubscriptionTier: {SubscriptionTier}",
                companyId,
                subscriptionInfo.SubscriptionTier);

            return false;
        }

        var hasIncentiveBillRecord = await _companyBillRecordService.HasBillRecordAsync(
            companyId,
            x => SubscriptionPlansId.FlowBuilderFlowEnrolmentsIncentiveAddOnsForAddOnPurchase.Contains(x.SubscriptionPlanId));

        if (hasIncentiveBillRecord)
        {
            _logger.LogInformation("Company({CompanyId}) already has Flow Enrollment Incentive BillRecord.", companyId);
            return false;
        }

        _logger.LogInformation(
            "Check if company already has FlowEnrollments Add-On or Incentive Add-On. " +
            "CompanyId: {CompanyId}, " +
            "StripeInvoiceLineItemId: {StripeInvoiceLineItemId}",
            companyId,
            stripeInvoiceLineItemId);

        var hasValidFlowEnrollmentAddOn = await _companyBillRecordService.HasValidBillRecordAsync(
            companyId,
            x => SubscriptionPlansId.FlowBuilderFlowEnrolmentsAddOns.Contains(x.SubscriptionPlanId) &&
                 (string.IsNullOrEmpty(stripeInvoiceLineItemId) || x.stripeId != stripeInvoiceLineItemId));

        if (hasValidFlowEnrollmentAddOn)
        {
            _logger.LogInformation("Company({CompanyId}) has valid Flow Enrollment Add-On BillRecord.", companyId);
            return false;
        }

        return true;
    }
}