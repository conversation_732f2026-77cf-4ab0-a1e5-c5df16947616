﻿namespace Travis_backend.CampaignAnalyticsDomain.ViewModels;

public class RecipientOverviewViewModel
{
    public string UserProfileId { get; set; }

    public string ConversationId { get; set; }

    public string FirstName { get; set; }

    public string LastName { get; set; }

    public string FullName { get; set; }

    public string PhoneNumber { get; set; }

    public RecipientOverviewViewModel(
        string userProfileId,
        string conversationId,
        string firstName,
        string lastName,
        string fullName,
        string phoneNumber)
    {
        UserProfileId = userProfileId;
        ConversationId = conversationId;
        FirstName = firstName;
        LastName = lastName;
        FullName = fullName;
        PhoneNumber = phoneNumber;
    }
}