using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Constants;
using Random = Pulumi.Random;
using Storage = Pulumi.AzureNative.Storage;

namespace Sleekflow.Core.Infra.Components;

public class BlobStorage
{
    private readonly MyConfig _myConfig;
    private readonly string _locationName;
    private readonly CorsConfig? _corsConfig;
    private readonly ResourceGroup _resourceGroup;

    public BlobStorage(MyConfig myConfig, string locationName, ResourceGroup resourceGroup)
    {
        _corsConfig = null;
        _myConfig = myConfig;
        _locationName = locationName;
        _resourceGroup = resourceGroup;
    }

    public BlobStorage(MyConfig myConfig, string locationName, CorsConfig? corsConfig, ResourceGroup resourceGroup)
    {
        _myConfig = myConfig;
        _corsConfig = corsConfig;
        _locationName = locationName;
        _resourceGroup = resourceGroup;
    }

    public Storage.StorageAccount InitBlobStorage()
    {
        var name = LocationNames.GetShortName(_locationName);
        var randomId = new Random.RandomId(
            $"sleekflow-core-storage-account-random-id-{name}",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "Location", _locationName
                    }
                },
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });
        var storageAccount = new Storage.StorageAccount(
            $"sleekflow-core-storage-{name}-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-core-storage-{_myConfig.Name}"
                    },
                    {
                        "Location", _locationName
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = randomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var blobServiceProperties = new Storage.BlobServiceProperties(
            $"sleekflow-core-storage-{name}-blob-service-properties",
            new Storage.BlobServicePropertiesArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = storageAccount.Name,
                Cors = new Storage.Inputs.CorsRulesArgs
                {
                    CorsRules = new[]
                    {
                        new Storage.Inputs.CorsRuleArgs
                        {
                            AllowedHeaders = new[]
                            {
                                "*"
                            },
                            AllowedMethods = new[]
                            {
                                Union<string, Storage.AllowedMethods>.FromT1(Storage.AllowedMethods.GET),
                                Union<string, Storage.AllowedMethods>.FromT1(Storage.AllowedMethods.HEAD),
                                Union<string, Storage.AllowedMethods>.FromT1(Storage.AllowedMethods.POST),
                                Union<string, Storage.AllowedMethods>.FromT1(Storage.AllowedMethods.OPTIONS),
                                Union<string, Storage.AllowedMethods>.FromT1(Storage.AllowedMethods.PUT)
                            },
                            AllowedOrigins = _corsConfig?.Domains.ToArray() ?? Array.Empty<string>(),
                            ExposedHeaders = new[]
                            {
                                "x-ms-meta-*",
                            },
                            MaxAgeInSeconds = 100,
                        },
                    },
                },
                BlobServicesName = "default",
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var _ = new Storage.BlobContainer(
            $"sleekflow-core-container-{name}",
            new Storage.BlobContainerArgs
            {
                AccountName = storageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "travis-backend-container"
            },
            new CustomResourceOptions
            {
                Parent = storageAccount
            });
        return storageAccount;
    }

    public (Storage.StorageAccount StorageAccount, Storage.BlobContainer BlobContainer, Storage.Blob Blob)
        InitFunctionAppBlobStorage(string appName, string path)
    {
        var location = LocationNames.GetShortName(_locationName);
        var randomId = new Random.RandomId(
            $"{appName}-storage-account-random-id-{location}",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "App_Name", appName
                    },
                    {
                        "Location", _locationName
                    }
                },
            });

        var storageAccount = new Storage.StorageAccount(
            $"{appName}-storage-account-{location}",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = randomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var container = new Storage.BlobContainer(
            $"{appName}-zips-container-{location}",
            new Storage.BlobContainerArgs
            {
                AccountName = storageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "zips-container"
            },
            new CustomResourceOptions
            {
                Parent = storageAccount
            });

        var ymdhms = new DirectoryInfo(path)
            .EnumerateFiles("*", SearchOption.AllDirectories)
            .Max(fi => fi.CreationTimeUtc)
            .ToString("yyyyMMddHHmmss");

        var blob = new Storage.Blob(
            $"{appName}_zip_{ymdhms}_{location}",
            new Storage.BlobArgs
            {
                AccountName = storageAccount.Name,
                ContainerName = container.Name,
                ResourceGroupName = _resourceGroup.Name,
                Type = Storage.BlobType.Block,
                Source = new FileArchive(path)
            },
            new CustomResourceOptions
            {
                Parent = container
            });
        return (storageAccount, container, blob);
    }

    public Storage.StorageAccount InitLiveChatV2BlobStorage()
    {
        var name = LocationNames.GetShortName(_locationName);
        var envName = _myConfig.Name;
        var randomId = new Random.RandomId(
            $"sleekflow-livechatv2-storage-account-random-id-{name}",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "Location", _locationName
                    }
                },
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var storageAccount = new Storage.StorageAccount(
            $"sleekflow-livechatv2-storage-{name}-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", envName
                    },
                    {
                        "StorageAccountName", $"sleekflow-livechatv2-storage-{envName}"
                    },
                    {
                        "Location", _locationName
                    },
                    {
                        "Purpose", "LiveChatV2StaticWebsite"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = randomId.Hex.Apply(h => $"slc{name}{envName.Substring(0, envName.Length > 4 ? 4 : envName.Length)}{h}")
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Enable static website hosting
        var staticWebsite = new Storage.StorageAccountStaticWebsite(
            $"sleekflow-livechatv2-static-website-{name}",
            new Storage.StorageAccountStaticWebsiteArgs
            {
                AccountName = storageAccount.Name,
                ResourceGroupName = _resourceGroup.Name,
            },
            new CustomResourceOptions
            {
                Parent = storageAccount
            });

        // Configure CORS for public access from any website
        var blobServiceProperties = new Storage.BlobServiceProperties(
            $"sleekflow-livechatv2-storage-{name}-blob-service-properties",
            new Storage.BlobServicePropertiesArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = storageAccount.Name,
                Cors = new Storage.Inputs.CorsRulesArgs
                {
                    CorsRules = new[]
                    {
                        new Storage.Inputs.CorsRuleArgs
                        {
                            AllowedHeaders = new[]
                            {
                                "*"
                            },
                            AllowedMethods = new[]
                            {
                                Union<string, Storage.AllowedMethods>.FromT1(Storage.AllowedMethods.GET),
                                Union<string, Storage.AllowedMethods>.FromT1(Storage.AllowedMethods.HEAD),
                                Union<string, Storage.AllowedMethods>.FromT1(Storage.AllowedMethods.POST),
                                Union<string, Storage.AllowedMethods>.FromT1(Storage.AllowedMethods.OPTIONS),
                                Union<string, Storage.AllowedMethods>.FromT1(Storage.AllowedMethods.PUT),
                            },
                            AllowedOrigins = new[]
                            {
                                "*" // Allow access from any website
                            },
                            ExposedHeaders = new[]
                            {
                                "x-ms-meta-*",
                            },
                            MaxAgeInSeconds = 3600, // 1 hour cache
                        },
                    },
                },
                BlobServicesName = "default",
            },
            new CustomResourceOptions
            {
                Parent = storageAccount
            });

        return storageAccount;
    }
}