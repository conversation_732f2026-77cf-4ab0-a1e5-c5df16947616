using Sleekflow.Core.Tests.Conversations.Rbac.RbacTestData;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.Conversations.Rbac.SendMessages;

[TestFixture]
public class CanSendUnitTests
{
    private IRbacConversationPermissionManager _rbacConversationPermissionManager;

        [SetUp]
        public void Setup()
        {
            _rbacConversationPermissionManager = new RbacConversationPermissionManager(new RbacDefaultChannelPermissionManager());
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetNoSendMessagePermissionTestCases))]
        public void staff_with_no_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAllConversationSendMessagePermissionTestCases))]
        public void staff_with_all_conversations_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test,
         TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAssignedToMeSendMessagePermissionTestCases))]
        public void staff_with_assigned_to_me_send_message_permission(
            StaffAccessControlAggregate staff,
            Conversation conversation,
            bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAssignedToMeAndAssignedToMyTeamSendMessagePermissionTestCases))]
        public void staff_with_assigned_to_me_and_assigned_to_my_team_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAssignedToMeAndAllAssignedConversationsSendMessagePermissionTestCases))]
        public void Staff_with_assigned_to_me_and_all_assigned_conversations_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAssignedToMeAndUnassignedConversationsUnderMyTeamSendMessagePermissionTestCases))]
        public void Staff_with_assigned_to_me_and_unassigned_conversations_under_my_team_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAssignedToMeAndAllUnassignedConversationsSendMessagePermissionTestCases))]
        public void Staff_with_assigned_to_me_and_all_unassigned_conversations_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsAndUnassignedConversationsUnderMyTeamSendMessagePermissionTestCases))]
        public void Staff_with_assigned_to_me_and_assigned_to_my_team_and_all_assigned_conversations_and_unassigned_conversations_under_my_team_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAssignedToMeAndAssignedToMyTeamAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsSendMessagePermissionTestCases))]
        public void Staff_with_assigned_to_me_and_assigned_to_my_team_and_unassigned_conversations_under_my_team_and_all_unassigned_conversations_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsAndAllUnassignedConversationsSendMessagePermissionTestCases))]
        public void Staff_with_assigned_to_me_and_assigned_to_my_team_and_all_assigned_conversations_and_all_unassigned_conversations_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAssignedToMeAndAllAssignedConversationsAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsSendMessagePermissionTestCases))]
        public void Staff_with_assigned_to_me_and_and_all_assigned_conversations_and_unassigned_conversations_under_my_team_and_all_unassigned_conversations_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsSendMessagePermissionTestCases))]
        public void Staff_with_assigned_to_me_and_assigned_to_my_team_and_all_assigned_conversations_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test, TestCaseSource(typeof(CanSendTestData), nameof(CanSendTestData.GetAssignedToMeAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsSendMessagePermissionTestCases))]
        public void Staff_with_assigned_to_me_and_unassigned_conversations_under_my_team_and_all_unassigned_conversations_send_message_permission(StaffAccessControlAggregate staff, Conversation conversation, bool expectedResult)
        {
            // Act
            var result = _rbacConversationPermissionManager.CanSend(staff, conversation);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }
}
