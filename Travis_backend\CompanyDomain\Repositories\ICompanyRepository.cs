using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;

namespace Travis_backend.CompanyDomain.Repositories;

/// <summary>
/// Repository for access CompanyCompanies table.
/// </summary>
public interface ICompanyRepository
{
    /// <summary>
    /// Find Company by Id.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <returns>Entity of Company.</returns>
    Task<Company?> GetCompany(string companyId);

    /// <summary>
    /// Update Company's Subscription Tier.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="subscriptionCountryTier">Subscription Country Tier.</param>
    /// <param name="currency">Currency.</param>
    /// <returns>Number of Affected Rows.</returns>
    Task<int> UpdateCompanySubscriptionTierAndCurrencyAsync(string companyId, string subscriptionCountryTier, string currency);

    /// <summary>
    /// Get whether if company has set subscription country tier.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <returns>Boolean.</returns>
    Task<bool> IsSubscriptionCountryTierHasValue(string companyId);

    /// <summary>
    /// Get whether if company has set subscription currency.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <returns>Boolean.</returns>
    Task<bool> IsSubscriptionCurrencyHasValue(string companyId);

    /// <summary>
    /// Update company's maximum agents limit.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="maximumAgents">Maximum agents limit.</param>
    /// <returns>Affected Rows.</returns>
    Task<int> UpdateMaximumAgentsAsync(string companyId, int maximumAgents);

    /// <summary>
    /// Update company's maximum contacts limit.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="maximumContacts">Maximum contacts limit.</param>
    /// <returns>Affected Rows.</returns>
    Task<int> UpdateMaximumContactsAsync(string companyId, int maximumContacts);

    /// <summary>
    /// Update company's maximum automations limit.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="maximumAutomations">Maximum automations limit.</param>
    /// <returns>Affected Rows.</returns>
    Task<int> UpdateMaximumAutomationsAsync(string companyId, int maximumAutomations);

    /// <summary>
    /// Update company's maximum broadcast message limit.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="maximumBroadcastMessage">Maximum broadcast message limit.</param>
    /// <returns>Affected Rows.</returns>
    Task<int> UpdateMaximumBroadcastMessageAsync(string companyId, int maximumBroadcastMessage);

    /// <summary>
    /// Update company's maximum WhatsApp instances limit.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="maximumWhatsAppInstances">Maximum WhatsApp instances limit.</param>
    /// <returns>Affected Rows.</returns>
    Task<int> UpdateMaximumWhatsAppInstancesAsync(string companyId, int maximumWhatsAppInstances);

    /// <summary>
    /// Update Company's CompanyUsageLimitOffsetProfile.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="profile">CompanyUsageLimitOffsetProfile to update.</param>
    /// <returns>Affected Rows.</returns>
    Task<int> UpdateCompanyUsageLimitOffsetProfileAsync(string companyId, CompanyUsageLimitOffsetProfile profile);
}