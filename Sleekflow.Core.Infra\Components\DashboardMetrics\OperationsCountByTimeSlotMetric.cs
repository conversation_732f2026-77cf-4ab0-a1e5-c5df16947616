﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class OperationsCountByTimeSlotMetric : IDashboardMetric
{
    private readonly Output<string>? _resourceId;
    private readonly Output<string>? _resourceName;

    public OperationsCountByTimeSlotMetric(Output<string>? resourceId, Output<string>? resourceName)
    {
        _resourceId = resourceId;
        _resourceName = resourceName;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return
            new DashboardPartsArgs
            {
                Position = position,
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                        new[]
                        {
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "ComponentId"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "ControlType"
                                },
                                {
                                    "value", "FrameControlChart"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "DashboardId"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "Dimensions"
                                },
                                {
                                    "value", new Dictionary<string, object>()
                                    {
                                        {
                                            "aggregation", "Sum"
                                        },
                                        {
                                            "splitBy", new[]
                                            {
                                                new Dictionary<string, object>()
                                                {
                                                    {
                                                        "name", "operation_Name"
                                                    },
                                                    {
                                                        "type", "string"
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            "xAxis", new Dictionary<string, object>()
                                            {
                                                {
                                                    "name", "TimeSlot"
                                                },
                                                {
                                                    "type", "datetime"
                                                }
                                            }
                                        },
                                        {
                                            "yAxis", new[]
                                            {
                                                new Dictionary<string, object>()
                                                {
                                                    {
                                                        "name", "count_"
                                                    },
                                                    {
                                                        "type", "long"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "DraftRequestParameters"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "IsQueryContainTimeRange"
                                },
                                {
                                    "value", false
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "LegendOptions"
                                },
                                {
                                    "value", new Dictionary<string, object>()
                                    {
                                        {
                                            "isEnabled", true
                                        },
                                        {
                                            "position", "Bottom"
                                        }
                                    }
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "PartSubTitle"
                                },
                                {
                                    "value", _resourceName!
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "PartTitle"
                                },
                                {
                                    "value", "OperationsCountByTimeSlot"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "Query"
                                },
                                {
                                    "value",
                                    "let timeGrain = 1m;\nlet OperationsStats=requests\n    | where client_Type != \"Browser\"\n        and operation_Name !contains \"swagger\"\n        and operation_Name != \"GET /\"\n    | summarize count_=sum(itemCount), totalTime=sum(duration) by operation_Name, bin(timestamp, timeGrain)\n    | extend averageResponseTime = totalTime / count_\n    | project operation_Name, timestamp, averageResponseTime, count_\n    | sort by operation_Name, timestamp asc;\nlet Top50SlowOperations = OperationsStats\n    | serialize\n    | extend\n        prev_averageResponseTime = prev(averageResponseTime, 1),\n        prev_timestamp = prev(timestamp, 1),\n        prev_operation_Name = prev(operation_Name, 1)\n    | where prev_operation_Name == operation_Name\n    | extend responseTimeIncrease = averageResponseTime - prev_averageResponseTime\n    | extend responseTimeIncreasePercentage = (responseTimeIncrease / prev_averageResponseTime) * 100\n    | summarize max(responseTimeIncreasePercentage), sum(count_) by operation_Name\n    | top 50 by max_responseTimeIncreasePercentage desc   \n    | project operation_Name;\nrequests\n| where client_Type != \"Browser\" and operation_Name in (Top50SlowOperations)\n| summarize count_ = sum(itemCount) by operation_Name, TimeSlot = bin(timestamp, timeGrain)\n| render timechart;\n"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "Scope"
                                },
                                {
                                    "value", new Dictionary<string, object>()
                                    {
                                        {
                                            "resourceIds", new[]
                                            {
                                                _resourceId!
                                            }
                                        }
                                    }
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "SpecificChart"
                                },
                                {
                                    "value", "Line"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "TimeRange"
                                },
                                {
                                    "value", "P1D"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "Version"
                                },
                                {
                                    "value", "2.0"
                                },
                                {
                                    "isOptional", true
                                }
                            },
                            new Dictionary<string, object>()
                            {
                                {
                                    "name", "resourceTypeMode"
                                },
                                {
                                    "isOptional", true
                                }
                            }
                        },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                        new Dictionary<string, object>()
                        {
                        }
                    },
                }
            };
    }
}