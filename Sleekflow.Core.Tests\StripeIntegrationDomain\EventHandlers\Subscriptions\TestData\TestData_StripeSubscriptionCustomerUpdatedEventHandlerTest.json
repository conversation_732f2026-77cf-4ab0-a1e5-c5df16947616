{"Test_UpdateCustomerBillingAddressCountryFromEmptyToMalaysia_InvoiceFooterUpdatedToMY": {"object": {"id": "cus_SJyQm1o1RS7Tk1", "object": "customer", "address": {"city": "", "country": "MY", "line1": "", "line2": "", "postal_code": "", "state": ""}}, "previous_attributes": {"address": null}}, "Test_UpdateCustomerInvoicePrefix_NoInvoiceFooterUpdated": {"object": {"id": "cus_SJyQm1o1RS7Tk1", "object": "customer", "address": {"city": "", "country": "MY", "line1": "", "line2": "", "postal_code": "", "state": ""}, "invoice_prefix": "ORVKMPDC"}, "previous_attributes": {"invoice_prefix": "ORVKMPDF"}}, "Test_UpdateCustomerBillingAddressCountryFromMalaysiaToHongKong_InvoiceFooterUpdatedToHK": {"object": {"id": "cus_SJyQm1o1RS7Tk1", "object": "customer", "address": {"city": "", "country": "HK", "line1": "", "line2": "", "postal_code": "", "state": ""}}, "previous_attributes": {"address": {"country": "MY"}}}, "Test_UpdateCustomerBillingAddressState_NoInvoiceFooterUpdated": {"object": {"id": "cus_SJyQm1o1RS7Tk1", "address": {"city": "", "country": "HK", "line1": "", "line2": "", "postal_code": "", "state": "Hong Kong"}}, "previous_attributes": {"address": {"state": ""}}}, "Test_RemoveCustomerBillingAddress_UpdateInvoiceFooterEmpty": {"object": {"id": "cus_SJyQm1o1RS7Tk1", "object": "customer", "address": null}, "previous_attributes": {"address": {"city": "", "country": "HK", "line1": "", "line2": "", "postal_code": "", "state": "Hong Kong"}}}}