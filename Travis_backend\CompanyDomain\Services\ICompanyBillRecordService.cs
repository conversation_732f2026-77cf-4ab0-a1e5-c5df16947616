using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;

namespace Travis_backend.CompanyDomain.Services;

/// <summary>
/// Company BillRecords related service.
/// </summary>
public interface ICompanyBillRecordService
{
    /// <summary>
    /// Create Company BillRecord.
    /// </summary>
    /// <param name="billRecord">BillRecord.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    Task CreateBillRecordAsync(BillRecord billRecord);

    /// <summary>
    /// Query if company has BillRecord that match predicate.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="predicate">Predicate of Query.</param>
    /// <returns>Boolean that indicate if company has BillRecord that match predicate.</returns>
    Task<bool> HasBillRecordAsync(string companyId, Expression<Func<BillRecord, bool>> predicate);

    /// <summary>
    /// Query if company has BillRecord that valid and the match predicate.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="predicate">Predicate of Query.</param>
    /// <returns>Boolean that indicate if company has BillRecord that match predicate.</returns>
    Task<bool> HasValidBillRecordAsync(string companyId, Expression<Func<BillRecord, bool>> predicate);

    /// <summary>
    /// Query company's last BillRecord that match the predicate.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="extraPredication">Predicate of Query.</param>
    /// <returns>BillRecord.</returns>
    Task<BillRecord?> GetLastBillRecordAsync(string companyId, Expression<Func<BillRecord, bool>> extraPredication = null);

    /// <summary>
    /// Query company BillRecords that valid and the match predicate.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="extraPredication">Predicate of Query.</param>
    /// <returns>Collection of BillRecords.</returns>
    Task<IReadOnlyCollection<BillRecord>> GetValidBillRecordsAsync(string companyId, Expression<Func<BillRecord, bool>> extraPredication = null);

    /// <summary>
    /// Delete company's BillRecord that match predication.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="predicate">Predicate of Query.</param>
    /// <returns>Count of affected row.</returns>
    Task<int> DeleteBillRecordsAsync(string companyId, Expression<Func<BillRecord, bool>> predicate);
}