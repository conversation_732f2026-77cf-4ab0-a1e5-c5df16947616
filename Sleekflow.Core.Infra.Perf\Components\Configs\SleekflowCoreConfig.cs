using Newtonsoft.Json;
using Sleekflow.Core.Infra.Perf.Components.Configs.SleekflowCore;
using Sleekflow.Core.Infra.Perf.Components.Configs.SleekflowCore.SleekPay;
using StripeConfig = Sleekflow.Core.Infra.Perf.Components.Configs.SleekflowCore.StripeConfig;

namespace Sleekflow.Core.Infra.Perf.Components.Configs;

public class SleekflowCoreConfig
{
    [JsonProperty("aspnetcore_environment")]
    public string AspnetcoreEnvironment { get; set; }

    [JsonProperty("application_insights")]
    public ApplicationInsightsConfig ApplicationInsightsConfig { get; set; }

    [JsonProperty("audit_hub")]
    public V2Config AuditHub { get; set; }

    [JsonProperty("auth0")]
    public Auth0Config Auth0 { get; set; }

    [JsonProperty("azure")]
    public AzureConfig Azure { get; set; }

    [Json<PERSON>roperty("beamer")]
    public BeamerConfig Beamer { get; set; }

    [JsonProperty("chat_api")]
    public ChatApiConfig ChatApi { get; set; }

    [JsonProperty("commerce_hub")]
    public V2Config CommerceHub { get; set; }

    [JsonProperty("crm_hub")]
    public V2Config CrmHub { get; set; }

    [JsonProperty("data_snapshot")]
    public DataSnapshotConfig DataSnapshot { get; set; }

    [JsonProperty("development")]
    public DevelopmentConfig Development { get; set; }

    [JsonProperty("environment_features")]
    public EnvironmentFeaturesConfig EnvironmentFeatures { get; set; }

    [JsonProperty("epplus")]
    public EPPlusConfig EpPlus { get; set; }

    [JsonProperty("facebook")]
    public FacebookConfig Facebook { get; set; }

    [JsonProperty("ffmpeg")]
    public FFMpegConfig FFMpeg { get; set; }

    [JsonProperty("flow_hub")]
    public V2Config FlowHub { get; set; }

    [JsonProperty("hub_spot")]
    public HubSpotConfig HubSpot { get; set; }

    [JsonProperty("instrumentation_engine_extension_version")]
    public string InstrumentationEngineExtensionVersion { get; set; }

    [JsonProperty("internal_google_cloud")]
    public InternalGoogleCloud InternalGoogleCloud { get; set; }

    [JsonProperty("general_google_cloud")]
    public GeneralGoogleCloud GeneralGoogleCloud { get; set; }

    [JsonProperty("ip_look_up")]
    public IpLookUp IpLookUp { get; set; }

    [JsonProperty("intelligent_hub")]
    public V2Config IntelligentHub { get; set; }

    [JsonProperty("webhook_hub")]
    public WebhookHubConfig WebhookHub { get; set; }

    [JsonProperty("logger")]
    public LoggerConfig Logger { get; set; }

    [JsonProperty("message_hub")]
    public V2Config MessagingHub { get; set; }

    [JsonProperty("mixpanel")]
    public MixpanelConfig Mixpanel { get; set; }

    [JsonProperty("mobile_app_management_extension_version")]
    public string MobileAppsManagementExtensionVersion { get; set; }

    [JsonProperty("notification_hub")]
    public NotificationHubConfig NotificationHub { get; set; }

    [JsonProperty("public_api_gateway")]
    public V2Config PublicApiGateway { get; set; }

    [JsonProperty("reseller")]
    public ResellerConfig Reseller { get; set; }

    [JsonProperty("rewardful")]
    public RewardfulConfig Rewardful { get; set; }

    [JsonProperty("salesforce")]
    public SalesforceConfig Salesforce { get; set; }

    [JsonProperty("share_hub")]
    public V2Config ShareHub { get; set; }

    [JsonProperty("shopify")]
    public ShopifyConfig Shopify { get; set; }

    [JsonProperty("snapshot_debugger_extension_version")]
    public string SnapshotDebuggerExtensionVersion { get; set; }

    [JsonProperty("sql_performance")]
    public SqlPerformanceConfig SqlPerformance { get; set; }

    [JsonProperty("stripe")]
    public StripeConfig Stripe { get; set; }

    [JsonProperty("stripe_payment")]
    public StripePaymentConfig StripePayment { get; set; }

    [JsonProperty("stripe_report")]
    public StripeReportConfig StripeReport { get; set; }

    [JsonProperty("tenant_hub")]
    public TenantHubConfig TenantHub { get; set; }

    [JsonProperty("ticketing_hub")]
    public TicketingHubConfig TicketingHub { get; set; }

    [JsonProperty("test_swaping")]
    public string TestSwaping { get; set; }

    [JsonProperty("token")]
    public TokenConfig Token { get; set; }

    [JsonProperty("tokens")]
    public TokensConfig Tokens { get; set; }

    [JsonProperty("user_event_hub")]
    public V2Config UserEventHub { get; set; }

    [JsonProperty("values")]
    public ValuesConfig Values { get; set; }

    [JsonProperty("website_http_logging_retention_days")]
    public string WebsiteHttpLoggingRetentionDays { get; set; }

    [JsonProperty("website_node_default_version")]
    public string WebsiteNodeDefaultVersion { get; set; }

    [JsonProperty("whatsapp_cloud_api_template")]
    public WhatsAppCloudApiTemplate WhatsAppCloudApiTemplate { get; set; }

    [JsonProperty("xdt_microsoft_application_insights_base_extensions")]
    public string XdtMicrosoftApplicationInsightsBaseExtensions { get; set; }

    [JsonProperty("xdt_microsoft_application_insight_mode")]
    public string XdtMicrosoftApplicationInsightsMode { get; set; }

    [JsonProperty("contact_safe_deletion")]
    public ContactSafeDeletionConfig ContactSafeDeletionConfig { get; set; }

    [JsonProperty("global_pricing")]
    public GlobalPricingConfig GlobalPricingConfig { get; set; }

    [JsonProperty("feature_flags")]
    public IEnumerable<FeatureFlag> FeatureFlags { get; set; }

    [JsonProperty("partner_stack")]
    public PartnerStackConfig PartnerStackConfig { get; set; }

    [JsonProperty("hangfire_worker")]
    public HangfireWorkerConfig HangfireWorkerConfig { get; set; }

    [JsonProperty("internal_integration_hub")]
    public V2Config InternalIntegrationHub { get; set; }

    [JsonProperty("hangfire_queues")]
    public HangfireQueuesConfig HangfireQueuesConfig { get; set; }

    [JsonProperty("integration_alert")]
    public IntegrationAlertConfig IntegrationAlertConfig { get; set; }

    [JsonProperty("rbac")]
    public RbacConfig RbacConfig { get; set; }

    [JsonProperty("hub_spot_smtp")]
    public HubSpotSmtpConfig HubSpotSmtp { get; set; }

    public SleekflowCoreConfig(
        string aspnetcoreEnvironment,
        ApplicationInsightsConfig applicationInsightsConfig,
        V2Config auditHub,
        Auth0Config auth0,
        AzureConfig azure,
        BeamerConfig beamer,
        ChatApiConfig chatApi,
        V2Config commerceHub,
        V2Config crmHub,
        DataSnapshotConfig dataSnapshot,
        DevelopmentConfig development,
        EnvironmentFeaturesConfig environmentFeaturesConfig,
        EPPlusConfig epPlus,
        FacebookConfig facebook,
        FFMpegConfig ffMpeg,
        V2Config flowHub,
        HubSpotConfig hubSpot,
        string instrumentationEngineExtensionVersion,
        InternalGoogleCloud internalGoogleCloud,
        GeneralGoogleCloud generalGoogleCloud,
        IpLookUp ipLookUp,
        V2Config intelligentHub,
        WebhookHubConfig webhookHub,
        LoggerConfig logger,
        V2Config messagingHub,
        MixpanelConfig mixpanel,
        string mobileAppsManagementExtensionVersion,
        NotificationHubConfig notificationHub,
        V2Config publicApiGateway,
        ResellerConfig reseller,
        RewardfulConfig rewardful,
        SalesforceConfig salesforce,
        ShopifyConfig shopify,
        string snapshotDebuggerExtensionVersion,
        SqlPerformanceConfig sqlPerformance,
        StripeConfig stripe,
        StripePaymentConfig stripePayment,
        StripeReportConfig stripeReport,
        TenantHubConfig tenantHub,
        TicketingHubConfig ticketingHub,
        string testSwaping,
        TokenConfig token,
        TokensConfig tokens,
        V2Config userEventHub,
        ValuesConfig values,
        string websiteHttpLoggingRetentionDays,
        string websiteNodeDefaultVersion,
        WhatsAppCloudApiTemplate whatsAppCloudApiTemplate,
        string xdtMicrosoftApplicationInsightsBaseExtensions,
        string xdtMicrosoftApplicationInsightsMode,
        ContactSafeDeletionConfig contactSafeDeletionConfig,
        GlobalPricingConfig globalPricingConfig,
        IEnumerable<FeatureFlag> featureFlags,
        PartnerStackConfig partnerStackConfig,
        HangfireWorkerConfig hangfireWorkerConfig,
        V2Config internalIntegrationHub,
        HangfireQueuesConfig hangfireQueuesConfig,
        IntegrationAlertConfig integrationAlertConfig,
        RbacConfig rbacConfig,
        HubSpotSmtpConfig hubSpotSmtp)
    {
        AspnetcoreEnvironment = aspnetcoreEnvironment;
        ApplicationInsightsConfig = applicationInsightsConfig;
        AuditHub = auditHub;
        Auth0 = auth0;
        Azure = azure;
        Beamer = beamer;
        ChatApi = chatApi;
        CommerceHub = commerceHub;
        CrmHub = crmHub;
        DataSnapshot = dataSnapshot;
        Development = development;
        EnvironmentFeatures = environmentFeaturesConfig;
        EpPlus = epPlus;
        Facebook = facebook;
        FFMpeg = ffMpeg;
        FlowHub = flowHub;
        HubSpot = hubSpot;
        InstrumentationEngineExtensionVersion = instrumentationEngineExtensionVersion;
        InternalGoogleCloud = internalGoogleCloud;
        GeneralGoogleCloud = generalGoogleCloud;
        IpLookUp = ipLookUp;
        IntelligentHub = intelligentHub;
        WebhookHub = webhookHub;
        Logger = logger;
        MessagingHub = messagingHub;
        Mixpanel = mixpanel;
        MobileAppsManagementExtensionVersion = mobileAppsManagementExtensionVersion;
        NotificationHub = notificationHub;
        PublicApiGateway = publicApiGateway;
        Reseller = reseller;
        Rewardful = rewardful;
        Salesforce = salesforce;
        Shopify = shopify;
        SnapshotDebuggerExtensionVersion = snapshotDebuggerExtensionVersion;
        SqlPerformance = sqlPerformance;
        Stripe = stripe;
        StripePayment = stripePayment;
        StripeReport = stripeReport;
        TenantHub = tenantHub;
        TicketingHub = ticketingHub;
        TestSwaping = testSwaping;
        Token = token;
        Tokens = tokens;
        UserEventHub = userEventHub;
        Values = values;
        WebsiteHttpLoggingRetentionDays = websiteHttpLoggingRetentionDays;
        WebsiteNodeDefaultVersion = websiteNodeDefaultVersion;
        WhatsAppCloudApiTemplate = whatsAppCloudApiTemplate;
        XdtMicrosoftApplicationInsightsBaseExtensions = xdtMicrosoftApplicationInsightsBaseExtensions;
        XdtMicrosoftApplicationInsightsMode = xdtMicrosoftApplicationInsightsMode;
        ContactSafeDeletionConfig = contactSafeDeletionConfig;
        GlobalPricingConfig = globalPricingConfig;
        FeatureFlags = featureFlags;
        PartnerStackConfig = partnerStackConfig;
        HangfireWorkerConfig = hangfireWorkerConfig;
        InternalIntegrationHub = internalIntegrationHub;
        HangfireQueuesConfig = hangfireQueuesConfig;
        IntegrationAlertConfig = integrationAlertConfig;
        RbacConfig = rbacConfig;
        HubSpotSmtp = hubSpotSmtp;
    }
}