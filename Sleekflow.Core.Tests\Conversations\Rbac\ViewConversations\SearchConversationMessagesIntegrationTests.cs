using System.Linq.Expressions;
using LinqKit;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.MessageDomain.Models;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationQueryables;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;


namespace Sleekflow.Core.Tests.Conversations.Rbac.ViewConversations;

public class SearchConversationMessagesIntegrationTests
{
    private DbContextOptions<ConversationDbContext> _options;
    private Conversation _conversation;
    private string _companyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
    private string _status = "open";
    private RbacRole _adminRole;
    private RbacRole _teamAdminRole;
    private StaffAccessControlAggregate _staff;

    [SetUp]
    public void Setup()
    {
        _options = new DbContextOptionsBuilder<ConversationDbContext>()
            .UseInMemoryDatabase("TestConversationDb")
            .Options;

        _conversation = new Conversation
        {
            Id = "1",
            // AssigneeId = 1,
            CompanyId = _companyId,
            Status = _status,
            ActiveStatus = ActiveStatus.Active
        };

        using (var context = new ConversationDbContext(_options))
        {
            context.Conversations.Add(_conversation);
            context.ConversationMessages.Add(new ConversationMessage { Id = 1, ConversationId = "1", CompanyId = _companyId, MessageContent = "Test message" });

            context.SaveChanges();
        }

        _adminRole = new RbacRole
        {
            SleekflowRoleName = "Admin",
            SleekflowCompanyId = _companyId,
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllAssignedConversations,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations
            ]
        };

        _teamAdminRole = new RbacRole
        {
            SleekflowRoleName = "TeamAdmin",
            SleekflowCompanyId = _companyId,
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations
            ]
        };

        _staff = new StaffAccessControlAggregate
        {
            CompanyId = _companyId,
            StaffId = 15201,
            RbacRoles = [_teamAdminRole],
            RoleType = StaffUserRole.TeamAdmin
        };
    }

    [Test]
    public void AdaptedExpression_ShouldFilterConversationMessages()
    {
        var keywords = "Test message";
        var dateThreshold = DateTime.UtcNow.AddDays(-365);
        // .ToString("yyyy-MM-dd")
        var keywordsParameter = new SqlParameter(
            "@keywords",
            string.IsNullOrEmpty(keywords) ? string.Empty : keywords);


        var conversationExpressionStarter = PredicateBuilder.New<Conversation>(false);
        var conversationExpression =  conversationExpressionStarter.IsUnassigned();

        Expression<Func<ConversationMessage, bool>> adaptedExpression = cm => conversationExpression.Invoke(cm.Conversation);

        using (var context = new ConversationDbContext(_options))
        {
            // var filteredConversationMessageQueryable = new SearchConversationMessageQueryableBuilder(context);
            //
            // var result = filteredConversationMessageQueryable.ToList();
            //
            // Assert.That(
            //     result.Count,
            //     Is.EqualTo(1),
            //     $"Expected 1 conversation to match the specification for conversation");
        }
    }
}