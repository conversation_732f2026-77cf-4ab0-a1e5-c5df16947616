using Microsoft.Extensions.Configuration;
using NSubstitute;
using Travis_backend.Infrastructures.SleekFlowEnvironments;
using Travis_backend.Infrastructures.SleekFlowEnvironments.Enums;
// ReSharper disable InconsistentNaming

namespace Sleekflow.Core.Tests.Infrastructures.SleekFlowEnvironments;

public class SleekFlowEnvironmentTest
{
    #region Test_SleekFlowEnvironment_Success

    public record Test_SleekFlowEnvironment_Success_TestData(string Environment, string Region, SleekFlowEnvironmentName ExpectedEnvironment, SleekFlowRegionName ExpectedRegion);

    [Test, TestCaseSource(nameof(Test_SleekFlowEnvironment_Success_TestCases)), Description("Test SleekFlowEnvironment Success")]
    public void Test_SleekFlowEnvironment_Success(Test_SleekFlowEnvironment_Success_TestData testData)
    {
        //// Arrange
        var configuration = Substitute.For<IConfiguration>();
        configuration["SF_ENVIRONMENT"].Returns(testData.Environment);
        configuration["SF_REGION"].Returns(testData.Region);

        //// Act & Assert
        ISleekFlowEnvironment sleekFlowEnvironment = null!;
        Assert.DoesNotThrow(() => sleekFlowEnvironment = new SleekFlowEnvironment(configuration));
        Assert.That(sleekFlowEnvironment.Environment, Is.EqualTo(testData.ExpectedEnvironment));
        Assert.That(sleekFlowEnvironment.Region, Is.EqualTo(testData.ExpectedRegion));
    }

    public static IEnumerable<Test_SleekFlowEnvironment_Success_TestData> Test_SleekFlowEnvironment_Success_TestCases()
    {
        foreach (var environment in Enum.GetValues<SleekFlowEnvironmentName>())
        {
            foreach (var region in Enum.GetValues<SleekFlowRegionName>())
            {
                yield return new Test_SleekFlowEnvironment_Success_TestData($"{environment}", $"{region}", environment, region);
            }
        }
    }

    #endregion

    [Test, Description("Test SleekFlowEnvironment Failed - Invalid Environment")]
    public void Test_SleekFlowEnvironment_Failed_InvalidEnvironment()
    {
        //// Arrange
        var configuration = Substitute.For<IConfiguration>();
        configuration["SF_ENVIRONMENT"].Returns("invalid_environment");
        configuration["SF_REGION"].Returns($"{SleekFlowRegionName.EastAsia}");

        //// Act & Assert
        Assert.Throws<ArgumentException>(() => new SleekFlowEnvironment(configuration));
    }

    [Test, Description("Test SleekFlowEnvironment Failed - Invalid Region")]
    public void Test_SleekFlowEnvironment_Failed_InvalidRegion()
    {
        //// Arrange
        var configuration = Substitute.For<IConfiguration>();
        configuration["SF_ENVIRONMENT"].Returns($"{SleekFlowEnvironmentName.Dev}");
        configuration["SF_REGION"].Returns("invalid_region");

        //// Act & Assert
        Assert.Throws<ArgumentException>(() => new SleekFlowEnvironment(configuration));
    }
}