using System;
using System.Collections.Generic;

namespace Travis_backend.AnalyticsDomain.ViewModels;

public class GetConversationCommonMetricResponse
{
    public string StartDate { get; set; }

    public string EndDate { get; set; }

    public ConversationCommonMetricViewModel Summary { get; set; }

    public List<ConversationCommonMetricViewModel> DailyLogs { get; set; }

    public DateTime? LastUpdateTime { get; set; }

    public GetConversationCommonMetricResponse(
        DateOnly startDate,
        DateOnly endDate,
        ConversationCommonMetricViewModel summary,
        List<ConversationCommonMetricViewModel> dailyLogs,
        DateTime? lastUpdateTime = null)
    {
        StartDate = startDate.ToString("yyyy-MM-dd");
        EndDate = endDate.ToString("yyyy-MM-dd");
        Summary = summary;
        DailyLogs = dailyLogs;
        LastUpdateTime = lastUpdateTime;
    }
}