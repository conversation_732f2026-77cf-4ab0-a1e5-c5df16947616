using SW = Slack.Webhooks;

namespace Sleekflow.Powerflow.Apis.Utils.Slack;

public class SlackClient
{
    private readonly SW.SlackClient _slackClient;

    public SlackClient(string webhookUrl)
    {
        _slackClient = new SW.SlackClient(webhookUrl);
    }

    public SlackClient(SlackChannel channel)
    {
        switch (channel)
        {
            case SlackChannel.PowerFlowBankTransfer:
                _slackClient = new SW.SlackClient("*******************************************************************************");
                break;
            default:
                throw new ArgumentException("Invalid channel");
        }
    }

    public async Task SendMessageAsync(SW.SlackMessage message)
    {
        await _slackClient.PostAsync(message);
    }
}

public enum SlackChannel
{
    PowerFlowBankTransfer
}