using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Models;
using Travis_backend.Enums;
using Travis_backend.Helpers;

namespace Travis_backend.ContactDomain.Helpers
{
    public static class FieldValueUtil
    {
        public static void ValidateFieldValueFormat(
            string fieldValue,
            CompanyCustomUserProfileField companyDefinedField,
            List<StaffWithIdentity> existingStaffIdentities = null,
            List<long> existingTeamIds = null)
        {
            if (string.IsNullOrWhiteSpace(fieldValue))
            {
                return;
            }

            switch (companyDefinedField.Type)
            {
                case FieldDataType.Boolean:
                    if (!bool.TryParse(fieldValue, out _))
                    {
                        throw new ArgumentException($"Cannot parse {fieldValue} to {companyDefinedField.Type.ToString()}");
                    }

                    break;
                case FieldDataType.Date:
                case FieldDataType.DateTime:
                    if (!DateTime.TryParse(fieldValue, out _)
                        && !DateTime.TryParseExact(
                                fieldValue,
                                "dd/MM/yyyy",
                                new CultureInfo("en-US"),
                                DateTimeStyles.AllowWhiteSpaces,
                                out _))
                    {
                        throw new ArgumentException($"Cannot parse {fieldValue} to {companyDefinedField.Type.ToString()}");
                    }

                    break;
                case FieldDataType.Number:
                    if (!double.TryParse(fieldValue, out _))
                    {
                        throw new ArgumentException($"Cannot parse {fieldValue} to {companyDefinedField.Type.ToString()}");
                    }

                    break;
                case FieldDataType.TravisUser:
                    if (existingStaffIdentities == null ||
                            (
                            !existingStaffIdentities.Exists(x =>
                                string.Equals(
                                    x.DisplayName,
                                    fieldValue,
                                    StringComparison.CurrentCultureIgnoreCase)) &&
                            !existingStaffIdentities.Exists(x =>
                                x.IdentityId == fieldValue) &&
                            !existingStaffIdentities.Exists(x =>
                                x.UserName == fieldValue)
                            )
                        )
                    {
                        throw new ArgumentException(
                            $"Cannot parse {fieldValue} to {companyDefinedField.Type.ToString()}");
                    }

                    break;
                case FieldDataType.Team:
                    if (!long.TryParse(fieldValue, out var teamId)
                        || existingTeamIds == null
                        || !existingTeamIds.Contains(teamId))
                    {
                        throw new ArgumentException($"Cannot parse {fieldValue} to {companyDefinedField.Type.ToString()}");
                    }

                    break;
                case FieldDataType.Options:
                    if (companyDefinedField.FieldName.Equals("AssignedTeam", StringComparison.OrdinalIgnoreCase)
                        && (!long.TryParse(fieldValue, out var optionTeamId)
                            || existingTeamIds == null
                            || !existingTeamIds.Contains(optionTeamId)))
                    {
                        throw new ArgumentException($"Cannot parse {fieldValue} to {companyDefinedField.Type.ToString()}");
                    }

                    break;
            }
        }

        public static string NormalizeFieldValue(
            string fieldValue,
            CompanyCustomUserProfileField companyDefinedField,
            string companyTimeZoneId,
            List<StaffWithIdentity> existingStaffIdentities = null)
        {
            return companyDefinedField.Type switch
            {
                FieldDataType.Date or FieldDataType.DateTime =>
                    DateTime.TryParse(fieldValue, out var dateTime)
                        && !string.IsNullOrEmpty(companyTimeZoneId)
                        && !IsUtcFormat(fieldValue) // already in correct UTC format
                            ? dateTime.Tolocaltime(companyTimeZoneId).ToString("o") + "Z"
                            : fieldValue,

                FieldDataType.TravisUser =>
                    existingStaffIdentities?
                        .Find(x => string.Equals(x.DisplayName, fieldValue, StringComparison.CurrentCultureIgnoreCase) || x.IdentityId == fieldValue || x.UserName == fieldValue)?.IdentityId
                        ?? null,

                FieldDataType.Labels =>
                    string.IsNullOrEmpty(fieldValue)
                        ? null
                        : string.Join(
                            ";",
                            fieldValue
                                .Split(";", StringSplitOptions.RemoveEmptyEntries)
                                .Select(x => x.ToLower())
                                .Distinct()),

                FieldDataType.Collaborators =>
                    string.IsNullOrEmpty(fieldValue) || existingStaffIdentities == null
                        ? null
                        : string.Join(
                            ";",
                            fieldValue
                                .Split(";", StringSplitOptions.RemoveEmptyEntries)
                                .Distinct()
                                .Select(x => existingStaffIdentities.Find(y => y.DisplayName == x)?.Id)
                                .Where(id => id != null)),

                _ => fieldValue
            };
        }

        private static bool IsUtcFormat(string value)
        {
            if (string.IsNullOrEmpty(value)) return false;
            return value.EndsWith("Z", StringComparison.OrdinalIgnoreCase) || value.Contains("+00:00");
        }
    }
}