﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Repositories;

namespace Travis_backend.BroadcastDomain.Services;

public interface IBroadcastChannelService
{
    Task MapChannelIdentityIdAsync(CompanyMessageTemplate companyMessageTemplate);

    Task<bool> PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync(
        CompanyMessageTemplate companyMessageTemplate);
}

public class BroadcastChannelService : IBroadcastChannelService
{
    private readonly ILogger<BroadcastChannelService> _logger;

    private readonly IChannelIdentityIdRepository _channelIdentityIdRepository;

    private readonly IWhatsappCloudApiService _whatsappCloudApiService;

    public BroadcastChannelService(
        ILogger<BroadcastChannelService> logger,
        IChannelIdentityIdRepository channelIdentityIdRepository,
        IWhatsappCloudApiService whatsappCloudApiService)
    {
        _logger = logger;
        _channelIdentityIdRepository = channelIdentityIdRepository;
        _whatsappCloudApiService = whatsappCloudApiService;
    }

    public async Task MapChannelIdentityIdAsync(CompanyMessageTemplate companyMessageTemplate)
    {
        var targetedChannel = companyMessageTemplate.TargetedChannelWithIds?
            .FirstOrDefault();

        if (targetedChannel is not null)
        {
            var channelType = targetedChannel.channel?.Replace("twilio_whatsapp", ChannelTypes.WhatsappTwilio);

            companyMessageTemplate.TargetedChannel = new TargetedChannel
            {
                ChannelType = channelType,
                ChannelIdentityId = await _channelIdentityIdRepository.GetChannelIdentityIdByTargetedChannelModel(
                    companyMessageTemplate.CompanyId,
                    channelType,
                    targetedChannel.ids?.FirstOrDefault())
            };
        }
        else
        {
            companyMessageTemplate.TargetedChannel = new TargetedChannel();
        }

        var channelMessage = companyMessageTemplate.CampaignChannelMessages?
            .FirstOrDefault();

        if (channelMessage is not null)
        {
            channelMessage.TargetedChannel = companyMessageTemplate.TargetedChannel;
        }
    }

    public async Task<bool> PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync(
        CompanyMessageTemplate companyMessageTemplate)
    {
        var isCompanyMessageTemplateModified = false;
        try
        {
            var whatsappCloudApiMessages = companyMessageTemplate.CampaignChannelMessages
                .Where(x => x.TargetedChannel.ChannelType == ChannelTypes.WhatsappCloudApi)
                .ToList();
            foreach (var campaignChannelMessage in whatsappCloudApiMessages)
            {
                var templateHeaderComponent = campaignChannelMessage?.ExtendedMessagePayloadDetail
                    ?.WhatsappCloudApiTemplateMessageObject
                    ?.Components
                    ?.FirstOrDefault(
                        c => string.Equals(c.Type, "header", StringComparison.CurrentCultureIgnoreCase));

                if (templateHeaderComponent is { Parameters: not null })
                {
                    foreach (var parameter in templateHeaderComponent.Parameters)
                    {
                        switch (parameter.Type.ToLower())
                        {
                            case "document":
                                parameter.Document.Id = await _whatsappCloudApiService
                                    .UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(
                                        companyMessageTemplate.CompanyId,
                                        campaignChannelMessage.TargetedChannel.ChannelIdentityId,
                                        parameter.Document?.Link);
                                isCompanyMessageTemplateModified = true;
                                break;
                            case "image":
                                parameter.Image.Id = await _whatsappCloudApiService
                                    .UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(
                                        companyMessageTemplate.CompanyId,
                                        campaignChannelMessage.TargetedChannel.ChannelIdentityId,
                                        parameter.Image?.Link);
                                isCompanyMessageTemplateModified = true;
                                break;
                            case "video":
                                parameter.Video.Id = await _whatsappCloudApiService
                                    .UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(
                                        companyMessageTemplate.CompanyId,
                                        campaignChannelMessage.TargetedChannel.ChannelIdentityId,
                                        parameter.Video?.Link);
                                isCompanyMessageTemplateModified = true;
                                break;
                        }
                    }
                }
            }

            return isCompanyMessageTemplateModified;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error populating Whatsapp Cloud API media");
        }

        return false;
    }
}