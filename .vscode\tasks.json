{
    "version": "2.0.0",
    "tasks": [
        // Build Tasks
        {
            "label": "build",
            "type": "shell",
            "command": "dotnet",
            "args": ["build", "${workspaceFolder}/<PERSON>_backend/<PERSON>_backend.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": "$dotnet"
        },
        {
            "label": "build-all",
            "type": "shell",
            "command": "dotnet",
            "args": ["build"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": "$dotnet"
        },

        // Specific Build Tasks for Debug Configurations
        {
            "label": "build-travis-backend",
            "type": "shell",
            "command": "dotnet",
            "args": ["build", "${workspaceFolder}/<PERSON>_backend/Travis_backend.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        },
        {
            "label": "build-travis-backend-auth0",
            "type": "shell",
            "command": "dotnet",
            "args": ["build", "${workspaceFolder}/Travis_backend.Auth0/Travis_backend.Auth0.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        },
        {
            "label": "build-powerflow-apis",
            "type": "shell",
            "command": "dotnet",
            "args": ["build", "${workspaceFolder}/Sleekflow.Powerflow.Apis/Sleekflow.Powerflow.Apis.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        },
        {
            "label": "build-data-migrator",
            "type": "shell",
            "command": "dotnet",
            "args": ["build", "${workspaceFolder}/Sleekflow.Core.DataMigrator/Sleekflow.Core.DataMigrator.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        },
        {
            "label": "build-core-exporter",
            "type": "shell",
            "command": "dotnet",
            "args": ["build", "${workspaceFolder}/Sleekflow.Core.Exporter/Sleekflow.Core.Exporter.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        },
        {
            "label": "build-core-benchmarks",
            "type": "shell",
            "command": "dotnet",
            "args": ["build", "${workspaceFolder}/Sleekflow.Core.Benchmarks/Sleekflow.Core.Benchmarks.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        },
        {
            "label": "build-core-stress-tests",
            "type": "shell",
            "command": "dotnet",
            "args": ["build", "${workspaceFolder}/Sleekflow.Core.StressTests/Sleekflow.Core.StressTests.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        },
        {
            "label": "build-core-infra",
            "type": "shell",
            "command": "dotnet",
            "args": ["build", "${workspaceFolder}/Sleekflow.Core.Infra/Sleekflow.Core.Infra.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        },
        {
            "label": "build-core-infra-perf",
            "type": "shell",
            "command": "dotnet",
            "args": ["build", "${workspaceFolder}/Sleekflow.Core.Infra.Perf/Sleekflow.Core.Infra.Perf.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        },
        {
            "label": "build-sleekpay",
            "type": "shell",
            "command": "dotnet",
            "args": ["build", "${workspaceFolder}/Sleekflow.SleekPay/Sleekflow.SleekPay.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        },

        // Test Tasks
        {
            "label": "test-all",
            "type": "shell",
            "command": "dotnet",
            "args": ["test", "${workspaceFolder}/Sleekflow.Core.Tests/Sleekflow.Core.Tests.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        },
        {
            "label": "test-sleekpay",
            "type": "shell",
            "command": "dotnet",
            "args": ["test", "${workspaceFolder}/Sleekflow.SleekPay.Tests/Sleekflow.SleekPay.Tests.csproj"],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": "$dotnet"
        }
    ]
}