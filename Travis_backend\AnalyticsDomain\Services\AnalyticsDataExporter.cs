﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.ViewModels;
using Travis_backend.BackgroundTaskServices.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.FileDomain.Services;

namespace Travis_backend.AnalyticsDomain.Services;

public interface IAnalyticsDataExporter
{
    Task<ExportConversationAnalyticListToCsvBackgroundTaskResultPayload> ExportConversationAnalyticsDataAsync(
        string companyId,
        DateOnly from,
        DateOnly to,
        List<Condition> conditions,
        AdvancedFilter advancedFilter,
        bool usingBusinessHour,
        CancellationToken cancellationToken,
        Func<int, ValueTask> onComplete = null);
}

public class AnalyticsDataExporter : IAnalyticsDataExporter
{
    private readonly IAnalyticsService _analyticsService;
    private readonly IConversationAnalyticsMetricsService _conversationAnalyticsMetricsService;
    private readonly ApplicationReadDbContext _applicationReadDbContext;
    private readonly IUploadService _uploadService;
    private readonly IAzureBlobStorageService _azureBlobStorageService;

    public AnalyticsDataExporter(
        IAnalyticsService analyticsService,
        IConversationAnalyticsMetricsService conversationAnalyticsMetricsService,
        ApplicationReadDbContext applicationReadDbContext,
        IUploadService uploadService,
        IAzureBlobStorageService azureBlobStorageService)
    {
        _analyticsService = analyticsService;
        _conversationAnalyticsMetricsService = conversationAnalyticsMetricsService;
        _applicationReadDbContext = applicationReadDbContext;
        _uploadService = uploadService;
        _azureBlobStorageService = azureBlobStorageService;
    }

    public async Task<ExportConversationAnalyticListToCsvBackgroundTaskResultPayload>
        ExportConversationAnalyticsDataAsync(
            string companyId,
            DateOnly from,
            DateOnly to,
            List<Condition> conditions,
            AdvancedFilter advancedFilter,
            bool usingBusinessHour,
            CancellationToken cancellationToken,
            Func<int, ValueTask> onComplete = null)
    {
        var commonMetricDailyLogs = (await _conversationAnalyticsMetricsService.GetCommonMetricDailyLogsAsync(
            companyId,
            from,
            to,
            conditions,
            advancedFilter,
            true,
            usingBusinessHour,
            cancellationToken))
            .ToDictionary(metric => metric.PeriodStartDate?.ToString("O"));

        // we assert backgroundTask.Total = 100
        // this is checkpoint for 40% progress
        if (onComplete != null)
        {
            await onComplete(40);
        }

        var broadcastMessageMetricDailyLogs = (await _analyticsService.GetBroadcastMessageMetricDailyLogsAsync(
            companyId,
            from,
            to,
            true,
            cancellationToken))
            .ToDictionary(metric => metric.Date);

        if (onComplete != null)
        {
            await onComplete(80);
        }

        // Prepare CSV header
        var csvHeader = GetConversationAnalyticsDataCsvHeader();
        var csvBuilder = new StringBuilder();
        csvBuilder.AppendLine(csvHeader);

        // Combine metrics into CSV format
        for (var date = from; date <= to; date = date.AddDays(1))
        {
            var dateStr = date.ToString("O");

            if (!commonMetricDailyLogs.TryGetValue(dateStr, out var commonMetric))
            {
                commonMetric = ConversationAnalyticsMetricDto.Default();
            }

            if (!broadcastMessageMetricDailyLogs.TryGetValue(dateStr, out var broadcastMetric))
            {
                broadcastMetric = BroadcastMessageMetricViewModel.Default();
            }

            var conversationAnalyticsData = new ConversationAnalyticsFileData(
                date,
                commonMetric.ToConversationCommonMetricViewModel(),
                broadcastMetric);

            var csvLine = conversationAnalyticsData.ToCsvLine();
            csvBuilder.AppendLine(csvLine);
        }

        var companyName = await _applicationReadDbContext.CompanyCompanies
            .Where(x => x.Id == companyId)
            .Select(x => x.CompanyName)
            .FirstAsync(cancellationToken: cancellationToken);

        var isFilterAppliedFileName = advancedFilter != null && advancedFilter.Filters.Count > 0 ? "filter_applied_" : string.Empty;
        var fileName = $"{isFilterAppliedFileName}conversation_report_{DateTimeOffset.UtcNow:yyyy-MM-dd}.csv";
        var filePath = $"ExportAnalytic/Conversation/{companyId}/{fileName}";

        var csvUploadResult = await UploadCsvToBlobAndGetDownloadUriAsync(
            companyId,
            filePath,
            csvBuilder,
            cancellationToken);

        if (onComplete != null)
        {
            await onComplete(90);
        }

        return new ExportConversationAnalyticListToCsvBackgroundTaskResultPayload
        {
            FileName = fileName,
            FilePath = filePath,
            MIMEType = "text/csv",
            Url = csvUploadResult.DownloadUri,
            FileSize = csvUploadResult.FileSizeInByte
        };
    }

    private async Task<CsvUploadResult> UploadCsvToBlobAndGetDownloadUriAsync(
        string companyId,
        string filePath,
        StringBuilder sb,
        CancellationToken cancellationToken)
    {
        var storageConfig = await _applicationReadDbContext.ConfigStorageConfigs
            .AsNoTracking()
            .FirstOrDefaultAsync(
                x => x.CompanyId == companyId,
                cancellationToken: cancellationToken);

        using var ms = new MemoryStream();
        await using var sw = new StreamWriter(ms, Encoding.UTF8);
        await sw.WriteAsync(sb.ToString());
        await sw.FlushAsync(cancellationToken);
        ms.Seek(0, SeekOrigin.Begin);

        var uploadFileResult = await _uploadService.UploadFileBySteam(
            storageConfig?.ContainerName ?? companyId,
            filePath,
            ms,
            "text/csv");

        var sasUri = _azureBlobStorageService.GetAzureBlobSasUri(
            filePath,
            storageConfig?.ContainerName ?? companyId,
            24);

        return new CsvUploadResult
        {
            DownloadUri = sasUri,
            FileSizeInByte = uploadFileResult.FileSizeInByte
        };
    }

    private static string GetConversationAnalyticsDataCsvHeader()
    {
        return string.Empty
               + ConversationAnalyticsFileData.PropertyNameDate + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfAllConversations + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfActiveConversations + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfMessagesSent + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfMessagesReceived + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfMessagesFailed + ","
               + ConversationAnalyticsFileData.PropertyNameResponseTimeForAllMessages + ","
               + ConversationAnalyticsFileData.PropertyNameResponseTimeForFirstMessages + ","
               + ConversationAnalyticsFileData.PropertyNameResolutionTime + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfNewEnquires + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfNewContacts + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfBroadcastSent + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfBroadcastBounced + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfBroadcastDelivered + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfBroadcastRead + ","
               + ConversationAnalyticsFileData.PropertyNameNumberOfBroadcastReplied;
    }

    private sealed record CsvUploadResult
    {
        public string DownloadUri { get; set; }

        public long FileSizeInByte { get; set; }
    }
}