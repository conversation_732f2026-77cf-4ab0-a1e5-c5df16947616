using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Models;

namespace Travis_backend.CompanyDomain.Models;

public class BillRecord
{
    public long Id { get; set; }

    public string CompanyId { get; set; }

    public string SubscriptionPlanId { get; set; }

    public SubscriptionPlan SubscriptionPlan { get; set; }

    public DateTime PeriodStart { get; set; }

    public DateTime PeriodEnd { get; set; }

    public BillStatus Status { get; set; }

    public PaymentStatus PaymentStatus { get; set; }

    public double PayAmount { get; set; }

    public Staff PurchaseStaff { get; set; }

    public long? PurchaseStaffId { get; set; }

    public string invoice_Id { get; set; }

    public string stripe_subscriptionId { get; set; }

    public string customerId { get; set; }

    public string customer_email { get; set; }

    public string hosted_invoice_url { get; set; }

    public string invoice_pdf { get; set; }

    public string chargeId { get; set; }

    public long amount_due { get; set; }

    public long amount_paid { get; set; }

    public long amount_remaining { get; set; }

    public string currency { get; set; }

    public DateTime created { get; set; } = DateTime.UtcNow;

    public Dictionary<string, string> metadata { get; set; } = new();

    public long quantity { get; set; }

    public long? UpgradeFromBillRecordId { get; set; }

    public long? DowngradeFromBillRecordId { get; set; }

    public long? ExtendFromBillRecordId { get; set; }

    public List<CmsSalesPaymentRecord> CmsSalesPaymentRecords { get; set; } = new();

    public DateTime? UpdatedAt { get; set; }

    public SubscriptionTier SubscriptionTier { get; set; }

    public string stripeId { get; set; }

    public bool IsFreeTrial { get; set; }

    public DateTime? UsageCycleStart { get; set; }

    public DateTime? UsageCycleEnd { get; set; }

    #region Irregular Plan

    // Indicates if the plan is irregular, meaning it does not follow a standard billing interval and payment interval.
    public bool IsIrregularPlan { get; set; }

    // Payment interval type
    // One-off/Monthly/Yearly/Custom
    public string PaymentIntervalType { get; set; }

    // Payment split in percentage
    public List<PaymentSplit> PaymentSplits { get; set; }

    // Payment terms in Days
    public int? PaymentTerms { get; set; }

    // Notice period in Days
    public int? NoticePeriod { get; set; }

    // Auto-renewal enabled for enterprise plans
    public bool IsAutoRenewalEnabled { get; set; }

    #endregion

    #region ShopifyPayment

    public long? ShopifyChargeId { get; set; }

    #endregion

    public bool PaidByReseller { get; set; }

    public bool IsCustomized { get; set; }

    public DateTime? StripeSubscriptionCancelDateTime { get; set; }

    public DateTime? TerminatedDateTime { get; set; }

    public string StripeSubscriptionItemId { get; set; }

    [NotMapped]
    public bool IsUsageCycleApplied => UsageCycleStart.HasValue && UsageCycleEnd.HasValue;

    [NotMapped]
    public bool IsOnCancelling
    {
        get
        {
            DateTime now = DateTime.Now;

            return StripeSubscriptionCancelDateTime.HasValue
                   || (Status == BillStatus.Canceled && !StripeSubscriptionCancelDateTime.HasValue && PeriodEnd > now);
        }
    }

    [NotMapped]
    public int SubscriptionInterval
    {
        get
        {
            return BillRecordRevenueCalculator.GetMonthDiff(PeriodStart, PeriodEnd);
        }
    }
}
