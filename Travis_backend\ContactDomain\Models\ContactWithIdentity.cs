using System;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.Enums;

namespace Travis_backend.ContactDomain.Models;

public class ContactWithIdentity
{
    public string Id { get; set; }

    public string ConversationId { get; set; }

    public string CompanyId { get; set; }

    public string Email { get; set; }

    public string PhoneNumber { get; set; }

    public bool IsActive { get; set; }

    public bool IsNew { get; set; }

    public string ContactOwnerId { get; set; }

    public string FirstName { get; set; }

    public string LastName { get; set; }

    public DateTime CreatedAt { get; set; }

    public UserProfile ToUserProfile()
    {
        return new UserProfile
        {
            Id = this.Id,
            Conversation = new Conversation()
            {
                Id = this.ConversationId,
            },
            CompanyId = this.CompanyId,
            Email = this.Email,
            PhoneNumber = this.PhoneNumber,
            ActiveStatus = this.IsActive ? ActiveStatus.Active : ActiveStatus.Inactive,
            ContactOwnerId = this.ContactOwnerId,
            FirstName = this.FirstName,
            LastName = this.LastName,
            CreatedAt = this.CreatedAt
        };
    }
}