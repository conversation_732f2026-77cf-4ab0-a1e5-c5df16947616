using System;
using System.Collections.Generic;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using DimensionKeys = Travis_backend.Constants.ConversationAnalyticsDimensionKeys;
using Dimensions = Travis_backend.Constants.ConversationAnalyticsDimensions;
using FieldNames = Travis_backend.Constants.ConversationAnalyticsAdvancedFilterFieldNames;

namespace Travis_backend.AnalyticsDomain.Utils;

public static class ConversationAnalyticsDimensionUtils
{
    private static readonly HashSet<ConversationAnalyticsDimensions> ContactDimensions =
    [
        Dimensions.CompanyContact,
        Dimensions.CompanyContactChannel,
        Dimensions.CompanyContactUser,
        Dimensions.CompanyContactUserChannel,
        Dimensions.CompanyContactWithBusinessHour,
        Dimensions.CompanyContactChannelWithBusinessHour,
        Dimensions.CompanyContactUserWithBusinessHour,
        Dimensions.CompanyContactUserChannelWithBusinessHour
    ];

    public static ConversationAnalyticsDimensionWrapper GetDimensionWrapper(
        bool isBusinessHourEnabled,
        List<Condition>? conditions,
        AdvancedFilter? advancedFilter)
    {
        // if have segment applied
        var isContactDimension = conditions is { Count: > 0 };

        var baseDimension = GetBaseDimension();

        Dimensions dimension;
        if (isBusinessHourEnabled)
        {
            dimension = baseDimension switch
            {
                Dimensions.Company => Dimensions.CompanyWithBusinessHour,
                Dimensions.CompanyChannel => Dimensions.CompanyChannelWithBusinessHour,
                Dimensions.CompanyUser => Dimensions.CompanyUserWithBusinessHour,
                Dimensions.CompanyUserChannel => Dimensions.CompanyUserChannelWithBusinessHour,
                Dimensions.CompanyContact => Dimensions.CompanyContactWithBusinessHour,
                Dimensions.CompanyContactChannel => Dimensions.CompanyContactChannelWithBusinessHour,
                Dimensions.CompanyContactUser => Dimensions.CompanyContactUserWithBusinessHour,
                Dimensions.CompanyContactUserChannel => Dimensions.CompanyContactUserChannelWithBusinessHour,
                _ => throw new InvalidOperationException($"Get Proxy Dimension: {baseDimension} is not a valid base dimension,"),
            };
        }
        else
        {
            dimension = baseDimension;
        }

        return new ConversationAnalyticsDimensionWrapper(dimension, isContactDimension, isBusinessHourEnabled);

        // Helper function
        // To determine the base dimension
        Dimensions GetBaseDimension()
        {
            if (advancedFilter is null || advancedFilter.Filters.Count == 0)
            {
                return isContactDimension ? Dimensions.CompanyContact : Dimensions.Company;
            }

            if (advancedFilter.Filters.TrueForAll(f => f.FieldName == FieldNames.Channel))
            {
                return isContactDimension ? Dimensions.CompanyContactChannel : Dimensions.CompanyChannel;
            }

            if (advancedFilter.Filters.TrueForAll(f => f.FieldName == FieldNames.Assignee))
            {
                return isContactDimension ? Dimensions.CompanyContactUser : Dimensions.CompanyUser;
            }

            return isContactDimension
                ? Dimensions.CompanyContactUserChannel
                : Dimensions.CompanyUserChannel;
        }
    }

    public static string GetDimensionKey(this ConversationAnalyticsDimensions dimension)
    {
        return dimension switch
        {
            Dimensions.Company => DimensionKeys.Company,
            Dimensions.CompanyChannel => DimensionKeys.CompanyChannel,
            Dimensions.CompanyUser => DimensionKeys.CompanyUser,
            Dimensions.CompanyUserChannel => DimensionKeys.CompanyUserChannel,
            Dimensions.CompanyContact => DimensionKeys.CompanyContact,
            Dimensions.CompanyContactChannel => DimensionKeys.CompanyContactChannel,
            Dimensions.CompanyContactUser => DimensionKeys.CompanyContactUser,
            Dimensions.CompanyContactUserChannel => DimensionKeys.CompanyContactUserChannel,
            Dimensions.CompanyWithBusinessHour => DimensionKeys.CompanyWithBusinessHour,
            Dimensions.CompanyChannelWithBusinessHour => DimensionKeys.CompanyChannelWithBusinessHour,
            Dimensions.CompanyUserWithBusinessHour => DimensionKeys.CompanyUserWithBusinessHour,
            Dimensions.CompanyUserChannelWithBusinessHour => DimensionKeys.CompanyUserChannelWithBusinessHour,
            Dimensions.CompanyContactWithBusinessHour => DimensionKeys.CompanyContactWithBusinessHour,
            Dimensions.CompanyContactChannelWithBusinessHour => DimensionKeys.CompanyContactChannelWithBusinessHour,
            Dimensions.CompanyContactUserWithBusinessHour => DimensionKeys.CompanyContactUserWithBusinessHour,
            Dimensions.CompanyContactUserChannelWithBusinessHour => DimensionKeys.CompanyContactUserChannelWithBusinessHour,
            _ => throw new InvalidOperationException($"Get Dimension Key: {dimension} is not a valid dimension,"),
        };
    }

    public static bool IsContactDimension(this ConversationAnalyticsDimensions dimension)
    {
        return ContactDimensions.Contains(dimension);
    }

    public static ConversationAnalyticsDimensionWrapper GetBaseDimensionWrapperFromBusinessHourDimension(
        ConversationAnalyticsDimensionWrapper dimensionWrapper)
    {
        var baseDimension = dimensionWrapper.Dimension switch
        {
            Dimensions.CompanyWithBusinessHour => Dimensions.Company,
            Dimensions.CompanyChannelWithBusinessHour => Dimensions.CompanyChannel,
            Dimensions.CompanyUserWithBusinessHour => Dimensions.CompanyUser,
            Dimensions.CompanyUserChannelWithBusinessHour => Dimensions.CompanyUserChannel,
            Dimensions.CompanyContactWithBusinessHour => Dimensions.CompanyContact,
            Dimensions.CompanyContactChannelWithBusinessHour => Dimensions.CompanyContactChannel,
            Dimensions.CompanyContactUserWithBusinessHour => Dimensions.CompanyContactUser,
            Dimensions.CompanyContactUserChannelWithBusinessHour => Dimensions.CompanyContactUserChannel,
            _ => throw new InvalidOperationException(
                $"Get Base Dimension Wrapper From Business Hour Dimension: {dimensionWrapper.Dimension} is not a business hour dimension,"),
        };

        return new ConversationAnalyticsDimensionWrapper(
            baseDimension,
            dimensionWrapper.IsContactDimension,
            false);
    }
}