using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Engines;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.Database;

namespace Sleekflow.Core.Benchmarks;

[SimpleJob(RunStrategy.Monitoring, launchCount: 1, warmupCount: 1, iterationCount: 10, invocationCount: 1)]
public class ConversationsBenchmark
{
    private bool _firstCall = true;
    private readonly ApplicationDbContext _applicationDbContext;
    private readonly ILogger<ConversationsBenchmark> _logger;

    public ConversationsBenchmark()
    {
        var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
        _logger = loggerFactory.CreateLogger<ConversationsBenchmark>();

        _applicationDbContext = new ApplicationDbContext(
            new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseSqlServer(new SqlConnection(DbConfig.ReadOnlyConnStr))
                .Options);
    }

    [Benchmark(Baseline = true)]
    public async Task<List<Conversation>> GetAllFromDb()
    {
        var companyId = DbConfig.Env switch
        {
            DbConfig.DevEnv => "b6d7e442-38ae-4b9a-b100-2951729768bc",
            DbConfig.ProdEnv => "471a6289-b9b7-43c3-b6ad-395a1992baea",
            _ => throw new Exception()
        };

        var queryable = _applicationDbContext.Conversations
            .Where(c => c.CompanyId == companyId)
            .OrderByDescending(c => c.UpdatedTime)
            .Include(x => x.ConversationBookmarks)
            .Include(x => x.UserProfile)
            .Include(x => x.facebookUser)
            .Include(x => x.WhatsappUser)
            .Include(x => x.Assignee.Identity)
            .Include(x => x.EmailAddress)
            .Include(x => x.WebClient)
            .Include(x => x.WeChatUser)
            .Include(x => x.LineUser)
            .Include(x => x.SMSUser)
            .Include(x => x.InstagramUser)
            .Include(x => x.WhatsApp360DialogUser)
            .Include(x => x.WhatsappCloudApiUser)
            .Include(x => x.TelegramUser)
            .Include(x => x.ViberUser)
            .Include(x => x.AssignedTeam)
            .Skip(100)
            .Take(20);

        if (_firstCall)
        {
            var queryString = queryable.ToQueryString();

            _logger.LogInformation(nameof(GetAllFromDb) + " " + queryString);

            _firstCall = false;
        }

        return await queryable.ToListAsync();
    }

    [Benchmark]
    public async Task<List<Conversation>> GetAllFromDbAsSplitQuery()
    {
        var companyId = DbConfig.Env switch
        {
            DbConfig.DevEnv => "b6d7e442-38ae-4b9a-b100-2951729768bc",
            DbConfig.ProdEnv => "471a6289-b9b7-43c3-b6ad-395a1992baea",
            _ => throw new Exception()
        };

        var queryable = _applicationDbContext.Conversations
            .Where(c => c.CompanyId == companyId)
            .OrderByDescending(c => c.UpdatedTime)
            .Include(x => x.ConversationBookmarks)
            .Include(x => x.UserProfile)
            .Include(x => x.facebookUser)
            .Include(x => x.WhatsappUser)
            .Include(x => x.Assignee.Identity)
            .Include(x => x.EmailAddress)
            .Include(x => x.WebClient)
            .Include(x => x.WeChatUser)
            .Include(x => x.LineUser)
            .Include(x => x.SMSUser)
            .Include(x => x.InstagramUser)
            .Include(x => x.WhatsApp360DialogUser)
            .Include(x => x.WhatsappCloudApiUser)
            .Include(x => x.TelegramUser)
            .Include(x => x.ViberUser)
            .Include(x => x.AssignedTeam)
            .AsSplitQuery()
            .Skip(100)
            .Take(20);

        if (_firstCall)
        {
            var queryString = queryable.ToQueryString();

            _logger.LogInformation(nameof(GetAllFromDbAsSplitQuery) + " " + queryString);

            _firstCall = false;
        }

        return await queryable.ToListAsync();
    }

    [Benchmark]
    public async Task<List<Conversation>> GetConversationIdsFirstAndGetAllFromDb()
    {
        var companyId = DbConfig.Env switch
        {
            DbConfig.DevEnv => "b6d7e442-38ae-4b9a-b100-2951729768bc",
            DbConfig.ProdEnv => "471a6289-b9b7-43c3-b6ad-395a1992baea",
            _ => throw new Exception()
        };

        var queryable1 = _applicationDbContext.Conversations
            .Where(c => c.CompanyId == companyId)
            .OrderByDescending(c => c.UpdatedTime)
            .Select(c => c.Id)
            .Skip(100)
            .Take(20);

        if (_firstCall)
        {
            var queryString = queryable1.ToQueryString();

            _logger.LogInformation(nameof(GetConversationIdsFirstAndGetAllFromDb) + " " + queryString);
        }

        var conversationIds2 = await queryable1.ToListAsync();

        var queryable2 = _applicationDbContext.Conversations
            .Where(c => conversationIds2.Contains(c.Id))
            .Include(x => x.ConversationBookmarks)
            .Include(x => x.UserProfile)
            .Include(x => x.facebookUser)
            .Include(x => x.WhatsappUser)
            .Include(x => x.Assignee.Identity)
            .Include(x => x.EmailAddress)
            .Include(x => x.WebClient)
            .Include(x => x.WeChatUser)
            .Include(x => x.LineUser)
            .Include(x => x.SMSUser)
            .Include(x => x.InstagramUser)
            .Include(x => x.WhatsApp360DialogUser)
            .Include(x => x.WhatsappCloudApiUser)
            .Include(x => x.TelegramUser)
            .Include(x => x.ViberUser)
            .Include(x => x.AssignedTeam);

        if (_firstCall)
        {
            var queryString = queryable2.ToQueryString();

            _logger.LogInformation(nameof(GetConversationIdsFirstAndGetAllFromDb) + " " + queryString);

            _firstCall = false;
        }

        return await queryable2.ToListAsync();
    }

    [Benchmark]
    public async Task<List<Conversation>> GetConversationIdsFirstAndGetAllFromDbAsSplitQuery()
    {
        var companyId = DbConfig.Env switch
        {
            DbConfig.DevEnv => "b6d7e442-38ae-4b9a-b100-2951729768bc",
            DbConfig.ProdEnv => "471a6289-b9b7-43c3-b6ad-395a1992baea",
            _ => throw new Exception()
        };

        var queryable1 = _applicationDbContext.Conversations
            .Where(c => c.CompanyId == companyId)
            .OrderByDescending(c => c.UpdatedTime)
            .Select(c => c.Id)
            .Skip(100)
            .Take(20);

        if (_firstCall)
        {
            var queryString = queryable1.ToQueryString();

            _logger.LogInformation(nameof(GetConversationIdsFirstAndGetAllFromDb) + " " + queryString);
        }

        var conversationIds2 = await queryable1.ToListAsync();

        var queryable2 = _applicationDbContext.Conversations
            .Where(c => conversationIds2.Contains(c.Id))
            .Include(x => x.ConversationBookmarks)
            .Include(x => x.UserProfile)
            .Include(x => x.facebookUser)
            .Include(x => x.WhatsappUser)
            .Include(x => x.Assignee.Identity)
            .Include(x => x.EmailAddress)
            .Include(x => x.WebClient)
            .Include(x => x.WeChatUser)
            .Include(x => x.LineUser)
            .Include(x => x.SMSUser)
            .Include(x => x.InstagramUser)
            .Include(x => x.WhatsApp360DialogUser)
            .Include(x => x.WhatsappCloudApiUser)
            .Include(x => x.TelegramUser)
            .Include(x => x.ViberUser)
            .Include(x => x.AssignedTeam)
            .AsSplitQuery();

        if (_firstCall)
        {
            var queryString = queryable2.ToQueryString();

            _logger.LogInformation(nameof(GetConversationIdsFirstAndGetAllFromDb) + " " + queryString);

            _firstCall = false;
        }

        return await queryable2.ToListAsync();
    }
}