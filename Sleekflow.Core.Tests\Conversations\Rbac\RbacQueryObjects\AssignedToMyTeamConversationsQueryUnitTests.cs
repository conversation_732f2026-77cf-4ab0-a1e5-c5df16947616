using System.Text.RegularExpressions;
using Travis_backend.ConversationDomain.ConversationSpecifications.RbacConversationSpecifications.QueryObjects.Rbac;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Rbac.RbacQueryObjects;

[TestFixture]
    public class AssignedToMyTeamConversationsQueryUnitTests
    {
        private StaffAccessControlAggregate _staff;
        private StaffAccessControlAggregate _staffWithNoTeam;

        [SetUp]
        public void Setup()
        {
            _staff = new StaffAccessControlAggregate
            {
                StaffId = 15201,
                CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
                AssociatedTeams = new List<TeamAccessControlAggregate>
                {
                    new ()
                    {
                        Id = 1
                    },
                    new ()
                    {
                        Id = 2
                    },
                    new ()
                    {
                        Id = 3
                    }
                }
            };

            _staffWithNoTeam = new StaffAccessControlAggregate
            {
                StaffId = 15201,
                CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
                AssociatedTeams = new List<TeamAccessControlAggregate>()
            };
        }

        [Test]
        public void can_generate_valid_sql()
        {
            // Arrange
            var query = new AssignedToMyTeamConversationsQuery(_staff);
            var alias = "C";

            var associatedTeamIds = string.Join(", ", _staff.AssociatedTeams.Select(x => x.Id));

            var expectedSql = $@"
            SELECT {alias}.*
            FROM Conversations {alias}
            WHERE {alias}.CompanyId = '{_staff.CompanyId}'
            AND {alias}.AssignedTeamId IN ( {associatedTeamIds} )

            UNION ALL

            SELECT {alias}.*
            FROM Conversations {alias}
            JOIN CompanyTeamMembers CTM ON {alias}.AssigneeId = CTM.StaffId
            WHERE CTM.CompanyTeamId IN ( {associatedTeamIds} )
            AND {alias}.CompanyId = '{_staff.CompanyId}'

            UNION ALL

	        SELECT {alias}.*
	        FROM Conversations {alias}
	        JOIN ConversationAdditionalAssignees CAA ON {alias}.Id = CAA.ConversationId
	        JOIN CompanyTeamMembers CTM ON CAA.AssigneeId = CTM.StaffId
	        WHERE CTM.CompanyTeamId IN ( {associatedTeamIds} )
	        AND {alias}.CompanyId = '{_staff.CompanyId}'
            ";

            // Act
            var actualSql = query.ToSql(alias);

            // Assert
            Assert.That(
                Regex.Replace(actualSql.ToLower().Trim(), @"\s+", ""),
                Is.EqualTo(Regex.Replace(expectedSql.ToLower().Trim(), @"\s+", "").Normalize()));

            // Output the SQL query
            TestContext.WriteLine("Generated SQL Query:");
            TestContext.WriteLine(actualSql.Trim());
            TestContext.WriteLine("Expected SQL Query:");
            TestContext.WriteLine(expectedSql.Trim());
        }

        [Test]
        public void can_generate_valid_sql_for_staff_with_no_team()
        {
            // Arrange
            var query = new AssignedToMyTeamConversationsQuery(_staffWithNoTeam);
            var alias = "C";

            // Act
            var actualSql = query.ToSql(alias);

            var expectedSql = $@"
            SELECT {alias}.*
            FROM Conversations {alias}
            WHERE {alias}.CompanyId = '{_staff.CompanyId}'
            AND {alias}.AssignedTeamId IN ( -1 )
         UNION ALL
            SELECT {alias}.*
            FROM Conversations {alias}
            JOIN CompanyTeamMembers CTM ON {alias}.AssigneeId = CTM.StaffId
            WHERE CTM.CompanyTeamId IN ( -1 )
            AND {alias}.CompanyId = '{_staff.CompanyId}'
         UNION ALL
	        SELECT {alias}.*
	        FROM Conversations {alias}
	        JOIN ConversationAdditionalAssignees CAA ON {alias}.Id = CAA.ConversationId
	        JOIN CompanyTeamMembers CTM ON CAA.AssigneeId = CTM.StaffId
	        WHERE CTM.CompanyTeamId IN ( -1 )
	        AND {alias}.CompanyId = '{_staff.CompanyId}'
          ";

            Assert.That(
                Regex.Replace(actualSql.ToLower().Trim(), @"\s+", ""),
                Is.EqualTo(Regex.Replace(expectedSql.ToLower().Trim(), @"\s+", "")));

            // Output the SQL query
            TestContext.WriteLine("Generated SQL Query:");
            TestContext.WriteLine(actualSql.Trim());
            TestContext.WriteLine("Expected SQL Query:");
            TestContext.WriteLine(expectedSql.Trim());
        }
    }