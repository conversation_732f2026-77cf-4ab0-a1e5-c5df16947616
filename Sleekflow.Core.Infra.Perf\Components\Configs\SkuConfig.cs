using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Perf.Components.Configs;

public class SkuConfig
{
    [JsonProperty("sleekflow_core")]
    public SkuConfigDetails SleekflowCore { get; set; }

    [JsonProperty("redis")]
    public Dictionary<string, SkuConfigDetails> Redis { get; set; }

    public SkuConfig(SkuConfigDetails sleekflowCore, Dictionary<string, SkuConfigDetails> redis)
    {
        Redis = redis;
        SleekflowCore = sleekflowCore;
    }
}