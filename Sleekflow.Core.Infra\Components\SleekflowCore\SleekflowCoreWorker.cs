using Pulumi;
using Pulumi.AzureNative.Web.Inputs;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Components.Models;
using Sleekflow.Core.Infra.Constants;
using Sleekflow.Core.Infra.Utils;
using Web = Pulumi.AzureNative.Web;
using Network = Pulumi.AzureNative.Network;
using Insights = Pulumi.AzureNative.Insights;

namespace Sleekflow.Core.Infra.Components.SleekflowCore;

public class SleekflowCoreWorker
{
    private readonly MyConfig _myConfig;
    private readonly List<EnvGroup> _envGroups;
    private readonly ServerConfig _serverConfig;
    private readonly ContainerRegistryOutput _containerRegistryOutput;

    private static readonly List<string> SupportedEnvironment = new ()
    {
        EnvironmentNames.Production, EnvironmentNames.Staging,
    };

    private static readonly List<string> SupportedLocations = new ()
    {
        LocationNames.EastAsia
    };

    public SleekflowCoreWorker(
        MyConfig myConfig,
        List<EnvGroup> envGroups,
        ServerConfig serverConfig,
        ContainerRegistryOutput containerRegistryOutput)
    {
        _myConfig = myConfig;
        _envGroups = envGroups;
        _serverConfig = serverConfig;
        _containerRegistryOutput = containerRegistryOutput;
    }

    public void InitSleekflowCoreWorker()
    {
        if (SupportedEnvironment.All(s => s != _myConfig.Name))
        {
            return;
        }

        var imageName = Output.Format(
            $"{_containerRegistryOutput.Registry.LoginServer}/{ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.SleekflowCoreWorker)}:{_myConfig.BuildTime}");
        var image = new DockerImages(
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.SleekflowCoreWorker),
            imageName,
            _containerRegistryOutput,
            _myConfig.BuildTime
        ).InitDockerImages();
        foreach (var envGroup in _envGroups.Where(s => SupportedLocations.Contains(s.LocationName)))
        {
            var blobStorage = envGroup.BlobStorage;
            var resourceGroup = envGroup.ResourceGroup;
            var workerWebApps = envGroup.WorkerWebApps;
            var logAnalyticsWorkspace = envGroup.LogAnalyticsWorkspace;

            var regionalConfig = _serverConfig
                .RegionalConfigs
                .First(s => s.LocationName == envGroup.LocationName);

            var appInsights = new Insights.Component(
                ResourceUtils.GetName(
                    $"sleekflow-core-worker-app-insight-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Insights.ComponentArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    ApplicationType = Insights.ApplicationType.Web,
                    FlowType = "Redfield",
                    RequestSource = "IbizaWebAppExtensionCreate",
                    Kind = "Web",
                    WorkspaceResourceId = logAnalyticsWorkspace.Id
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var skuConfig = regionalConfig.SkuConfig.SleekflowCore;

            var autoScaleConfig = regionalConfig.AutoScaleConfig.CoreWorker;

            var appServicePlanName = ResourceUtils.GetName(
                $"sleekflow-core-worker-plan-{LocationNames.GetShortName(envGroup.LocationName)}",
                _myConfig);
            var plan = new Web.AppServicePlan(
                appServicePlanName,
                new Web.AppServicePlanArgs
                {
                    Kind = "Linux",
                    ResourceGroupName = resourceGroup.Name,
                    Sku = new Web.Inputs.SkuDescriptionArgs
                    {
                        Name = skuConfig.Name, Tier = skuConfig.Tier
                    },
                    Reserved = true,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var webAppName = ResourceUtils.GetName(
                envGroup.FormatAppName(ServiceNames.GetShortName(ServiceNames.SleekflowCoreWorker)),
                _myConfig);

            var appSettings = AppSettingUtils.GetWebAppSettings(
                webAppName,
                envGroup.Redis,
                image,
                _myConfig,
                envGroup.LocationName,
                resourceGroup,
                appInsights,
                envGroup.SignalR,
                regionalConfig.SleekflowCoreConfig,
                _serverConfig.DefaultSleekflowCoreFrontDoorDomain,
                _containerRegistryOutput,
                logAnalyticsWorkspace);

            appSettings.Add(
                new NameValuePairArgs()
                {
                    Name = "Hangfire__WorkerCount", Value = "100"
                });

            var app = new Web.WebApp(
                webAppName,
                new Web.WebAppArgs
                {
                    ServerFarmId = plan.Id,
                    Name = webAppName,
                    ClientAffinityEnabled = false,
                    ResourceGroupName = resourceGroup.Name,
                    SiteConfig = new Web.Inputs.SiteConfigArgs
                    {
                        AlwaysOn = true,
                        NumberOfWorkers = 1,
                        AppSettings = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(appSettings),
                        HealthCheckPath = "/__health",
                        ConnectionStrings = new[]
                        {
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "DefaultConnection",
                                ConnectionString = ConnectionStringUtils.GetSqlServerConnStr(
                                    resourceGroup.Name,
                                    envGroup.SqlServerProperties!,
                                    regionalConfig.SleekflowCoreConfig.GeoSqlDb),
                                Type = Web.ConnectionStringType.SQLAzure
                            },
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "ReadConnection",
                                ConnectionString = ConnectionStringUtils.GetSqlServerConnStr(
                                    resourceGroup.Name,
                                    envGroup.SqlServerProperties!,
                                    regionalConfig.SleekflowCoreConfig.GeoSqlDb,
                                    isReadOnly: true),
                                Type = Web.ConnectionStringType.SQLAzure
                            },
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "AnalyticDbConnection",
                                ConnectionString = ConnectionStringUtils.GetSqlServerConnStr(
                                    resourceGroup.Name,
                                    envGroup.AnalyticSqlServerProperties!,
                                    null,
                                    isReadOnly: true),
                                Type = Web.ConnectionStringType.SQLAzure
                            },
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "StorageConnectionString",
                                ConnectionString = ConnectionStringUtils.GetStorageConnStr(
                                    resourceGroup.Name,
                                    blobStorage.Name,
                                    regionalConfig.SleekflowCoreConfig.GeoStorage),
                                Type = Web.ConnectionStringType.Custom
                            },
                        },
                        LinuxFxVersion =
                            imageName.Apply(n => $"DOCKER|{n}")
                    },
                    Kind = "app,linux,container",
                    Reserved = true,
                    PublicNetworkAccess = "Enabled",
                    HttpsOnly = true,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var webTest = Output.Tuple(envGroup.ClientConfig, resourceGroup.Name, appInsights.Name)
                .Apply(
                    values =>
                        new Insights.WebTest(
                            ResourceUtils.GetName(
                                $"sleekflow-core-worker-app-insight-web-test-{LocationNames.GetShortName(envGroup.LocationName)}",
                                _myConfig),
                            new Insights.WebTestArgs
                            {
                                ResourceGroupName = resourceGroup.Name,
                                Frequency = 300,
                                Timeout = 120,
                                Enabled = true,
                                RetryEnabled = true,
                                WebTestName = $"{webAppName}-health-check",
                                SyntheticMonitorId = $"{webAppName}-health-check",
                                Locations = new[]
                                {
                                    new Insights.Inputs.WebTestGeolocationArgs
                                    {
                                        Location = "apac-hk-hkn-azr"
                                    },
                                    new Insights.Inputs.WebTestGeolocationArgs
                                    {
                                        Location = "us-va-ash-azr"
                                    },
                                    new Insights.Inputs.WebTestGeolocationArgs
                                    {
                                        Location = "apac-sg-sin-azr"
                                    },
                                },
                                Request = new Insights.Inputs.WebTestPropertiesRequestArgs
                                {
                                    HttpVerb = "POST", RequestUrl = $"https://{webAppName}.azurewebsites.net/__health"
                                },
                                Tags = new InputMap<string>
                                {
                                    {
                                        $"hidden-link:/subscriptions/{values.Item1.SubscriptionId}/resourceGroups/{values.Item2}/providers/microsoft.insights/components/{values.Item3}",
                                        "Resource"
                                    }
                                },
                                WebTestKind = Insights.WebTestKind.Standard
                            },
                            new CustomResourceOptions
                            {
                                Parent = resourceGroup
                            }));

            var subnet = new Network.Subnet(
                ResourceUtils.GetName(
                    $"sleekflow-core-worker-app-subnet-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Network.SubnetArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    VirtualNetworkName = envGroup.VirtualNetwork.Name,
                    AddressPrefix = regionalConfig.VnetConfig.SleekflowCoreWorkerAddressPrefix,
                    Delegations =
                    {
                        new Network.Inputs.DelegationArgs
                        {
                            Name = "sleekflow-core-worker-app-subnet-delegation",
                            ServiceName = "Microsoft.Web/serverfarms"
                        }
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var webAppSwiftVirtualNetworkConnection = new Web.WebAppSwiftVirtualNetworkConnection(
                ResourceUtils.GetName(
                    $"sleekflow-core-worker-app-swift-virtual-network-connection-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Web.WebAppSwiftVirtualNetworkConnectionArgs
                {
                    Name = app.Name, SubnetResourceId = subnet.Id, ResourceGroupName = resourceGroup.Name,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var autoScaleSettingName =
                $"sleekflow-core-worker-app-autoscale-setting-{LocationNames.GetShortName(envGroup.LocationName)}";
            var autoScaleSetting = new Insights.AutoscaleSetting(
                autoScaleSettingName,
                new Insights.AutoscaleSettingArgs
                {
                    Enabled = true,
                    TargetResourceUri = plan.Id,
                    ResourceGroupName = resourceGroup.Name,
                    Profiles = new List<Insights.Inputs.AutoscaleProfileArgs>()
                    {
                        new Insights.Inputs.AutoscaleProfileArgs
                        {
                            Name = "DefaultAutoScalingRuleSets",
                            Capacity = new Insights.Inputs.ScaleCapacityArgs
                            {
                                Default = autoScaleConfig.Capacity.Default,
                                Maximum = autoScaleConfig.Capacity.Maximum,
                                Minimum = autoScaleConfig.Capacity.Minimum,
                            },
                            Rules = new List<Insights.Inputs.ScaleRuleArgs>
                            {
                                new Insights.Inputs.ScaleRuleArgs
                                {
                                    ScaleAction = new Insights.Inputs.ScaleActionArgs
                                    {
                                        Direction = Insights.ScaleDirection.Increase,
                                        Type = Insights.ScaleType.ChangeCount,
                                        Value = autoScaleConfig.ScaleOutInstances,
                                        Cooldown = "PT10M"
                                    },
                                    MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                    {
                                        MetricName = "CpuPercentage",
                                        MetricNamespace = "microsoft.web/serverfarms",
                                        MetricResourceUri = plan.Id,
                                        Operator = Insights.ComparisonOperationType.GreaterThan,
                                        Statistic = Insights.MetricStatisticType.Sum,
                                        Threshold = 50,
                                        TimeAggregation = Insights.TimeAggregationType.Average,
                                        TimeGrain = "PT1M",
                                        TimeWindow = "PT5M",
                                        DividePerInstance = true
                                    }
                                },
                                new Insights.Inputs.ScaleRuleArgs
                                {
                                    ScaleAction = new Insights.Inputs.ScaleActionArgs
                                    {
                                        Direction = Insights.ScaleDirection.Decrease,
                                        Type = Insights.ScaleType.ChangeCount,
                                        Value = autoScaleConfig.ScaleDownInstances,
                                        Cooldown = "PT1H"
                                    },
                                    MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                    {
                                        MetricName = "CpuPercentage",
                                        MetricNamespace = "microsoft.web/serverfarms",
                                        MetricResourceUri = plan.Id,
                                        Operator = Insights.ComparisonOperationType.LessThan,
                                        Statistic = Insights.MetricStatisticType.Sum,
                                        Threshold = 30,
                                        TimeAggregation = Insights.TimeAggregationType.Average,
                                        TimeGrain = "PT1M",
                                        TimeWindow = "PT15M",
                                        DividePerInstance = true
                                    }
                                },
                                new Insights.Inputs.ScaleRuleArgs
                                {
                                    ScaleAction = new Insights.Inputs.ScaleActionArgs
                                    {
                                        Direction = Insights.ScaleDirection.Increase,
                                        Type = Insights.ScaleType.ChangeCount,
                                        Value = autoScaleConfig.ScaleOutInstances,
                                        Cooldown = "PT10M"
                                    },
                                    MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                    {
                                        MetricName = "MemoryPercentage",
                                        MetricNamespace = "microsoft.web/serverfarms",
                                        MetricResourceUri = plan.Id,
                                        Operator = Insights.ComparisonOperationType.GreaterThan,
                                        Statistic = Insights.MetricStatisticType.Sum,
                                        Threshold = 70,
                                        TimeAggregation = Insights.TimeAggregationType.Average,
                                        TimeGrain = "PT1M",
                                        TimeWindow = "PT5M",
                                        DividePerInstance = true
                                    }
                                },
                                new Insights.Inputs.ScaleRuleArgs
                                {
                                    ScaleAction = new Insights.Inputs.ScaleActionArgs
                                    {
                                        Direction = Insights.ScaleDirection.Decrease,
                                        Type = Insights.ScaleType.ChangeCount,
                                        Value = autoScaleConfig.ScaleDownInstances,
                                        Cooldown = "PT1H"
                                    },
                                    MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                    {
                                        MetricName = "MemoryPercentage",
                                        MetricNamespace = "microsoft.web/serverfarms",
                                        MetricResourceUri = plan.Id,
                                        Operator = Insights.ComparisonOperationType.LessThan,
                                        Statistic = Insights.MetricStatisticType.Sum,
                                        Threshold = 50,
                                        TimeAggregation = Insights.TimeAggregationType.Average,
                                        TimeGrain = "PT1M",
                                        TimeWindow = "PT15M",
                                        DividePerInstance = true
                                    }
                                }
                            },
                            Recurrence = new Insights.Inputs.RecurrenceArgs
                            {
                                Frequency = Insights.RecurrenceFrequency.Week,
                                Schedule = new Insights.Inputs.RecurrentScheduleArgs
                                {
                                    Days = new[]
                                    {
                                        "Monday",
                                        "Tuesday",
                                        "Wednesday",
                                        "Thursday",
                                        "Friday",
                                        "Saturday",
                                        "Sunday"
                                    },
                                    Hours = 0,
                                    Minutes = 5,
                                    TimeZone = "UTC"
                                }
                            }
                        },
                    }
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            workerWebApps.Add(
                ServiceNames.SleekflowCoreWorker,
                new AppServiceConfiguration(webAppName, app, appInsights, plan, autoScaleSetting));
        }
    }
}