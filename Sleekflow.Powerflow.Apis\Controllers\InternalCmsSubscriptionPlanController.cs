﻿using System.Net.Mime;
using System.Text;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Configuration;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.ResellerDomain.ViewModels;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Services;
using CmsCacheKeyHelper = Sleekflow.Powerflow.Apis.Helpers.CmsCacheKeyHelper;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Internal Cms APIs subscription plan and manage custom plan mapping.
/// </summary>
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)] // Basic Role Requirement
[Route("/internal/subscription-plan/[action]")]
public class InternalCmsSubscriptionPlanController : InternalControllerBase
{
    private readonly ICompanyUsageService _companyUsageService;
    private readonly ICompanyService _companyService;
    private readonly ISubscriptionPlanService _subscriptionPlanService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<InternalCmsSubscriptionPlanController> _logger;
    private readonly IConfiguration _configuration;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IUsageCycleCalculator _usageCycleCalculator;

    public InternalCmsSubscriptionPlanController(
        ICompanyUsageService companyUsageService,
        ICompanyService companyService,
        ISubscriptionPlanService subscriptionPlanService,
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<InternalCmsSubscriptionPlanController> logger,
        ICacheManagerService cacheManagerService,
        IUsageCycleCalculator usageCycleCalculator)
        : base(userManager)
    {
        _companyUsageService = companyUsageService;
        _companyService = companyService;
        _subscriptionPlanService = subscriptionPlanService;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _cacheManagerService = cacheManagerService;
        _usageCycleCalculator = usageCycleCalculator;
    }

    [HttpPost]
    public async Task<ActionResult<GetSubscriptionPlansResponse>> GetSubscriptionPlans(
        [FromForm]
        GetSubscriptionPlansRequest request)
    {
        var cacheKey = CmsCacheKeyHelper.GetCmsGetSubscriptionPlansKey();

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);

            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetSubscriptionPlansResponse(
            await _appDbContext.CoreSubscriptionPlans
                .OrderByDescending(x => x.Version)
                .ProjectTo<SubscriptionPlanDto>(_mapper.ConfigurationProvider)
                .ToListAsync(),
            await _appDbContext.CustomSubscriptionPlanTranslationMaps
                .OrderBy(x => x.IsDeleted)
                .ThenByDescending(x => x.CreatedAt)
                .Select(
                    x => new CustomSubscriptionPlanTranslationMapDto
                    {
                        Id = x.Id,
                        SourceSubscriptionPlanId = x.SourceSubscriptionPlanId,
                        DestinationSubscriptionPlanId = x.DestinationSubscriptionPlanId,
                        CreatedByUserName = x.CreatedByUser.DisplayName,
                        CreatedByUserId = x.CreatedByUserId,
                        UpdatedByUserName = x.UpdatedByUser.DisplayName,
                        UpdatedByUserId = x.UpdatedByUserId,
                        IsDeleted = x.IsDeleted
                    })
                .ToListAsync());

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> CreateSubscriptionPlanTranslationMap(
        [FromBody]
        CreateSubscriptionPlanTranslationMapRequest request)
    {
        var now = DateTime.UtcNow;

        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        if (SubscriptionPlansId.AllPlans.ContainsIgnoreCase(request.SourceSubscriptionPlanId))
        {
            return BadRequest(new ResponseViewModel("Source Subscription Plan Id can't be standard Subscription Plan."));
        }

        var sourceSubscriptionPlanByStripePlanId = await _subscriptionPlanService.GetSubscriptionPlanByStripePlanIdAsync(request.SourceSubscriptionPlanId);

        if (sourceSubscriptionPlanByStripePlanId != null && SubscriptionPlansId.AllPlans.Contains(sourceSubscriptionPlanByStripePlanId.Id, StringComparer.InvariantCultureIgnoreCase))
        {
            return BadRequest(new ResponseViewModel("Source Subscription Plan Id can't be standard Subscription Plan."));
        }

        if (!await _appDbContext.CoreSubscriptionPlans.AnyAsync(x => x.Id == request.DestinationSubscriptionPlanId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Destination Subscription Plan Id is not found."
                });
        }

        if (await _appDbContext.CompanyBillRecords.CountAsync(
                x => x.SubscriptionPlanId == request.SourceSubscriptionPlanId) >= 10)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "More then 10 bill records used the Source Subscription Plan Id, so it cannot be created."
                });
        }

        if (await _appDbContext.CustomSubscriptionPlanTranslationMaps.AnyAsync(
                x => x.SourceSubscriptionPlanId == request.SourceSubscriptionPlanId && !x.IsDeleted))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Duplicated Source Subscription Plan Id mapping found."
                });
        }

        _appDbContext.CustomSubscriptionPlanTranslationMaps.Add(
            new CustomSubscriptionPlanTranslationMap()
            {
                SourceSubscriptionPlanId = request.SourceSubscriptionPlanId,
                DestinationSubscriptionPlanId = request.DestinationSubscriptionPlanId,
                CreatedByUserId = user.Id,
                UpdatedByUserId = user.Id,
            });

        await _appDbContext.SaveChangesAsync();

        if (request.IsModifyExistingBillRecords)
        {
            var subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanAsync(request.SourceSubscriptionPlanId);
            var destinationSubscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanAsync(request.DestinationSubscriptionPlanId);

            if (subscriptionPlan != null && destinationSubscriptionPlan != null)
            {
                var billRecords = await _appDbContext.CompanyBillRecords
                    .Where(x => x.SubscriptionPlanId == request.SourceSubscriptionPlanId)
                    .ToListAsync();

                foreach (var billRecord in billRecords)
                {
                    billRecord.SubscriptionPlanId = request.DestinationSubscriptionPlanId;

                    if (destinationSubscriptionPlan.PlanTypeCode == PlanTypeCodes.BasePlan)
                    {
                        var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
                        billRecord.UsageCycleStart = usageCycle.From;
                        billRecord.UsageCycleEnd = usageCycle.To;

                        if (billRecord.PeriodStart <= now && now < billRecord.PeriodEnd)
                        {
                            await _companyUsageService.EnableUsageLimitOffsetProfileAsync(billRecord.CompanyId, true);
                        }
                    }
                }

                if (!string.IsNullOrWhiteSpace(destinationSubscriptionPlan!.CountryTier) && !string.IsNullOrWhiteSpace(destinationSubscriptionPlan!.Currency))
                {
                    var companyIds = billRecords.Select(x => x.CompanyId).ToHashSet();

                    foreach (var companyId in companyIds)
                    {
                        var hasSubscriptionCountryTierAndCurrency = await _companyService.HasSubscriptionCountryTierAndCurrencyAsync(companyId);

                        if (!hasSubscriptionCountryTierAndCurrency)
                        {
                            _ = await _companyService.UpdateCompanySubscriptionTierAndCurrencyAsync(companyId, destinationSubscriptionPlan.CountryTier, destinationSubscriptionPlan.Currency);
                        }
                    }
                }

                await _appDbContext.SaveChangesAsync();
            }

        }

        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(CmsCacheKeyHelper.GetCmsGetSubscriptionPlansKey());

        return Ok(
            new ResponseViewModel()
            {
                message = "Subscription Plan translation map created."
            });
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> DeleteSubscriptionPlanTranslationMap(
        [FromBody]
        DeleteSubscriptionPlanTranslationMapRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        var customSubscriptionPlanTranslationMap = await _appDbContext.CustomSubscriptionPlanTranslationMaps
            .FirstOrDefaultAsync(x => x.Id == request.CustomSubscriptionPlanTranslationMapId && !x.IsDeleted);

        if (customSubscriptionPlanTranslationMap is null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Destination Subscription Plan Id is not found."
                });
        }

        customSubscriptionPlanTranslationMap.IsDeleted = true;
        customSubscriptionPlanTranslationMap.UpdatedByUserId = user.Id;

        await _appDbContext.SaveChangesAsync();

        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(CmsCacheKeyHelper.GetCmsGetSubscriptionPlansKey());

        return Ok(
            new ResponseViewModel()
            {
                message = "Subscription Plan translation map created."
            });
    }
}

public record GetSubscriptionPlansRequest(bool AllowCache = true);

public record GetSubscriptionPlansResponse(
    List<SubscriptionPlanDto> SubscriptionPlans,
    List<CustomSubscriptionPlanTranslationMapDto> CustomSubscriptionPlanTranslationMaps);

public record CreateSubscriptionPlanTranslationMapRequest(
    string SourceSubscriptionPlanId,
    string DestinationSubscriptionPlanId,
    bool IsModifyExistingBillRecords);

public record DeleteSubscriptionPlanTranslationMapRequest(long CustomSubscriptionPlanTranslationMapId);

public class CustomSubscriptionPlanTranslationMapDto
{
    public long Id { get; set; }

    public string SourceSubscriptionPlanId { get; set; }

    public string DestinationSubscriptionPlanId { get; set; }

    public string CreatedByUserName { get; set; }

    public string CreatedByUserId { get; set; }

    public string UpdatedByUserName { get; set; }

    public string UpdatedByUserId { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }
}