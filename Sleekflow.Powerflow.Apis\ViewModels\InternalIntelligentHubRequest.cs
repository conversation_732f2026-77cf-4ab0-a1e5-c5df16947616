using System.ComponentModel.DataAnnotations;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class GetInternalIntelligentHubDataRequest
{
    [Required]
    public string CompanyId { get; set; }
}

public class UpdateIntelligentHubConfigUsageLimitOffsetsRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public Dictionary<string, int> UsageLimitOffsets { get; set; }
}