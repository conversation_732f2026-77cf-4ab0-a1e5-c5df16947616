### Login

POST https://localhost:5000/auth0/account/getusertoken
content-type: application/json-patch+json

{
  "username": "",
  "password": ""
}

> {% client.global.set("token", response.body.accessToken); %}

### Send Note Message
POST https://{{host}}/Conversation/Note/260e988a-c494-4306-89a2-b30152480afa
content-type: application/x-www-form-urlencoded
Authorization: Bearer {{token}}

MessageChecksum={{$random.uuid}}&Channel=note&MessageGroupName=69b366bb-13bb-4ec9-8f69-ab4dd02bc0d0&MessageType=text&MessageContent=Sam OR&AssigneeId=52376b8e-f573-4039-a404-1880b7758a79