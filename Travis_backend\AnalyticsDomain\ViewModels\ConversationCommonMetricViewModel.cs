using System;

namespace Travis_backend.AnalyticsDomain.ViewModels;

public class ConversationCommonMetricViewModel
{
    public string Date { get; set; }

    // Conversation metrics
    public int NumberOfAllConversations { get; set; }

    public int NumberOfActiveConversations { get; set; }

    // Message metrics
    public int NumberOfMessagesSent { get; set; }

    public int NumberOfMessagesReceived { get; set; }

    public int NumberOfMessagesFailed { get; set; }

    // Performance metrics
    public long ResponseTimeForAllMessages { get; set; }

    public long ResponseTimeForFirstMessages { get; set; }

    public long ResolutionTime { get; set; }

    // Other metrics
    public int NumberOfNewEnquires { get; set; }

    public int NumberOfNewContacts { get; set; }

    public ConversationCommonMetricViewModel(
        DateOnly? date,
        int numberOfAllConversations,
        int numberOfActiveConversations,
        int numberOfMessagesSent,
        int numberOfMessagesReceived,
        int numberOfMessagesFailed,
        long responseTimeForAllMessages,
        long responseTimeForFirstMessages,
        long resolutionTime,
        int numberOfNewEnquires,
        int numberOfNewContacts)
    {
        Date = date?.ToString("yyyy-MM-dd");
        NumberOfAllConversations = numberOfAllConversations;
        NumberOfActiveConversations = numberOfActiveConversations;
        NumberOfMessagesSent = numberOfMessagesSent;
        NumberOfMessagesReceived = numberOfMessagesReceived;
        NumberOfMessagesFailed = numberOfMessagesFailed;
        ResponseTimeForAllMessages = responseTimeForAllMessages;
        ResponseTimeForFirstMessages = responseTimeForFirstMessages;
        ResolutionTime = resolutionTime;
        NumberOfNewEnquires = numberOfNewEnquires;
        NumberOfNewContacts = numberOfNewContacts;
    }

    public static ConversationCommonMetricViewModel Default(DateOnly? date = null)
    {
        return new ConversationCommonMetricViewModel(
            date,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0);
    }
}