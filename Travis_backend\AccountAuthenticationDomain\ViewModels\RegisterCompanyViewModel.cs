using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Travis_backend.Enums;

namespace Travis_backend.AccountAuthenticationDomain.ViewModels;

public class RegisterCompanyViewModel
{
    public string Id { get; set; }

    [Required]
    public string CompanyName { get; set; }

    public string TimeZoneInfoId { get; set; }

    public string CompanySize { get; set; }

    public string PhoneNumber { get; set; }

    public string Industry { get; set; }

    public string? OnlineShopSystem { get; set; }

    public List<string> CommunicationTools { get; set; }

    public string CompanyWebsite { get; set; }

    public string SubscriptionPlanId { get; set; } = "sleekflow_free";

    public string lmref { get; set; }

    public string HeardFrom { get; set; }

    public string PromotionCode { get; set; }

    public string WebClientUUID { get; set; }

    public string Referral { get; set; }

    public bool IsShopifyAccount { get; set; }

    public CompanyType CompanyType { get; set; } = CompanyType.DirectClient;

    public string? PlatformUsageIntent { get; set; }
}