using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;

namespace Travis_backend.CompanyDomain.Repositories;

public interface IBusinessHourConfigRepository
{
    Task<BusinessHourConfig> GetAsync(string companyId);

    Task<BusinessHourConfig> CreateAndGetAsync(string companyId, bool isEnabled, WeeklyHours weeklyHours);

    Task<int> UpdateAsync(string companyId, bool isEnabled, WeeklyHours weeklyHours);
}