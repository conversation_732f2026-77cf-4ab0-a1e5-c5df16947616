﻿using System.Net.Mime;
using System.Text;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Sleekflow.Powerflow.Apis.Helpers;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.Services;
using Travis_backend.TenantHubDomain.Services;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Internal Cms Revenue for display MRR.
/// </summary>
[Authorize(
    Roles = ApplicationUserRole.InternalCmsTeamLead + "," + ApplicationUserRole.InternalCmsCustomerSuccessUser +
            "," + ApplicationUserRole.InternalCmsSalesUser)]
[Route("/internal/analytic/[action]")]
public class InternalCmsAnalyticController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IInternalAnalyticService _internalAnalyticService;
    private readonly IConfiguration _configuration;
    private readonly ICompaniesService _companiesService;

    public InternalCmsAnalyticController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        ICacheManagerService cacheManagerService,
        IInternalAnalyticService internalAnalyticService,
        IConfiguration configuration,
        ICompaniesService companiesService)
        : base(userManager)
    {
        _mapper = mapper;
        _cacheManagerService = cacheManagerService;
        _internalAnalyticService = internalAnalyticService;
        _appDbContext = appDbContext;
        _configuration = configuration;
        _companiesService = companiesService;
    }

    /// <summary>
    /// Get All Company Revenue Analytic.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticResponse>> GetCmsAnalytic(
        [FromBody]
        InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.All,
            AnalyticCategory.CmsAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.All,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);

        var result = new GetCmsAnalyticResponse
        {
            DailyAnalytics = await _internalAnalyticService.GetCmsAnalytic(
                start,
                end,
                companiesCacheKey,
                request.AllowCompaniesCache,
                excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get All Company Monthly Recurring Revenue Analytic With Analytic Database.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyMonthlyRecurringRevenueAnalyticResponse>>
        GetCmsMonthlyRecurringRevenueAnalyticWithAnalyticDatabase(
            [FromBody]
            InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.All,
            AnalyticCategory.CmsMrrAnalyticWithAnalyticDatabase,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsDailyMonthlyRecurringRevenueAnalyticResponse
        {
            DailyMonthlyRecurringRevenueAnalytics =
                await _internalAnalyticService.GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabase(
                    start,
                    end)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get All Company Monthly Recurring Revenue Analytic.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyMonthlyRecurringRevenueAnalyticResponse>>
        GetCmsMonthlyRecurringRevenueAnalytic(
            [FromBody]
            InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.All,
            AnalyticCategory.CmsMrrAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.All,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);

        var result = new GetCmsDailyMonthlyRecurringRevenueAnalyticResponse
        {
            DailyMonthlyRecurringRevenueAnalytics =
                await _internalAnalyticService.GetCmsDailyMonthlyRecurringRevenueAnalytic(
                    start,
                    end,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get All Company Daily Revenue Analytic.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyRevenueAnalyticResponse>> GetCmsDailyRevenueAnalytic(
        [FromBody]
        InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.All,
            AnalyticCategory.CmsDailyRevenueAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.All,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);

        var result = new GetCmsDailyRevenueAnalyticResponse
        {
            DailyRevenueAnalytics =
                await _internalAnalyticService.GetCmsDailyRevenueAnalytic(
                    start,
                    end,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get All Company Plan Distribution Analytic.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsPlanDistributionAnalyticResponse>> GetCmsPlanDistributionAnalytic(
        [FromBody]
        InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.All,
            AnalyticCategory.CmsPlanDistributionAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.All,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);

        var result = new GetCmsPlanDistributionAnalyticResponse
        {
            DailyPlanDistributionAnalytics = await _internalAnalyticService.GetCmsDailyPlanDistributionAnalytic(
                start,
                end,
                companiesCacheKey,
                request.AllowCompaniesCache,
                excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get All Company Accrued Revenue Analytic.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAccruedAnalyticResponse>> GetCmsAccruedAnalytic(
        [FromBody]
        InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetCmsAccruedAnalyticKey(
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsAccruedAnalyticResponse
        {
            DailyAccruedAnalytics = await _internalAnalyticService.GetCmsDailyAccruedRevenueAnalytic(
                start,
                end,
                excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get All Company Revenue Analytic.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDistributionAnalyticResponse>> GetCmsDistributionAnalytic(
        [FromBody]
        InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var result = new GetCmsDistributionAnalyticResponse
        {
            DailyDistributionAnalytics = await _internalAnalyticService.GetCmsDistributionAnalytic(start, end)
        };

        return Ok(result);
    }

    /// <summary>
    /// Get Company Revenue Analytic by Sales / Activation Owner.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticByOwnersResponse>> GetCmsAnalyticByActivationOwnerIds(
        [FromBody]
        InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
            [
                ApplicationUserRole.InternalCmsTeamLead,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser
            ]) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.ActivationOwner,
            AnalyticCategory.CmsAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.ActivationOwner,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsAnalyticByOwnersResponse
        {
            ByOwnersDailyAnalytic = await _internalAnalyticService.GetCmsAnalyticByContactOwnerIds(
                CmsContactOwnerType.ActivationOwner,
                start,
                end,
                request.OwnerIds,
                request.TeamNames,
                request.IsByOwnerTeams,
                companiesCacheKey,
                request.AllowCompaniesCache,
                request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Monthly Recurring Revenue Analytic With Analytic Database by Sales / Activation Owner
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse>>
        GetCmsMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByActivationOwnerIds(
            [FromBody]
            InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.ActivationOwner,
            AnalyticCategory.CmsMrrAnalyticWithAnalyticDatabase,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse
        {
            ByOwnersDailyMonthlyRecurringRevenueAnalytics =
                await _internalAnalyticService
                    .GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByContactOwnerIds(
                        CmsContactOwnerType.ActivationOwner,
                        start,
                        end,
                        request.OwnerIds,
                        request.TeamNames,
                        request.IsByOwnerTeams)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Monthly Recurring Revenue Analytic by Sales / Activation Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse>>
        GetCmsMonthlyRecurringRevenueAnalyticByActivationOwnerIds(
            [FromBody]
            InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.ActivationOwner,
            AnalyticCategory.CmsMrrAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.ActivationOwner,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse
        {
            ByOwnersDailyMonthlyRecurringRevenueAnalytics =
                await _internalAnalyticService.GetCmsDailyMonthlyRecurringRevenueAnalyticByContactOwnerIds(
                    CmsContactOwnerType.ActivationOwner,
                    start,
                    end,
                    request.OwnerIds,
                    request.TeamNames,
                    request.IsByOwnerTeams,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Daily Revenue Analytic by Sales / Activation Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyRevenueAnalyticByOwnersResponse>>
        GetCmsDailyRevenueAnalyticByActivationOwnerIds(
            [FromBody]
            InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.ActivationOwner,
            AnalyticCategory.CmsDailyRevenueAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.ActivationOwner,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsDailyRevenueAnalyticByOwnersResponse
        {
            ByOwnersDailyRevenueAnalytic =
                await _internalAnalyticService.GetCmsDailyRevenueAnalyticByContactOwnerIds(
                    CmsContactOwnerType.ActivationOwner,
                    start,
                    end,
                    request.OwnerIds,
                    request.TeamNames,
                    request.IsByOwnerTeams,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Plan Distribution Analytic by Sales / Activation Owner.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsPlanDistributionAnalyticByOwnersResponse>>
        GetCmsPlanDistributionAnalyticByActivationOwnerIds(
            [FromBody]
            InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
            [
                ApplicationUserRole.InternalCmsTeamLead,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser
            ]) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.ActivationOwner,
            AnalyticCategory.CmsPlanDistributionAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.ActivationOwner,
            AnalyticCategory.CmsPlanDistributionAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsPlanDistributionAnalyticByOwnersResponse
        {
            ByOwnersDailyPlanDistributionAnalytic =
                await _internalAnalyticService.GetCmsPlanDistributionAnalyticByContactOwnerIds(
                    CmsContactOwnerType.ActivationOwner,
                    start,
                    end,
                    request.OwnerIds,
                    request.TeamNames,
                    request.IsByOwnerTeams,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Revenue Analytic by Company Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticByOwnersResponse>> GetCmsAnalyticByCompanyOwnerIds(
        [FromBody]
        InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
            [
                ApplicationUserRole.InternalCmsTeamLead,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser
            ]) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CompanyOwner,
            AnalyticCategory.CmsAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CompanyOwner,
            AnalyticCategory.CmsAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsAnalyticByOwnersResponse
        {
            ByOwnersDailyAnalytic = await _internalAnalyticService.GetCmsAnalyticByContactOwnerIds(
                CmsContactOwnerType.CompanyOwner,
                start,
                end,
                request.OwnerIds,
                request.TeamNames,
                request.IsByOwnerTeams,
                companiesCacheKey,
                request.AllowCompaniesCache,
                request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Monthly Recurring Revenue Analytic With Analytic Database by Company Owner
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse>>
        GetCmsMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByCompanyOwnerIds(
            [FromBody]
            InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CompanyOwner,
            AnalyticCategory.CmsMrrAnalyticWithAnalyticDatabase,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse
        {
            ByOwnersDailyMonthlyRecurringRevenueAnalytics =
                await _internalAnalyticService
                    .GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByContactOwnerIds(
                        CmsContactOwnerType.CompanyOwner,
                        start,
                        end,
                        request.OwnerIds,
                        request.TeamNames,
                        request.IsByOwnerTeams)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Monthly Recurring Revenue Analytic by Company Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse>>
        GetCmsMonthlyRecurringRevenueAnalyticByCompanyOwnerIds(
            [FromBody]
            InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CompanyOwner,
            AnalyticCategory.CmsMrrAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CompanyOwner,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse
        {
            ByOwnersDailyMonthlyRecurringRevenueAnalytics =
                await _internalAnalyticService.GetCmsDailyMonthlyRecurringRevenueAnalyticByContactOwnerIds(
                    CmsContactOwnerType.CompanyOwner,
                    start,
                    end,
                    request.OwnerIds,
                    request.TeamNames,
                    request.IsByOwnerTeams,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Daily Revenue Analytic by Company Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyRevenueAnalyticByOwnersResponse>>
        GetCmsDailyRevenueAnalyticByCompanyOwnerIds(
            [FromBody]
            InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CompanyOwner,
            AnalyticCategory.CmsDailyRevenueAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CompanyOwner,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsDailyRevenueAnalyticByOwnersResponse
        {
            ByOwnersDailyRevenueAnalytic =
                await _internalAnalyticService.GetCmsDailyRevenueAnalyticByContactOwnerIds(
                    CmsContactOwnerType.CompanyOwner,
                    start,
                    end,
                    request.OwnerIds,
                    request.TeamNames,
                    request.IsByOwnerTeams,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Plan Distribution Analytic by Company Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsPlanDistributionAnalyticByOwnersResponse>>
        GetCmsPlanDistributionAnalyticByCompanyOwnerIds(
            [FromBody]
            InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
            [
                ApplicationUserRole.InternalCmsTeamLead,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser
            ]) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CompanyOwner,
            AnalyticCategory.CmsPlanDistributionAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CompanyOwner,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsPlanDistributionAnalyticByOwnersResponse
        {
            ByOwnersDailyPlanDistributionAnalytic =
                await _internalAnalyticService.GetCmsPlanDistributionAnalyticByContactOwnerIds(
                    CmsContactOwnerType.CompanyOwner,
                    start,
                    end,
                    request.OwnerIds,
                    request.TeamNames,
                    request.IsByOwnerTeams,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Revenue Analytic by Cs Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticByOwnersResponse>> GetCmsAnalyticByCsOwnerIds(
        [FromBody]
        InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
            [
                ApplicationUserRole.InternalCmsTeamLead,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser
            ]) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CsOwner,
            AnalyticCategory.CmsAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CsOwner,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsAnalyticByOwnersResponse
        {
            ByOwnersDailyAnalytic = await _internalAnalyticService.GetCmsAnalyticByContactOwnerIds(
                CmsContactOwnerType.CsOwner,
                start,
                end,
                request.OwnerIds,
                request.TeamNames,
                request.IsByOwnerTeams,
                companiesCacheKey,
                request.AllowCompaniesCache,
                request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Monthly Recurring Revenue Analytic With Analytic Database by Cs Owner
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse>>
        GetCmsMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByCsOwnerIds(
            [FromBody]
            InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CsOwner,
            AnalyticCategory.CmsMrrAnalyticWithAnalyticDatabase,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse
        {
            ByOwnersDailyMonthlyRecurringRevenueAnalytics =
                await _internalAnalyticService
                    .GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByContactOwnerIds(
                        CmsContactOwnerType.CsOwner,
                        start,
                        end,
                        request.OwnerIds,
                        request.TeamNames,
                        request.IsByOwnerTeams)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Monthly Recurring Revenue Analytic by Cs Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse>>
        GetCmsMonthlyRecurringRevenueAnalyticByCsOwnerIds(
            [FromBody]
            InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CsOwner,
            AnalyticCategory.CmsMrrAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CsOwner,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse
        {
            ByOwnersDailyMonthlyRecurringRevenueAnalytics =
                await _internalAnalyticService.GetCmsDailyMonthlyRecurringRevenueAnalyticByContactOwnerIds(
                    CmsContactOwnerType.CsOwner,
                    start,
                    end,
                    request.OwnerIds,
                    request.TeamNames,
                    request.IsByOwnerTeams,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Daily Revenue Analytic by Cs Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyRevenueAnalyticByOwnersResponse>> GetCmsDailyRevenueAnalyticByCsOwnerIds(
        [FromBody]
        InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CsOwner,
            AnalyticCategory.CmsDailyRevenueAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CsOwner,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsDailyRevenueAnalyticByOwnersResponse
        {
            ByOwnersDailyRevenueAnalytic =
                await _internalAnalyticService.GetCmsDailyRevenueAnalyticByContactOwnerIds(
                    CmsContactOwnerType.CsOwner,
                    start,
                    end,
                    request.OwnerIds,
                    request.TeamNames,
                    request.IsByOwnerTeams,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Plan Distribution Analytic by Cs Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsPlanDistributionAnalyticByOwnersResponse>>
        GetCmsPlanDistributionAnalyticByCsOwnerIds(
            [FromBody]
            InternalCmsAnalyticByOwnersRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
            [
                ApplicationUserRole.InternalCmsTeamLead,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser
            ]) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CsOwner,
            AnalyticCategory.CmsPlanDistributionAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.CsOwner,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.IsByOwnerTeams,
            request.OwnerIds,
            request.TeamNames);

        var result = new GetCmsPlanDistributionAnalyticByOwnersResponse
        {
            ByOwnersDailyPlanDistributionAnalytic =
                await _internalAnalyticService.GetCmsPlanDistributionAnalyticByContactOwnerIds(
                    CmsContactOwnerType.CsOwner,
                    start,
                    end,
                    request.OwnerIds,
                    request.TeamNames,
                    request.IsByOwnerTeams,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Revenue Analytic by Partner Stack Groups.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticByPartnerStackGroupNamesResponse>>
        GetCmsAnalyticByPartnerStackGroupNames(
            [FromBody]
            InternalCmsAnalyticByPartnerStackGroupNamesRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.PartnerStackGroup,
            AnalyticCategory.CmsAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            groupNames: request.GroupNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.PartnerStackGroup,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            groupNames: request.GroupNames);

        var result = new GetCmsAnalyticByPartnerStackGroupNamesResponse
        {
            ByPartnerStackGroupNamesDailyAnalytic =
                await _internalAnalyticService.GetCmsAnalyticByPartnerStackGroupNames(
                    start,
                    end,
                    request.GroupNames,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Monthly Recurring Revenue Analytic With Analytic Database by Partner Stack Groups
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesResponse>>
        GetCmsMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByPartnerStackGroupNames(
            [FromBody]
            InternalCmsAnalyticByPartnerStackGroupNamesRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.PartnerStackGroup,
            AnalyticCategory.CmsMrrAnalyticWithAnalyticDatabase,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            groupNames: request.GroupNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesResponse
        {
            ByPartnerStackGroupNamesDailyMonthlyRecurringRevenueAnalytics =
                await _internalAnalyticService
                    .GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByPartnerStackGroupNames(
                        start,
                        end,
                        request.GroupNames)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Monthly Recurring Revenue Analytic by Partner Stack Groups.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesResponse>>
        GetCmsMonthlyRecurringRevenueAnalyticByPartnerStackGroupNames(
            [FromBody]
            InternalCmsAnalyticByPartnerStackGroupNamesRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.PartnerStackGroup,
            AnalyticCategory.CmsMrrAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            groupNames: request.GroupNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.PartnerStackGroup,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            groupNames: request.GroupNames);

        var result = new GetCmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesResponse
        {
            ByPartnerStackGroupNamesDailyMonthlyRecurringRevenueAnalytics =
                await _internalAnalyticService.GetCmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNames(
                    start,
                    end,
                    request.GroupNames,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Daily Revenue Analytic by Partner Stack Groups.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDailyRevenueAnalyticByPartnerStackGroupNamesResponse>>
        GetCmsDailyRevenueAnalyticByPartnerStackGroupNames(
            [FromBody]
            InternalCmsAnalyticByPartnerStackGroupNamesRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.PartnerStackGroup,
            AnalyticCategory.CmsDailyRevenueAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            groupNames: request.GroupNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.PartnerStackGroup,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            groupNames: request.GroupNames);

        var result = new GetCmsDailyRevenueAnalyticByPartnerStackGroupNamesResponse
        {
            ByPartnerStackGroupNamesDailyRevenueAnalytic =
                await _internalAnalyticService.GetCmsDailyRevenueAnalyticByPartnerStackGroupNames(
                    start,
                    end,
                    request.GroupNames,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Revenue Analytic by Partner Stack Groups.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsPlanDistributionAnalyticByPartnerStackGroupNamesResponse>>
        GetCmsPlanDistributionAnalyticByPartnerStackGroupNames(
            [FromBody]
            InternalCmsAnalyticByPartnerStackGroupNamesRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.PartnerStackGroup,
            AnalyticCategory.CmsPlanDistributionAnalytic,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            groupNames: request.GroupNames);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companiesCacheKey = CmsCacheKeyHelper.GetRevenueAnalyticKey(
            AnalyticType.PartnerStackGroup,
            AnalyticCategory.CmsAnalyticCompanies,
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            groupNames: request.GroupNames);

        var result = new GetCmsPlanDistributionAnalyticByPartnerStackGroupNamesResponse
        {
            ByPartnerStackGroupNamesDailyPlanDistributionAnalytic =
                await _internalAnalyticService.GetCmsPlanDistributionAnalyticByPartnerStackGroupNames(
                    start,
                    end,
                    request.GroupNames,
                    companiesCacheKey,
                    request.AllowCompaniesCache,
                    excludeMarkupPlan: request.IsExcludeMarkupPlan)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Additional Infos.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsCompanyAdditionalInfoResponse>> GetCmsCompanyAdditionalInfos(
        [FromBody]
        GetCmsCompanyAdditionalInfosRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var cacheKey = CmsCacheKeyHelper.GetCmsCompaniesChurnReasonKey(
            request.CompanyIds,
            request.HasChurnReason,
            request.HasTier,
            request.HasAllTimeRevenueAnalyticData);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companyAdditionalInfos = await _appDbContext
            .CmsCompanyAdditionalInfos
            .WhereIf(request.HasChurnReason, info => info.ChurnReason != null)
            .WhereIf(request.HasTier, info => info.CompanyTier != null)
            .ProjectTo<CmsCompanyAdditionalInfoViewModel>(_mapper.ConfigurationProvider)
            .ToListAsync();

        if (request.HasAllTimeRevenueAnalyticData)
        {
            companyAdditionalInfos = companyAdditionalInfos
                .Where(x => x.AllTimeRevenueAnalyticData?.TotalRevenue > 0).ToList();
        }

        // Deduplication logic
        var serverLocation = _configuration["SF_REGION"] ?? LocationNames.EastAsia;
        var deduplicationCompanyIdServerLocationLookup =
            await _companiesService.GetDeduplicationCompanyIdServerLocationLookup(serverLocation);

        companyAdditionalInfos = companyAdditionalInfos
            .Where(
                x => serverLocation == LocationNames.EastAsia
                    ? !deduplicationCompanyIdServerLocationLookup.ContainsKey(x.CompanyId)
                    : deduplicationCompanyIdServerLocationLookup.ContainsKey(x.CompanyId))
            .ToList();

        var result = new GetCmsCompanyAdditionalInfoResponse
        {
            CmsCompanyAdditionalInfos = companyAdditionalInfos
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Update Companies All-Time Revenue Analytic Data.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdateCompaniesAllTimeRevenueAnalyticData(
        [FromBody]
        UpdateCompaniesAllTimeRevenueAnalyticDataRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        await _internalAnalyticService.UpdateCompanyAllTimeRevenueAnalyticInfo(request.CompanyIds);

        return Ok(
            new ResponseViewModel
            {
                message = "Companies All Time Revenue Analytic updated"
            });
    }

    /// <summary>
    /// Get Companies Last Subscription Date.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> GetCompaniesLastSubscriptionDate(
        [FromBody]
        GetCompaniesLastSubscriptionDateRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var result = await _internalAnalyticService.GetCompanyLastSubscriptionDate(request.CompanyIds);

        return Ok(result);
    }
}