using System;
using System.Collections.Generic;
using System.Linq;
using Travis_backend.Enums;

namespace Travis_backend.CompanyDomain.Utils;

// Generic Result type for handling operation results with possible errors
public class Result<T, TError>
{
    public bool IsSuccess { get; }

    public T Value { get; }

    public TError Error { get; }

    private Result(bool isSuccess, T value, TError error)
    {
        IsSuccess = isSuccess;
        Value = value;
        Error = error;
    }

    public bool IsFailure => !IsSuccess;

    public static Result<T, TError> Success(T value) =>
        new Result<T, TError>(true, value, default);

    public static Result<T, TError> Failure(TError error) =>
        new Result<T, TError>(false, default, error);
}

public class RoleTypeConverter
{
    private static readonly IReadOnlyDictionary<string, StaffUserRole> RoleMapping = new Dictionary<string, StaffUserRole>(StringComparer.OrdinalIgnoreCase)
    {
        { "admin", StaffUserRole.Admin },
        { "superadmin", StaffUserRole.SuperAdmin },
        { "teamadmin", StaffUserRole.TeamAdmin },
        { "staff", StaffUserRole.Staff },
        { "marketing", StaffUserRole.Marketing },
        { "demoadmin", StaffUserRole.DemoAdmin },
        { "customrole", StaffUserRole.CustomRole }
    };

    private static readonly IReadOnlyDictionary<StaffUserRole, string> ReverseRoleMapping = new Dictionary<StaffUserRole, string>
    {
        { StaffUserRole.SuperAdmin, "SuperAdmin" },
        { StaffUserRole.Admin, "Admin" },
        { StaffUserRole.TeamAdmin, "TeamAdmin" },
        { StaffUserRole.Staff, "Staff" },
        { StaffUserRole.Marketing, "Marketing" },
        { StaffUserRole.DemoAdmin, "DemoAdmin" },
        { StaffUserRole.CustomRole, "CustomRole" }
    };

    public static Result<StaffUserRole, string> ConvertToEnum(string roleString)
    {
        if (string.IsNullOrWhiteSpace(roleString))
        {
            return Result<StaffUserRole, string>.Failure("Role string cannot be empty");
        }

        // Remove all whitespace and special characters
        var normalizedRole = new string(roleString
            .Where(c => !char.IsWhiteSpace(c))
            .ToArray());

        // Ignored the case
        if (RoleMapping.TryGetValue(normalizedRole, out var role))
        {
            return Result<StaffUserRole, string>.Success(role);
        }

        return Result<StaffUserRole, string>.Failure($"Invalid role string: {roleString}");
    }

    public static string ConvertToString(StaffUserRole role)
    {
        if (ReverseRoleMapping.TryGetValue(role, out var roleString))
        {
            return roleString;
        }

        throw new ArgumentException($"Unknown role enum value: {role}");
    }
}
