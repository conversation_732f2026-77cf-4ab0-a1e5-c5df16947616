﻿using System;

namespace Travis_backend.CampaignAnalyticsDomain.Models;

public class ReplyMessageOverviewDto
{
    public long OutMessageId { get; set; }

    public long ReplyMessageId { get; set; }

    public string ReplyMessageContent { get; set; }

    public DateTime RepliedAt { get; set; }

    public ReplyMessageOverviewDto(
        long outMessageId,
        long replyMessageId,
        string replyMessageContent,
        DateTime repliedAt)
    {
        OutMessageId = outMessageId;
        ReplyMessageId = replyMessageId;
        ReplyMessageContent = replyMessageContent;
        RepliedAt = repliedAt;
    }
}