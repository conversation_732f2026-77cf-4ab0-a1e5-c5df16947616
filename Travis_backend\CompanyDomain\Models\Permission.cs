namespace Travis_backend.CompanyDomain.Models;

public class Permission
{
    // See default channel message in only
    public bool IsShowDefaultChannelMessagesOnly { get; set; }

    public bool IsShowDefaultChannelBroadcastOnly { get; set; } = false;

    // Receive unassigned notification
    public bool ReceiveUnassignedNotifications { get; set; } = true;

    public bool IsMaskPhoneNumber { get; set; }

    public bool IsMaskEmail { get; set; }

    public bool AddAsCollaboratorWhenReply { get; set; }

    public bool AddAsCollaboratorWhenAssignedToOthers { get; set; } = false;

    public bool FilterMessageWithSelectedChannel { get; set; }

    public bool RemainAsCollaboratorWhenReassignedToTeamUnassigned { get; set; } = false;
}