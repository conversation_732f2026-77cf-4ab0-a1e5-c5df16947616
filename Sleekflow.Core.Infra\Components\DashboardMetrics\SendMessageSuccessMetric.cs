﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class SendMessageSuccessMetric : IDashboardMetric
{
    private readonly Output<string>? _resourceId;
    private readonly Output<string>? _resourceName;

    public SendMessageSuccessMetric(Output<string>? resourceId, Output<string>? resourceName)
    {
        _resourceId = resourceId;
        _resourceName = resourceName;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(
        DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs =
                    new[]
                    {
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "options"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "sharedTimeRange"
                            },
                            {
                                "isOptional", true
                            }
                        }
                    },
                Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                Settings =
                {
                    new Dictionary<string, object>()
                    {
                        {
                            "content", new Dictionary<string, object>()
                            {
                                {
                                    "options", new Dictionary<string, object>()
                                    {
                                        {
                                            "chart", new Dictionary<string, object>()
                                            {
                                                {
                                                    "metrics", new[]
                                                    {
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName",
                                                                        "conversation_meters.trigger_type.facebook.send_success"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name",
                                                                "conversation_meters.trigger_type.facebook.send_success"
                                                            },
                                                            {
                                                                "namespace", "azure.applicationinsights"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName",
                                                                        "conversation_meters.trigger_type.instagram.send_success"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name",
                                                                "conversation_meters.trigger_type.instagram.send_success"
                                                            },
                                                            {
                                                                "namespace", "azure.applicationinsights"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName",
                                                                        "conversation_meters.trigger_type.line.send_success"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name",
                                                                "conversation_meters.trigger_type.line.send_success"
                                                            },
                                                            {
                                                                "namespace", "azure.applicationinsights"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName",
                                                                        "conversation_meters.trigger_type.live_chat.send_success"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name",
                                                                "conversation_meters.trigger_type.live_chat.send_success"
                                                            },
                                                            {
                                                                "namespace", "azure.applicationinsights"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName",
                                                                        "conversation_meters.trigger_type.sms.send_success"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name",
                                                                "conversation_meters.trigger_type.sms.send_success"
                                                            },
                                                            {
                                                                "namespace", "azure.applicationinsights"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName",
                                                                        "conversation_meters.trigger_type.telegram.send_success"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name",
                                                                "conversation_meters.trigger_type.telegram.send_success"
                                                            },
                                                            {
                                                                "namespace", "azure.applicationinsights"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName",
                                                                        "conversation_meters.trigger_type.viber.send_success"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name",
                                                                "conversation_meters.trigger_type.viber.send_success"
                                                            },
                                                            {
                                                                "namespace", "azure.applicationinsights"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName",
                                                                        "conversation_meters.trigger_type.wechat.send_success"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name",
                                                                "conversation_meters.trigger_type.wechat.send_success"
                                                            },
                                                            {
                                                                "namespace", "azure.applicationinsights"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName",
                                                                        "conversation_meters.trigger_type.whatsapp_360_dialog.send_success"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name",
                                                                "conversation_meters.trigger_type.whatsapp_360_dialog.send_success"
                                                            },
                                                            {
                                                                "namespace", "azure.applicationinsights"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 7
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName",
                                                                        "conversation_meters.trigger_type.whatsapp_cloud_api.send_success"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _resourceName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name",
                                                                "conversation_meters.trigger_type.whatsapp_cloud_api.send_success"
                                                            },
                                                            {
                                                                "namespace", "azure.applicationinsights"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _resourceId!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    "title",
                                                    "[EAST-ASIA][COUNT] Conversation meter send message success "
                                                },
                                                {
                                                    "titleKind", 2
                                                },
                                                {
                                                    "visualization", new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "axisVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "x", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 2
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    "y", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 1
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "chartType", 2
                                                        },
                                                        {
                                                            "disablePinning", true
                                                        },
                                                        {
                                                            "legendVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "hideHoverCard", false
                                                                },
                                                                {
                                                                    "hideLabelNames", true
                                                                },
                                                                {
                                                                    "isVisible", true
                                                                },
                                                                {
                                                                    "position", 2
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}