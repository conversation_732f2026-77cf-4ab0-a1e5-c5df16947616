using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.FlowHubs.Handlers.ChannelMessage;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.FlowHubs.Handlers.ChannelMessage;

[TestFixture]
public class FacebookMessengerMessageHandlerTests
{
  private FacebookMessengerMessageHandler _handler;
  private ILogger<FacebookMessengerMessageHandler> _mockLogger;

  [SetUp]
  public void SetUp()
  {
    _mockLogger = Substitute.For<ILogger<FacebookMessengerMessageHandler>>();

    _handler = new FacebookMessengerMessageHandler(
        null,
        _mockLogger);
  }

  [Test]
  public void ChannelType_ShouldReturnFacebook()
  {
    // Act
    var result = _handler.ChannelType;

    // Assert
    Assert.That(result, Is.EqualTo(ChannelTypes.Facebook));
  }

  [Test]
  public async Task PrepareConversationMessageAsync_WithTextMessage_ShouldSetCorrectProperties()
  {
    // Arrange
    var companyId = "test-company-id";
    var channelIdentityId = "test-channel-id";
    var messageType = "text";
    var messageText = "Hello, this is a test message";
    var messageTag = "HUMAN_AGENT";

    var messageBody = new MessageBody
    {
      FacebookMessengerMessage = new FacebookMessengerMessageObject
      {
        Message = new FacebookPageMessengerMessageObject
        {
          Text = messageText
        },
        Tag = messageTag
      }
    };

    var conversation = new Conversation
    {
      Id = "test-conversation-id",
      facebookUser = new FacebookSender
      {
        FacebookId = "test-facebook-user-id",
        name = "Test User"
      }
    };

    var files = new List<IFormFile>();

    // Act
    var result = await _handler.PrepareConversationMessageAsync(
        companyId, channelIdentityId, messageType, messageBody, conversation, files);

    // Assert
    Assert.Multiple(() =>
    {
      Assert.That(result.Channel, Is.EqualTo(ChannelTypes.Facebook));
      Assert.That(result.ChannelIdentityId, Is.EqualTo(channelIdentityId));
      Assert.That(result.ConversationId, Is.EqualTo(conversation.Id));
      Assert.That(result.CompanyId, Is.EqualTo(companyId));
      Assert.That(result.IsSentFromSleekflow, Is.False);
      Assert.That(result.MessageType, Is.EqualTo(messageType));
      Assert.That(result.MessageContent, Is.EqualTo(messageText));
      Assert.That(result.MessageTag, Is.EqualTo(messageTag));
      Assert.That(result.facebookReceiver, Is.EqualTo(conversation.facebookUser));
      Assert.That(result.DeliveryType, Is.EqualTo(DeliveryType.FlowHubAction));
      Assert.That(result.AnalyticTags, Is.Null);
      Assert.That(files, Is.Empty);
    });
  }

  [Test]
  public void PrepareConversationMessageAsync_WithUnsupportedMessageType_ShouldThrowNotImplementedException()
  {
    // Arrange
    var companyId = "test-company-id";
    var channelIdentityId = "test-channel-id";
    var messageType = "unsupported";
    var messageBody = new MessageBody();
    var conversation = new Conversation { Id = "test-conversation-id" };
    var files = new List<IFormFile>();

    // Act & Assert
    var exception = Assert.ThrowsAsync<NotImplementedException>(async () =>
        await _handler.PrepareConversationMessageAsync(
            companyId, channelIdentityId, messageType, messageBody, conversation, files));

    Assert.That(exception.Message, Is.EqualTo($"Message type {messageType} not implemented for {ChannelTypes.Facebook}"));
  }
}