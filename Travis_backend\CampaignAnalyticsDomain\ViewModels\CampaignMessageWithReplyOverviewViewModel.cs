﻿using System;
using Travis_backend.CampaignAnalyticsDomain.Models;

namespace Travis_backend.CampaignAnalyticsDomain.ViewModels;

public class CampaignMessageWithReplyOverviewViewModel : CampaignMessageOverviewViewModel
{
    public long? ReplyMessageId { get; set; }

    public string? ReplyMessageContent { get; set; }

    public DateTime? RepliedAt { get; set; }

    public CampaignMessageWithReplyOverviewViewModel(
        long messageId,
        DateTime createdAt,
        string failedReason,
        string status,
        RecipientOverviewViewModel recipientOverview,
        long? replyMessageId,
        string? replyMessageContent,
        DateTime? repliedAt)
        : base(messageId, createdAt, failedReason, status, recipientOverview)
    {
        ReplyMessageId = replyMessageId;
        ReplyMessageContent = replyMessageContent;
        RepliedAt = repliedAt;
    }

    public CampaignMessageWithReplyOverviewViewModel(
        CampaignMessageOverviewDto campaignMessageOverviewDto,
        ReplyMessageOverviewDto? replyMessageOverviewDto)
        : base(campaignMessageOverviewDto)
    {
        ReplyMessageId = replyMessageOverviewDto?.ReplyMessageId;
        ReplyMessageContent = replyMessageOverviewDto?.ReplyMessageContent;
        RepliedAt = replyMessageOverviewDto?.RepliedAt;
    }

    public CampaignMessageWithReplyOverviewViewModel(
        CampaignMessageOverviewViewModel campaignMessageOverviewViewModel,
        ReplyMessageOverviewDto? replyMessageOverviewDto)
        : base(
            campaignMessageOverviewViewModel.MessageId,
            campaignMessageOverviewViewModel.CreatedAt,
            campaignMessageOverviewViewModel.FailedReason,
            campaignMessageOverviewViewModel.Status,
            campaignMessageOverviewViewModel.RecipientOverview)
    {
        ReplyMessageId = replyMessageOverviewDto?.ReplyMessageId;
        ReplyMessageContent = replyMessageOverviewDto?.ReplyMessageContent;
        RepliedAt = replyMessageOverviewDto?.RepliedAt;
    }
}