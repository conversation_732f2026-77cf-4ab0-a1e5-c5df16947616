using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Travis_backend.Enums;

namespace Travis_backend.AnalyticsDomain.Models;

public class AdvancedFilter
{
    [RegularExpression("^(AND|and|OR|or)$")]
    public string LogicalOperator { get; set; }

    public List<Filter> Filters { get; set; }

    public AdvancedFilter(string logicalOperator, List<Filter> filters)
    {
        LogicalOperator = logicalOperator;
        Filters = filters;
    }

    public static AdvancedFilter EmptyFilter()
    {
        return new AdvancedFilter("AND", new List<Filter>());
    }
}

public class Filter
{
    public string FieldName { get; set; }

    public SupportedOperator Operator { get; set; }

    public List<string> Values { get; set; }

    public Filter(string fieldName, SupportedOperator @operator, List<string> values)
    {
        FieldName = fieldName;
        Operator = @operator;
        Values = values;
    }
}