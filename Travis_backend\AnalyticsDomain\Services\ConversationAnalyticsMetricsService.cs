﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.Utils;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Services;

namespace Travis_backend.AnalyticsDomain.Services;

public interface IConversationAnalyticsMetricsService
{
    Task<List<ConversationAnalyticsMetricDto>> GetCommonMetricDailyLogsAsync(
        string companyId,
        DateOnly from,
        DateOnly to,
        List<Condition> conditions,
        AdvancedFilter advancedFilter,
        bool usingCache,
        bool usingBusinessHour,
        CancellationToken cancellationToken);

    ConversationAnalyticsMetricDto CalculateSummedLog(
        DateOnly? date,
        IReadOnlyCollection<ConversationAnalyticsMetricDto> metrics);

    Task<DateTime?> GetLastUpdateTime();
}

public class ConversationAnalyticsMetricsService : IConversationAnalyticsMetricsService
{
    private readonly IBusinessHourConfigService _businessHourConfigService;
    private readonly IAnalyticsService _analyticsService;
    private readonly IServiceProvider _serviceProvider;
    private readonly IMessagingChannelService _messagingChannelService;
    private readonly ICacheManagerService _cacheManagerService;

    public ConversationAnalyticsMetricsService(
        IBusinessHourConfigService businessHourConfigService,
        IAnalyticsService analyticsService,
        IServiceProvider serviceProvider,
        IMessagingChannelService messagingChannelService,
        ICacheManagerService cacheManagerService)
    {
        _businessHourConfigService = businessHourConfigService;
        _analyticsService = analyticsService;
        _serviceProvider = serviceProvider;
        _messagingChannelService = messagingChannelService;
        _cacheManagerService = cacheManagerService;
    }

    public async Task<List<ConversationAnalyticsMetricDto>> GetCommonMetricDailyLogsAsync(
        string companyId,
        DateOnly from,
        DateOnly to,
        List<Condition> conditions,
        AdvancedFilter advancedFilter,
        bool usingCache,
        bool usingBusinessHour,
        CancellationToken cancellationToken)
    {
        var isBusinessHourEnabled = await _businessHourConfigService.IsEnabledAsync(companyId) && usingBusinessHour;
        (conditions, advancedFilter) = await ConditionsSanitize(companyId, conditions, advancedFilter);
        var dimensionWrapper = ConversationAnalyticsDimensionUtils.GetDimensionWrapper(
            isBusinessHourEnabled,
            conditions,
            advancedFilter);

        var userProfileIds = await GetConditionalUserProfileIdAsync(companyId, conditions);

        var searchConditionHash = GetSearchConditionHash(dimensionWrapper, advancedFilter, conditions);

        var dateRange = new List<DateOnly>();
        for (var date = from; date <= to; date = date.AddDays(1))
        {
            dateRange.Add(date);
        }

        var dailyLogs = new ConcurrentBag<ConversationAnalyticsMetricDto>();
        await Parallel.ForEachAsync(
            dateRange,
            new ParallelOptions { MaxDegreeOfParallelism = 6 },
            async (thisDate, thisCancellationToken) =>
            {
                // todo remove the code block after performance testing
                if (!usingCache)
                {
                    dailyLogs.Add(
                        await GetCommonMetricDailyLogAsync(
                            companyId,
                            thisDate,
                            dimensionWrapper,
                            userProfileIds,
                            advancedFilter,
                            thisCancellationToken));

                    return;
                }

                var cachedKeyPattern = new ConversationAnalyticsMetricsCacheKeyPattern(
                    companyId,
                    thisDate,
                    searchConditionHash);

                var dailyLog = await _cacheManagerService.GetAndSaveCacheAsync(
                    cachedKeyPattern,
                    async () => await GetCommonMetricDailyLogAsync(
                        companyId,
                        thisDate,
                        dimensionWrapper,
                        userProfileIds,
                        advancedFilter,
                        thisCancellationToken));

                dailyLogs.Add(dailyLog);
            });

        return dailyLogs.OrderBy(d => d.PeriodStartDate).ToList();
    }

    public ConversationAnalyticsMetricDto CalculateSummedLog(
        DateOnly? date,
        IReadOnlyCollection<ConversationAnalyticsMetricDto> metrics)
    {
        return ConversationAnalyticsMetricUtils.Aggregate(date, metrics);
    }

    private async Task<ConversationAnalyticsMetricDto> GetCommonMetricDailyLogAsync(
        string companyId,
        DateOnly date,
        ConversationAnalyticsDimensionWrapper dimensionWrapper,
        ICollection<string> userProfileIds,
        AdvancedFilter advancedFilter,
        CancellationToken cancellationToken)
    {
        var conversationAnalyticsMetrics = await GetFilteredConversationAnalyticsMetricsAsync(
            companyId,
            date,
            dimensionWrapper,
            userProfileIds,
            advancedFilter,
            cancellationToken);

        // Business hour dimension only contains time related data points,
        // so we need to fetch other data points from it's base dimension
        if (dimensionWrapper.IsBusinessHourDimension)
        {
            var baseDimensionWrapper =
                ConversationAnalyticsDimensionUtils.GetBaseDimensionWrapperFromBusinessHourDimension(
                    dimensionWrapper);

            var partialResultsForNonTimeDataPoints = await GetFilteredConversationAnalyticsMetricsAsync(
                companyId,
                date,
                baseDimensionWrapper,
                userProfileIds,
                advancedFilter,
                cancellationToken);

            // aggregate and set time data point
            var aggregatedResult = ConversationAnalyticsMetricUtils.Aggregate(partialResultsForNonTimeDataPoints);
            aggregatedResult.SetTimeRelatedDataPointsToZero();

            conversationAnalyticsMetrics.Add(aggregatedResult);
        }

        var dailyLog = ConversationAnalyticsMetricUtils.AggregateToDto(date, conversationAnalyticsMetrics);

        return dailyLog;
    }

    public async Task<DateTime?> GetLastUpdateTime()
    {
        using var scope = _serviceProvider.CreateScope();
        var conversationAnalyticsMetricsRepository =
            scope.ServiceProvider.GetRequiredService<IConversationAnalyticsMetricsRepository>();

        return await conversationAnalyticsMetricsRepository.GetLastUpdateTime();
    }


    public async Task<(List<Condition>, AdvancedFilter)> ConditionsSanitize(
        string companyId,
        List<Condition> conditions,
        AdvancedFilter advancedFilter)
    {
        var allChannels = await _messagingChannelService.GetAllChannelsAsync(companyId);

        foreach (var condition in conditions)
        {
            Filter filter;
            switch (condition.FieldName.ToLower())
            {
                case ConditionFields.ConversationAssignee:
                    filter = new Filter(
                        "assignee",
                        condition.ConditionOperator,
                        new List<string>
                        {
                        });
                    foreach (var value in condition.Values)
                    {
                        filter.Values.Add(value);
                    }

                    advancedFilter.Filters.Add(filter);

                    break;
                case ConditionFields.ConversationChannel:

                    filter = new Filter(
                        "channel",
                        condition.ConditionOperator,
                        new List<string>
                        {
                        });

                    foreach (var value in condition.Values)
                    {
                        filter.Values.Add(value);
                    }

                    advancedFilter.Filters.Add(filter);

                    break;
                case ConditionFields.ConversationChannelType:
                    if (condition.Values.Contains("whatsapp"))
                    {
                        condition.Values.Add(ChannelTypes.Whatsapp360Dialog);
                        condition.Values.Add(ChannelTypes.WhatsappCloudApi);
                    }

                    var channels = allChannels.Where(x => condition.Values.Contains(x.ChannelType))
                        .Select(x => x.ChannelType + ":" + x.ChannelIdentityId).ToList();

                    filter = new Filter(
                        "channel",
                        condition.ConditionOperator,
                        new List<string>
                        {
                        });

                    foreach (var channel in channels)
                    {
                        filter.Values.Add(channel);
                    }

                    advancedFilter.Filters.Add(filter);

                    break;
                default:
                    break;
            }
        }

        if (conditions.Any() && advancedFilter.Filters.Any())
        {
            advancedFilter.LogicalOperator = conditions[0].NextOperator == SupportedNextOperator.Or ? "or" : "and";
        }

        conditions.RemoveAll(x=>x.FieldName.ToLower() == ConditionFields.ConversationAssignee || x.FieldName.ToLower() == ConditionFields.ConversationChannel || x.FieldName.ToLower() == ConditionFields.ConversationChannelType);
        return (conditions, advancedFilter);
    }

    private async Task<List<ConversationAnalyticsMetric>> GetFilteredConversationAnalyticsMetricsAsync(
        string companyId,
        DateOnly date,
        ConversationAnalyticsDimensionWrapper dimensionWrapper,
        ICollection<string> userProfileIds,
        AdvancedFilter advancedFilter,
        CancellationToken cancellationToken)
    {
        // This function is designed to be called in parallel, thus we assign a new scope for each call.
        // Analytic DB is readonly so guaranteed to be thread-safe
        using var scope = _serviceProvider.CreateScope();
        var conversationAnalyticsMetricsRepository =
            scope.ServiceProvider.GetRequiredService<IConversationAnalyticsMetricsRepository>();

        var conversationAnalyticsMetrics = new List<ConversationAnalyticsMetric>();
        if (dimensionWrapper.IsContactDimension && userProfileIds is { Count: 0})
        {
            // directly return the empty list
        }
        else if (userProfileIds.Count < 10000)
        {
            conversationAnalyticsMetrics = await conversationAnalyticsMetricsRepository.GetDailyDataByDimensionAsync(
                companyId,
                date,
                dimensionWrapper,
                userProfileIds,
                cancellationToken);

            ConversationAnalyticsMetricUtils.Sanitize(ref conversationAnalyticsMetrics);
            ConversationAnalyticsMetricUtils.ApplyAdvancedFilter(ref conversationAnalyticsMetrics, advancedFilter);
        }
        else
        {
            // Given assumption:
            //   "In most cases, the maximum volume of a daily dimensional data is less than 10,000 records."
            //
            // So when userProfileIds > 10,000,
            // we do not add the userProfileIds as the query condition,
            // instead we fetch the daily data by page, then filter & aggregate the data in memory.
            const int pageSize = 10000;
            var skip = 0;

            var pageCount = 0;
            do
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    break;
                }

                var pageResult = await conversationAnalyticsMetricsRepository.GetPaginatedDailyDataAsync(
                    companyId,
                    date,
                    dimensionWrapper,
                    skip,
                    pageSize,
                    cancellationToken);

                pageCount = pageResult.Count;

                var filteredResults = pageResult.Where(x => userProfileIds.Contains(x.UserProfileId)).ToList();

                ConversationAnalyticsMetricUtils.Sanitize(ref filteredResults);
                ConversationAnalyticsMetricUtils.ApplyAdvancedFilter(ref filteredResults, advancedFilter);

                var aggregatedResult = ConversationAnalyticsMetricUtils.Aggregate(filteredResults);
                conversationAnalyticsMetrics.Add(aggregatedResult);

                skip += pageCount;
            }
            while (pageCount == pageSize);
        }

        return conversationAnalyticsMetrics;
    }

    private async Task<List<string>> GetConditionalUserProfileIdAsync(string companyId, List<Condition> conditions)
    {
        if (conditions is { Count: 0 })
        {
            return[];
        }

        var conditionHash = _analyticsService.GetConditionHash(conditions);
        var (_, userProfileIds) = await _analyticsService.GetConditionalUserProfileIds(
            null,
            null!,
            companyId,
            conditions,
            conditionHash);

        return userProfileIds;
    }

    private static string GetSearchConditionHash(
        ConversationAnalyticsDimensionWrapper dimensionWrapper,
        AdvancedFilter advancedFilter,
        List<Condition> conditions)
    {
        var filterJString = JsonConvert.SerializeObject(new
        {
            IsBusinessHourEnabled = dimensionWrapper.IsBusinessHourDimension,
            AdvancedFilter = advancedFilter,
            Conditions = conditions
        });

        return SHA256Helper.sha256_hash(filterJString);
    }
}