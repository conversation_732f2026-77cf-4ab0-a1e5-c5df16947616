name: master_sleekflow-prod-api(preproduction)

on:
  push:
    branches:
      - master
  workflow_dispatch:

jobs:
  set-env:
    name: Set Environment Variables
    runs-on: ubuntu-latest
    outputs:
      branch_name: ${{ steps.set-branch.outputs.name }}
    steps:
      - id: set-branch
        run: |
          # Get the branch name and replace '/' with '-'
          branch="${{ github.ref_name }}"
          if [ -z "$branch" ]; then
            echo "name=latest" >> $GITHUB_OUTPUT
          else
            echo "name=${branch//\//-}" >> $GITHUB_OUTPUT
          fi

  build-and-deploy:
    needs: set-env
    runs-on: windows-latest
    concurrency:
      group: sleekflow-core-deploy-api--${{ needs.set-env.outputs.branch_name }}
      cancel-in-progress: false

    environment:
      name: 'preproduction'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}

    env:
      NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages
      AZURE_WEBAPP_NAME: sleekflow-prod-api
      AZURE_WEBAPP_SLOT_NAME: preproduction
      AZURE_RESOURCE_GROUP: SleekFlow_Prod_V3
      AZURE_WEBAPP_PACKAGE_PATH: ${{github.workspace}}/myapp
      DOTNET_VERSION: 8.0.303
      DOTNET_PROJECT: .\Travis_backend.Auth0\Travis_backend.Auth0.csproj

    permissions:
      id-token: write
      contents: read

    steps:
      - uses: actions/checkout@v3

      - name: Set up .NET Core
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}

      - name: Load packages form cache
        uses: actions/cache@v3
        with:
          path: ${{ env.NUGET_PACKAGES }}
          key: ${{ runner.os }}-nuget-packages-${{ hashFiles('**/*.csproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-packages-

      - name: Restore packages with dotnet
        run: dotnet restore ${{ env.DOTNET_PROJECT }}

      - name: dotnet publish
        run: dotnet publish ${{ env.DOTNET_PROJECT }} --configuration Release -property:PublishDir=${{ env.AZURE_WEBAPP_PACKAGE_PATH }} --runtime win-x64 --self-contained /p:ExcludeBuildDbMigration=TRUE

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CSP_LOGIN_CREDS }}

      - name: Stop App Service Slot
        run: |
          az webapp stop --name ${{ env.AZURE_WEBAPP_NAME }} --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --slot ${{ env.AZURE_WEBAPP_SLOT_NAME }}

      - name: Deploy to Azure Web App
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v2.2.11
        with:
          app-name: ${{ env.AZURE_WEBAPP_NAME }}
          slot-name: ${{ env.AZURE_WEBAPP_SLOT_NAME }}
          publish-profile: ${{ secrets.AZUREAPPSERVICE_PUBLISHPROFILE_SLEEKFLOW_PROD_API_PREPRODUCTION }}
          package: ${{ env.AZURE_WEBAPP_PACKAGE_PATH }}

      - name: Start App Service Slot
        run: |
          az webapp start --name ${{ env.AZURE_WEBAPP_NAME }} --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --slot ${{ env.AZURE_WEBAPP_SLOT_NAME }}
