using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Travis_backend.Enums;

namespace Travis_backend.ContactDomain.SqlBuilders;

public partial class DefaultFieldSetterSqlBuilder(
    string companyId,
    string userProfileId,
    string companyDefinedFieldId,
    string fieldName,
    string value)
{
    private readonly string fieldName = NormalizeFieldName(fieldName);

    private string OverwriteSqlTemplate => $@"
        MERGE UserProfileCustomFields AS target
        USING (
            SELECT 
                @{fieldName}_CompanyDefinedFieldId AS CompanyDefinedFieldId,
                @{fieldName}_Value AS Value,
                @{fieldName}_CompanyId AS CompanyId,
                @{fieldName}_UserProfileId AS UserProfileId
        ) AS source ON (
            target.CompanyId = source.CompanyId
            AND target.CompanyDefinedFieldId = source.CompanyDefinedFieldId
            AND target.UserProfileId = source.UserProfileId
        )
        WHEN MATCHED AND (
            target.Value != source.Value
            OR (target.Value IS NULL AND source.Value IS NOT NULL)
            OR (target.Value IS NOT NULL AND source.Value IS NULL)
        ) THEN
            UPDATE SET Value = source.Value
        WHEN NOT MATCHED THEN
            INSERT (CompanyDefinedFieldId, Value, CompanyId, UserProfileId)
            VALUES (source.CompanyDefinedFieldId, source.Value, source.CompanyId, source.UserProfileId);";

    private string UpdateBlankOnlySqlTemplate => $@"
        MERGE UserProfileCustomFields AS target
        USING (
            SELECT 
                @{fieldName}_CompanyDefinedFieldId AS CompanyDefinedFieldId,
                @{fieldName}_Value AS Value,
                @{fieldName}_CompanyId AS CompanyId,
                @{fieldName}_UserProfileId AS UserProfileId
        ) AS source ON (
            target.CompanyId = source.CompanyId
            AND target.CompanyDefinedFieldId = source.CompanyDefinedFieldId
            AND target.UserProfileId = source.UserProfileId
        )
        WHEN MATCHED AND (target.Value IS NULL OR target.Value = '') THEN
            UPDATE SET Value = source.Value
        WHEN NOT MATCHED THEN
            INSERT (CompanyDefinedFieldId, Value, CompanyId, UserProfileId)
            VALUES (source.CompanyDefinedFieldId, source.Value, source.CompanyId, source.UserProfileId);";

    private string AppendSqlTemplate => $@"
        MERGE UserProfileCustomFields AS target
        USING (
            SELECT 
                @{fieldName}_CompanyDefinedFieldId AS CompanyDefinedFieldId,
                @{fieldName}_Value AS Value,
                @{fieldName}_CompanyId AS CompanyId,
                @{fieldName}_UserProfileId AS UserProfileId,
                ccupf.Type AS FieldType
            FROM (
                SELECT 
                    @{fieldName}_CompanyDefinedFieldId AS CompanyDefinedFieldId,
                    @{fieldName}_Value AS Value,
                    @{fieldName}_CompanyId AS CompanyId,
                    @{fieldName}_UserProfileId AS UserProfileId
            ) AS input
            INNER JOIN CompanyCustomUserProfileFields ccupf ON ccupf.Id = input.CompanyDefinedFieldId
            WHERE ccupf.Type = 1
        ) AS source ON (
            target.CompanyId = source.CompanyId
            AND target.CompanyDefinedFieldId = source.CompanyDefinedFieldId
            AND target.UserProfileId = source.UserProfileId
        )
        WHEN MATCHED THEN
            UPDATE SET Value = CASE
                WHEN (target.Value IS NULL OR target.Value = '') THEN source.Value
                ELSE target.Value + ';' + source.Value
            END
        WHEN NOT MATCHED THEN
            INSERT (CompanyDefinedFieldId, Value, CompanyId, UserProfileId)
            VALUES (source.CompanyDefinedFieldId, source.Value, source.CompanyId, source.UserProfileId);";

    public (string Sql, Dictionary<string, object> ParamNameToValue) ToSqlWithParameters(ImportAction importAction)
    {
        var paramNameToValue = new Dictionary<string, object>
            {
                { $"{fieldName}_CompanyId", companyId },
                { $"{fieldName}_UserProfileId", userProfileId },
                { $"{fieldName}_CompanyDefinedFieldId", companyDefinedFieldId },
                { $"{fieldName}_Value", (object)value ?? DBNull.Value }
            };

        return importAction switch
        {
            ImportAction.Overwrite => (OverwriteSqlTemplate, paramNameToValue),
            ImportAction.UpdateBlankOnly => (UpdateBlankOnlySqlTemplate, paramNameToValue),
            ImportAction.Append => (AppendSqlTemplate, paramNameToValue),
            _ => throw new NotImplementedException()
        };
    }

    [GeneratedRegex(@"[\s\W]+")]
    private static partial Regex WhitespaceAndSpecialCharRegex();

    private static string NormalizeFieldName(string fieldName)
    {
        if (string.IsNullOrWhiteSpace(fieldName))
        {
            throw new ArgumentException("Field name cannot be null or whitespace.");
        }

        return WhitespaceAndSpecialCharRegex()
            .Replace(fieldName, "_")
            .ToLowerInvariant();
    }
}
