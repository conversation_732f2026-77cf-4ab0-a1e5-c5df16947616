﻿using System;
using Travis_backend.Enums;

namespace Travis_backend.AnalyticsDomain.Services.ConversationAnalyticsAdvanceFilters;

public static class FilterStrategyFactory
{
    public static IFilterStrategy GetFilterStrategy(SupportedOperator operatorType)
    {
        return operatorType switch
        {
            SupportedOperator.ContainsAny => new ContainsAnyFilterStrategy(),
            SupportedOperator.IsNotContainsAny => new IsNotContainsAnyFilterStrategy(),
            _ => throw new ArgumentOutOfRangeException($"Not supported operator: {operatorType}"),
        };
    }
}