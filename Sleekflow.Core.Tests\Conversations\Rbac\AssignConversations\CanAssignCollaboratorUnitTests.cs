using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;

namespace Sleekflow.Core.Tests.Conversations.Rbac.AssignConversations;

[TestFixture]
public class CanAssignCollaboratorUnitTests
{
    private IRbacConversationPermissionManager _rbacConversationPermissionManager;

    private StaffAccessControlAggregate _adminA;
    private StaffAccessControlAggregate _teamAdminA;
    private StaffAccessControlAggregate _staffA;
    private StaffAccessControlAggregate _adminB;
    private StaffAccessControlAggregate _teamAdminB;
    private StaffAccessControlAggregate _staffB;
    private StaffAccessControlAggregate _adminC;
    private StaffAccessControlAggregate _adminInAnotherCompany;
    private StaffAccessControlAggregate _staffC;
    private StaffAccessControlAggregate _staffD;
    private TeamAccessControlAggregate _teamA;
    private TeamAccessControlAggregate _teamB;
    private RbacRole _admin;
    private RbacRole _teamAdmin;
    private RbacRole _staff;
    private string _companyId;
    private string _anotherCompanyId;
    private StaffCollaboratorChangeViewModel _addStaffA_And_StaffB_And_StaffC_AsCollaborator;
    private StaffCollaboratorChangeViewModel _addStaffCAsCollaborator;
    private StaffCollaboratorChangeViewModel _removeCollaboratorStaff_A_And_Staff_B;
    private StaffCollaboratorChangeViewModel _removeCollaboratorStaff_C;

    private StaffAssigneeChangeViewModel _staffAssigneeChange;

    private StaffAssignmentViewModel _staffAssignmentViewModel;


    [SetUp]
    public void Setup()
    {
        _rbacConversationPermissionManager =
            new RbacConversationPermissionManager(new RbacDefaultChannelPermissionManager());

        _companyId = "sleekflow";
        _anotherCompanyId = "another_companyId";

        _addStaffA_And_StaffB_And_StaffC_AsCollaborator = new StaffCollaboratorChangeViewModel
        {
            AdditionalAssigneeIds = ["StaffA", "StaffB", "StaffC"]
        };

        _addStaffCAsCollaborator = new StaffCollaboratorChangeViewModel
        {
            AdditionalAssigneeIds = ["StaffC"]
        };

        _removeCollaboratorStaff_C = new StaffCollaboratorChangeViewModel()
        {
            AdditionalAssigneeIds = ["StaffA", "StaffB"],
        };

        _removeCollaboratorStaff_A_And_Staff_B = new StaffCollaboratorChangeViewModel()
        {
            AdditionalAssigneeIds = ["StaffC"],
        };

        // Teams
        _teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long>
            {
                1, 2, 3
            }
        };
        _teamB = new TeamAccessControlAggregate
        {
            Id = 2,
            TeamMemberStaffIds = new List<long>
            {
                4, 5, 6
            }
        };

        _admin = new RbacRole
        {
            SleekflowRoleName = "Admin",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllAssignedConversations,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations,
                RbacAssignConversationsPermissions.AssignToAnyUser
            ]
        };

        // Conversation access rules for a team admin:
        // - Assigned to Associated Teams: Must be linked to at least one team in `associatedTeamIds`.
        // - Team Member as Contact Owner: Must have a contact owner from `teamMemberStaffIds`.
        // - Team Member as Collaborator: Must include a collaborator from `teamMemberStaffIds`.
        _teamAdmin = new RbacRole
        {
            SleekflowRoleName = "TeamAdmin",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.AssignedToMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations,
                RbacAssignConversationsPermissions.AssignToAnyUser
            ]
        };

        _staff = new RbacRole
        {
            SleekflowRoleName = "Staff",
            SleekflowCompanyId = "sleekflow",
            RbacRolePermissions =
            [
                RbacViewConversationsPermissions.AssignedToMe,
                RbacViewConversationsPermissions.UnassignedConversationsUnderMyTeam,
                RbacViewConversationsPermissions.AllUnassignedConversations,
                RbacAssignConversationsPermissions.AssignToMe
            ]
        };

        #region Team A members

        _adminA = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = [_admin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };

        _teamAdminA = new StaffAccessControlAggregate
        {
            StaffId = 2,
            RbacRoles = [_teamAdmin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };


        _staffA = new StaffAccessControlAggregate
        {
            StaffId = 3,
            CompanyId = _companyId,
            RbacRoles = [_staff],
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };

        #endregion

        #region Team B members

        _adminB = new StaffAccessControlAggregate
        {
            StaffId = 4,
            RbacRoles = [_admin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _teamAdminB = new StaffAccessControlAggregate
        {
            StaffId = 5,
            RbacRoles = [_teamAdmin],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _staffB = new StaffAccessControlAggregate
        {
            StaffId = 6,
            RbacRoles = [_staff],
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };

        #endregion


        // Others
        _adminC = new StaffAccessControlAggregate
        {
            StaffId = 7, RbacRoles = [_admin], CompanyId = _companyId
        };

        _staffC = new StaffAccessControlAggregate
        {
            StaffId = 8, RbacRoles = [_staff], CompanyId = _companyId, StaffIdentityId = "StaffC"
        };


    }

    [Test]
    public void staff_with_can_assign_conversation_to_any_user_permission_can_add_multiple_collaborators()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId, AssigneeId = _staffC.StaffId, AdditionalAssignees = new List<AdditionalAssignee>()
        };

        // Add Staff A, Staff B, and Staff C as collaborators
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssignCollaborator(_adminA, conversation, _addStaffA_And_StaffB_And_StaffC_AsCollaborator));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssignCollaborator(_teamAdminA, conversation, _addStaffA_And_StaffB_And_StaffC_AsCollaborator));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssignCollaborator(_adminA, conversation, _addStaffCAsCollaborator));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssignCollaborator(_teamAdminA, conversation, _addStaffCAsCollaborator));

    }

    [Test]
    public void staff_with_can_assign_conversation_to_me_permission_can_only_add_themselves_as_collaborator()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId, AssigneeId = _staffC.StaffId, AdditionalAssignees = new List<AdditionalAssignee>()
        };

        // Add Staff A, Staff B, and Staff C as collaborators
        Assert.IsFalse(_rbacConversationPermissionManager.CanAssignCollaborator(_staffA, conversation, _addStaffA_And_StaffB_And_StaffC_AsCollaborator));
        Assert.IsFalse(_rbacConversationPermissionManager.CanAssignCollaborator(_staffC, conversation, _addStaffA_And_StaffB_And_StaffC_AsCollaborator));

        // Add only Staff C as collaborator
        Assert.IsFalse(_rbacConversationPermissionManager.CanAssignCollaborator(_staffA, conversation, _addStaffCAsCollaborator));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssignCollaborator(_staffC, conversation, _addStaffCAsCollaborator));

    }

    [Test]
    public void staff_with_can_assign_conversation_to_me_permission_can_only_remove_themselves_from_collaborators()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId, AssigneeId = _staffC.StaffId, AdditionalAssignees = new List<AdditionalAssignee>()
            {
                new AdditionalAssignee
                {
                    Assignee = new Staff {Id = 3, IdentityId = "StaffA"}
                },
                new AdditionalAssignee
                {
                    Assignee = new Staff {Id = 6, IdentityId = "StaffB"}
                },
                new AdditionalAssignee
                {
                    Assignee = new Staff {Id = 8, IdentityId = "StaffC"}
                }
            }
        };

        Assert.IsFalse(_rbacConversationPermissionManager.CanAssignCollaborator(_staffA, conversation, _removeCollaboratorStaff_A_And_Staff_B));
        Assert.IsFalse(_rbacConversationPermissionManager.CanAssignCollaborator(_staffC, conversation, _removeCollaboratorStaff_A_And_Staff_B));
        Assert.IsFalse(_rbacConversationPermissionManager.CanAssignCollaborator(_staffA, conversation, _removeCollaboratorStaff_C));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssignCollaborator(_staffC, conversation, _removeCollaboratorStaff_C));
    }

    [Test]
    public void staff_with_can_assign_conversation_to_any_user_permission_can_remove_multiple_collaborators()
    {
        var conversation = new Conversation
        {
            Id = "conversationC", CompanyId = _companyId, AssigneeId = _staffC.StaffId, AdditionalAssignees = new List<AdditionalAssignee>()
            {
                new AdditionalAssignee
                {
                    Assignee = new Staff {Id = 3, IdentityId = "StaffA"}
                },
                new AdditionalAssignee
                {
                    Assignee = new Staff {Id = 6, IdentityId = "StaffB"}
                },
                new AdditionalAssignee
                {
                    Assignee = new Staff {Id = 8, IdentityId = "StaffC"}
                }
            }
        };

        Assert.IsTrue(_rbacConversationPermissionManager.CanAssignCollaborator(_adminA, conversation, _removeCollaboratorStaff_A_And_Staff_B));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssignCollaborator(_teamAdminA, conversation, _removeCollaboratorStaff_A_And_Staff_B));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssignCollaborator(_adminA, conversation, _removeCollaboratorStaff_C));
        Assert.IsTrue(_rbacConversationPermissionManager.CanAssignCollaborator(_teamAdminA, conversation, _removeCollaboratorStaff_C));
    }
}