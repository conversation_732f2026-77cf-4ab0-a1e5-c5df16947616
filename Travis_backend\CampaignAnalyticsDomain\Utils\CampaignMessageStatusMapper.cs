﻿using System;
using System.Collections.Generic;
using Travis_backend.CampaignAnalyticsDomain.Constants;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.CampaignAnalyticsDomain.Utils;

public static class CampaignMessageStatusMapper
{
    public static string Map(MessageStatus messageStatus)
    {
        // Escalate downward.
        // CampaignMessageStatuses.Replied will be handled separately
        if (MessageStatusGroups.Bounced.Contains(messageStatus))
        {
            return CampaignMessageStatuses.Bounced;
        }

        if (MessageStatusGroups.Read.Contains(messageStatus))
        {
            return CampaignMessageStatuses.Read;
        }

        if (MessageStatusGroups.Delivered.Contains(messageStatus))
        {
            return CampaignMessageStatuses.Delivered;
        }

        return CampaignMessageStatuses.Sent;
    }

    public static IEnumerable<MessageStatus> Map(string messageStatus)
    {
        return messageStatus switch
        {
            CampaignMessageStatuses.Sent => MessageStatusGroups.Sent,
            CampaignMessageStatuses.Delivered => MessageStatusGroups.Delivered,
            CampaignMessageStatuses.Read => MessageStatusGroups.Read,
            CampaignMessageStatuses.Bounced => MessageStatusGroups.Bounced,
            _ => throw new ArgumentOutOfRangeException()
        };
    }
}