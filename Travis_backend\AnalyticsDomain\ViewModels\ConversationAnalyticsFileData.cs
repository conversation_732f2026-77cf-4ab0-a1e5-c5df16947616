﻿using System;

namespace Travis_backend.AnalyticsDomain.ViewModels;

public class ConversationAnalyticsFileData
{
    public const string PropertyNameDate = "Date";
    public const string PropertyNameNumberOfAllConversations = "Number of all conversations";
    public const string PropertyNameNumberOfActiveConversations = "Number of active conversations";
    public const string PropertyNameNumberOfMessagesSent = "Number of messages sent";
    public const string PropertyNameNumberOfMessagesReceived = "Number of messages received";
    public const string PropertyNameNumberOfMessagesFailed = "Number of messages failed";
    public const string PropertyNameResponseTimeForAllMessages = "Average reply time";
    public const string PropertyNameResponseTimeForFirstMessages = "Average first reply time";
    public const string PropertyNameResolutionTime = "Average resolution time";
    public const string PropertyNameNumberOfNewEnquires = "Number of new inquires";
    public const string PropertyNameNumberOfNewContacts = "Number of new contacts";
    public const string PropertyNameNumberOfBroadcastSent = "Number of broadcast sent";
    public const string PropertyNameNumberOfBroadcastBounced = "Number of broadcast bounced";
    public const string PropertyNameNumberOfBroadcastDelivered = "Number of broadcast delivered";
    public const string PropertyNameNumberOfBroadcastRead = "Number of broadcast read";
    public const string PropertyNameNumberOfBroadcastReplied = "Number of broadcast replied";

    public string Date { get; set; }

    public int NumberOfAllConversations { get; set; }

    public int NumberOfActiveConversations { get; set; }

    public int NumberOfMessagesSent { get; set; }

    public int NumberOfMessagesReceived { get; set; }

    public int NumberOfMessagesFailed { get; set; }

    public long ResponseTimeForAllMessages { get; set; }

    public long ResponseTimeForFirstMessages { get; set; }

    public long ResolutionTime { get; set; }

    public int NumberOfNewEnquires { get; set; }

    public int NumberOfNewContacts { get; set; }

    public int NumberOfBroadcastSent { get; set; }

    public int NumberOfBroadcastBounced { get; set; }

    public int NumberOfBroadcastDelivered { get; set; }

    public int NumberOfBroadcastRead { get; set; }

    public int NumberOfBroadcastReplied { get; set; }

    public ConversationAnalyticsFileData(
        DateOnly? date,
        int numberOfAllConversations,
        int numberOfActiveConversations,
        int numberOfMessagesSent,
        int numberOfMessagesReceived,
        int numberOfMessagesFailed,
        long responseTimeForAllMessages,
        long responseTimeForFirstMessages,
        long resolutionTime,
        int numberOfNewEnquires,
        int numberOfNewContacts,
        int numberOfBroadcastSent,
        int numberOfBroadcastBounced,
        int numberOfBroadcastDelivered,
        int numberOfBroadcastRead,
        int numberOfBroadcastReplied)
    {
        Date = date?.ToString("yyyy-MM-dd");
        NumberOfAllConversations = numberOfAllConversations;
        NumberOfActiveConversations = numberOfActiveConversations;
        NumberOfMessagesSent = numberOfMessagesSent;
        NumberOfMessagesReceived = numberOfMessagesReceived;
        NumberOfMessagesFailed = numberOfMessagesFailed;
        ResponseTimeForAllMessages = responseTimeForAllMessages;
        ResponseTimeForFirstMessages = responseTimeForFirstMessages;
        ResolutionTime = resolutionTime;
        NumberOfNewEnquires = numberOfNewEnquires;
        NumberOfNewContacts = numberOfNewContacts;
        NumberOfBroadcastSent = numberOfBroadcastSent;
        NumberOfBroadcastBounced = numberOfBroadcastBounced;
        NumberOfBroadcastDelivered = numberOfBroadcastDelivered;
        NumberOfBroadcastRead = numberOfBroadcastRead;
        NumberOfBroadcastReplied = numberOfBroadcastReplied;
    }

    public ConversationAnalyticsFileData(
        DateOnly? date,
        ConversationCommonMetricViewModel commonMetricViewModel,
        BroadcastMessageMetricViewModel broadcastMetricViewModel)
    {
        Date = date?.ToString("yyyy-MM-dd");
        NumberOfAllConversations = commonMetricViewModel.NumberOfAllConversations;
        NumberOfActiveConversations = commonMetricViewModel.NumberOfActiveConversations;
        NumberOfMessagesSent = commonMetricViewModel.NumberOfMessagesSent;
        NumberOfMessagesReceived = commonMetricViewModel.NumberOfMessagesReceived;
        NumberOfMessagesFailed = commonMetricViewModel.NumberOfMessagesFailed;
        ResponseTimeForAllMessages = commonMetricViewModel.ResponseTimeForAllMessages;
        ResponseTimeForFirstMessages = commonMetricViewModel.ResponseTimeForFirstMessages;
        ResolutionTime = commonMetricViewModel.ResolutionTime;
        NumberOfNewEnquires = commonMetricViewModel.NumberOfNewEnquires;
        NumberOfNewContacts = commonMetricViewModel.NumberOfNewContacts;
        NumberOfBroadcastSent = broadcastMetricViewModel.NumberOfBroadcastSent;
        NumberOfBroadcastBounced = broadcastMetricViewModel.NumberOfBroadcastBounced;
        NumberOfBroadcastDelivered = broadcastMetricViewModel.NumberOfBroadcastDelivered;
        NumberOfBroadcastRead = broadcastMetricViewModel.NumberOfBroadcastRead;
        NumberOfBroadcastReplied = broadcastMetricViewModel.NumberOfBroadcastReplied;
    }

    public string ToCsvLine()
    {
        var responseTimeForAllMessagesTimeSpan = TimeSpan.FromSeconds(ResponseTimeForAllMessages);
        var responseTimeForFirstMessagesTimeSpan = TimeSpan.FromSeconds(ResponseTimeForFirstMessages);
        var resolutionTimeTimeSpan = TimeSpan.FromSeconds(ResolutionTime);

        return string.Empty
               + $"{Date},"
               + $"{NumberOfAllConversations},"
               + $"{NumberOfActiveConversations},"
               + $"{NumberOfMessagesSent},"
               + $"{NumberOfMessagesReceived},"
               + $"{NumberOfMessagesFailed},"
               + $@"{responseTimeForAllMessagesTimeSpan:hh\:mm\:ss},"
               + $@"{responseTimeForFirstMessagesTimeSpan:hh\:mm\:ss},"
               + $@"{resolutionTimeTimeSpan:hh\:mm\:ss},"
               + $"{NumberOfNewEnquires},"
               + $"{NumberOfNewContacts},"
               + $"{NumberOfBroadcastSent},"
               + $"{NumberOfBroadcastBounced},"
               + $"{NumberOfBroadcastDelivered},"
               + $"{NumberOfBroadcastRead},"
               + $"{NumberOfBroadcastReplied}";
    }
}