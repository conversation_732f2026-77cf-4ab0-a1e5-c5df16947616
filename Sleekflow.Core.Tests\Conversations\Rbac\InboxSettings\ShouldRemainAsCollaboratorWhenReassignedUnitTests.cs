using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationSettingsConstants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Rbac.InboxSettings;

[TestFixture]
public class ShouldRemainAsCollaboratorWhenReassignedUnitTests
{
    private IInboxSettingManager _inboxSettingManager;

    [SetUp]
    public void SetUp()
    {
        _inboxSettingManager = new InboxSettingManager();
    }

    [Test]
    public void Staff_is_null_return_false()
    {
        // Arrange
        StaffAccessControlAggregate staff = null;
        var newContactOwner = new StaffAccessControlAggregate();
        var conversation = new Conversation();

        // Act
        bool result = _inboxSettingManager.ShouldRemainAsCollaboratorWhenReassigned(staff, newContactOwner, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void New_contact_owner_is_null_return_false()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate();
        StaffAccessControlAggregate newContactOwner = null;
        var conversation = new Conversation();

        // Act
        bool result = _inboxSettingManager.ShouldRemainAsCollaboratorWhenReassigned(staff, newContactOwner, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void Conversation_is_null_should_return_false()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate();
        var newContactOwner = new StaffAccessControlAggregate();
        Conversation conversation = null;

        // Act
        bool result = _inboxSettingManager.ShouldRemainAsCollaboratorWhenReassigned(staff, newContactOwner, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void Contact_owner_is_unchanged_return_false()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1
        };
        var newContactOwner = new StaffAccessControlAggregate
        {
            StaffId = 1
        };
        var conversation = new Conversation
        {
            AssigneeId = 1
        };

        // Act
        bool result = _inboxSettingManager.ShouldRemainAsCollaboratorWhenReassigned(staff, newContactOwner, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void No_assign_conversation_permission_return_false()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacInboxSettings.RemainAsCollaboratorWhenReassigned
                    }
                }
            }
        };
        var newContactOwner = new StaffAccessControlAggregate
        {
            StaffId = 2
        };
        var conversation = new Conversation
        {
            AssigneeId = 1
        };

        // Act
        bool result = _inboxSettingManager.ShouldRemainAsCollaboratorWhenReassigned(staff, newContactOwner, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void No_remain_as_collaborator_permission_return_false()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacAssignConversationsPermissions.AssignToMe
                    }
                }
            }
        };
        var newContactOwner = new StaffAccessControlAggregate
        {
            StaffId = 2
        };
        var conversation = new Conversation
        {
            AssigneeId = 1,
            AdditionalAssignees = new List<AdditionalAssignee>
            {
                new AdditionalAssignee
                {
                    AssigneeId = 1
                }
            }
        };

        // Act
        bool result = _inboxSettingManager.ShouldRemainAsCollaboratorWhenReassigned(staff,newContactOwner, conversation);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void Can_assign_conversation_and_has_remain_as_collaborator_permission_return_true()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacInboxSettings.RemainAsCollaboratorWhenReassigned,
                        RbacAssignConversationsPermissions.AssignToMe
                    }
                }
            }
        };
        var newContactOwner = new StaffAccessControlAggregate
        {
            StaffId = 2
        };
        var conversation = new Conversation
        {
            AssigneeId = 1, AdditionalAssignees = new List<AdditionalAssignee>()
        };

        // Act
        bool result = _inboxSettingManager.ShouldRemainAsCollaboratorWhenReassigned(staff, newContactOwner, conversation);

        // Assert
        Assert.IsTrue(result);
    }

    [Test]
    public void Can_assign_conversation_to_any_user_and_has_remain_as_collaborator_permission_return_true()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacInboxSettings.RemainAsCollaboratorWhenReassigned,
                        RbacAssignConversationsPermissions.AssignToAnyUser
                    }
                }
            }
        };
        var newContactOwner = new StaffAccessControlAggregate
        {
            StaffId = 2
        };
        var conversation = new Conversation
        {
            AssigneeId = 1, AdditionalAssignees = new List<AdditionalAssignee>()
        };

        // Act
        bool result = _inboxSettingManager.ShouldRemainAsCollaboratorWhenReassigned(staff, newContactOwner, conversation);

        // Assert
        Assert.IsTrue(result);
    }


    [Test]
    public void Already_a_collaborator_return_false()
    {
        // Arrange
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacInboxSettings.RemainAsCollaboratorWhenReassigned,
                        RbacAssignConversationsPermissions.AssignToMe
                    }
                }
            }
        };
        var newContactOwner = new StaffAccessControlAggregate
        {
            StaffId = 2
        };

        var conversation = new Conversation
        {
            AssigneeId = 1, AdditionalAssignees = new List<AdditionalAssignee> { new AdditionalAssignee {AssigneeId = 1}}
        };

        // Act
        bool result = _inboxSettingManager.ShouldRemainAsCollaboratorWhenReassigned(staff, newContactOwner, conversation);

        // Assert
        Assert.IsFalse(result);
    }
}