using Newtonsoft.Json.Linq;
using NSubstitute;
using Stripe;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.StripeIntegrationDomain.EventHandlers;
using Travis_backend.StripeIntegrationDomain.EventHandlers.Subscriptions;
using Travis_backend.StripeIntegrationDomain.Models.Subscriptions;
using Travis_backend.StripeIntegrationDomain.Services;
using File = System.IO.File;

namespace Sleekflow.Core.Tests.StripeIntegrationDomain.EventHandlers.Subscriptions;

[TestOf(typeof(StripeSubscriptionCustomerUpdatedEventHandler))]
public class StripeSubscriptionCustomerUpdatedEventHandlerTest
{
    /// <summary>
    /// ICompanySubscriptionService.
    /// </summary>
    private ICompanySubscriptionService _mockCompanySubscriptionService;

    /// <summary>
    /// IStripeCustomerService.
    /// </summary>
    private IStripeCustomerService _mockStripeCustomerService;

    /// <summary>
    /// IStripeEventHandler.
    /// </summary>
    private IStripeEventHandler _handler;

    /// <summary>
    /// Test Data
    /// </summary>
    private static JObject JsonTestData;

    [OneTimeSetUp]
    public void LoadTestData()
    {
        string path = Path.Combine("StripeIntegrationDomain", "EventHandlers", "Subscriptions", "TestData", "TestData_StripeSubscriptionCustomerUpdatedEventHandlerTest.json");
        JsonTestData = JObject.Parse(File.ReadAllText(path));
    }

    [SetUp]
    public void Setup()
    {
        _mockCompanySubscriptionService = Substitute.For<ICompanySubscriptionService>();
        _mockStripeCustomerService = Substitute.For<IStripeCustomerService>();

        _handler = new StripeSubscriptionCustomerUpdatedEventHandler(
            _mockCompanySubscriptionService,
            _mockStripeCustomerService);

        //// Common Mock
        _mockCompanySubscriptionService.GetSleekFlowStripeInvoiceFooter(Arg.Any<string>())
            .Returns(ci => string.IsNullOrWhiteSpace(ci.ArgAt<string>(0)) ? string.Empty : $"Bill From: {ci.ArgAt<string>(0)} Address");
    }

    [Test]
    public async Task Test_UpdateCustomerBillingAddressCountryFromEmptyToMalaysia_InvoiceFooterUpdatedToMY()
    {
        //// Arrange
        var data = JsonTestData[nameof(Test_UpdateCustomerBillingAddressCountryFromEmptyToMalaysia_InvoiceFooterUpdatedToMY)]!.ToObject<JObject>();
        var stripeEvent = CreateStripeEvent<Customer>(data!);

        //// Act
        await _handler.HandleAsync(stripeEvent);

        //// Assert
        await _mockCompanySubscriptionService.Received(1).GetSleekFlowStripeInvoiceFooter("MY");
        await _mockStripeCustomerService.Received(1).UpdateAsync(Arg.Is<UpdateCustomerRequest>(x => x.InvoiceFooter == "Bill From: MY Address"));
    }

    [Test]
    public async Task Test_UpdateCustomerInvoicePrefix_NoInvoiceFooterUpdated()
    {
        //// Arrange
        var data = JsonTestData[nameof(Test_UpdateCustomerInvoicePrefix_NoInvoiceFooterUpdated)]!.ToObject<JObject>();
        var stripeEvent = CreateStripeEvent<Customer>(data!);

        //// Act
        await _handler.HandleAsync(stripeEvent);

        //// Assert
        await _mockCompanySubscriptionService.Received(0).GetSleekFlowStripeInvoiceFooter(Arg.Any<string>());
        await _mockStripeCustomerService.Received(1).UpdateAsync(Arg.Is<UpdateCustomerRequest>(x => x.InvoiceFooter == null));
    }

    [Test]
    public async Task Test_UpdateCustomerBillingAddressCountryFromMalaysiaToHongKong_InvoiceFooterUpdatedToHK()
    {
        //// Arrange
        var data = JsonTestData[nameof(Test_UpdateCustomerBillingAddressCountryFromMalaysiaToHongKong_InvoiceFooterUpdatedToHK)]!.ToObject<JObject>();
        var stripeEvent = CreateStripeEvent<Customer>(data!);

        //// Act
        await _handler.HandleAsync(stripeEvent);

        //// Assert
        await _mockCompanySubscriptionService.Received(1).GetSleekFlowStripeInvoiceFooter("HK");
        await _mockStripeCustomerService.Received(1).UpdateAsync(Arg.Is<UpdateCustomerRequest>(x => x.InvoiceFooter == "Bill From: HK Address"));
    }

    [Test]
    public async Task Test_UpdateCustomerBillingAddressState_NoInvoiceFooterUpdated()
    {
        //// Arrange
        var data = JsonTestData[nameof(Test_UpdateCustomerBillingAddressState_NoInvoiceFooterUpdated)]!.ToObject<JObject>();
        var stripeEvent = CreateStripeEvent<Customer>(data!);

        //// Act
        await _handler.HandleAsync(stripeEvent);

        //// Assert
        await _mockCompanySubscriptionService.Received(0).GetSleekFlowStripeInvoiceFooter(Arg.Any<string>());
        await _mockStripeCustomerService.Received(1).UpdateAsync(Arg.Is<UpdateCustomerRequest>(x => x.InvoiceFooter == null));
    }

    [Test]
    public async Task Test_RemoveCustomerBillingAddress_UpdateInvoiceFooterEmpty()
    {
        //// Arrange
        var data = JsonTestData[nameof(Test_RemoveCustomerBillingAddress_UpdateInvoiceFooterEmpty)]!.ToObject<JObject>();
        var stripeEvent = CreateStripeEvent<Customer>(data!);

        //// Act
        await _handler.HandleAsync(stripeEvent);

        //// Assert
        await _mockCompanySubscriptionService.Received(1).GetSleekFlowStripeInvoiceFooter(null);
        await _mockStripeCustomerService.Received(1).UpdateAsync(Arg.Is<UpdateCustomerRequest>(x => x.InvoiceFooter == string.Empty));
    }

    private Event CreateStripeEvent<T>(JObject json) where T : IHasObject
    {
        var obj = json["object"]!.ToObject<T>();
        var pre = json["previous_attributes"]!.ToObject<JObject>();
        var stripeEvent = new Event();
        stripeEvent.Data = new EventData { Object = obj, PreviousAttributes = pre };
        return stripeEvent;
    }
}