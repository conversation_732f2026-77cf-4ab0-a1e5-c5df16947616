using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Travis_backend.AnalyticsDomain.Models;

[Table("topic_analytics_update_log")]
public class TopicAnalyticsUpdateLog
{
    [Column("triggered_pipeline_run_id")]
    [MaxLength(36)]
    public string TriggeredPipelineRunId { get; set; }

    [Column("start_time_utc")]
    public DateTime? StartTimeUtc { get; set; }

    [Column("end_time_utc")]
    public DateTime? EndTimeUtc { get; set; }

    [Column("sub_pipeline_run_id")]
    [MaxLength(36)]
    public string SubPipelineRunId { get; set; }
}