### Basic filter
POST {{baseUrl}}/api/contact/dynamicSearch
X-Sleekflow-Api-Key: BmRu1DrI4hEh01Up864pLdoTHKKxOJJCvuxIqPAsw
Content-Type: application/json

{
}

### All include fields
POST {{baseUrl}}/api/contact/dynamicSearch
X-Sleekflow-Api-Key: BmRu1DrI4hEh01Up864pLdoTHKKxOJJCvuxIqPAsw
Content-Type: application/json

{
    "Conditions": [
         {
             "FieldName": "firstName",
             "ConditionOperator": "contains",
             "Values": [
                 "Phil"
             ]
         }
    ],
    "Include": {
        "CustomFields": [
            "Email",
            "Subscriber"
        ],
        "labels": true,
        "latestMessage": true
    },
    "sort": {
        "field": "createdat",
        "order": "asc"
    },
    "Pagination": {
        "Offset": 5,
        "limit": 10
    }
}


### Condition: PhoneNumber ContainsAny
POST {{baseUrl}}/api/contact/dynamicSearch
X-Sleekflow-Api-Key: BmRu1DrI4hEh01Up864pLdoTHKKxOJJCvuxIqPAsw
Content-Type: application/json

{
  "Conditions": [
    {
      "FieldName": "PhoneNumber",
      "ConditionOperator": "containsAny",
      "Values": [
        "85294977061",
        "85291491162",
        "85291491132",
      ]
    }
  ],
  "Include": {
    "CustomFields": [
      "Email",
      "Subscriber"
    ],
    "labels": true,
    "latestMessage": true
  },
  "sort": {
    "field": "UpdatedAt",
    "order": "desc"
  },
  "Pagination": {
    "Offset": 0,
    "limit": 10
  }
}