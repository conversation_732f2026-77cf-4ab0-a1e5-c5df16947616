﻿using Pulumi;
using Pulumi.AzureNative.Authorization;
using Pulumi.AzureNative.Portal;
using Pulumi.AzureNative.Portal.Inputs;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Components.Configs.SleekflowCore;
using Sleekflow.Core.Infra.Components.DashboardMetrics;
using Sleekflow.Core.Infra.Components.Models;
using Sleekflow.Core.Infra.Constants;

namespace Sleekflow.Core.Infra.Components;

public class AzurePortalDashboard
{
    private readonly MyConfig _myConfig;
    private readonly List<EnvGroup> _envGroups;
    private readonly ServerConfig _serverConfig;
    private static readonly int RedisYAxis = 18;
    private static readonly int SignalRYAxis = 18;
    private static readonly int HangfireYAxis = 23;

    public AzurePortalDashboard(MyConfig myConfig, List<EnvGroup> envGroups, ServerConfig serverConfig)
    {
        _myConfig = myConfig;
        _envGroups = envGroups;
        _serverConfig = serverConfig;
    }

    public void InitPortalDashboard()
    {
        foreach (var envGroup in _envGroups)
        {
            var locationName = Output.Create(envGroup.LocationName);
            var dashboardName = $"sleekflow-core-dashboard-{_myConfig.Name}-{envGroup.LocationName}";
            var subscriptionId = GetClientConfig.Invoke().Apply(c => c.SubscriptionId);

            var allElements = new List<DashboardPartsArgs>();

            allElements.AddRange(GetCoreDashboardElements(envGroup, _serverConfig));
            allElements.AddRange(GetApiClusterDashboardElements(envGroup));
            allElements.AddRange(GetOperationsAnalyticsDashboardElements(envGroup));
            allElements.AddRange(GetSystemRuntimeDashboardElements(envGroup));
            allElements.AddRange(GetRedisDashboardElements(envGroup, locationName, subscriptionId));
            allElements.AddRange(GetSignalrDashboardElements(envGroup));
            allElements.AddRange(GetHangfireDashboardElements(envGroup));
            allElements.AddRange(GetMessageDashboardElements(envGroup));
            allElements.AddRange(GetMarkdownResource());

            var dashboard = new Dashboard(
                dashboardName,
                new DashboardArgs
                {
                    DashboardName = $"sleekflow-core-dashboard-{_myConfig.Name}-{envGroup.LocationName}",
                    Location = envGroup.LocationName,
                    Properties = new DashboardPropertiesWithProvisioningStateArgs
                    {
                        Lenses = new[]
                        {
                            new DashboardLensArgs
                            {
                                Order = 0, Parts = allElements.ToArray()
                            }
                        },
                        Metadata = GetDashboardMetadata()
                    },
                    ResourceGroupName = envGroup.ResourceGroup.Name
                });
        }
    }


    private static List<DashboardPartsArgs> GetCoreDashboardElements(EnvGroup envGroup, ServerConfig serverConfig)
    {
        // sleekflow core
        envGroup.WebApps.TryGetValue(ServiceNames.SleekflowCore, out var sleekflowCore);

        var sleekflowCoreName = sleekflowCore?.WebApp.Name;
        var sleekflowCoreResourceId = sleekflowCore?.WebApp.Id;

        // sleekflow core app insight
        var sleekflowCoreAppInsightName = sleekflowCore?.AppInsight.Name;
        var sleekflowCoreAppInsightResourceId = sleekflowCore?.AppInsight.Id;

        // sleekflow core auto scale
        var sleekflowCoreAutoscaleName = sleekflowCore?.AutoScaleConfig?.Name;
        var sleekflowCoreAutoscaleResourceId = sleekflowCore?.AutoScaleConfig?.Id;

        // sleekflow core worker
        envGroup.WorkerWebApps.TryGetValue(ServiceNames.SleekflowCoreWorker, out var sleekflowCoreWorker);
        var hasCoreWorker = sleekflowCoreWorker != null;
        var sleekflowCoreWorkerResourceId = sleekflowCoreWorker?.WebApp?.Id;
        var sleekflowCoreWorkerName = sleekflowCoreWorker?.WebApp?.Name;

        // sleekflow core worker app insight
        var sleekflowCoreWorkerAppInsightResourceId = sleekflowCoreWorker?.AppInsight?.Id;
        var sleekflowCoreWorkerAppInsightName = sleekflowCoreWorker?.AppInsight?.Name;

        // sleekflow core worker auto scale
        var sleekflowCoreWorkerAutoscaleName = sleekflowCoreWorker?.AutoScaleConfig?.Name;
        var sleekflowCoreWorkerAutoscaleResourceId = sleekflowCoreWorker?.AutoScaleConfig?.Id;

        var regionalConfig = serverConfig
            .RegionalConfigs
            .First(s => s.LocationName == envGroup.LocationName);

        // sql server
        var (sleekflowSqlServerResourceId, sleekflowSqlServerName) = GetSqlServerResource(
            regionalConfig.SleekflowCoreConfig.GeoSqlDb,
            envGroup.SqlServerProperties!);

        var coreElement = new List<DashboardPartsArgs>()
        {
            new AvgResponseTimeMetric(sleekflowCoreResourceId, sleekflowCoreName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 0, Y = 0, ColSpan = 4, RowSpan = 4
                    }
                ),
            new ApplicationDurationPlotMetric(
                    sleekflowCoreAppInsightResourceId,
                    sleekflowCoreAppInsightName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 9, Y = 0, ColSpan = 8, RowSpan = 4
                    }),
            new ApplicationDurationAnalyticMetric(
                    sleekflowCoreAppInsightResourceId,
                    sleekflowCoreAppInsightName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 17, Y = 0, ColSpan = 8, RowSpan = 2
                    }),
            new MinHealthCheckStatusMetric(sleekflowCoreResourceId, sleekflowCoreName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 17, Y = 2, ColSpan = 8, RowSpan = 2
                    }),
            new SleekFlowDatabaseMetric(sleekflowSqlServerResourceId, sleekflowSqlServerName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 0, Y = 4, ColSpan = 8, RowSpan = 4
                    }),
            new MaxSQLServerProcessCorePercentMetric(sleekflowSqlServerResourceId, sleekflowSqlServerName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 8, Y = 4, ColSpan = 4, RowSpan = 4
                    }),
            new MaxSqlServerSessionsCountMetric(sleekflowSqlServerResourceId, sleekflowSqlServerName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 12, Y = 4, ColSpan = 4, RowSpan = 4
                    }),
            new MaxSqlServerCpuLimitMetric(sleekflowSqlServerResourceId, sleekflowSqlServerName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 16, Y = 4, ColSpan = 4, RowSpan = 4
                    }),
            new SumFailedRequestsMetric(
                    sleekflowCoreAppInsightResourceId,
                    sleekflowCoreAppInsightName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 8, Y = 8, ColSpan = 4, RowSpan = 4
                    }
                ),
            new ServerExceptionsAndFailuresRequestMetric(sleekflowCoreAppInsightResourceId)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 12, Y = 8, ColSpan = 8, RowSpan = 4
                    }),
            new RequestsForSleekFlowMetric(
                    sleekflowCoreName,
                    sleekflowCoreResourceId,
                    sleekflowCoreWorkerName,
                    sleekflowCoreWorkerResourceId)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 0, Y = 8, ColSpan = 8, RowSpan = 4
                    }),
        };

        if (!hasCoreWorker)
        {
            return coreElement;
        }

        var coreWorkerElement = new List<DashboardPartsArgs>()
        {
            new AvgResponseTimeMetric(
                    sleekflowCoreWorkerResourceId,
                    sleekflowCoreWorkerName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 4, Y = 0, ColSpan = 4, RowSpan = 4
                    }),
            new AppServiceInstanceCountMetric(
                    sleekflowCoreAutoscaleName,
                    sleekflowCoreAutoscaleResourceId,
                    sleekflowCoreWorkerAutoscaleName,
                    sleekflowCoreWorkerAutoscaleResourceId)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 20, Y = 8, ColSpan = 4, RowSpan = 4
                    }),
            new ServerExceptionsAndFailuresCountMetric(
                    sleekflowCoreAppInsightName,
                    sleekflowCoreAppInsightResourceId,
                    sleekflowCoreWorkerAppInsightResourceId)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 24, Y = 8, ColSpan = 4, RowSpan = 4
                    })
        };
        coreElement.AddRange(coreWorkerElement);

        return coreElement;
    }

    private static List<DashboardPartsArgs> GetApiClusterDashboardElements(EnvGroup envGroup)
    {
        // sleekflow core
        envGroup.WebApps.TryGetValue(ServiceNames.SleekflowCore, out var sleekflowCore);

        var sleekflowCoreName = sleekflowCore?.WebApp.Name;
        var sleekflowCoreResourceId = sleekflowCore?.WebApp.Id;

        // sleekflow core sku config
        var sleekflowCoreLinuxPlanName = sleekflowCore?.SkuConfig.Name;
        var sleekflowCoreLinuxResourceId = sleekflowCore?.SkuConfig.Id;

        return
        [
            new RequestsForSleekFlowByInstanceMetric(sleekflowCoreResourceId, sleekflowCoreName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 0, Y = 13, ColSpan = 8, RowSpan = 4
                    }),

            new AppServiceCpuPercentageMaxMetric(
                sleekflowCoreLinuxPlanName,
                sleekflowCoreLinuxResourceId).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 8, Y = 13, ColSpan = 4, RowSpan = 4
                }),

            new AppServiceMemoryPercentageMaxMetric(
                sleekflowCoreLinuxPlanName,
                sleekflowCoreLinuxResourceId).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 12, Y = 13, ColSpan = 4, RowSpan = 4
                }),

            new AvgResponseTimeByInstanceMetric(
                sleekflowCoreName,
                sleekflowCoreResourceId).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 16, Y = 13, ColSpan = 4, RowSpan = 4
                }),

            new AppServiceCpuPercentageMetric(
                sleekflowCoreLinuxPlanName,
                sleekflowCoreLinuxResourceId).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 20, Y = 13, ColSpan = 4, RowSpan = 4
                }),

            new AppServiceMemoryPercentageMetric(
                sleekflowCoreLinuxPlanName,
                sleekflowCoreLinuxResourceId).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 24, Y = 13, ColSpan = 4, RowSpan = 4
                })
        ];
    }

    private static List<DashboardPartsArgs> GetOperationsAnalyticsDashboardElements(EnvGroup envGroup)
    {
        envGroup.WebApps.TryGetValue(ServiceNames.SleekflowCore, out var sleekflowCore);

        var resourceId = sleekflowCore?.AppInsight.Id;
        var resourceName = sleekflowCore?.AppInsight.Name;
        return
        [
            new OperationsSortedDependencyCountMetric(resourceId, resourceName).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 21, Y = 27, ColSpan = 6, RowSpan = 8
                }),
            new OperationsSortedResponseTimeMetric(resourceId, resourceName).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 15, Y = 27, ColSpan = 6, RowSpan = 8
                }),
            new OperationsCountByTimeSlotMetric(resourceId, resourceName).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 0, Y = 27, ColSpan = 14, RowSpan = 8
                })
        ];
    }

    private static List<DashboardPartsArgs> GetSystemRuntimeDashboardElements(EnvGroup envGroup)
    {
        envGroup.WebApps.TryGetValue(ServiceNames.SleekflowCore, out var sleekflowCore);

        var sleekflowCoreName = sleekflowCore?.WebApp.Name;
        var sleekflowCoreAppInsightName = sleekflowCore?.AppInsight.Name;
        var sleekflowCoreAppInsightResourceId = sleekflowCore?.AppInsight.Id;

        return
        [
            new AvgSystemRuntimeAllocationRateMetric(
                    sleekflowCoreName,
                    sleekflowCoreAppInsightResourceId,
                    sleekflowCoreAppInsightName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 30, Y = 0, ColSpan = 6, RowSpan = 4
                    }),

            new AvgSystemRuntimeGen0SizeMetric(
                    sleekflowCoreName,
                    sleekflowCoreAppInsightResourceId,
                    sleekflowCoreAppInsightName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 36, Y = 0, ColSpan = 6, RowSpan = 4
                    }),

            new AvgSystemRuntimeWorkingSetMetric(
                    sleekflowCoreName,
                    sleekflowCoreAppInsightName,
                    sleekflowCoreAppInsightResourceId)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 30, Y = 4, ColSpan = 6, RowSpan = 4
                    }),

            new AvgSystemRuntimeGen1SizeMetric(
                    sleekflowCoreName,
                    sleekflowCoreAppInsightName,
                    sleekflowCoreAppInsightResourceId)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 36, Y = 4, ColSpan = 6, RowSpan = 4
                    }),

            new AvgSystemRuntimeGCMetric(
                    sleekflowCoreName,
                    sleekflowCoreAppInsightResourceId,
                    sleekflowCoreAppInsightName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 30, Y = 8, ColSpan = 6, RowSpan = 4
                    }),

            new AvgSystemRuntimeGen2SizeMetric(
                    sleekflowCoreName,
                    sleekflowCoreAppInsightResourceId,
                    sleekflowCoreAppInsightName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 36, Y = 8, ColSpan = 6, RowSpan = 4
                    }),

            new AvgSystemRuntimeThreadPoolCountMetric(
                    sleekflowCoreName,
                    sleekflowCoreAppInsightResourceId,
                    sleekflowCoreAppInsightName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 30, Y = 12, ColSpan = 6, RowSpan = 4
                    }),

            new AvgSystemRuntimeLOHSizeMetric(
                sleekflowCoreName,
                sleekflowCoreAppInsightResourceId,
                sleekflowCoreAppInsightName).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 36, Y = 12, ColSpan = 6, RowSpan = 4
                }),

            new MaxSystemRuntimeThreadPoolQueueLengthMetric(
                    sleekflowCoreName,
                    sleekflowCoreAppInsightName,
                    sleekflowCoreAppInsightResourceId)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 30, Y = 16, ColSpan = 4, RowSpan = 4
                    })
        ];
    }

    private static List<DashboardPartsArgs> GetRedisDashboardElements(
        EnvGroup envGroup,
        Output<string> locationName,
        Output<string> subscriptionId)
    {
        // Redis
        envGroup.Redis.TryGetValue(RedisInstances.GetInstances()[0], out var redis);
        var resourceId = redis?.Id;
        var resourceName = redis?.Name;

        return
        [
            new MaxServerLoadRedisMetric(resourceId, resourceName, locationName, subscriptionId).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 0, Y = RedisYAxis, ColSpan = 6, RowSpan = 4
                }),

            new MaxUsedMemoryRedisMetric(
                    resourceId,
                    resourceName,
                    locationName,
                    subscriptionId)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 6, Y = RedisYAxis, ColSpan = 6, RowSpan = 4
                    }),

            new MaxUsedMemoryPercentageRedisMetric(
                    resourceId,
                    resourceName,
                    locationName,
                    subscriptionId)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 12, Y = RedisYAxis, ColSpan = 6, RowSpan = 4
                    })
        ];
    }

    private static List<DashboardPartsArgs> GetSignalrDashboardElements(EnvGroup envGroup)
    {
        // signalR
        var resourceId = envGroup.SignalR?.Id;
        var resourceName = envGroup.SignalR?.Name;
        return
        [
            new SignalRConnectionMetric(resourceId, resourceName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 20, Y = SignalRYAxis, ColSpan = 6, RowSpan = 4
                    })
        ];
    }

    private static List<DashboardPartsArgs> GetHangfireDashboardElements(EnvGroup envGroup)
    {
        // sleekflow core worker
        envGroup.WorkerWebApps.TryGetValue(ServiceNames.SleekflowCoreWorker, out var sleekflowCoreWorker);
        if (sleekflowCoreWorker == null)
        {
            return [];
        }

        var sleekflowCoreWorkerResourceId = sleekflowCoreWorker?.WebApp?.Id;
        var sleekflowCoreWorkerName = sleekflowCoreWorker?.WebApp?.Name;
        // sleekflow core worker sku config
        var sleekflowCoreWorkerLinuxPlanName = sleekflowCoreWorker?.SkuConfig?.Name;
        var sleekflowCoreWorkerLinuxResourceId = sleekflowCoreWorker?.SkuConfig?.Id;

        return
        [
            new RequestsForSleekFlowByInstanceMetric(
                    sleekflowCoreWorkerResourceId,
                    sleekflowCoreWorkerName)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 0, Y = HangfireYAxis, ColSpan = 8, RowSpan = 4
                    }),

            new AppServiceCpuPercentageMaxMetric(
                    sleekflowCoreWorkerLinuxPlanName,
                    sleekflowCoreWorkerLinuxResourceId)
                .GetDashboardPartsArgs(
                    new DashboardPartsPositionArgs
                    {
                        X = 8, Y = HangfireYAxis, ColSpan = 4, RowSpan = 4
                    }),

            new AppServiceMemoryPercentageMaxMetric(
                sleekflowCoreWorkerLinuxPlanName,
                sleekflowCoreWorkerLinuxResourceId).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 12, Y = HangfireYAxis, ColSpan = 4, RowSpan = 4
                }),

            new AvgResponseTimeByInstanceMetric(
                sleekflowCoreWorkerName,
                sleekflowCoreWorkerResourceId).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 16, Y = HangfireYAxis, ColSpan = 4, RowSpan = 4
                }),

            new AppServiceCpuPercentageMetric(
                sleekflowCoreWorkerLinuxPlanName,
                sleekflowCoreWorkerLinuxResourceId).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 20, Y = HangfireYAxis, ColSpan = 4, RowSpan = 4
                }),

            new AppServiceMemoryPercentageMetric(
                sleekflowCoreWorkerLinuxPlanName,
                sleekflowCoreWorkerLinuxResourceId).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 24, Y = HangfireYAxis, ColSpan = 4, RowSpan = 4
                }),
            new MarkdownPartMetric("# hangfire cluster").GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 0, Y = HangfireYAxis - 1, ColSpan = 2, RowSpan = 1
                }),
        ];
    }

    private static List<DashboardPartsArgs> GetMessageDashboardElements(EnvGroup envGroup)
    {
        // sleekflow core
        envGroup.WebApps.TryGetValue(ServiceNames.SleekflowCore, out var sleekflowCore);
        // sleekflow core app insight
        var resourceId = sleekflowCore?.AppInsight.Id;
        var resourceName = sleekflowCore?.AppInsight.Name;

        return
        [
            new SendMessageSuccessMetric(resourceId, resourceName).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 30, Y = 22, ColSpan = 6, RowSpan = 4
                }),

            new SendMessageFailMetric(resourceId, resourceName).GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 36, Y = 22, ColSpan = 6, RowSpan = 4
                })
        ];
    }

    private static (Output<string> ResourceId, Output<string> ResourceName) GetSqlServerResource(
        GeoSqlDbConfig? geoSqlDb,
        SqlServerProperties sqlServerProperties)
    {
        // sql server
        var geoSqlDbName = geoSqlDb?.Name;
        var resourceName = sqlServerProperties.Database.Name;
        var resourceId = sqlServerProperties.Database.Id;

        if (string.IsNullOrEmpty(geoSqlDbName))
        {
            return (resourceId, resourceName);
        }

        // Assuming that the GEO SL DB is using the same sql server, just different database name
        return (Output.Tuple(resourceId, resourceName).Apply(t => t.Item1.Replace(t.Item2, geoSqlDbName)),
            Output.Create(geoSqlDbName));
    }

    private static List<DashboardPartsArgs> GetMarkdownResource()
    {
        return
        [
            new MarkdownPartMetric("# api cluster").GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 0, Y = 12, ColSpan = 2, RowSpan = 1
                }),
            new MarkdownPartMetric("# redis").GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 0, Y = RedisYAxis - 1, ColSpan = 2, RowSpan = 1
                }),
            new MarkdownPartMetric("# singalr").GetDashboardPartsArgs(
                new DashboardPartsPositionArgs
                {
                    X = 20, Y = SignalRYAxis - 1, ColSpan = 2, RowSpan = 1
                })
        ];
    }

    private static Dictionary<string, object> GetDashboardMetadata()
    {
        return new Dictionary<string, object>
        {
            {
                "model", new Dictionary<string, object>
                {
                    {
                        "timeRange", new Dictionary<string, object>
                        {
                            {
                                "value", new Dictionary<string, object>
                                {
                                    {
                                        "relative", new Dictionary<string, object>
                                        {
                                            {
                                                "duration", 24
                                            },
                                            {
                                                "timeUnit", 1
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "type", "MsPortalFx.Composition.Configuration.ValueTypes.TimeRange"
                            }
                        }
                    },
                    {
                        "filterLocale", new Dictionary<string, object>
                        {
                            {
                                "value", "en-us"
                            }
                        }
                    },
                    {
                        "filters", new Dictionary<string, object>
                        {
                            {
                                "value", new Dictionary<string, object>
                                {
                                    {
                                        "MsPortalFx_TimeRange", new Dictionary<string, object>
                                        {
                                            {
                                                "model", new Dictionary<string, object>
                                                {
                                                    {
                                                        "format", "local"
                                                    },
                                                    {
                                                        "granularity", "1m"
                                                    },
                                                    {
                                                        "relative", "12h"
                                                    }
                                                }
                                            },
                                            {
                                                "displayCache", new Dictionary<string, object>
                                                {
                                                    {
                                                        "name", "Local Time"
                                                    },
                                                    {
                                                        "value", "Past 12 hours"
                                                    }
                                                }
                                            },
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        };
    }
}