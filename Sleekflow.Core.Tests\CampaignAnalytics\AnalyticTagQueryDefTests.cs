﻿using Travis_backend.CampaignAnalyticsDomain.Constants;
using Travis_backend.CampaignAnalyticsDomain.Models;
using Travis_backend.CampaignAnalyticsDomain.Services;

namespace Sleekflow.Core.Tests.CampaignAnalytics;

public class AnalyticTagQueryDefTests
{
    [Test]
    public void GetCountsPerMessageStatusByAnalyticTagQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string getCountsPerMessageStatusByTagQ =
            """
            SELECT
                CM.Status AS MessageStatus,
                COUNT(*) AS Count
            FROM
                ConversationMessages CM
            WHERE
                CM.CompanyId = @CompanyId
                AND CM.AnalyticTags LIKE @AnalyticTag
            GROUP BY
                CM.Status;
            """;

        // Act
        var queryDef = CampaignAnalyticsQueryBuilder.GetCountsPerMessageStatusByAnalyticTagQueryDef(
            string.Empty,
            string.Empty);

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(getCountsPerMessageStatusByTagQ));
    }

    [Test]
    public void GetMessageOverviewByAnalyticTagQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string getMessageOverviewByTagQ =
            """
            SELECT
                CM.Id AS MessageId,
                CM.ConversationId AS ConversationId,
                UP.Id AS UserProfileId,
                UP.FirstName AS FirstName,
                UP.LastName AS LastName,
                UP.FullName AS FullName,
                UP.PhoneNumber AS PhoneNumber,
                CM.Status AS MessageStatus,
                CM.ChannelStatusMessage AS ChannelStatusMessage,
                CM.CreatedAt AS CreatedAt
            FROM
                ConversationMessages CM
                    JOIN Conversations C
                         ON CM.ConversationId = C.Id
                    JOIN UserProfiles UP
                         ON C.UserProfileId = UP.Id
            WHERE
                CM.CompanyId = @CompanyId
                AND CM.Status IN (2, 3)
                AND CM.AnalyticTags LIKE @AnalyticTag
            ORDER BY CM.CreatedAt ASC
            OFFSET @Offset ROWS
                FETCH NEXT @Limit ROWS ONLY;
            """;

        // Act
        var queryDef = CampaignAnalyticsQueryBuilder.GetMessageOverviewByAnalyticTagQueryDef(
            string.Empty,
            string.Empty,
            MessageStatusGroups.Delivered,
            GetMessageOverviewSortableFields.CreatedAt,
            "asc",
            0,
            0);

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(getMessageOverviewByTagQ));
    }

    [Test]
    public void GetUserProfileIdsByAnalyticTagQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string getUserProfileIdsByTagQ =
            """
            SELECT
                C.UserProfileId as UserProfileId
            FROM
                ConversationMessages CM
                  JOIN Conversations C
                       ON CM.ConversationId = C.Id
            WHERE
                CM.CompanyId = @CompanyId
                AND CM.Status IN (2, 3)
                AND CM.AnalyticTags LIKE @AnalyticTag;
            """;

        // Act
        var queryDef = CampaignAnalyticsQueryBuilder.GetUserProfileIdsByAnalyticTagQueryDef(
            string.Empty,
            string.Empty,
            MessageStatusGroups.Delivered);

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(getUserProfileIdsByTagQ));
    }

    [Test]
    public void GetRepliedCountByAnalyticTagQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string getRepliedCountByTagQ =
            """
            WITH
            OutMessages AS (
                SELECT
                CM.Id AS OutMessageId,
                CM.ConversationId,
                CM.CreatedAt AS OutCreatedAt,
                CM.ChannelStatusMessage,
                CM.Status
            FROM
              ConversationMessages CM
            WHERE
               CM.CompanyId = @CompanyId
               AND CM.Status IN (2, 3)
               AND CM.AnalyticTags LIKE @AnalyticTag
            ),
            RepliedMessages AS (
                SELECT
                O.OutMessageId
            FROM
                OutMessages o
            WHERE
                EXISTS (
                SELECT 1
                FROM
                    ConversationMessages CM_E
                WHERE
                    O.ConversationId = CM_E.ConversationId
                    AND CM_E.CompanyId = @CompanyId
                    AND CM_E.IsSentFromSleekflow = 0
                    AND CM_E.CreatedAt BETWEEN O.OutCreatedAt AND DATEADD(HOUR, @ReplyWindowLengthInHours, O.OutCreatedAt)
                )
            )
            SELECT
                Count(1) AS Count
            FROM
                RepliedMessages;
            """;

        var queryDef = CampaignAnalyticsQueryBuilder.GetRepliedCountByAnalyticTagQueryDef(
            string.Empty,
            string.Empty,
            new ReplyWindow("day", 1));

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(getRepliedCountByTagQ));
    }

    [Test]
    public void GetRepliedMessageOverviewByAnalyticTagQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string getRepliedMessageOverviewByTagQ =
            """
            WITH
            OutMessages AS (
                SELECT
                CM.Id AS OutMessageId,
                CM.ConversationId,
                CM.CreatedAt AS OutCreatedAt,
                CM.ChannelStatusMessage,
                CM.Status
            FROM
              ConversationMessages CM
            WHERE
               CM.CompanyId = @CompanyId
               AND CM.Status IN (2, 3)
               AND CM.AnalyticTags LIKE @AnalyticTag
            ),
            RepliedMessages AS (
                SELECT
                O.OutMessageId,
                C.Id as ConversationId,
                C.UserProfileId,
                U.FirstName,
                U.LastName,
                U.FullName,
                U.PhoneNumber,
                O.ChannelStatusMessage,
                O.Status,
                O.OutCreatedAt
            FROM
                OutMessages O
            JOIN
                Conversations C
            ON
                O.ConversationId = C.Id
            JOIN
                UserProfiles U
            ON
                C.UserProfileId = U.Id
             WHERE
                EXISTS(
                    SELECT
                        1
                    FROM
                        ConversationMessages CM_E
                    WHERE
                        O.ConversationId = CM_E.ConversationId
                        AND CM_E.CompanyId = @CompanyId
                        AND CM_E.IsSentFromSleekflow = 0
                        AND CM_E.CreatedAt BETWEEN O.OutCreatedAt AND DATEADD(HOUR, @ReplyWindowLengthInHours, O.OutCreatedAt)
                )
             ORDER BY
                 O.OutCreatedAt ASC
             OFFSET @Offset ROWS
                 FETCH NEXT @Limit ROWS ONLY
            )
            SELECT
                OutMessageId AS MessageId,
                ConversationId AS ConversationId,
                UserProfileId AS UserProfileId,
                FirstName AS FirstName,
                LastName AS LastName,
                FullName AS FullName,
                PhoneNumber AS PhoneNumber,
                Status AS MessageStatus,
                ChannelStatusMessage AS ChannelStatusMessage,
                OutCreatedAt AS CreatedAt
            FROM
                RepliedMessages;
            """;

        // Act
        var queryDef = CampaignAnalyticsQueryBuilder.GetRepliedMessageOverviewByAnalyticTagQueryDef(
            string.Empty,
            string.Empty,
            new ReplyWindow("day", 1),
            GetMessageOverviewSortableFields.CreatedAt,
            "asc",
            0,
            0);

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(getRepliedMessageOverviewByTagQ));
    }

    [Test]
    public void GetRepliedMessageUserProfileIdsByAnalyticTagQueryDef_Should_ReturnsCorrectQuery()
    {
        // Arrange
        const string getRepliedMessageUserProfileIdsByTagQ =
            """
            WITH
            OutMessages AS (
                SELECT
                CM.Id AS OutMessageId,
                CM.ConversationId,
                CM.CreatedAt AS OutCreatedAt,
                CM.ChannelStatusMessage,
                CM.Status
            FROM
              ConversationMessages CM
            WHERE
               CM.CompanyId = @CompanyId
               AND CM.Status IN (2, 3)
               AND CM.AnalyticTags LIKE @AnalyticTag
            ),
            RepliedMessages AS (
                SELECT
                C.UserProfileId
            FROM
                OutMessages O
            JOIN
                Conversations C
            ON
                O.ConversationId = C.Id
            WHERE
                EXISTS(
                    SELECT
                        1
                    FROM
                        ConversationMessages CM_E
                    WHERE
                        O.ConversationId = CM_E.ConversationId
                        AND CM_E.CompanyId = @CompanyId
                        AND CM_E.IsSentFromSleekflow = 0
                        AND CM_E.CreatedAt BETWEEN O.OutCreatedAt AND DATEADD(HOUR, @ReplyWindowLengthInHours, O.OutCreatedAt)
                )
            )
            SELECT
                UserProfileId
            FROM
                RepliedMessages;
            """;

        // Act
        var queryDef = CampaignAnalyticsQueryBuilder.GetRepliedMessageUserProfileIdsByAnalyticTagQueryDef(
            string.Empty,
            string.Empty,
            new ReplyWindow("day", 1));

        // Assert
        Assert.That(queryDef.Query, Is.EqualTo(getRepliedMessageUserProfileIdsByTagQ));
    }
}
