﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class AppServiceInstanceCountMetric : IDashboardMetric
{
    private readonly Output<string>? _sleekflowCoreAutoscaleName;
    private readonly Output<string>? _sleekflowCoreAutoscaleResourceId;
    private readonly Output<string>? _sleekflowCoreWorkerAutoscaleName;
    private readonly Output<string>? _sleekflowCoreWorkerAutoscaleResourceId;

    public AppServiceInstanceCountMetric(
        Output<string>? sleekflowCoreAutoscaleName,
        Output<string>? sleekflowCoreAutoscaleResourceId,
        Output<string>? sleekflowCoreWorkerAutoscaleName,
        Output<string>? sleekflowCoreWorkerAutoscaleResourceId)
    {
        _sleekflowCoreAutoscaleName = sleekflowCoreAutoscaleName;
        _sleekflowCoreAutoscaleResourceId = sleekflowCoreAutoscaleResourceId;
        _sleekflowCoreWorkerAutoscaleName = sleekflowCoreWorkerAutoscaleName;
        _sleekflowCoreWorkerAutoscaleResourceId = sleekflowCoreWorkerAutoscaleResourceId;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs =
                    new[]
                    {
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "options"
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "sharedTimeRange"
                            },
                            {
                                "isOptional", true
                            }
                        }
                    },
                Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                Settings =
                {
                    new Dictionary<string, object>()
                    {
                        {
                            "content", new Dictionary<string, object>()
                            {
                                {
                                    "options", new Dictionary<string, object>()
                                    {
                                        {
                                            "chart", new Dictionary<string, object>()
                                            {
                                                {
                                                    "metrics", new[]
                                                    {
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 3
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "Observed Capacity"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _sleekflowCoreAutoscaleName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "ObservedCapacity"
                                                            },
                                                            {
                                                                "namespace", "microsoft.insights/autoscalesettings"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreAutoscaleResourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 3
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "Observed Capacity"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName", _sleekflowCoreWorkerAutoscaleName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "ObservedCapacity"
                                                            },
                                                            {
                                                                "namespace", "microsoft.insights/autoscalesettings"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreWorkerAutoscaleResourceId!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    "title", "App Service Instance Count"
                                                },
                                                {
                                                    "titleKind", 2
                                                },
                                                {
                                                    "visualization", new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "axisVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "x", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 2
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    "y", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 1
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "chartType", 2
                                                        },
                                                        {
                                                            "disablePinning", true
                                                        },
                                                        {
                                                            "legendVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "hideHoverCard", false
                                                                },
                                                                {
                                                                    "hideLabelNames", true
                                                                },
                                                                {
                                                                    "hideSubtitle", false
                                                                },
                                                                {
                                                                    "isVisible", true
                                                                },
                                                                {
                                                                    "position", 2
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}