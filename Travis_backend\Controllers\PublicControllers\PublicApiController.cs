﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Mail;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Sleekflow.Apis.MessagingHub.Client;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AnalyticsDomain.Services;
using Travis_backend.AnalyticsDomain.ViewModels;
using Travis_backend.Attributes;
using Travis_backend.BroadcastDomain.Services;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ChannelDomain.Services;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.ApiKeyResolvers;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.Services.Cache;
using Travis_backend.ContactDomain.Services.Import;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ContactDomain.Exceptions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels.Mappers;
using Travis_backend.ConversationServices;
using Travis_backend.CoreDomain.Models;
using Travis_backend.CustomObjectDomain.Constants;
using Travis_backend.CustomObjectDomain.Filters;
using Travis_backend.CustomObjectDomain.Helpers;
using Travis_backend.CustomObjectDomain.Services;
using Travis_backend.Data.WhatsappCloudApi;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Exceptions;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.Helpers;
using Travis_backend.Infrastructures.Attributes;
using Travis_backend.InternalDomain.Services;
using Travis_backend.MessageDomain.Exceptions;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.PiiMasking.Models;
using Travis_backend.PiiMaskingDomain.Services;
using Travis_backend.PublicDomain.Helpers;
using Travis_backend.PublicDomain.ViewModels;
using WABA360Dialog.ApiClient.Exceptions;
using WABA360Dialog.ApiClient.Payloads.Enums;
using ResponseViewModel = Travis_backend.CommonDomain.ViewModels.ResponseViewModel;
using WhatsAppLanguage = WABA360Dialog.Common.Enums.WhatsAppLanguage;


namespace Travis_backend.Controllers.PublicControllers
{
    [UpdateUserProfileTriggerSource(UpdateUserProfileTriggerSource.PublicApi)]
    [TypeFilter(typeof(ApiKeyExceptionFilter))]
    public class PublicApiController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly ILogger<PublicApiController> _logger;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly IConversationSummaryService _conversationSummaryService;
        private readonly IConversationAssigneeService _conversationAssigneeService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IUserProfileService _userProfileService;
        private readonly IBroadcastService _broadcastService;
        private readonly ICompanyService _companyService;
        private readonly ICompanyTeamService _companyTeamService;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly ILockService _lockService;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;
        private readonly IUserProfileSqlService _userProfileSqlService;
        private readonly IAnalyticsService _analyticsService;
        private readonly ICoreService _coreService;
        private readonly IWhatsApp360DialogService _whatsApp360DialogService;
        private readonly IWhatsappCloudApiService _whatsappCloudApiService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ICompanyTeamAssignmentQueueService _companyTeamAssignmentQueueService;
        private readonly ICustomObjectService _customObjectService;
        private readonly ISchemafulObjectsApi _schemafulObjectsApi;
        private readonly IStaffHooks _staffHooks;
        private readonly IApiKeyResolver _publicApiKeyResolver;
        private readonly IContactWhatsappSenderLockService _contactWhatsappSenderLockService;
        private readonly IContactDeletionConfig _contactDeletionConfig;
        private readonly IUserProfileSafeDeleteService _userProfileSafeDeleteService;
        private readonly IPiiMaskingService _piiMaskingService;
        private readonly ITwilioService _twilioService;
        private readonly IWhatsAppMessageNotificationService _whatsAppMessageNotificationService;
        private readonly IServiceProvider _serviceProvider;
        private readonly IContactUpsertService _contactUpsertService;
        private readonly IContactCacheService _contactCacheService;
        private readonly IConfiguration _configuration;
        private readonly IConversationNoCompanyResponseViewModelMapper _conversationNoCompanyResponseViewModelMapper;

        public PublicApiController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            ILogger<PublicApiController> logger,
            IConversationMessageService messagingService,
            IConversationSummaryService conversationSummaryService,
            IConversationAssigneeService conversationAssigneeService,
            IUserProfileService userProfileService,
            IBroadcastService broadcastService,
            IAzureBlobStorageService azureBlobStorageService,
            ICompanyService companyService,
            ICompanyTeamService companyTeamService,
            ICompanyUsageService companyUsageService,
            ICacheManagerService cacheManagerService,
            ICompanyInfoCacheService companyInfoCacheService,
            ILockService lockService,
            IUserProfileSqlService userProfileSqlService,
            IAnalyticsService analyticsService,
            ICoreService coreService,
            IWhatsApp360DialogService whatsApp360DialogService,
            IWhatsappCloudApiService whatsappCloudApiService,
            IHttpClientFactory httpClientFactory,
            ICompanyTeamAssignmentQueueService companyTeamAssignmentQueueService,
            ICustomObjectService customObjectService,
            ISchemafulObjectsApi schemafulObjectsApi,
            ApiKeyResolverFactory apiKeyResolverFactory,
            IContactWhatsappSenderLockService contactWhatsappSenderLockService,
            IContactDeletionConfig contactDeletionConfig,
            IUserProfileSafeDeleteService userProfileSafeDeleteService,
            IStaffHooks staffHooks,
            IPiiMaskingService piiMaskingService,
            ITwilioService twilioService,
            IWhatsAppMessageNotificationService whatsAppMessageNotificationService,
            IServiceProvider serviceProvider,
            IContactUpsertService contactUpsertService,
            IContactCacheService contactCacheService,
            IConfiguration configuration,
            IConversationNoCompanyResponseViewModelMapper conversationNoCompanyResponseViewModelMapper)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _logger = logger;
            _conversationMessageService = messagingService;
            _conversationAssigneeService = conversationAssigneeService;
            _conversationSummaryService = conversationSummaryService;
            _azureBlobStorageService = azureBlobStorageService;
            _userProfileService = userProfileService;
            _broadcastService = broadcastService;
            _companyService = companyService;
            _companyTeamService = companyTeamService;
            _companyUsageService = companyUsageService;
            _cacheManagerService = cacheManagerService;
            _companyInfoCacheService = companyInfoCacheService;
            _lockService = lockService;
            _userProfileSqlService = userProfileSqlService;
            _analyticsService = analyticsService;
            _coreService = coreService;
            _whatsApp360DialogService = whatsApp360DialogService;
            _whatsappCloudApiService = whatsappCloudApiService;
            _httpClientFactory = httpClientFactory;
            _companyTeamAssignmentQueueService = companyTeamAssignmentQueueService;
            _customObjectService = customObjectService;
            _schemafulObjectsApi = schemafulObjectsApi;
            _contactDeletionConfig = contactDeletionConfig;
            _userProfileSafeDeleteService = userProfileSafeDeleteService;
            _staffHooks = staffHooks;
            _publicApiKeyResolver = apiKeyResolverFactory.GetApiKeyResolver(ApiKeyTypes.PublicApi);
            _contactWhatsappSenderLockService = contactWhatsappSenderLockService;
            _piiMaskingService = piiMaskingService;
            _twilioService = twilioService;
            _whatsAppMessageNotificationService = whatsAppMessageNotificationService;
            _serviceProvider = serviceProvider;
            _contactUpsertService = contactUpsertService;
            _contactCacheService = contactCacheService;
            _configuration = configuration;
            _conversationNoCompanyResponseViewModelMapper = conversationNoCompanyResponseViewModelMapper;
        }

        /// <summary>
        /// During acknowledgement period:
        /// As Zapier/Make/PublicApi are sharing this same endpoint, we will grant all permissions to the keys issued here,
        /// With KeyType=PublicAPI.
        /// So that the behavior of v1 could keep consistent
        ///
        /// Get or Gen API Key.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize]
        [Route("api/generate/APIKey")]
        public async Task<IActionResult> IssueAPIKey([FromBody] CoreIssueAPIKey coreIssueAPIKey)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser.RoleType != StaffUserRole.Admin)
                {
                    return Unauthorized();
                }

                var response = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

                var apiKey = await _publicApiKeyResolver.GetCompanyApiKeyAsync(companyUser.CompanyId);
                dynamic result = new JObject();

                if (apiKey != null)
                {
                    result.result = "authenticated";
                    result.companyName = companyUser.Company.CompanyName;
                    result.permissions = string.Join(",", apiKey.Permissions ?? new List<string>());
                    result.apiKey = apiKey.APIKey;

                    return Ok(result);
                }

                var keyseed = Guid.NewGuid().ToString();
                var APIKey = string.Empty;

                using (var sha256 = SHA256.Create())
                {
                    byte[] saltedPasswordAsBytes = Encoding.UTF8.GetBytes(keyseed);
                    APIKey = RemoveSpecialCharacters(Convert.ToBase64String(sha256.ComputeHash(saltedPasswordAsBytes)));
                }

                apiKey = new CompanyAPIKey
                {
                    APIKey = APIKey,
                    CompanyId = companyUser.CompanyId,
                    KeyType = ApiKeyTypes.PublicApi,
                    Permissions = ApiKeyTypes.All.ToList(), // grant all permissions
                    CallLimit = response.MaximumAPICalls,
                    Calls = 0,
                };
                _appDbContext.CompanyAPIKeys.Add(apiKey);
                await _appDbContext.SaveChangesAsync();

                result.result = "authenticated";
                result.companyName = companyUser.Company.CompanyName;
                result.permissions = string.Join(",", apiKey.Permissions ?? new List<string>());
                result.apiKey = apiKey.APIKey;

                return Ok(result);
            }

            return BadRequest();
        }

        #region Example

        /// <summary>
        /// Sample endpoint for test API Key authentication and usage.
        /// </summary>
        /// <param name="apikey">apiKey.</param>
        /// <param name="apikeyHeader">apiKeyHeader.</param>
        /// <returns>ApiUsage.</returns>
        [HttpPost]
        [Route("api/verifyEndpoint")]
        public async Task<IActionResult> VerifyEndpoint(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;

            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.AddUsageAsync(apiKey);

            var apiUsage = await _publicApiKeyResolver.GetApiUsageAsync(apiKey);

            return Ok(apiUsage);
        }

        #endregion

        #region Message API

        [HttpPost]
        [Route("API/Message/Send")]
        public async Task<ActionResult<ResponseViewModel>> PublicApiSendMessageAsync(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromForm]
            SendMessageFromAPIViewModel sendMessageFromAPIViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (ModelState.IsValid)
            {

                if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Apikey from query param is different from header."
                        });
                }

                apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
                var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
                await _publicApiKeyResolver.AddUsageAsync(apiKey);

                if (sendMessageFromAPIViewModel.Channel == null || sendMessageFromAPIViewModel.MessageType == null)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Incorrect format"
                        });
                }

                if (!ChannelTypes.AllChannelTypes.Value.Contains(sendMessageFromAPIViewModel.Channel))
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Channel not supported."
                        });
                }

                if (sendMessageFromAPIViewModel.MessageType == "text" &&
                    string.IsNullOrWhiteSpace(sendMessageFromAPIViewModel.MessageContent))
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Text message content is required."
                        });
                }

                ILockService.Lock whatsappPhoneNumberLock = null;

                if (sendMessageFromAPIViewModel.Channel is ChannelTypes.WhatsappCloudApi
                    or ChannelTypes.Whatsapp360Dialog or ChannelTypes.WhatsappTwilio)
                {
                    sendMessageFromAPIViewModel.To =
                        PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To);

                    try
                    {
                        whatsappPhoneNumberLock = await _contactWhatsappSenderLockService.GetSendWhatsappMessageLock(
                            apiKey.CompanyId,
                            sendMessageFromAPIViewModel.To,
                            sendMessageFromAPIViewModel.Channel,
                            sendMessageFromAPIViewModel.From);
                    }
                    catch (ContactWhatsappSenderLockException contactWhatsappSenderLockException)
                    {
                        _logger.LogError(
                            contactWhatsappSenderLockException,
                            "[Rejected] [PublicAPI] {MethodName}: {CompanyId} cant acquire lock for contact whatsapp phone number {WhatsappPhoneNumber}",
                            nameof(PublicApiSendMessageAsync),
                            apiKey.CompanyId,
                            sendMessageFromAPIViewModel.To);

                        return BadRequest(new ResponseViewModel
                        {
                            code = 400, message = "Server busy"
                        });
                    }
                }

                var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == apiKey.CompanyId);

                var conversation = new Conversation
                {
                    MessageGroupName = company.SignalRGroupName, CompanyId = company.Id
                };

                var conversationMessage = new ConversationMessage
                {
                    Channel = sendMessageFromAPIViewModel.Channel,
                    MessageType = sendMessageFromAPIViewModel.MessageType,
                    MessageContent = sendMessageFromAPIViewModel.MessageContent,
                    DeliveryType = DeliveryType.AutomatedMessage,
                    CompanyId = company.Id,
                    AnalyticTags = sendMessageFromAPIViewModel.AnalyticTags
                };

                switch (conversationMessage.Channel)
                {
                    case ChannelTypes.Facebook:
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.facebookUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.facebookUser.FacebookId == sendMessageFromAPIViewModel.FacebookReceiverId
                                    && x.CompanyId == company.Id);

                        if (conversation.facebookUser == null)
                        {
                            conversation.facebookUser = await _appDbContext.SenderFacebookSenders
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.FacebookId == sendMessageFromAPIViewModel.FacebookReceiverId
                                        && x.CompanyId == company.Id);

                            if (conversation.facebookUser == null)
                            {
                                conversation.facebookUser = new FacebookSender
                                {
                                    FacebookId = sendMessageFromAPIViewModel.FacebookReceiverId, CompanyId = company.Id
                                };
                            }
                        }

                        conversationMessage.facebookReceiver = conversation.facebookUser;

                        break;
                    case ChannelTypes.WhatsappTwilio:
                        var whatsappUser = new WhatsAppSender();
                        Conversation existingConversation = null;
                        string instanceId = string.Empty;
                        string instancePhoneNumber = null;

                        var twilioSender = await _appDbContext.ConfigWhatsAppConfigs
                            .Where(x => x.WhatsAppSender.Contains(sendMessageFromAPIViewModel.From))
                            .FirstOrDefaultAsync();

                        if (twilioSender == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    code = 400, message = "no sender found"
                                });
                        }

                        whatsappUser = await _appDbContext.SenderWhatsappSenders.FirstOrDefaultAsync(
                            x => x.whatsAppId ==
                                 $"whatsapp:+{PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To)}" &&
                                 x.CompanyId == company.Id && x.InstanceId == twilioSender.TwilioAccountId);

                        if (whatsappUser == null)
                        {
                            whatsappUser = new WhatsAppSender
                            {
                                whatsAppId =
                                    $"whatsapp:+{PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To)}",
                                InstanceId = twilioSender.TwilioAccountId,
                                CompanyId = company.Id,
                                phone_number = PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To),
                                name = sendMessageFromAPIViewModel.To,
                                InstaneSender = twilioSender.WhatsAppSender
                            };
                        }

                        instanceId = twilioSender.TwilioAccountId;
                        instancePhoneNumber = twilioSender.WhatsAppSender;

                        existingConversation = await _appDbContext.Conversations
                            .Where(
                                x => x.WhatsappUser.whatsAppId == whatsappUser.whatsAppId &&
                                     x.WhatsappUser.InstanceId == whatsappUser.InstanceId && x.CompanyId == company.Id)
                            .Include(x => x.WhatsappUser).FirstOrDefaultAsync();

                        if (existingConversation != null)
                        {
                            conversation = existingConversation;
                        }
                        else
                        {
                            conversation.WhatsappUser = whatsappUser;
                        }

                        conversationMessage.whatsappReceiver = conversation.WhatsappUser;

                        try
                        {
                            if (!string.IsNullOrEmpty(conversation.UserProfileId))
                            {
                                if (instanceId != conversation.WhatsappUser.InstanceId ||
                                    (conversation.WhatsappUser.InstaneSender != instancePhoneNumber))
                                {
                                    // change sender
                                    if (!string.IsNullOrEmpty(instancePhoneNumber))
                                    {
                                        await _userProfileService.SwitchWhatsappChannel(
                                            conversation.UserProfileId,
                                            new ChangeChatAPIInstance
                                            {
                                                InstanceId = $"{instanceId};{instancePhoneNumber}"
                                            });
                                    }
                                    else
                                    {
                                        await _userProfileService.SwitchWhatsappChannel(
                                            conversation.UserProfileId,
                                            new ChangeChatAPIInstance
                                            {
                                                InstanceId = $"{instanceId}"
                                            });
                                    }

                                    var userProfile = await _appDbContext.UserProfiles
                                        .Where(x => x.Id == conversation.UserProfileId).Include(x => x.WhatsAppAccount)
                                        .FirstOrDefaultAsync();
                                    conversationMessage.whatsappReceiver = userProfile.WhatsAppAccount;
                                    conversation.WhatsappUser = userProfile.WhatsAppAccount;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName} endpoint] error when switching WhatsApp channel for user profile {UserProfileId}: {ExceptionMessage}",
                                nameof(PublicApiSendMessageAsync),
                                conversation?.UserProfileId,
                                ex.Message);
                        }

                        // Twilio Content API
                        if (!string.IsNullOrEmpty(sendMessageFromAPIViewModel.ExtendedMessageJson))
                        {
                            conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                            {
                                ExtendedMessageType = ExtendedMessageType.TwilioContentApi,
                                ExtendedMessagePayloadDetail =
                                    JsonConvert.DeserializeObject<ExtendedMessagePayloadDetail>(
                                        sendMessageFromAPIViewModel.ExtendedMessageJson)
                            };
                        }

                        break;
                    case ChannelTypes.Whatsapp360Dialog:
                        if (!string.IsNullOrEmpty(sendMessageFromAPIViewModel.To))
                        {
                            var cache = await _cacheManagerService.GetCacheWithConstantKeyAsync(
                                AddNewContactLockKey(
                                    company.Id,
                                    PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To)));

                            var acquiredLock = !string.IsNullOrEmpty(cache);
                            if (acquiredLock)
                            {
                                await Task.Delay(5000);
                            }
                        }

                        var conversation360dialog = await _appDbContext.Conversations
                            .Where(
                                x => x.CompanyId == apiKey.CompanyId
                                     && x.WhatsApp360DialogUser.CompanyId == apiKey.CompanyId
                                     && x.WhatsApp360DialogUser.WhatsAppId ==
                                     PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To))
                            .Include(x => x.WhatsApp360DialogUser)
                            .OrderByDescending(x => x.UpdatedTime)
                            .FirstOrDefaultAsync();

                        if (conversation360dialog == null)
                        {
                            var userProfile = await _appDbContext.UserProfiles
                                .Where(
                                    x => x.CompanyId == apiKey.CompanyId
                                         && x.WhatsApp360DialogUser.CompanyId == apiKey.CompanyId
                                         && x.WhatsApp360DialogUser.WhatsAppId ==
                                         PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To))
                                .FirstOrDefaultAsync();

                            if (userProfile != null)
                            {
                                conversation360dialog =
                                    await _userProfileService.GetConversationByUserProfileId(
                                        apiKey.CompanyId,
                                        userProfile.Id);
                            }
                        }

                        if (conversation360dialog != null)
                        {
                            conversation = conversation360dialog;
                        }

                        var config = await _appDbContext
                            .ConfigWhatsApp360DialogConfigs
                            .AsNoTracking()
                            .FirstOrDefaultAsync(
                                x => x.CompanyId == apiKey.CompanyId &&
                                     x.WhatsAppPhoneNumber == sendMessageFromAPIViewModel.From);

                        if (config == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    code = 400,
                                    message =
                                        $"360Dialog Channel with Number {sendMessageFromAPIViewModel.From} not found."
                                });
                        }

                        if (conversation.WhatsApp360DialogUser == null ||
                            conversation.WhatsApp360DialogUser.ChannelId != config.Id)
                        {
                            var whatsApp360DialogSender = new WhatsApp360DialogSender()
                            {
                                WhatsAppId = PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To),
                                PhoneNumber =
                                    "+" + PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To),
                                CompanyId = company.Id,
                                ChannelId = config.Id,
                                ChannelWhatsAppPhoneNumber = config.WhatsAppPhoneNumber,
                            };
                            conversation.WhatsApp360DialogUser = whatsApp360DialogSender;
                            conversationMessage.Whatsapp360DialogReceiver = whatsApp360DialogSender;
                        }
                        else
                        {
                            conversationMessage.Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser;
                        }

                        break;
                    case ChannelTypes.WhatsappCloudApi:
                        var sender = await _appDbContext.WhatsappCloudApiSenders
                            .AsNoTracking()
                            .Where(
                                x => x.CompanyId == apiKey.CompanyId && x.WhatsappId ==
                                    PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To))
                            .FirstOrDefaultAsync();

                        if (!string.IsNullOrEmpty(sendMessageFromAPIViewModel.To))
                        {
                            var cache = await _cacheManagerService.GetCacheWithConstantKeyAsync(
                                AddNewContactLockKey(
                                    company.Id,
                                    PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To)));

                            var acquiredLock = !string.IsNullOrEmpty(cache);
                            if (acquiredLock)
                            {
                                await Task.Delay(5000);
                            }
                        }

                        var cloudApiConfig = await _appDbContext
                            .ConfigWhatsappCloudApiConfigs
                            .AsNoTracking()
                            .FirstOrDefaultAsync(
                                x => x.CompanyId == apiKey.CompanyId &&
                                     x.WhatsappPhoneNumber == sendMessageFromAPIViewModel.From);

                        if (cloudApiConfig == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    code = 400,
                                    message =
                                        $"WhatsApp Cloud Api Channel with Number {sendMessageFromAPIViewModel.From} not found."
                                });
                        }

                        if (sender == null)
                        {
                            sender = new WhatsappCloudApiSender()
                            {
                                CompanyId = apiKey.CompanyId,
                                WhatsappId = PhoneNumberHelper.NormalizePhoneNumber(sendMessageFromAPIViewModel.To),
                                WhatsappUserDisplayName = "Anonymous",
                                WhatsappChannelPhoneNumber = sendMessageFromAPIViewModel.From,
                            };
                        }

                        if (sender.WhatsappChannelPhoneNumber != sendMessageFromAPIViewModel.From)
                        {
                            sender.WhatsappChannelPhoneNumber = sendMessageFromAPIViewModel.From;
                        }

                        conversation.WhatsappCloudApiUser = sender;
                        conversationMessage.WhatsappCloudApiSender = conversation.WhatsappCloudApiUser;
                        conversationMessage.ChannelIdentityId = sendMessageFromAPIViewModel.From;

                        break;
                    case ChannelTypes.LiveChat:
                        if (!string.IsNullOrEmpty(sendMessageFromAPIViewModel.WebClientSenderId))
                        {
                            conversationMessage.WebClientSender = await _appDbContext.SenderWebClientSenders
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.WebClientUUID == sendMessageFromAPIViewModel.WebClientSenderId
                                        && x.CompanyId == company.Id);

                            conversation.WebClient = conversationMessage.WebClientSender;
                        }
                        else if (!string.IsNullOrEmpty(sendMessageFromAPIViewModel.WebClientReceiverId))
                        {
                            if (conversationMessage.Sender == null)
                            {
                                return BadRequest(
                                    new ResponseViewModel
                                    {
                                        code = 400, message = "Please login as staff to reply"
                                    });
                            }

                            conversationMessage.WebClientReceiver = await _appDbContext.SenderWebClientSenders
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.WebClientUUID == sendMessageFromAPIViewModel.WebClientReceiverId
                                        && x.CompanyId == company.Id);

                            conversation.WebClient = conversationMessage.WebClientReceiver;
                        }

                        if (conversation.WebClient == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    code = 400, message = "No webclient found"
                                });
                        }

                        break;
                    case ChannelTypes.Wechat:
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.WeChatUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.WeChatUser.openid == sendMessageFromAPIViewModel.WeChatReceiverOpenId
                                    && x.CompanyId == company.Id);

                        if (conversation.WeChatUser == null)
                        {
                            var wechatId = await _appDbContext.ConfigWeChatConfigs
                                .Where(x => x.Id == company.WeChatConfigId)
                                .Select(x => x.AppId)
                                .FirstOrDefaultAsync();

                            conversation.WeChatUser = new WeChatSender
                            {
                                openid = sendMessageFromAPIViewModel.WeChatReceiverOpenId,
                                CompanyId = company.Id,
                                ChannelIdentityId = wechatId,
                            };
                        }

                        conversationMessage.WeChatReceiver = conversation.WeChatUser;

                        break;
                    case ChannelTypes.Line:
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.LineUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.LineUser.userId == sendMessageFromAPIViewModel.LineReceiverId
                                    && x.CompanyId == company.Id);

                        if (conversation.LineUser == null)
                        {
                            var lineChannelId = await _appDbContext.ConfigLineConfigs
                                .Where(x => x.CompanyId == company.Id)
                                .Select(x => x.ChannelID)
                                .FirstOrDefaultAsync();

                            conversation.LineUser = new LineSender
                            {
                                userId = sendMessageFromAPIViewModel.LineReceiverId,
                                CompanyId = company.Id,
                                ChannelIdentityId = lineChannelId,
                            };
                        }

                        conversationMessage.LineReceiver = conversation.LineUser;

                        break;
                    case ChannelTypes.Sms:

                        // Bug fix DEVS-9193 Moi Moi - A new contact will be generated when user reply via SMS channel
                        // Normalize phone number to ensure all the phone numbers are in the same format
                        var from = sendMessageFromAPIViewModel.From;
                        var to = sendMessageFromAPIViewModel.To;
                        var e164From = PhoneNumberHelper.ToE164Format(from);
                        var e164To = PhoneNumberHelper.ToE164Format(to);
                        var normalizedTo = PhoneNumberHelper.NormalizePhoneNumber(to);

                        var smsChannel = await _appDbContext.ConfigSMSConfigs
                            .Where(x => x.SMSSender.Contains(e164From)).FirstOrDefaultAsync();

                        if (smsChannel == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    code = 400, message = "No sender found"
                                });
                        }

                        if (await _twilioService.ArePhoneNumbersEquivalentInTwilioAsync(
                                to,
                                normalizedTo,
                                company.Id))
                        {
                            // Since both phone number in Twilio are Equivalent so normalized number will be used
                            // If not then stick to the original to
                            to = normalizedTo;
                            e164To = PhoneNumberHelper.ToE164Format(to);
                        }

                        var smsSender = await _appDbContext.SenderSMSSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.SMSId == e164To
                                    && x.CompanyId == company.Id
                                    && x.InstanceId == smsChannel.TwilioAccountId) ?? new SMSSender
                        {
                            SMSId = e164To,
                            InstanceId = smsChannel.TwilioAccountId,
                            CompanyId = company.Id,
                            phone_number = to,
                            name = to
                        };

                        var existingSmsConversation = await _appDbContext.Conversations
                            .Include(x => x.SMSUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.SMSUser.SMSId == smsSender.SMSId
                                    && x.SMSUser.InstanceId == smsSender.InstanceId
                                    && x.CompanyId == company.Id);

                        if (existingSmsConversation != null)
                        {
                            conversation = existingSmsConversation;
}
                        else
                        {
                            conversation.SMSUser = smsSender;
                        }

                        conversationMessage.SMSReceiver = conversation.SMSUser;

                        break;
                    case ChannelTypes.Note:
                        conversation = await _appDbContext.Conversations
                            .FirstOrDefaultAsync(x => x.Id == sendMessageFromAPIViewModel.ConversationId);

                        if (conversation == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    code = 400, message = "Conversation not found."
                                });
                        }

                        break;
                }

                try
                {
                    IList<ConversationMessage> results = null;

                    if (sendMessageFromAPIViewModel.fileURLs?.Count > 0)
                    {
                        // When file url has value
                        var provider = new FileExtensionContentTypeProvider();
                        string contentType;

                        var fileURLMessages = new List<FileURLMessage>();

                        foreach (var url in sendMessageFromAPIViewModel.fileURLs)
                        {
                            if (!provider.TryGetContentType(url, out contentType))
                            {
                                contentType = "application/octet-stream";
                            }

                            fileURLMessages.Add(
                                new FileURLMessage
                                {
                                    FileName = Path.GetFileName(url), FileURL = url, MIMEType = contentType
                                });
                        }

                        if (sendMessageFromAPIViewModel.Channel == ChannelTypes.Note)
                        {
                            // Send Note
                            var noteMessage = await _conversationMessageService.SendConversationNote(
                                apiKey.CompanyId,
                                conversation.Id,
                                null,
                                conversationMessage,
                                new ConversationNoteViewModel()
                                {
                                    fileUrls = sendMessageFromAPIViewModel.fileURLs.ToList()
                                });

                            results = new List<ConversationMessage>()
                            {
                                noteMessage
                            };
                        }
                        else
                        {
                            // Send File Url Message
                            results = await _conversationMessageService.SendFileMessageByFBURL(
                                conversation,
                                conversationMessage,
                                fileURLMessages);
                        }
                    }
                    else if (sendMessageFromAPIViewModel.Channel == ChannelTypes.Note)
                    {
                        // Send Text Note
                        var noteMessage = await _conversationMessageService.SendConversationNote(
                            apiKey.CompanyId,
                            conversation.Id,
                            !string.IsNullOrWhiteSpace(sendMessageFromAPIViewModel.StaffUserId)
                                ? sendMessageFromAPIViewModel.StaffUserId
                                : null,
                            conversationMessage,
                            new ConversationNoteViewModel()
                            {
                                AssigneeId = sendMessageFromAPIViewModel.AssigneeId,
                                files = sendMessageFromAPIViewModel.files
                            });

                        results = new List<ConversationMessage>()
                        {
                            noteMessage
                        };
                    }
                    else
                    {
                        if (sendMessageFromAPIViewModel.MessageType == "file")
                        {
                            results = await _conversationMessageService.SendFileMessage(
                                conversation,
                                conversationMessage,
                                new ConversationMessageViewModel
                                {
                                    files = sendMessageFromAPIViewModel.files
                                });
                        }
                        else if (sendMessageFromAPIViewModel.MessageType == "text")
                        {
                            results = await _conversationMessageService.SendMessage(conversation, conversationMessage);
                        }
                        else if (sendMessageFromAPIViewModel.Channel == ChannelTypes.Whatsapp360Dialog &&
                                 sendMessageFromAPIViewModel.MessageType is "template" or "interactive")
                        {
                            conversationMessage.Whatsapp360DialogExtendedMessagePayload =
                                _mapper.Map<Whatsapp360DialogExtendedMessagePayload>(
                                    JsonConvert
                                        .DeserializeObject<Whatsapp360DialogExtendedMessagePayloadRequestViewModel>(
                                            sendMessageFromAPIViewModel.ExtendedMessageJson));

                            switch (sendMessageFromAPIViewModel.MessageType)
                            {
                                case "template":
                                    if (conversationMessage.Whatsapp360DialogExtendedMessagePayload
                                            ?.Whatsapp360DialogTemplateMessage == null ||
                                        string.IsNullOrWhiteSpace(
                                            conversationMessage.Whatsapp360DialogExtendedMessagePayload
                                                .Whatsapp360DialogTemplateMessage.TemplateNamespace) ||
                                        string.IsNullOrWhiteSpace(
                                            conversationMessage.Whatsapp360DialogExtendedMessagePayload
                                                .Whatsapp360DialogTemplateMessage.TemplateName))
                                    {
                                        return BadRequest(
                                            new ResponseViewModel
                                            {
                                                code = 400, message = "Template message format is invalid."
                                            });
                                    }

                                    try
                                    {
                                        // var cacheKey = WhatsApp360DialogCacheKeyHelper.GetTemplateResponseCacheKey(
                                        //     apiKey.CompanyId,
                                        //     conversation.WhatsApp360DialogUser.ChannelId.Value,
                                        //     1000,
                                        //     0);

                                        var whatsApp360DialogGetTemplateCacheKeyPattern = new WhatsApp360DialogGetTemplateCacheKeyPattern(
                                            apiKey.CompanyId,
                                            conversation.WhatsApp360DialogUser.ChannelId.Value);

                                        var cache = await _cacheManagerService.GetCacheAsync(whatsApp360DialogGetTemplateCacheKeyPattern);
                                        GetWhatsApp360DialogTemplateResponse result;
                                        WhatsAppBusinessApiTemplateViewModel template = null;

                                        if (cache != null)
                                        {
                                            result =
                                                JsonConvert.DeserializeObject<GetWhatsApp360DialogTemplateResponse>(
                                                    cache);

                                            template = result.WhatsAppTemplates.FirstOrDefault(
                                                x => x.Name == conversationMessage
                                                         .Whatsapp360DialogExtendedMessagePayload
                                                         .Whatsapp360DialogTemplateMessage.TemplateName &&
                                                     x.Language == conversationMessage
                                                         .Whatsapp360DialogExtendedMessagePayload
                                                         .Whatsapp360DialogTemplateMessage.Language &&
                                                     x.Namespace == conversationMessage
                                                         .Whatsapp360DialogExtendedMessagePayload
                                                         .Whatsapp360DialogTemplateMessage.TemplateNamespace);
                                        }

                                        if (template == null)
                                        {
                                            result = await _whatsApp360DialogService.GetWhatsApp360DialogTemplate(
                                                apiKey.CompanyId,
                                                conversation.WhatsApp360DialogUser.ChannelId.Value,
                                                1000,
                                                0);

                                            await _cacheManagerService.SaveCacheAsync(
                                                whatsApp360DialogGetTemplateCacheKeyPattern,
                                                result,
                                                null,
                                                new JsonSerializerSettings
                                                {
                                                    DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                                                    NullValueHandling = NullValueHandling.Ignore,
                                                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                                                });

                                            template = result.WhatsAppTemplates.FirstOrDefault(
                                                x => x.Name == conversationMessage
                                                         .Whatsapp360DialogExtendedMessagePayload
                                                         .Whatsapp360DialogTemplateMessage.TemplateName &&
                                                     x.Language == conversationMessage
                                                         .Whatsapp360DialogExtendedMessagePayload
                                                         .Whatsapp360DialogTemplateMessage.Language &&
                                                     x.Namespace == conversationMessage
                                                         .Whatsapp360DialogExtendedMessagePayload
                                                         .Whatsapp360DialogTemplateMessage.TemplateNamespace);
                                        }

                                        if (template == null)
                                        {
                                            return BadRequest(
                                                new ResponseViewModel
                                                {
                                                    code = 400,
                                                    message =
                                                        $"Template '{conversationMessage.Whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage.TemplateName}' not found."
                                                });
                                        }

                                        // Format Message Content with param
                                        conversationMessage.MessageContent = template
                                            .Components
                                            .First(x => x.Type == TemplateComponentType.BODY)
                                            .Text;

                                        conversationMessage.MessageContent =
                                            conversationMessage.MessageContent.FormatParamToBodyText(
                                                conversationMessage.Whatsapp360DialogExtendedMessagePayload
                                                    .Whatsapp360DialogTemplateMessage.Components);
                                    }
                                    catch (ApiClientException e)
                                    {
                                        return BadRequest(
                                            new ResponseViewModel
                                            {
                                                code = 400, message = "Error on sending message"
                                            });
                                    }
                                    catch (Exception e)
                                    {
                                        _logger.LogError(e, "Error formatting the message payload to message content.");
                                    }

                                    break;
                                case "interactive":
                                    if (conversationMessage.Whatsapp360DialogExtendedMessagePayload
                                            ?.Whatsapp360DialogInteractiveObject == null)
                                    {
                                        return BadRequest(
                                            new ResponseViewModel
                                            {
                                                code = 400, message = "Interactive message format is invalid."
                                            });
                                    }

                                    break;
                                default:
                                    throw new ArgumentException("Supported message type.");
                            }

                            results = await _conversationMessageService.SendMessage(conversation, conversationMessage);
                        }

                        else if (sendMessageFromAPIViewModel.Channel == ChannelTypes.WhatsappCloudApi &&
                                 sendMessageFromAPIViewModel.MessageType is "template" or "interactive" or "contacts"
                                     or "location" or "reaction")
                        {
                            conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                            {
                                Channel = ChannelTypes.WhatsappCloudApi
                            };

                            conversationMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                                JsonConvert.DeserializeObject<ExtendedMessagePayloadDetail>(
                                    sendMessageFromAPIViewModel.ExtendedMessageJson));

                            switch (sendMessageFromAPIViewModel.MessageType)
                            {
                                case "template":
                                    if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail ==
                                        null || conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                            .WhatsappCloudApiTemplateMessageObject == null ||
                                        string.IsNullOrWhiteSpace(
                                            conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                                .WhatsappCloudApiTemplateMessageObject.TemplateName) ||
                                        string.IsNullOrWhiteSpace(
                                            conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                                .WhatsappCloudApiTemplateMessageObject.Language) ||
                                        conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                            .WhatsappCloudApiTemplateMessageObject == null)
                                    {
                                        return BadRequest(
                                            new ResponseViewModel
                                            {
                                                code = 400, message = "Template message format is invalid."
                                            });
                                    }

                                    var messagingHubWabaId = await _appDbContext.ConfigWhatsappCloudApiConfigs
                                        .Where(
                                            x => x.CompanyId == company.Id && x.WhatsappPhoneNumber ==
                                                conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber)
                                        .Select(x => x.MessagingHubWabaId).FirstOrDefaultAsync();

                                    try
                                    {
                                        var cacheKey = WhatsappCloudApiCacheKeyHelper.GetTemplateResponseCacheKey(
                                            company.Id,
                                            messagingHubWabaId);

                                        var cache = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);

                                        List<WhatsappCloudApiTemplate> result;
                                        WhatsappCloudApiTemplate template = null;

                                        if (!string.IsNullOrEmpty(cache))
                                        {
                                            result = JsonConvert.DeserializeObject<List<WhatsappCloudApiTemplate>>(
                                                cache);

                                            template = result.FirstOrDefault(
                                                x => x.Name == conversationMessage.ExtendedMessagePayload
                                                         .ExtendedMessagePayloadDetail
                                                         .WhatsappCloudApiTemplateMessageObject.TemplateName &&
                                                     x.Language == conversationMessage.ExtendedMessagePayload
                                                         .ExtendedMessagePayloadDetail
                                                         .WhatsappCloudApiTemplateMessageObject.Language);
                                        }

                                        if (template == null)
                                        {
                                            result = _mapper.Map<List<WhatsappCloudApiTemplate>>(
                                                await _whatsappCloudApiService.GetTemplates(
                                                    company.Id,
                                                    messagingHubWabaId));

                                            template = result.FirstOrDefault(
                                                x => x.Name == conversationMessage.ExtendedMessagePayload
                                                         .ExtendedMessagePayloadDetail
                                                         .WhatsappCloudApiTemplateMessageObject.TemplateName &&
                                                     x.Language == conversationMessage.ExtendedMessagePayload
                                                         .ExtendedMessagePayloadDetail
                                                         .WhatsappCloudApiTemplateMessageObject.Language);
                                        }

                                        if (template == null)
                                        {
                                            return BadRequest(
                                                new ResponseViewModel
                                                {
                                                    code = 400,
                                                    message =
                                                        $"Template '{conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.TemplateName}' not found."
                                                });
                                        }

                                        conversationMessage.MessageContent =
                                            template.Components.First(x => x.Type == "BODY").Text;

                                        conversationMessage.MessageContent =
                                            conversationMessage.MessageContent
                                                .FormatWhatsappCloudApiTemplateParamToBodyText(
                                                    conversationMessage.ExtendedMessagePayload
                                                        .ExtendedMessagePayloadDetail
                                                        .WhatsappCloudApiTemplateMessageObject.Components);
                                    }
                                    catch (SleekflowErrorCodeException ex)
                                    {
                                        _logger.LogError(
                                            "Public Api Sending Message Error, CompanyId {CompanyId} with error: {ExceptionString}",
                                            company.Id,
                                            ex.ToString());

                                        return BadRequest(
                                            new ResponseViewModel
                                            {
                                                code = 400, message = ex.Message
                                            });
                                    }

                                    break;
                                case "interactive":
                                    if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                            .WhatsappCloudApiInteractiveObject == null)
                                    {
                                        return BadRequest(
                                            new ResponseViewModel
                                            {
                                                code = 400, message = "Interactive message format is invalid."
                                            });
                                    }

                                    break;
                                case "contacts":
                                    if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                            .WhatsappCloudApiContactsObject == null)
                                    {
                                        return BadRequest(
                                            new ResponseViewModel
                                            {
                                                code = 400, message = "Contacts message format is invalid."
                                            });
                                    }

                                    break;
                                case "location":
                                    if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                            .WhatsappCloudApiLocationObject == null)
                                    {
                                        return BadRequest(
                                            new ResponseViewModel
                                            {
                                                code = 400, message = "Contacts message format is invalid."
                                            });
                                    }

                                    break;
                                case "reaction":
                                    if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                            .WhatsappCloudApiReactionObject == null)
                                    {
                                        return BadRequest(
                                            new ResponseViewModel
                                            {
                                                code = 400, message = "Reaction message format is invalid."
                                            });
                                    }

                                    break;
                                default:
                                    throw new ArgumentException("Supported message type.");
                            }

                            results = await _conversationMessageService.SendMessage(conversation, conversationMessage);
                        }
                    }

                    if (whatsappPhoneNumberLock != null)
                    {
                        await _lockService.ReleaseLockAsync(whatsappPhoneNumberLock);
                    }

                    var conversationMessagesVM =
                        _mapper.Map<ConversationMessageResponseViewModel>(results.FirstOrDefault());

                    return Ok(conversationMessagesVM);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName} endpoint] error: {ExceptionMessage}",
                        nameof(PublicApiSendMessageAsync),
                        ex.Message);

                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = ex.Message
                        });
                }
            }

            return BadRequest(
                new ResponseViewModel
                {
                    code = 400, message = "Incorrect format"
                });
        }

        [HttpPost]
        [Route("API/message/send/json")]
        public async Task<ActionResult<ResponseViewModel>> APISendJsonBody(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            SendMessageFromAPIWithBodyViewModel request,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (request.MessageType == "file" && (request.fileURLs == null || request.fileURLs.Count == 0))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Incorrect format"
                    });
            }

            _logger.LogInformation(
                "[Method {MethodName}] Payload {request}",
                nameof(APISendJsonBody),
                JsonConvert.SerializeObject(request));

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;

            return await PublicApiSendMessageAsync(
                apikey,
                new SendMessageFromAPIViewModel
                {
                    MessageChecksum = request.MessageChecksum,
                    Channel = request.Channel,
                    From = request.From,
                    To = request.To,
                    ConversationId = request.ConversationId,
                    FacebookReceiverId = request.FacebookReceiverId,
                    WebClientSenderId = request.WebClientSenderId,
                    WebClientReceiverId = request.WebClientReceiverId,
                    WeChatReceiverOpenId = request.WeChatReceiverOpenId,
                    LineReceiverId = request.LineReceiverId,
                    Subject = request.Subject,
                    MessageType = request.MessageType,
                    MessageContent = request.MessageContent,
                    fileURLs = request.fileURLs,
                    QuotedMsgId = request.QuotedMsgId,
                    StaffUserId = request.StaffUserId,
                    AssigneeId = request.AssigneeId,
                    ExtendedMessageJson = JsonConvert.SerializeObject(request.ExtendedMessage),
                    AnalyticTags = request.AnalyticTags
                });
        }

        /// <summary>
        /// Send a note in a conversation, support @mention staff.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("API/message/send/note")]
        public async Task<ActionResult<ResponseViewModel>> APISendNote(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            SendNoteFromAPIWithBodyViewModel request,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.AddUsageAsync(apiKey);

            return await PublicApiSendMessageAsync(
                apikey,
                new SendMessageFromAPIViewModel
                {
                    MessageChecksum = null,
                    Channel = request.Channel,
                    From = null,
                    To = null,
                    ConversationId = request.ConversationId,
                    FacebookReceiverId = null,
                    WebClientSenderId = null,
                    WebClientReceiverId = null,
                    WeChatReceiverOpenId = null,
                    LineReceiverId = null,
                    Subject = null,
                    MessageType = request.MessageType,
                    MessageContent = request.MessageContent,
                    fileURLs = null,
                    QuotedMsgId = null,
                    StaffUserId = request.StaffUserId,
                    AssigneeId = request.AssigneeId,
                    ExtendedMessageJson = null,
                });
        }

        /// <summary>
        ///  Get Whatsapp official Reply Window Status.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("api/whatsapp/reply-window-status")]
        public async Task<ActionResult<List<WhatsappConversationSessionStatus>>> CheckWhatsappConversationSessionStatus(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            CheckWhatsappConversationSessionStatusViewModel viewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;

            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.AddUsageAsync(apiKey);

            bool isUseConversationIds = viewModel.ConversationIds is { Count: > 0 };
            bool isUsePhoneNumbers = !isUseConversationIds && viewModel.WhatsappPhoneNumbers is {Count: > 0};

            if (!isUseConversationIds && !isUsePhoneNumbers)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Please provide either Conversation Ids or Whatsapp Phone Numbers."
                    });
            }

            var result = await _appDbContext.ConversationMessages
                .Include(x => x.whatsappSender)
                .Where(x => x.CompanyId == apiKey.CompanyId && x.whatsappSender != null && !x.IsSentFromSleekflow)
                .WhereIf(
                    isUseConversationIds,
                    x =>
                        viewModel.ConversationIds.Distinct().Take(30).Contains(x.ConversationId) &&
                        x.whatsappSender.whatsAppId.StartsWith("whatsapp:+"))
                .WhereIf(
                    isUsePhoneNumbers,
                    x => viewModel.WhatsappPhoneNumbers.Distinct().Take(30).Select(w => "whatsapp:+" + w)
                        .Contains(x.whatsappSender.whatsAppId))
                .OrderByDescending(x => x.CreatedAt)
                .GroupBy(x => x.ConversationId)
                .Select(
                    x => new WhatsappConversationSessionStatus
                    {
                        ConversationId = x.Key, LastClientMessageReceivedAt = x.Max(message => message.CreatedAt)
                    })
                .ToListAsync();

            var phoneNumbers = await _appDbContext.Conversations
                .Include(x => x.WhatsappUser)
                .Where(x => result.Select(w => w.ConversationId).Contains(x.Id))
                .Select(
                    x => new
                    {
                        x.WhatsappUser.phone_number, x.Id
                    })
                .ToListAsync();

            foreach (var sessionStatus in result)
            {
                sessionStatus.WhatsappPhoneNumber =
                    phoneNumbers.FirstOrDefault(x => x.Id == sessionStatus.ConversationId)?.phone_number;
            }

            return Ok(result);
        }

        #endregion

        #region Broadcast API

        /// <summary>
        /// Get the list of broadcasts.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/broadcasts")]
        public async Task<ActionResult> GetBroadcasts(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "name")]
            string name = null,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.AddUsageAsync(apiKey);

            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        code = 400, message = InvalidModelStateResponse.FromModelState(ModelState).Message
                    });
            }

            var getMessageTemplatesQuery = _appDbContext.CompanyMessageTemplates
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.UploadedFiles)
                .Include(x => x.CampaignChannelMessages)
                .ThenInclude(x => x.UploadedFiles)
                .Include(x => x.SavedBy.Identity)
                .Include(x => x.LastSentBy.Identity)
                .Include(x => x.CampaignAutomationActions)
                .ThenInclude(x => x.UploadedFiles)
                .OrderByDescending(broadcastMessage => broadcastMessage.UpdatedAt)
                .Where(broadcastMessage => broadcastMessage.CompanyId == apiKey.CompanyId)
                .WhereIf(!string.IsNullOrEmpty(name), broadcastMessage => broadcastMessage.TemplateName.Contains(name));

            var messageTemplates = await getMessageTemplatesQuery
                .Skip(offset)
                .Take(limit)
                .ToListAsync(HttpContext.RequestAborted);

            var response = _mapper.Map<List<BroadcastCampaignViewModel>>(messageTemplates);

            return Ok(response);
        }

        /// <summary>
        /// Get individual broadcast by broadcastId.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/broadcasts/{broadcastId}")]
        public async Task<ActionResult> GetBroadcast(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute(Name = "broadcastId"), Required]
            string broadcastId,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.AddUsageAsync(apiKey);

            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        code = 400, message = InvalidModelStateResponse.FromModelState(ModelState).Message
                    });
            }

            var broadcastExists = await _broadcastService.BroadcastExists(apiKey.CompanyId, broadcastId);

            if (!broadcastExists)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Broadcast not found. Id: {broadcastId}"
                    });
            }

            var getMessageTemplatesQuery = _appDbContext.CompanyMessageTemplates
                .AsNoTracking()
                .AsSplitQuery()
                .Where(x => x.Id == broadcastId)
                .Include(x => x.UploadedFiles)
                .Include(x => x.CampaignChannelMessages)
                .ThenInclude(x => x.UploadedFiles)
                .Include(x => x.SavedBy.Identity)
                .Include(x => x.LastSentBy.Identity)
                .Include(x => x.CampaignAutomationActions)
                .ThenInclude(x => x.UploadedFiles)
                .OrderByDescending(broadcastMessage => broadcastMessage.UpdatedAt)
                .Where(broadcastMessage => broadcastMessage.CompanyId == apiKey.CompanyId);

            var messageTemplates = await getMessageTemplatesQuery.FirstOrDefaultAsync();

            var response = _mapper.Map<BroadcastCampaignViewModel>(messageTemplates);

            return Ok(response);
        }

        /// <summary>
        /// Get individual broadcast recipients detailed information by broadcastId.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/broadcasts/{broadcastId}/recipients")]
        public async Task<ActionResult> GetBroadcastRecipients(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute(Name = "broadcastId"), Required]
            string broadcastId,
            [FromQuery(Name = "status"), Required,
             RegularExpression(
                 "sent|delivered|failed|read|replied",
                 ErrorMessage = $"Invalid parameter: status must be either sent, delivered, failed, read or replied ")]
            string status,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = ""
        )
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.AddUsageAsync(apiKey);

            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        code = 400, message = InvalidModelStateResponse.FromModelState(ModelState).Message
                    });
            }

            var broadcastExists = await _broadcastService.BroadcastExists(apiKey.CompanyId, broadcastId);

            if (!broadcastExists)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Broadcast not found. Id: {broadcastId}"
                    });
            }

            var broadcastStatusCondition = new List<Condition>();

            broadcastStatusCondition.Add(
                status switch
                {
                    "sent" => new Condition()
                    {
                        CompanyMessageTemplateId = broadcastId, BroadcastMessageStatus = BroadcastMessageStatus.Sent
                    },
                    "delivered" => new Condition()
                    {
                        CompanyMessageTemplateId = broadcastId,
                        BroadcastMessageStatus = BroadcastMessageStatus.Delivered
                    },
                    "failed" => new Condition()
                    {
                        CompanyMessageTemplateId = broadcastId, BroadcastMessageStatus = BroadcastMessageStatus.Failed
                    },
                    "read" => new Condition()
                    {
                        CompanyMessageTemplateId = broadcastId, BroadcastMessageStatus = BroadcastMessageStatus.Read
                    },
                    "replied" => new Condition()
                    {
                        CompanyMessageTemplateId = broadcastId, BroadcastMessageStatus = BroadcastMessageStatus.Replied
                    },
                    _ => throw new ArgumentException()
                });

            var targetUserProfiles =
                await _userProfileSqlService.GetUserProfilesAsync(
                    apiKey.CompanyId,
                    broadcastStatusCondition,
                    getUserProfileIdOnly: true);

            var response = new BroadcastCampaignRecipientsViewModel
            {
                RecipientIds = targetUserProfiles.UserProfiles.Select(x => x.Id).ToList(),
                Count = targetUserProfiles.TotalCount
            };

            return Ok(response);
        }

        #endregion

        #region Contacts

        [HttpPost]
        [Route("API/Contact/Add")]
        [Route("API/Contact/AddOrUpdate")]
        public async Task<ActionResult<ResponseViewModel>> APIAddContact(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            List<NewProfileViewModel> newProfileViewModels,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Incorrect format"
                    });
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey froxm query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var companyApiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(companyApiKey);

            _logger.LogInformation(
                "[PublicAPI] AddOrUpdateUserProfile: {CompanyId}, request payload: {AddContactPayload}",
                companyApiKey.CompanyId,
                JsonConvert.SerializeObject(newProfileViewModels));

            try
            {
                var results = new List<UserProfileNoCompanyResponse>();
                var companyUsage = await _companyUsageService.GetCompanyContactUsageAsync(companyApiKey.CompanyId);
                var availableContactCount = companyUsage.MaximumContacts - companyUsage.TotalContactCount;

                if ( availableContactCount < newProfileViewModels.Count )
                {
                    _logger.LogWarning(
                        "Company {CompanyId} exceeded contact limitation: maximum {MaximumContactCount} contacts",
                        companyApiKey.CompanyId,
                        companyUsage.MaximumContacts);
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400,
                            ErrorCode = "ContactLimitExceeded",
                            message =
                                "Your company has reached the maximum number of contacts allowed. Please upgrade your plan or purchase additional add on to add more contacts"
                        });
                }

                if (newProfileViewModels.Count == 1)
                {
                    // Single contact - use direct business logic
                    var userProfile = newProfileViewModels[0];

                    try
                    {
                        var result = await ProcessSingleContactAsync(companyApiKey, userProfile);
                        results.Add(result);
                    }
                    catch (InvalidOperationException ex) when (ex.Message == "Server busy")
                    {
                        return BadRequest(new ResponseViewModel
                        {
                            code = 400, message = "Server busy"
                        });
                    }
                }
                else
                {
                    // Multiple contacts - process in parallel using separate service scopes
                    // Each scope gets its own DbContext instance to avoid EF Core concurrency issues
                    var tasks = new List<Task<UserProfileNoCompanyResponse>>();

                    foreach (var userProfile in newProfileViewModels)
                    {
                        var task = ProcessSingleContactAsync(companyApiKey, userProfile);
                        tasks.Add(task);
                    }

                    // Wait for all tasks to complete
                    var taskResults = await Task.WhenAll(tasks);

                    // Add all results to the response
                    foreach (var taskResult in taskResults)
                    {
                        if (taskResult != null)
                        {
                            results.Add(taskResult);
                        }
                    }
                }

                return Ok(results);
            }
            catch (FormatException fex)
            {
                try
                {
                    var errorDetail = JsonConvert.DeserializeObject<ErrorDetail>(fex.Message);

                    if (await _appDbContext.UserProfiles
                            .AnyAsync(x => x.Id == errorDetail.UserProfileId && x.CompanyId == companyApiKey.CompanyId))
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                ErrorCode = "DuplicateContact",
                                message =
                                    $"Duplicate contact found. ID: {errorDetail.UserProfileId}. Reason: {errorDetail.FieldName}"
                            });
                    }
                }
                catch (Exception)
                {
                    var splitMessage = fex.Message.Split(':');

                    if (splitMessage.Length != 2)
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = fex.Message
                            });
                    }

                    var userProfileId = splitMessage[0];
                    var duplicateReason = splitMessage[1];

                    if (await _appDbContext.UserProfiles
                            .AnyAsync(x => x.Id == userProfileId && x.CompanyId == companyApiKey.CompanyId))
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                ErrorCode = "DuplicateContact",
                                message = $"Duplicate contact found. ID: {userProfileId}. Reason: {duplicateReason}"
                            });
                    }
                }

                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = fex.Message
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = ex.Message
                    });
            }
        }



        private string AddNewContactLockKey(string companyId, string phoneNumber)
        {
            return $"publicapi_add_contact_{companyId}_{phoneNumber}";
        }

        [HttpPost]
        [Route("API/Contact/Update/{userProfileId}")]
        public async Task<ActionResult<ResponseViewModel>> UpdateUserProfile(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            NewProfileViewModel profileUpdateViewModel,
            string userProfileId,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (ModelState.IsValid)
            {
                if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Apikey from query param is different from header."
                        });
                }

                apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
                var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
                await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

                var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == apiKey.CompanyId);

                if (company == null)
                {
                    return Unauthorized();
                }

                try
                {
                    var userProfile = await _appDbContext.UserProfiles
                        .Where(x => x.CompanyId == company.Id && x.Id == userProfileId).FirstOrDefaultAsync();

                    if (userProfile == null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                code = 400, message = $"UserProfile not found, Id: {userProfileId}"
                            });
                    }

                    userProfile.FirstName = profileUpdateViewModel.FirstName;
                    userProfile.LastName = profileUpdateViewModel.LastName;
                    await _appDbContext.SaveChangesAsync();

                    ArrangeUserProfileFields(profileUpdateViewModel);

                    if (profileUpdateViewModel.UserProfileFields != null)
                    {
                        userProfile = await _userProfileService.UpdateUserProfileCustomFields(
                            company.Id,
                            userProfileId,
                            null,
                            profileUpdateViewModel.UserProfileFields,
                            userProfileSource: UserProfileSource.ManualEntry);
                    }

                    var responseVM = _mapper.Map<UserProfileNoCompanyResponse>(userProfile);

                    return Ok(responseVM);
                }
                catch (EntryPointNotFoundException nex)
                {
                    return NotFound(
                        new ResponseViewModel
                        {
                            code = 404, message = nex.Message
                        });
                }
                catch (FormatException fex)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = fex.Message
                        });
                }
                catch (Exception ex)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = ex.Message
                        });
                }
            }

            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        /// <summary>
        /// Get contact user profile by id.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/contact/{userProfileId}")]
        public async Task<ActionResult<ResponseViewModel>> GetUserProfile(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute]
            string userProfileId,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            try
            {
                var company = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == apiKey.CompanyId)
                    .AsNoTracking()
                    .Include(x => x.CustomUserProfileFields)
                    .FirstOrDefaultAsync();

                if (company == null)
                {
                    return Unauthorized();
                }

                var isUserProfileExist = await _userProfileService.IsUserProfileExist(company.Id, userProfileId);

                if (!isUserProfileExist)
                {
                    return NotFound(
                        new ResponseViewModel
                        {
                            code = 404, message = $"UserProfile not found, Id: {userProfileId}"
                        });
                }

                var userProfile = await _userProfileService.GetUserProfileByUserProfileId(company.Id, userProfileId);
                var response = await ToZapierResponse(company, userProfile);

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = ex.Message
                    });
            }
        }

        [HttpDelete]
        [Route("api/contact/{userProfileId}")]
        public async Task<ActionResult<ResponseViewModel>> DeleteUserProfile(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute]
            string userProfileId,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            try
            {
                var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == apiKey.CompanyId);

                if (company == null)
                {
                    return Unauthorized();
                }

                var isUserProfileExist = await _userProfileService.IsUserProfileExist(company.Id, userProfileId);

                if (!isUserProfileExist)
                {
                    return NotFound(
                        new ResponseViewModel
                        {
                            code = 404, message = $"UserProfile not found, Id: {userProfileId}"
                        });
                }

                _logger.LogInformation(
                    "[Public API {MethodName} endpoint]Company {CompanyId} is deleting user profile {UserProfileId}",
                    nameof(DeleteUserProfile),
                    company.Id,
                    userProfileId);

                if (_contactDeletionConfig.IsContactSafeDeleteEnabled)
                {
                    await _userProfileSafeDeleteService.SoftDeleteUserProfilesAsync(
                        null,
                        company.Id,
                        new HashSet<string>
                        {
                            userProfileId
                        },
                        new UserProfileDeletionTriggerContext(
                            UpdateUserProfileTriggerSource.PublicApi,
                            null));
                }
                else
                {
                    await _userProfileService.DeleteUserProfiles(
                        company.Id,
                        new UserProfileIdsViewModel
                        {
                            UserProfileIds = new List<string>
                            {
                                userProfileId
                            }
                        },
                        new UserProfileDeletionTriggerContext(
                            UpdateUserProfileTriggerSource.PublicApi,
                            null));
                }

                return Ok(
                    new ResponseViewModel
                    {
                        code = 200, message = $"UserProfile deleted, Id: {userProfileId}"
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = ex.Message
                    });
            }
        }

        #endregion

        #region Conversation

        /// <summary>
        /// Get conversation by conversation id.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/conversation/{conversationId}")]
        public async Task<ActionResult<ConversationNoCompanyResponseViewModel>> GetConversation(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute(Name = "conversationId")]
            string conversationId,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == apiKey.CompanyId);

            var myLock = await _lockService.AcquireLockAsync(
                $"public_api_conversation_{company.Id}_conversation_{conversationId}",
                TimeSpan.FromSeconds(1));

            if (myLock == null)
            {
                _logger.LogWarning(
                    "[Rejected] [PublicAPI] GetConversations Company: {CompanyId}, queryStr: {RequestQueryString}",
                    company.Id,
                    Request.QueryString);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Server busy"
                    });
            }

            _logger.LogInformation(
                "[PublicAPI] GetConversations Company: {CompanyId}, queryStr: {RequestQueryString}",
                company.Id,
                Request.QueryString);

            var conversations = await _conversationMessageService.GetConversations(
                company.Id,
                new Staff
                {
                    RoleType = StaffUserRole.Admin
                });

            var isConversationExist = await conversations.AnyAsync(x => x.Id == conversationId);

            if (!isConversationExist)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Conversation not found, Id: {conversationId}"
                    });
            }

            var listResult = await conversations
                .Where(x => x.Id == conversationId)
                .Include(x => x.AdditionalAssignees)
                .ThenInclude(x => x.Assignee.Identity)
                .Include(x => x.UserProfile)
                .Include(x => x.facebookUser)
                .Include(x => x.WhatsappUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .Include(x => x.NaiveUser.Identity)
                .Include(x => x.Assignee.Identity)
                .Include(x => x.EmailAddress)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(x => x.LineUser)
                .Include(x => x.SMSUser)
                .Include(x => x.AssignedTeam)
                .Include(x => x.TelegramUser)
                .Include(x => x.ViberUser)
                .FirstOrDefaultAsync();

            listResult.conversationHashtags = await _appDbContext.ConversationHashtags
                .Where(y => y.ConversationId == listResult.Id)
                .Include(y => y.Hashtag)
                .OrderByDescending(x => x.Id)
                .ToListAsync(HttpContext.RequestAborted);

            var mappedResult = await _conversationNoCompanyResponseViewModelMapper.ToViewModelAsync(listResult);

            await MaskConversationNoCompanyResponseViewModelAsync(
                mappedResult,
                company.Id,
                StaffUserRole.Admin);

            return Ok(mappedResult);
        }

        /// <summary>
        /// Assign the conversation to an assignee or no assignee.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("api/conversation/{conversationId}/assignee")]
        public async Task<ActionResult<ResponseViewModel>> AssignConversationAssignee(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute(Name = "conversationId")]
            string conversationId,
            [FromBody]
            StaffAssignmentViewModel staffAssignmentViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = ""
        )
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == apiKey.CompanyId);

            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            var myLock =
                await _lockService.AcquireLockAsync(
                    $"public_api_conversation_{company.Id}_assign_conversation_assignee_{conversationId}",
                    TimeSpan.FromSeconds(1));

            if (myLock == null)
            {
                _logger.LogWarning(
                    "[Rejected] [PublicAPI] GetConversations Company: {CompanyId}, queryStr: {RequestQueryString}",
                    company.Id,
                    Request.QueryString);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Server busy"
                    });
            }

            _logger.LogInformation(
                "[PublicAPI] GetConversations Company: {CompanyId}, queryStr: {RequestQueryString}",
                company.Id,
                Request.QueryString);

            var conversations = await _conversationMessageService.DefaultGetConversations(
                company.Id,
                new Staff
                {
                    RoleType = StaffUserRole.Admin
                });

            var conversationExists = await conversations.AnyAsync(x => x.Id == conversationId);

            if (!conversationExists)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Conversation not found, Id: {conversationId}"
                    });
            }

            var companyStaffExists = await _companyService.IsCompanyStaffExist(
                apiKey.CompanyId,
                staffAssignmentViewModel.StaffId);

            if (!companyStaffExists && staffAssignmentViewModel.StaffId.ToLower() != "unassigned")
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Assignee does not exist. Id: {staffAssignmentViewModel.StaffId}"
                    });
            }

            var conversation = await _appDbContext.Conversations
                .Where(x => x.Id == conversationId && x.CompanyId == apiKey.CompanyId)
                .Include(x => x.Assignee)
                .Include(x => x.UserProfile)
                .Include(x => x.AdditionalAssignees)
                .ThenInclude(x => x.Assignee.Identity)
                .FirstOrDefaultAsync();

            try
            {
                Staff assignee = null;

                if (!string.IsNullOrEmpty(staffAssignmentViewModel.StaffId))
                {
                    assignee = await _appDbContext.UserRoleStaffs.Where(
                            x =>
                                x.CompanyId == apiKey.CompanyId && x.IdentityId == staffAssignmentViewModel.StaffId)
                        .FirstOrDefaultAsync();
                }

                if (conversation.AssigneeId != assignee?.Id)
                {
                    conversation = await _conversationMessageService.ChangeConversationAssignee(conversation, assignee);
                }
            }
            catch (Exception e)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Error on assigning conversation assignee."
                    });
            }

            return Ok(
                new ResponseViewModel
                {
                    code = 200,
                    message =
                        $"Conversation {conversationId} assignee updated. Assignee Id: {staffAssignmentViewModel.StaffId}"
                });
        }

        /// <summary>
        /// Assign the conversation to collaborators or no collaborator.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("api/conversation/{conversationId}/collaborators")]
        public async Task<ActionResult<ResponseViewModel>> AssignConversationCollaborators(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute(Name = "conversationId"), Required]
            string conversationId,
            [FromBody]
            StaffAssignmentViewModel staffAssignmentViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == apiKey.CompanyId);

            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            var myLock =
                await _lockService.AcquireLockAsync(
                    $"public_api_conversation_{company.Id}_assign_conversation_collaborators_{conversationId}",
                    TimeSpan.FromSeconds(1));

            if (myLock == null)
            {
                _logger.LogWarning(
                    "[Rejected] [PublicAPI] GetConversations Company: {CompanyId}, queryStr: {RequestQueryString}",
                    company.Id,
                    Request.QueryString);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Server busy"
                    });
            }

            _logger.LogInformation(
                "[PublicAPI] GetConversations Company: {CompanyId}, queryStr: {RequestQueryString}",
                company.Id,
                Request.QueryString);

            var conversations = await _conversationMessageService.DefaultGetConversations(
                company.Id,
                new Staff
                {
                    RoleType = StaffUserRole.Admin
                });

            var conversationExists = await conversations.AnyAsync(x => x.Id == conversationId);

            if (!conversationExists)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Conversation not found, Id: {conversationId}"
                    });
            }

            if (staffAssignmentViewModel.AdditionalAssigneeIds.Count > 5)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Number of the conversation's collaborators cannot exceed 5"
                    });
            }

            foreach (var additionalAssigneeId in staffAssignmentViewModel.AdditionalAssigneeIds)
            {
                var companyStaffExists = await _companyService.IsCompanyStaffExist(
                    apiKey.CompanyId,
                    additionalAssigneeId);

                if (!companyStaffExists)
                {
                    return NotFound(
                        new ResponseViewModel
                        {
                            code = 404, message = $"Collaborator does not exist. Id: {additionalAssigneeId}"
                        });
                }
            }

            var conversation = await _appDbContext.Conversations
                .Where(x => x.Id == conversationId && x.CompanyId == apiKey.CompanyId)
                .Include(x => x.Assignee)
                .Include(x => x.UserProfile)
                .Include(x => x.AdditionalAssignees)
                .ThenInclude(x => x.Assignee.Identity)
                .FirstOrDefaultAsync();

            try
            {
                if (staffAssignmentViewModel.AdditionalAssigneeIds != null &&
                    (staffAssignmentViewModel.AdditionalAssigneeIds?.Count > 0 ||
                     conversation.AdditionalAssignees.Count > 0))
                {
                    var toRemove = conversation.AdditionalAssignees.Where(
                        x =>
                            !staffAssignmentViewModel.AdditionalAssigneeIds.Contains(x.Assignee.IdentityId)).ToList();

                    await _conversationAssigneeService.RemoveAdditionalAssignees(
                        conversation,
                        toRemove.Select(x => (long) x.AssigneeId).ToList(),
                        null,
                        true);

                    var toAdd = staffAssignmentViewModel.AdditionalAssigneeIds.Where(
                        y => !conversation.AdditionalAssignees.Select(x => x.Assignee.IdentityId).Contains(y)).ToList();

                    await _conversationAssigneeService.AddAdditionalAssignees(
                        conversation,
                        await _appDbContext.UserRoleStaffs
                            .Where(
                                x =>
                                    x.CompanyId == apiKey.CompanyId
                                    && toAdd.Contains(x.IdentityId))
                            .Select(x => x.Id)
                            .ToListAsync(),
                        null,
                        true,
                        userProfileSource: UserProfileSource.ManualEntry);
                }

                return Ok(
                    new ResponseViewModel
                    {
                        code = 200,
                        message =
                            $"Conversation {conversationId} collaborators updated. Collaborators Ids: [{string.Join(", ", staffAssignmentViewModel.AdditionalAssigneeIds!)}]"
                    });
            }
            catch (Exception e)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Error on assigning conversation collaborators."
                    });
            }
        }

        [HttpGet]
        [Route("api/conversation/all")]
        public async Task<ActionResult<List<ConversationNoCompanyResponseViewModel>>> GetConversations(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery(Name = "assignedTo")]
            string assignedTo = "all",
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "status")]
            string status = "open",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "phoneNumber")]
            string phoneNumber = null,
            [FromQuery(Name = "afterUpdatedAt")]
            DateTime? afterUpdatedAt = null,
            [FromQuery(Name = "afterModifiedAt")]
            DateTime? afterModifiedAt = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "tags")]
            string tags = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null,
            [FromQuery(Name = "isTeamUnassigned")]
            bool? isTeamUnassigned = null,
            [FromQuery(Name = "isUnread")]
            bool? isUnread = null,
            [FromQuery(Name = "orderBy")]
            string orderBy = null,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            if (limit > 1000)
            {
                limit = 1000;
            }

            var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == apiKey.CompanyId);

            var myLock = await _lockService.AcquireLockAsync(
                $"public_api_conversation_{company.Id}_all_conversations_{assignedTo}_{status}_{channels}_{phoneNumber}_{afterUpdatedAt?.ToString("o")}_{afterModifiedAt?.ToString("o")}_{channelIds}_{tags}_{teamId}_{isTeamUnassigned}_{isUnread}_{orderBy}",
                TimeSpan.FromSeconds(1));

            if (myLock == null)
            {
                _logger.LogWarning(
                    "[Rejected] [PublicAPI] GetConversations Company: {CompanyId}, queryStr: {RequestQueryString}",
                    company.Id,
                    Request.QueryString);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Server busy"
                    });
            }

            _logger.LogInformation(
                "[PublicAPI] GetConversations Company: {CompanyId}, queryStr: {RequestQueryString}",
                company.Id,
                Request.QueryString);

            var conversations = await _conversationMessageService.DefaultGetConversations(
                company.Id,
                new Staff
                {
                    CompanyId = company.Id,
                    RoleType = StaffUserRole.Admin
                },
                status,
                assignedTo,
                channels,
                afterUpdatedAt,
                afterModifiedAt,
                channelIds,
                tags,
                teamId,
                isTeamUnassigned,
                isUnread,
                orderBy);

            phoneNumber = PhoneNumberHelper.NormalizePhoneNumber(phoneNumber) == null
                ? string.Empty
                : PhoneNumberHelper.NormalizePhoneNumber(phoneNumber);

            var listResult = await conversations
                .Include(x => x.AdditionalAssignees)
                .ThenInclude(x => x.Assignee.Identity)
                .Include(x => x.UserProfile)
                .WhereIf(!string.IsNullOrEmpty(phoneNumber), x => x.UserProfile.PhoneNumber.Contains(phoneNumber))
                .Include(x => x.facebookUser)
                .Include(x => x.WhatsappUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .Include(x => x.NaiveUser.Identity)
                .Include(x => x.Assignee.Identity)
                .Include(x => x.EmailAddress)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(x => x.LineUser)
                .Include(x => x.SMSUser)
                .Include(x => x.AssignedTeam)
                .Include(x => x.TelegramUser)
                .Include(x => x.TikTokUser)
                .Include(x => x.ViberUser)
                .Skip(offset)
                .Take(limit)
                .ToListAsync(HttpContext.RequestAborted);

            var conversationIds = listResult.Select(x => x.Id).ToList();

            var conversationHashtags = await _appDbContext.ConversationHashtags
                .Where(y => conversationIds.Contains(y.ConversationId))
                .Include(y => y.Hashtag)
                .OrderByDescending(x => x.Id)
                .ToListAsync(HttpContext.RequestAborted);

            listResult.ForEach(
                x =>
                    x.conversationHashtags = conversationHashtags.Where(y => y.ConversationId == x.Id).ToList());

            var mappedResult = await _conversationNoCompanyResponseViewModelMapper.ToViewModelsAsync(listResult);

            foreach (var result in mappedResult)
            {
                await MaskConversationNoCompanyResponseViewModelAsync(
                    result,
                    company.Id,
                    StaffUserRole.Admin);
            }

            return Ok(mappedResult);
        }

        [HttpGet]
        [Route("api/conversation/message/{conversationId}")]
        public async Task<ActionResult<List<ConversationMessageResponseViewModel>>> GetConversationMessages(
            [FromRoute]
            string conversationId,
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "beforeMessageId")]
            long? beforeMessageId = null,
            [FromQuery(Name = "afterMessageId")]
            long? afterMessageId = null,
            [FromQuery(Name = "startTimestamp")] // startTimestamp = afterTimestamp, renamed for better readability
            long? startTimestamp = null,
            [FromQuery(Name = "endTimestamp")] // endTimestamp = beforeTimestamp, renamed for better readability
            long? endTimestamp = null,
            [FromQuery(Name = "channel")]
            string channel = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "isFromUser")]
            bool? isFromUser = null,
            [FromQuery(Name = "fileUrlExpirationTimestamp")]
            [ExpirationTimestamp(maxYears: 1, maxMonths: 6, allowExpired: false)]
            long? fileUrlExpirationTimestamp = null,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);

            if (limit > 1000)
            {
                return BadRequest(
                    new ResponseViewModel(400)
                    {
                        message = "Cannot fetch messages more then 1000 pre request"
                    });
            }

            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            fileUrlExpirationTimestamp ??= new DateTimeOffset(DateTime.UtcNow.AddYears(1).AddMonths(6)).ToUnixTimeSeconds();

            var errorResponse = GetValidationErrorResponse<List<ConversationMessageResponseViewModel>>(ModelState, nameof(fileUrlExpirationTimestamp));

            if (errorResponse != null)
            {
                return errorResponse;
            }

            var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == apiKey.CompanyId);

            var messages = _conversationMessageService.GetConversationMessage(
                conversationId,
                company.Id,
                beforeMessageId,
                afterMessageId,
                afterTimestamp: startTimestamp,
                beforeTimestamp: endTimestamp,
                channels: channel,
                channelIds: channelIds,
                IsFromUser: isFromUser);

            var response = await messages
                .Include(x => x.UploadedFiles)
                .Include(x => x.EmailFrom)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.SenderDevice)
                .Include(x => x.ReceiverDevice)
                .Include(x => x.facebookSender)
                .Include(x => x.facebookReceiver)
                .Include(x => x.whatsappSender)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.WebClientSender)
                .Include(x => x.WebClientReceiver)
                .Include(x => x.MessageAssignee.Identity)
                .Include(x => x.WeChatSender)
                .Include(x => x.WeChatReceiver)
                .Include(x => x.LineSender)
                .Include(x => x.LineReceiver)
                .Include(x => x.SMSSender)
                .Include(x => x.SMSReceiver)
                .Include(x => x.InstagramSender)
                .Include(x => x.InstagramReceiver)
                .Include(x => x.Whatsapp360DialogReceiver)
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .Include(x => x.ExtendedMessagePayload)
                .OrderByDescending(x => x.Timestamp)
                .Skip(offset)
                .Take(limit)
                .ToListAsync(HttpContext.RequestAborted);

            var responseVM = _mapper.Map<List<ConversationMessageWebhookResponse>>(response);

            responseVM.ForEach(
                message =>
                {
                    if (string.IsNullOrEmpty(message.ContactEmail))
                    {
                        message.ContactEmail = null;
                    }

                    message.UploadedFiles.ForEach(
                        x => x.Url = _azureBlobStorageService.GetAzureBlobSasUri(
                            x.Filename,
                            x.BlobContainer,
                            (long) fileUrlExpirationTimestamp));
                });

            await MaskConversationMessageWebhookResponsesAsync(
                responseVM,
                company.Id,
                StaffUserRole.Admin);

            return Ok(responseVM);
        }

        [HttpGet]
        [Route("api/conversation/message/search")]
        public async Task<ActionResult<List<ConversationNoCompanyResponseViewModel>>> SearchConversationMessage(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery(Name = "keywords")]
            string keywords,
            [FromQuery(Name = "contactOwner")]
            string assignedTo,
            [FromQuery(Name = "teamId")]
            long? teamId = null,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 5,
            [FromQuery(Name = "status")]
            string status = "all",
            [FromQuery(Name = "channel")]
            string channel = null,
            [FromQuery(Name = "channelNumber")]
            string channelNumber = null,
            [FromQuery(Name = "isGetFileOnly")]
            bool isGetFileOnly = false,
            [FromQuery(Name = "tags")]
            string tags = null,
            [FromQuery(Name = "fileUrlExpirationTimestamp")]
            [ExpirationTimestamp(maxYears: 1, maxMonths: 6, allowExpired: false)]
            long? fileUrlExpirationTimestamp = null,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);

            if (limit > 20)
            {
                limit = 20;
            }

            var publicApiSearchMessageCacheKeyPattern = new PublicApiSearchMessageCacheKeyPattern(
                apiKey.CompanyId,
                assignedTo,
                keywords,
                teamId,
                offset,
                limit,
                status,
                channel,
                channelNumber,
                isGetFileOnly,
                tags,
                fileUrlExpirationTimestamp);

            var data = await _cacheManagerService.GetCacheAsync(publicApiSearchMessageCacheKeyPattern);

            if (!string.IsNullOrEmpty(data))
            {
                return Ok(JsonConvert.DeserializeObject<List<ConversationNoCompanyResponseViewModel>>(data));
            }

            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            _logger.LogInformation(
                "[PublicAPI] SearchConversationMessage: {Key}",
                $"public_api_search_message{publicApiSearchMessageCacheKeyPattern.GenerateKeyPattern()}");

            fileUrlExpirationTimestamp ??= new DateTimeOffset(DateTime.UtcNow.AddYears(1).AddMonths(6)).ToUnixTimeSeconds();

            var errorResponse = GetValidationErrorResponse<List<ConversationNoCompanyResponseViewModel>>(ModelState, nameof(fileUrlExpirationTimestamp));

            if (errorResponse != null)
            {
                return errorResponse;
            }

            var channelNumberList = new List<string>();

            if (!string.IsNullOrEmpty(channelNumber))
            {
                channelNumberList = channelNumber.Split(",").ToList();
            }

            var tagsList = new List<string>();

            if (!string.IsNullOrEmpty(tags))
            {
                tagsList = tags.Split(",").ToList();
            }

            var hashtagsIdList = await _appDbContext.CompanyDefinedHashtags
                .Where(
                    x =>
                        x.CompanyId == apiKey.CompanyId
                        && (tagsList.Contains(x.Hashtag) || tagsList.Contains(x.Id)))
                .Select(x => x.Id)
                .ToListAsync(HttpContext.RequestAborted);

            var conversationMessageResult = _appDbContext.ConversationMessages
                .Where(
                    conversationMessages =>
                        conversationMessages.CompanyId == apiKey.CompanyId
                        && conversationMessages.Conversation.ActiveStatus == ActiveStatus.Active)
                .WhereIf(
                    status != "all",
                    message => message.Conversation.Status == status)
                .WhereIf(
                    !string.IsNullOrEmpty(assignedTo) && assignedTo != "all",
                    message => message.Conversation.Assignee.IdentityId == assignedTo)
                .WhereIf(
                    teamId.HasValue,
                    message => message.Conversation.AssignedTeamId == teamId)
                .WhereIf(
                    !string.IsNullOrEmpty(keywords),
                    message => message.MessageContent.ToLower().Contains(keywords.ToLower()))
                .WhereIf(
                    isGetFileOnly,
                    message => message.MessageType == "file")
                .WhereIf(
                    tagsList.Any() && hashtagsIdList.Any(),
                    message => message.Conversation.conversationHashtags
                        .Any(x => hashtagsIdList.Contains(x.HashtagId)))

                // filter by channel
                .WhereIf(
                    channel != null,
                    message => message.Channel == channel)

                // filter by 360dialog number
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogReceiver)
                .WhereIf(
                    channel == ChannelTypes.Whatsapp360Dialog && channelNumberList.Any(),
                    x =>
                        channelNumberList.Contains(x.Whatsapp360DialogSender.ChannelWhatsAppPhoneNumber) ||
                        channelNumberList.Contains(x.Whatsapp360DialogReceiver.ChannelWhatsAppPhoneNumber))

                // filter by cloudapi number
                .WhereIf(
                    channel == ChannelTypes.WhatsappCloudApi && channelNumberList.Any(),
                    x => channelNumberList.Contains(x.ChannelIdentityId))
                .OrderByDescending(conversationMessages => conversationMessages.CreatedAt)
                .Skip(offset)
                .Take(limit);

            if (channelNumberList.Count > 0)
            {
                try
                {
                    // filter for 'whatsapp'
                    if (channel != null && channel.ToLower() == ChannelTypes.WhatsappTwilio)
                    {
                        var newChannelIds = new List<string>();
                        var newInstanceSender = new List<string>();

                        foreach (var myChannelId in channelNumberList)
                        {
                            var config = await _appDbContext.ConfigWhatsAppConfigs
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == apiKey.CompanyId
                                        && x.WhatsAppSender.Contains(myChannelId));

                            if (config == null)
                            {
                                continue;
                            }

                            newChannelIds.Add(config.TwilioAccountId);
                            newInstanceSender.Add(config.WhatsAppSender);
                        }

                        if (newChannelIds.Any())
                        {
                            conversationMessageResult = (from conversationMessage in conversationMessageResult
                                where newChannelIds.Contains(conversationMessage.whatsappReceiver.InstanceId) ||
                                      newChannelIds.Contains(conversationMessage.whatsappSender.InstanceId)
                                orderby conversationMessage.CreatedAt descending
                                select conversationMessage);
                        }

                        if (newInstanceSender.Count > 0)
                        {
                            conversationMessageResult = (from conversationMessage in conversationMessageResult
                                where newInstanceSender.Contains(conversationMessage.whatsappReceiver.InstaneSender) ||
                                      newInstanceSender.Contains(conversationMessage.whatsappSender.InstaneSender)
                                orderby conversationMessage.CreatedAt descending
                                select conversationMessage);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Unable to parse TargetedChannelModel {ExceptionMessage}",
                        ex.Message);
                }
            }

            var response = new List<ConversationNoCompanyResponseViewModel>();

            var conversationsMessages = await conversationMessageResult
                .Include(x => x.UploadedFiles)
                .Include(x => x.EmailFrom)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.SenderDevice)
                .Include(x => x.ReceiverDevice)
                .Include(x => x.facebookSender)
                .Include(x => x.facebookReceiver)
                .Include(x => x.whatsappSender)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.WebClientSender)
                .Include(x => x.WebClientReceiver)
                .Include(x => x.MessageAssignee.Identity)
                .Include(x => x.WeChatSender)
                .Include(x => x.WeChatReceiver)
                .Include(x => x.LineSender)
                .Include(x => x.LineReceiver)
                .Include(x => x.SMSSender)
                .Include(x => x.SMSReceiver)
                .Include(x => x.InstagramSender)
                .Include(x => x.InstagramReceiver)
                .Include(x => x.Whatsapp360DialogReceiver)
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .Include(x => x.ExtendedMessagePayload)
                .ToListAsync(HttpContext.RequestAborted);

            var conversationIds = conversationsMessages
                .GroupBy(x => x.ConversationId)
                .ToList();

            // Get All conversations
            var conversationList = await _appDbContext.Conversations
                .Where(
                    x =>
                        x.CompanyId == apiKey.CompanyId
                        && conversationsMessages
                            .Select(y => y.ConversationId)
                            .Distinct()
                            .Contains(x.Id))
                .Include(x => x.UserProfile)
                .Include(x => x.WhatsappUser)
                .Include(x => x.facebookUser)
                .Include(x => x.WhatsappUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .Include(x => x.Assignee.Identity)
                .Include(x => x.EmailAddress)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(x => x.LineUser)
                .Include(x => x.SMSUser)
                .ToListAsync(HttpContext.RequestAborted);

            foreach (var conversationId in conversationIds)
            {
                var conversation = conversationList.FirstOrDefault(x => x.Id == conversationId.Key);

                // split one-to-many query
                conversation.conversationHashtags = await _appDbContext.ConversationHashtags.Include(x => x.Hashtag)
                    .Where(x => x.ConversationId == conversation.Id).ToListAsync(HttpContext.RequestAborted);

                conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                    .Include(x => x.Assignee.Identity).Where(x => x.ConversationId == conversation.Id).ToListAsync(HttpContext.RequestAborted);

                if (conversation.UserProfile != null)
                {
                    conversation.UserProfile.CustomFields = await _appDbContext.UserProfileCustomFields
                        .Where(x => x.UserProfileId == conversation.UserProfileId).ToListAsync(HttpContext.RequestAborted);
                }

                var conversationResponse = await _conversationNoCompanyResponseViewModelMapper.ToViewModelAsync(conversation);
                var messages = _mapper.Map<List<ConversationMessageResponseViewModel>>(conversationId.ToList());

                conversationResponse.Messages = messages;

                conversationResponse.Messages?.ForEach(
                    message =>
                    {
                        message.UploadedFiles?.ForEach(
                            x => x.Url = _azureBlobStorageService.GetAzureBlobSasUri(
                                x.Filename,
                                x.BlobContainer,
                                (long) fileUrlExpirationTimestamp));
                    });

                await MaskConversationNoCompanyResponseViewModelAsync(
                    conversationResponse,
                    apiKey.CompanyId,
                    StaffUserRole.Admin);

                response.Add(conversationResponse);
            }

            await _cacheManagerService.SaveCacheAsync(publicApiSearchMessageCacheKeyPattern, response);

            return Ok(response);
        }

        [HttpDelete]
        [Route("api/conversation/message/{conversationMessageId}")]
        public async Task<IActionResult> DeleteConversationMessage(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader,
            [FromRoute]
            long conversationMessageId)
        {
            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                _logger.LogError(
                    null,
                    "[{MethodName} endpoint] apiKey {apiKey}, apiKeyHeader {apikeyHeader} error: {errorMessage}",
                    nameof(DeleteConversationMessage),
                    apikey,
                    apikeyHeader,
                    "Apikey from query param is different from header.");

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            CompanyAPIKey companyApiKey = null;

            try
            {
                companyApiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
                await _publicApiKeyResolver.ValidateUsageAsync(companyApiKey);

                var input = new DeleteMessageInput(companyApiKey.CompanyId, conversationMessageId);
                await _conversationMessageService.DeleteMessage(input);
            }
            catch (ApiKeyAuthenticateFailedException ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] ApiKey {apikey} error: {ExceptionString}",
                    nameof(DeleteConversationMessage),
                    apikey,
                    ex.ToString());

                return StatusCode(
                    401,
                    new ResponseViewModel
                    {
                        code = 401, message = "Apikey authentication has failed."
                    });
            }
            catch (ApiKeyCallLimitExceededException ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error: {ExceptionString}",
                    nameof(DeleteConversationMessage),
                    companyApiKey.CompanyId,
                    ex.ToString());

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey call limitation has exceeded."
                    });
            }
            catch (MessageNotFoundException ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId}, Message Id {messageId} error: {ExceptionString}",
                    nameof(DeleteConversationMessage),
                    companyApiKey.CompanyId,
                    conversationMessageId,
                    ex.ToString());

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Message Id was not found."
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error: {ExceptionString}",
                    nameof(DeleteConversationMessage),
                    companyApiKey.CompanyId,
                    ex.ToString());

                return StatusCode(
                    500,
                    new ResponseViewModel
                    {
                        code = 500, message = "Internal server error"
                    });
            }

            return Ok(
                new ResponseViewModel
                {
                    code = 200, message = "Messages have been deleted successfully.",
                });
        }

        [HttpGet]
        [Route("api/contact")]
        public async Task<IActionResult> GetUpdatedContact(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 50,
            [FromQuery(Name = "lastUpdatedAt")]
            DateTime? lastUpdatedAt = null,
            [FromQuery(Name = "afterCreatedAt")]
            DateTime? createdAt = null,
            [FromQuery(Name = "phoneNumber")]
            string phoneNumber = null,
            [FromQuery(Name = "email")]
            string email = null,
            [FromQuery(Name = "firstName")]
            string firstName = null,
            [FromQuery(Name = "lastName")]
            string lastName = null,
            [FromQuery(Name = "isExactMatch")]
            bool isExactMatch = false,
            [FromQuery(Name = "listId")]
            string listId = null,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Incorrect format"
                    });
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            if (limit > 1000)
            {
                limit = 1000;
            }

            var page = offset / limit + 1;

            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == apiKey.CompanyId)
                .AsNoTracking()
                .Include(x => x.CustomUserProfileFields)
                .FirstOrDefaultAsync();

            if (company == null)
            {
                return Unauthorized();
            }

            try
            {
                var searchResult = await SearchContactLogicAsync(
                    new List<Condition>(),
                    lastUpdatedAt,
                    createdAt,
                    phoneNumber,
                    email,
                    firstName,
                    lastName,
                    isExactMatch,
                    listId,
                    page,
                    limit,
                    "createdat",
                    "desc",
                    apiKey);
                var response = await ToZapierResponse(company, searchResult.UserProfiles);

                return Ok(response);
            }
            catch (PublicApiException publicApiException)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        ErrorCode = publicApiException.ErrorCode, ErrorMessage = publicApiException.Message
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        code = 500, ErrorMessage = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("api/contact/search")]
        public async Task<IActionResult> SearchUserProfile(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            List<Condition> conditions = null,
            [FromQuery(Name = "lastUpdatedAt")]
            DateTime? lastUpdatedAt = null,
            [FromQuery(Name = "afterCreatedAt")]
            DateTime? createdAt = null,
            [FromQuery(Name = "phoneNumber")]
            string phoneNumber = null,
            [FromQuery(Name = "email")]
            string email = null,
            [FromQuery(Name = "firstName")]
            string firstName = null,
            [FromQuery(Name = "lastName")]
            string lastName = null,
            [FromQuery(Name = "isExactMatch")]
            bool isExactMatch = false,
            [FromQuery(Name = "listId")]
            string listId = null,
            [FromQuery(Name = "page")]
            int page = 1,
            [FromQuery(Name = "pageSize")]
            int pageSize = 50,
            [FromQuery(Name = "sortBy")]
            string sortBy = "createdat",
            [FromQuery(Name = "sortOrder")]
            string sortOrder = "desc",
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = ""
        )
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Incorrect format"
                    });
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            conditions ??= new List<Condition>();

            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == apiKey.CompanyId)
                .AsNoTracking()
                .Include(x => x.CustomUserProfileFields)
                .FirstOrDefaultAsync();

            if (company == null)
            {
                return Unauthorized();
            }

            try
            {
                var searchResult = await SearchContactLogicAsync(
                    conditions,
                    lastUpdatedAt,
                    createdAt,
                    phoneNumber,
                    email,
                    firstName,
                    lastName,
                    isExactMatch,
                    listId,
                    page,
                    pageSize,
                    sortBy,
                    sortOrder,
                    apiKey);

                var response = await ToZapierResponse(company, searchResult.UserProfiles);

                return Ok(
                    new
                    {
                        results = response, totalContact = searchResult.TotalResult
                    });
            }
            catch (PublicApiException publicApiException)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        ErrorCode = publicApiException.ErrorCode, ErrorMessage = publicApiException.Message
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        code = 500, ErrorMessage = ex.Message
                    });
            }
        }

        private async Task<SearchUserProfileResult> SearchContactLogicAsync(
            List<Condition> conditions,
            DateTime? lastUpdatedAt,
            DateTime? createdAt,
            string phoneNumber,
            string email,
            string firstName,
            string lastName,
            bool isExactMatch,
            string listId,
            int page,
            int pageSize,
            string sortBy,
            string sortOrder,
            CompanyAPIKey apiKey)
        {
            if (pageSize > 1000)
            {
                pageSize = 1000;
            }

            // Convert to offset and limit
            var offset = (page - 1) * pageSize;
            var limit = pageSize;

            // Check if the custom fields exist
            if (conditions.Any())
            {
                var fieldNames = conditions.Select(x => x.FieldName.ToLower()).ToList();

                var companyFields =
                    await _contactCacheService.GetCompanyCustomUserProfileFieldsCachedAsync(apiKey.CompanyId);

                var customFields = companyFields
                    .Select(x => x.FieldName.ToLower())
                    .ToList();

                // check if the custom fields are valid
                if (!customFields.Any(field => fieldNames.Contains(field)))
                {
                    var missingFields = fieldNames.Except(customFields);

                    throw new PublicApiException(
                        $"{string.Join(", ", missingFields)} is not valid for the contact resource.",
                        "InvalidCustomField");
                }
            }

            if (lastUpdatedAt.HasValue)
            {
                conditions.Add(
                    new Condition
                    {
                        FieldName = "updatedat",
                        ConditionOperator = SupportedOperator.HigherThan,
                        Values = new List<string>
                        {
                            lastUpdatedAt.Value.ToString("o")
                        }
                    });
            }

            if (createdAt.HasValue)
            {
                conditions.Add(
                    new Condition
                    {
                        FieldName = "createdat",
                        ConditionOperator = SupportedOperator.HigherThan,
                        Values = new List<string>
                        {
                            createdAt.Value.ToString("o")
                        }
                    });
            }

            if (!string.IsNullOrEmpty(phoneNumber))
            {
                conditions.Add(
                    new Condition
                    {
                        FieldName = "phonenumber",
                        ConditionOperator = isExactMatch ? SupportedOperator.Equals : SupportedOperator.Contains,
                        Values = new List<string>
                        {
                            phoneNumber
                        }
                    });
            }

            if (!string.IsNullOrEmpty(email))
            {
                conditions.Add(
                    new Condition
                    {
                        FieldName = "email",
                        ConditionOperator = isExactMatch ? SupportedOperator.Equals : SupportedOperator.Contains,
                        Values = new List<string>
                        {
                            email
                        }
                    });
            }

            if (!string.IsNullOrEmpty(firstName))
            {
                conditions.Add(
                    new Condition
                    {
                        FieldName = "firstname",
                        ConditionOperator = isExactMatch ? SupportedOperator.Equals : SupportedOperator.Contains,
                        Values = new List<string>
                        {
                            firstName
                        }
                    });
            }

            if (!string.IsNullOrEmpty(lastName))
            {
                conditions.Add(
                    new Condition
                    {
                        FieldName = "lastname",
                        ConditionOperator = isExactMatch ? SupportedOperator.Equals : SupportedOperator.Contains,
                        Values = new List<string>
                        {
                            lastName
                        }
                    });
            }

            // importfrom Platform API - Fetch Contacts from Contact List
            if (!string.IsNullOrEmpty(listId))
            {
                long? listIdLong = null;
                // Check if multiple lists are provided

                var listIdArray = listId.Split(',');

                if (listIdArray.Length > 1)
                {
                    throw new PublicApiException(
                        "The API only supports retrieving contacts from a single contact list at a time. Please make separate requests for each list",
                        "MultipleListsNotSupported");
                }

                // Check if the list ID is a number
                if (long.TryParse(listIdArray[0], out var parsedListId))
                {
                    listIdLong = parsedListId;
                }
                else
                {
                    throw new PublicApiException(
                        $"The contact list with ID '{listId}' does not match the format",
                        "InvalidContactListFormat");
                }

                // Check if the list exists
                if (!await _appDbContext.CompanyImportContactHistories.AnyAsync(x => x.Id == listIdLong.Value))
                {
                    throw new PublicApiException(
                        $"The contact list with ID '{listId}' does not exist",
                        "InvalidContactList");
                }

                conditions.Add(
                    new Condition
                    {
                        FieldName = "importfrom",
                        ConditionOperator = isExactMatch ? SupportedOperator.Equals : SupportedOperator.Contains,
                        Values = new List<string>
                        {
                            listIdLong.ToString()
                        }
                    });
            }

            var searchResult = await _userProfileService.GetUserProfilesByFields(
                apiKey.CompanyId,
                conditions,
                offset,
                limit,
                sortBy,
                sortOrder);

            return searchResult;
        }

        [HttpPost]
        [Route("api/contact/dynamicSearch")]
        public async Task<ActionResult<PublicApiContactDynamicSearchResponse>> DynamicSearchUserProfile(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader,
            [FromBody]
            PublicApiContactDynamicSearchRequest requestBody,
            CancellationToken cancellationToken = default)
        {
            // validations
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Incorrect format"
                    });
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            // authentications
            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            try
            {
                // search UserProfile
                var searchResult = await _userProfileService.GetUserProfilesByFields(
                    apiKey.CompanyId,
                    requestBody.Conditions,
                    requestBody.Pagination.Offset,
                    requestBody.Pagination.Limit,
                    requestBody.Sort.Field,
                    requestBody.Sort.Order);

                // process dynamic includes
                var (companyCustomUserProfileFields, includeFieldMap) = await ExecuteContactDynamicSearchParallelTasksAsync(
                    apiKey.CompanyId,
                    searchResult.UserProfiles.Select(x => x.Id).ToHashSet(),
                    requestBody.Include,
                    cancellationToken);

                // prepare response
                var response = PublicApiContactDynamicSearchResponseGenerator.Generate(
                    searchResult.UserProfiles,
                    requestBody.Include,
                    includeFieldMap,
                    companyCustomUserProfileFields,
                    searchResult.TotalResult);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Public API Dynamic Search Contact. {CompanyId} {RequestBody}",
                    apiKey.CompanyId,
                    requestBody);

                return BadRequest(
                    new ResponseViewModel()
                    {
                        code = 500, ErrorMessage = ex.Message
                    });
            }
        }

        /// <returns>[UserProfileId]: [ContactDynamicIncludeFieldsDto].</returns>
        private async Task<(
            List<CompanyCustomUserProfileField> CompanyCustomUserProfileFields,
            Dictionary<string, ContactDynamicIncludeFieldsDto> IncludeFieldMap)>
            ExecuteContactDynamicSearchParallelTasksAsync(
                string companyId,
                HashSet<string> userProfileIds,
                IncludeFields include,
                CancellationToken cancellationToken)
        {
            var userProfileIdMap = await GetUserProfileConversationIdMapAsync(
                companyId,
                userProfileIds,
                cancellationToken);

            // prepare async tasks
            Task<List<CompanyCustomUserProfileField>> customFieldTask;
            Task<Dictionary<string, List<ConversationHashtag>>>? labelTask = null;
            Task<Dictionary<string, ConversationMessage>>? latestMessageTask = null;

            customFieldTask = Task.Run(
                async () =>
                {
                    using var scope = _serviceProvider.CreateScope();
                    var contactCacheService = scope.ServiceProvider.GetRequiredService<IContactCacheService>();

                    return await contactCacheService.GetCompanyCustomUserProfileFieldsCachedAsync(companyId);
                });

            if (include.Labels)
            {
                labelTask = Task.Run(
                    async () =>
                    {
                        using var scope = _serviceProvider.CreateScope();
                        var conversationHashtagService =
                            scope.ServiceProvider.GetRequiredService<IConversationHashtagService>();

                        return await conversationHashtagService.BulkGetConversationHashTagsByConversationIdsAsync(
                            companyId,
                            userProfileIdMap.Values.ToList(),
                            cancellationToken);
                    });
            }

            if (include.LatestMessage)
            {
                latestMessageTask = Task.Run(
                    async () =>
                    {
                        using var scope = _serviceProvider.CreateScope();
                        var conversationMessageService =
                            scope.ServiceProvider.GetRequiredService<IConversationMessageService>();

                        return await conversationMessageService.BulkGetLatestConversationMessageByConversationIdAsync(
                            companyId,
                            userProfileIdMap.Values.ToList(),
                            cancellationToken);
                    });
            }

            await Task.WhenAll(
                customFieldTask,
                labelTask ?? Task.CompletedTask,
                latestMessageTask ?? Task.CompletedTask);

            // extract result
            var labelsResult = labelTask?.Result ?? new Dictionary<string, List<ConversationHashtag>>();
            var latestMessagesResult = latestMessageTask?.Result ?? new Dictionary<string, ConversationMessage>();

            // assemble output
            var includeFieldMap = new Dictionary<string, ContactDynamicIncludeFieldsDto>();

            foreach (var userProfileId in userProfileIds)
            {
                if (!userProfileIdMap.TryGetValue(userProfileId, out var conversationId))
                {
                    includeFieldMap[userProfileId] = ContactDynamicIncludeFieldsDto.Default;
                    continue;
                }

                var labels = labelsResult.TryGetValue(conversationId, out var hashtags) ? hashtags : [];
                var latestMessage = latestMessagesResult.TryGetValue(conversationId, out var message) ? message : null;

                includeFieldMap[userProfileId] = new ContactDynamicIncludeFieldsDto(
                    labels,
                    latestMessage);
            }

            return (customFieldTask.Result, includeFieldMap);
        }

        /// <returns>[UserProfileId]: [ConversationId].</returns>
        private async Task<Dictionary<string, string>> GetUserProfileConversationIdMapAsync(
            string companyId,
            ICollection<string> userProfileIds,
            CancellationToken cancellationToken)
        {
            if (userProfileIds.Count == 0)
            {
                return new Dictionary<string, string>();
            }

            return await _appDbContext.Conversations
                .AsNoTracking()
                .Where(
                    conversation => conversation.CompanyId == companyId &&
                                    userProfileIds.Contains(conversation.UserProfileId))
                .ToDictionaryAsync(
                    conversation => conversation.UserProfileId,
                    conversation => conversation.Id,
                    cancellationToken);
        }

        #endregion

        #region Channel Setting

        [HttpGet]
        [Route("api/twilio/template")]
        public async Task<ActionResult<TwilioTemplate>> GetTwilioTemplate(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery(Name = "twilioAccountSid"), Required]
            string twilioAccountSid,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var twilioConfig = await _appDbContext.ConfigWhatsAppConfigs
                .Where(x => x.TwilioAccountId == twilioAccountSid && x.CompanyId == apiKey.CompanyId)
                .FirstOrDefaultAsync();

            if (twilioConfig == null)
            {
                return Unauthorized();
            }

            var client = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue(
                "Basic",
                Convert.ToBase64String(
                    System.Text.ASCIIEncoding.ASCII.GetBytes(
                        $"{twilioConfig.TwilioAccountId}:{twilioConfig.TwilioSecret}")));

            // Get page size 250 and page 0
            var templates = await client.GetAsync(
                $"https://messaging.twilio.com/v1/Channels/WhatsApp/Templates?PageSize=250&Page=0");

            if (templates.IsSuccessStatusCode)
            {
                var templatesResponse =
                    JsonConvert.DeserializeObject<TwilioTemplate>(await templates.Content.ReadAsStringAsync());

                return Ok(templatesResponse);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    code = 400, message = "Cannot fetch Template"
                });
        }

        [HttpGet]
        [Route("api/360dialog/template")]
        public async Task<ActionResult<GetWhatsApp360DialogTemplateResponse>> Get360dialogTemplates(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery, Required]
            string channelNumber,
            [FromQuery]
            int limit = 1000,
            [FromQuery]
            int offset = 0,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var channel = await _appDbContext.ConfigWhatsApp360DialogConfigs.FirstOrDefaultAsync(
                x => x.CompanyId == apiKey.CompanyId && x.WhatsAppPhoneNumber == channelNumber);

            if (channel == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "360Dialog Config not found"
                    });
            }

            // Cache

            try
            {
                var whatsApp360DialogGetTemplateCacheKeyPattern = new WhatsApp360DialogGetTemplateCacheKeyPattern(
                    apiKey.CompanyId,
                    channel.Id);
                var cache = await _cacheManagerService.GetCacheAsync(whatsApp360DialogGetTemplateCacheKeyPattern);
                GetWhatsApp360DialogTemplateResponse result;

                if (!string.IsNullOrEmpty(cache))
                {
                    // return Content(cache, MediaTypeNames.Application.Json, Encoding.UTF8);
                    result = JsonConvert.DeserializeObject<GetWhatsApp360DialogTemplateResponse>(cache);
                }
                else
                {
                    result = await _whatsApp360DialogService.GetWhatsApp360DialogTemplate(
                        apiKey.CompanyId,
                        channel.Id,
                        limit,
                        offset);

                    await _cacheManagerService.SaveCacheAsync(
                        whatsApp360DialogGetTemplateCacheKeyPattern,
                        result,
                        null,
                        new JsonSerializerSettings
                        {
                            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                            NullValueHandling = NullValueHandling.Ignore,
                            ContractResolver = new CamelCasePropertyNamesContractResolver()
                        });
                }

                if (apiKey.CompanyId == "52c0d4ca-40ed-4740-a61b-0672fb79409a") // Demo only
                {
                    var allowedNamespace = new List<string>()
                    {
                        "optin_1",
                        "sample_flight_confirmation",
                        "sample_purchase_feedback",
                        "sample_issue_resolution",
                        "sample_shipping_confirmation",
                        "bmf_survey",
                        "shc_booking",
                        "shc_reminder1",
                        "shc_reminder2"
                    };

                    var allowedLanguage = new List<WhatsAppLanguage>()
                    {
                        WhatsAppLanguage.English, WhatsAppLanguage.English_US, WhatsAppLanguage.Chinese_HK,
                    };

                    result.WhatsAppTemplates = result.WhatsAppTemplates
                        .Where(x => allowedNamespace.Contains(x.Name) && allowedLanguage.Contains(x.Language))
                        .ToList();

                    result.Count = result.WhatsAppTemplates.Count;
                    result.Offset = 0;
                    result.Total = result.Count;
                }

                return Ok(result);
            }
            catch (ApiClientException ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error: {ExceptionString}",
                    nameof(Get360dialogTemplates),
                    apiKey.CompanyId,
                    ex.ToString());

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Cannot fetch Template"
                    });
            }
        }

        [HttpGet]
        [Route("api/cloudapi/template")]
        public async Task<ActionResult<GetTemplatesResponse>> GetCloudApiTemplates(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery, Required]
            string channelNumber,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var channel = await _appDbContext.ConfigWhatsappCloudApiConfigs.FirstOrDefaultAsync(
                x => x.CompanyId == apiKey.CompanyId && x.WhatsappPhoneNumber == channelNumber);

            if (channel == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Whatsapp Cloud Api Config not found"
                    });
            }

            // Cache
            try
            {
                var cacheKey = WhatsappCloudApiCacheKeyHelper.GetTemplateResponseCacheKey(
                    apiKey.CompanyId,
                    channel.MessagingHubWabaId);

                var cache = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);

                GetTemplatesResponse result;

                if (!string.IsNullOrEmpty(cache))
                {
                    var templates = JsonConvert.DeserializeObject<List<WhatsappCloudApiTemplateResponse>>(cache);

                    result = new GetTemplatesResponse(templates);
                }
                else
                {
                    var templates = await _whatsappCloudApiService.GetTemplates(
                        apiKey.CompanyId,
                        channel.MessagingHubWabaId);
                    result = new GetTemplatesResponse(templates);
                }

                return Ok(result);
            }
            catch (SleekflowErrorCodeException ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Get company id {CompanyId} whatsapp channel number {ChannelWhatsappPhoneNumber} with error {ExceptionMessage}",
                    nameof(GetCloudApiTemplates),
                    apiKey.CompanyId,
                    channel.WhatsappPhoneNumber,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Cannot fetch Template"
                    });
            }
        }

        [HttpGet]
        [Route("api/conversation/channel")]
        public async Task<ActionResult<CompanyChannelResponse>> GetConversationChannel(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            try
            {
                if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Apikey from query param is different from header."
                        });
                }

                apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
                var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
                await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

                var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == apiKey.CompanyId)
                    .Include(x => x.FacebookConfigs)
                    .Include(x => x.WhatsAppConfigs)
                    .Include(x => x.EmailConfig)
                    .Include(x => x.WeChatConfig)
                    .Include(x => x.LineConfigs)
                    .Include(x => x.CompanyHashtags)
                    .Include(x => x.SMSConfigs)
                    .Include(x => x.ShopifyConfigs)
                    .Include(x => x.ShoplineConfigs).FirstOrDefaultAsync();

                if (company == null)
                {
                    return Unauthorized();
                }

                var channelResponse = _mapper.Map<CompanyChannelResponse>(company);

                return Ok(channelResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(GetConversationChannel),
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = ex.Message
                    });
            }
        }

        #endregion

        #region Contact List

        // ref: UserProfileController.GetGroupList()
        /// <summary>
        ///  Get Contact Lists with paging.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/contact/list")]
        public async Task<ActionResult<UserGroupResult>> GetContactLists(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var histories = _appDbContext.CompanyImportContactHistories
                .Include(x => x.ImportedFrom.Identity)
                .Where(x => x.CompanyId == apiKey.CompanyId)
                .OrderByDescending(x => x.IsBookmarked)
                .ThenBy(x => x.Order)
                .ThenByDescending(x => x.CreatedAt);

            var response = new UserGroupResult
            {
                userGroups = _mapper.Map<List<ImportContactHistoryResponse>>(
                    await histories
                        .Skip(offset)
                        .Take(limit)
                        .ToListAsync(HttpContext.RequestAborted)),
                TotalGroups = histories.Count()
            };

            var listGroupContactCountDict = await _appDbContext.CompanyImportedUserProfiles
                .Where(
                    x =>
                        response.userGroups
                            .Select(y => y.Id)
                            .Contains(x.ImportContactHistoryId))
                .GroupBy(x => x.ImportContactHistoryId)
                .ToDictionaryAsync(
                    g => g.Key,
                    g => g.Count());

            response.userGroups
                .ForEach(
                    x =>
                    {
                        x.TotalContactCount = listGroupContactCountDict.TryGetValue(x.Id, out var count) ? count : 0;
                    });

            return Ok(response);
        }

        /// <summary>
        ///  Get Contact List Detail.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        // ref: UserProfileController.AddUserProfileToList() ???
        [HttpGet]
        [Route("api/contact/list/{contactListId}")]
        public async Task<ActionResult<ImportContactHistoryResponse>> GetContactListDetail(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute, Required]
            long contactListId,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var group = await _appDbContext.CompanyImportContactHistories
                .Where(x => x.Id == contactListId && x.CompanyId == apiKey.CompanyId)
                .Include(x => x.ImportedFrom.Identity).FirstOrDefaultAsync();

            if (group == null)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Contact List with ID {contactListId} not found."
                    });
            }

            var response = _mapper.Map<ImportContactHistoryResponse>(group);

            response.TotalContactCount = await _appDbContext.CompanyImportedUserProfiles
                .CountAsync(y => y.ImportContactHistoryId == response.Id);

            return Ok(response);
        }

        /// <summary>
        ///  Create Contact List.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        // ref: UserProfileController.CreateUserProfileList()
        [HttpPost]
        [Route("api/contact/list")]
        public async Task<ActionResult<ImportContactHistoryResponse>> CreateContactList(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            UserProfileIdsViewModel userprofileViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            try
            {
                if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Apikey from query param is different from header."
                        });
                }

                apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
                var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
                await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

                List<UserProfile> userProfileList = new List<UserProfile>();

                if (userprofileViewModel.UserProfileIds?.Count > 0)
                {
                    userProfileList = await _appDbContext.UserProfiles.Where(
                            x => userprofileViewModel.UserProfileIds.Contains(x.Id) && x.CompanyId == apiKey.CompanyId)
                        .ToListAsync();
                }

                var newImport = new ImportContactHistory
                {
                    CompanyId = apiKey.CompanyId,
                    ImportName = userprofileViewModel.GroupListName,
                    ImportedUserProfiles = new List<ImportedUserProfile>(),
                    IsImported = false,
                    Status = ImportStatus.Imported,
                };

                foreach (var userprofile in userProfileList)
                {
                    newImport.ImportedUserProfiles.Add(
                        new ImportedUserProfile
                        {
                            UserProfileId = userprofile.Id
                        });
                }

                var olderList = await _appDbContext.CompanyImportContactHistories
                    .Where(x => x.CompanyId == apiKey.CompanyId).ToListAsync();
                olderList.ForEach(x => x.Order += 1);

                _appDbContext.CompanyImportContactHistories.Add(newImport);
                await _appDbContext.SaveChangesAsync();

                var response = _mapper.Map<ImportContactHistoryResponse>(newImport);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(CreateContactList),
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = ex.Message
                    });
            }
        }

        /// <summary>
        ///  Add User Profile to Contact List.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        // ref: UserProfileController.AddUserProfileToList()
        [HttpPost]
        [Route("api/contact/list/{contactListId}/add")]
        public async Task<ActionResult<ImportContactHistoryResponse>> AddUserProfileToList(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute]
            long contactListId,
            [FromBody]
            UserProfileIdsViewModel userprofileViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            try
            {
                var addToUserList = await _userProfileService.AddToUserList(
                    apiKey.CompanyId,
                    contactListId,
                    userprofileViewModel);

                if (addToUserList == null)
                {
                    return NotFound(
                        new ResponseViewModel
                        {
                            code = 404, message = $"Contact List with ID {contactListId} not found."
                        });
                }

                var group = await _appDbContext.CompanyImportContactHistories
                    .Where(x => x.Id == contactListId && x.CompanyId == apiKey.CompanyId)
                    .Include(x => x.ImportedFrom.Identity).FirstOrDefaultAsync();
                var response = _mapper.Map<ImportContactHistoryResponse>(group);
                response.TotalContactCount = group.ImportedUserProfiles.Count;

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(AddUserProfileToList),
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Error occurred during add contact to list."
                    });
            }
        }

        /// <summary>
        ///  Delete User Profile at Contact List.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        // ref: UserProfileController.RemoveUserProfileToList()
        [HttpPost]
        [HttpDelete]
        [Route("api/contact/list/{contactListId}/remove")]
        public async Task<ActionResult<ImportContactHistoryResponse>> RemoveUserProfileToList(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute]
            long contactListId,
            [FromBody]
            UserProfileIdsViewModel userprofileViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            try
            {
                _logger.LogInformation(
                    "RemoveFromUserList by API: {CompanyId}, listId: {ContactListId} payload: {RequestPayload}",
                    apiKey.CompanyId,
                    contactListId,
                    JsonConvert.SerializeObject(userprofileViewModel));

                var group = await _userProfileService.RemoveFromUserList(
                    apiKey.CompanyId,
                    contactListId,
                    userprofileViewModel);

                var response = _mapper.Map<ImportContactHistoryResponse>(group);
                response.TotalContactCount = group.ImportedUserProfiles.Count;

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(RemoveUserProfileToList),
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Error occurred during delete contact at list."
                    });
            }
        }

        #endregion

        #region Label / Hashtags / Tag

        // ref TagController

        /// <summary>
        ///  Get Labels.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        ///
        [HttpGet]
        [Route("api/labels")]
        public async Task<ActionResult<List<CompanyHashtagResponse>>> GetCompanyCompanyHashTags(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var companyHashtags = await _appDbContext
                .CompanyDefinedHashtags
                .Where(x => x.CompanyId == apiKey.CompanyId)
                .ProjectTo<CompanyHashtagResponse>(_mapper.ConfigurationProvider)
                .ToListAsync(HttpContext.RequestAborted);

            return Ok(companyHashtags);
        }

        /// <summary>
        ///  Add Labels.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("api/labels")]
        public async Task<ActionResult<CompanyHashtagResponse>> AddCompanyHashTags(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            List<ConversationHashtagViewModel> conversationTagViewModels,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var addOrUpdatedLabelIds = new List<string>();

            if (conversationTagViewModels == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Please provide at least one label."
                    });
            }

            foreach (var conversationTag in conversationTagViewModels)
            {
                if (string.IsNullOrEmpty(conversationTag.Hashtag))
                {
                    continue;
                }

                var company_hashTag = await _appDbContext.CompanyDefinedHashtags.Where(
                        x => x.CompanyId == apiKey.CompanyId && (x.Id == conversationTag.Id ||
                                                                 x.Hashtag.ToLower() ==
                                                                 conversationTag.HashtagNormalized))
                    .FirstOrDefaultAsync();

                if (company_hashTag == null)
                {
                    company_hashTag = new CompanyHashtag
                    {
                        CompanyId = apiKey.CompanyId, Hashtag = conversationTag.Hashtag
                    };
                    _appDbContext.CompanyDefinedHashtags.Add(company_hashTag);
                }

                if (conversationTag.HashTagColor.HasValue)
                {
                    company_hashTag.HashTagColor = conversationTag.HashTagColor.Value;
                }

                if (await _appDbContext.CompanyDefinedHashtags.AnyAsync(
                        x => x.CompanyId == apiKey.CompanyId &&
                             x.Hashtag.ToLower() == conversationTag.HashtagNormalized && x.Id != company_hashTag.Id))
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = $"Error Duplicated Label: {conversationTag.Hashtag} is not allowed."
                        });
                }

                company_hashTag.Hashtag = conversationTag.Hashtag;

                await _appDbContext.SaveChangesAsync();

                addOrUpdatedLabelIds.Add(company_hashTag.Id);
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(apiKey.CompanyId);

            var companyHashtags = await _appDbContext
                .CompanyDefinedHashtags
                .Where(x => x.CompanyId == apiKey.CompanyId && addOrUpdatedLabelIds.Contains(x.Id))
                .ProjectTo<CompanyHashtagResponse>(_mapper.ConfigurationProvider)
                .ToListAsync();

            return Ok(companyHashtags);
        }

        /// <summary>
        ///  Delete Labels.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpDelete]
        [Route("api/labels")]
        public async Task<ActionResult<ResponseViewModel>> DeleteCompanyHashTags(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            List<ConversationHashtagViewModel> conversationTagViewModels,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var labelsToDelete = await _appDbContext.CompanyDefinedHashtags
                .Where(x => conversationTagViewModels.Select(l => l.Id).ToList().Contains(x.Id)).ToListAsync();

            foreach (var labelToDelete in labelsToDelete)
            {
                _logger.LogInformation(
                    "CompanyId: {CompanyId} Removed label {HashtagValue}",
                    apiKey.CompanyId,
                    labelToDelete?.Hashtag);

                // Remove Label used in Conversation
                _appDbContext.ConversationHashtags.RemoveRange(
                    await _appDbContext.ConversationHashtags
                        .Where(x => x.Hashtag.Id == labelToDelete.Id)
                        .ToListAsync());

                _appDbContext.CompanyDefinedHashtags.Remove(labelToDelete);
                await _appDbContext.SaveChangesAsync();
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(apiKey.CompanyId);

            return Ok(
                new ResponseViewModel
                {
                    code = 200, message = $"{labelsToDelete.Count} Label Delete"
                });
        }

        #endregion

        #region Staffs

        /// <summary>
        /// Get staffs, supports searching staff by phone number and/or email address.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/staffs")]
        public async Task<ActionResult<List<PublicApiStaffDto>>> GetStaffs(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery(Name = "phoneNumber")]
            string phoneNumber = null,
            [FromQuery(Name = "email")]
            string email = null,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            if (!IsEmailValid(email) && email != null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Invalid email address."
                    });
            }

            var staffs = await _appDbContext.UserRoleStaffs
                .Where(
                    x => x.CompanyId == apiKey.CompanyId &&
                         x.Id != 1)
                .WhereIf(!string.IsNullOrEmpty(phoneNumber), x => x.Identity.PhoneNumber.Contains(phoneNumber))
                .WhereIf(!string.IsNullOrEmpty(email), x => x.Identity.Email.ToLower().Contains(email.ToLower()))
                .Include(x => x.Identity)
                .Include(x => x.ProfilePicture)
                .OrderBy(x => x.Order).ThenBy(x => x.Id)
                .Select(
                    x => new PublicApiStaffDto
                    {
                        StaffId = x.Id,
                        UserId = x.Identity.Id,
                        FirstName = x.Identity.FirstName,
                        LastName = x.Identity.LastName,
                        DisplayName = x.Identity.DisplayName,
                        UserName = x.Identity.UserName,
                        Email = x.Identity.Email,
                        PhoneNumber = x.Identity.PhoneNumber,
                        EmailConfirmed = x.Identity.EmailConfirmed,
                        RoleType = x.RoleType,
                        TimeZoneInfoId = x.TimeZoneInfoId,
                        Position = x.Position,
                        CreatedAt = x.Identity.CreatedAt,
                        LastLoginAt = x.Identity.LastLoginAt,
                    })
                .ToListAsync(HttpContext.RequestAborted);
            staffs.ForEach(x => x.TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(x.TimeZoneInfoId));

            return Ok(staffs);
        }

        /// <summary>
        /// Get staff by staff user id.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/staffs/{staffUserId}")]
        public async Task<ActionResult<PublicApiStaffDto>> GetStaff(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute, Required]
            string staffUserId,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var staff = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == apiKey.CompanyId && x.Id != 1 & x.Identity.Id == staffUserId)
                .Include(x => x.Identity)
                .Include(x => x.ProfilePicture)
                .OrderBy(x => x.Order).ThenBy(x => x.Id)
                .Select(
                    x => new PublicApiStaffDto
                    {
                        StaffId = x.Id,
                        UserId = x.Identity.Id,
                        FirstName = x.Identity.FirstName,
                        LastName = x.Identity.LastName,
                        DisplayName = x.Identity.DisplayName,
                        UserName = x.Identity.UserName,
                        Email = x.Identity.Email,
                        PhoneNumber = x.Identity.PhoneNumber,
                        EmailConfirmed = x.Identity.EmailConfirmed,
                        RoleType = x.RoleType,
                        TimeZoneInfoId = x.TimeZoneInfoId,
                        Position = x.Position,
                        CreatedAt = x.Identity.CreatedAt,
                        LastLoginAt = x.Identity.LastLoginAt,
                    })
                .FirstOrDefaultAsync();

            if (staff == null)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Staff not found. Id: {staffUserId}"
                    });
            }

            staff.TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(staff.TimeZoneInfoId);

            return Ok(staff);
        }

        /// <summary>
        /// Create a new staff.
        ///</summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("api/staffs")]
        public async Task<ActionResult<PublicApiStaffDto>> CreateStaff(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            CreateStaffViewModel createStaffViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var companyUsage = await _companyUsageService.GetCompanyUsage(apiKey.CompanyId);

            if (companyUsage.totalAgents >= companyUsage.MaximumAgents)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "You have reached your maximum number of agents."
                    });
            }

            if (!IsEmailValid(createStaffViewModel.Email))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Invalid email address."
                    });
            }

            var staffUser = await _userManager.FindByEmailAsync(createStaffViewModel.Email);

            if (staffUser != null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Email address already existed."
                    });
            }

            try
            {
                var identityCreation = await _userManager.CreateAsync(
                    new ApplicationUser
                    {
                        UserName = createStaffViewModel.Email,
                        Email = createStaffViewModel.Email,
                        FirstName = createStaffViewModel.FirstName,
                        LastName = createStaffViewModel.LastName,
                        DisplayName = $"{createStaffViewModel.FirstName} {createStaffViewModel.LastName}",
                        PhoneNumber = PhoneNumberHelper.NormalizePhoneNumber(createStaffViewModel.PhoneNumber)
                    },
                    createStaffViewModel.Password);

                if (identityCreation.Succeeded)
                {
                    var company =
                        await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == apiKey.CompanyId);
                    var user = await _userManager.FindByEmailAsync(createStaffViewModel.Email);
                    var emailConfirmationToken = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                    await _userManager.ConfirmEmailAsync(user, emailConfirmationToken);

                    var staffUserRole = new StaffUserRole();

                    switch (createStaffViewModel.RoleType.ToLower())
                    {
                        case "staff":
                            staffUserRole = StaffUserRole.Staff;

                            break;
                        case "admin":
                            staffUserRole = StaffUserRole.Admin;

                            break;
                        case "teamadmin":
                            staffUserRole = StaffUserRole.TeamAdmin;

                            break;
                        default:
                            break;
                    }

                    var staffToCreate = new Staff
                    {
                        CompanyId = apiKey.CompanyId,
                        IdentityId = user.Id,
                        Identity = user,
                        Locale = "en",
                        RoleType = staffUserRole
                    };
                    await _appDbContext.UserRoleStaffs.AddAsync(staffToCreate);
                    await _appDbContext.SaveChangesAsync();

                    BackgroundJob.Enqueue<IInternalHubSpotService>(x => x.SyncCompanyStaffs(staffToCreate.CompanyId));

                    var staff = await _appDbContext.UserRoleStaffs
                        .Where(
                            x => x.CompanyId == apiKey.CompanyId &&
                                 x.Id != 1 & x.Identity.Id == staffToCreate.IdentityId)
                        .Include(x => x.Identity)
                        .Include(x => x.ProfilePicture)
                        .OrderBy(x => x.Order).ThenBy(x => x.Id)
                        .Select(
                            x => new PublicApiStaffDto
                            {
                                StaffId = x.Id,
                                UserId = x.Identity.Id,
                                FirstName = x.Identity.FirstName,
                                LastName = x.Identity.LastName,
                                DisplayName = x.Identity.DisplayName,
                                UserName = x.Identity.UserName,
                                Email = x.Identity.Email,
                                PhoneNumber = x.Identity.PhoneNumber,
                                EmailConfirmed = x.Identity.EmailConfirmed,
                                RoleType = x.RoleType,
                                TimeZoneInfoId = x.TimeZoneInfoId,
                                Position = x.Position,
                                CreatedAt = x.Identity.CreatedAt,
                                LastLoginAt = x.Identity.LastLoginAt,
                            })
                        .FirstOrDefaultAsync();

                    return Ok(staff);
                }
                else
                {
                    return BadRequest(identityCreation.Errors);
                }
            }
            catch (Exception e)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Error on creating staff."
                    });
            }
        }

        /// <summary>
        /// Delete staff by staff user id.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpDelete]
        [Route("api/staffs/{staffUserId}")]
        public async Task<ActionResult<ResponseViewModel>> DeleteStaff(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute, Required]
            string staffUserId,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var IsStaffExisted = await _companyService.IsCompanyStaffExist(apiKey.CompanyId, staffUserId);

            if (!IsStaffExisted)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Staff not found. Id: {staffUserId}"
                    });
            }

            try
            {
                var staffIdTobeDeleted = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == apiKey.CompanyId && x.IdentityId == staffUserId)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();
                var isRemoved = await _coreService.RemoveStaffData(apiKey.CompanyId, staffIdTobeDeleted);

                await _companyInfoCacheService.RemoveCompanyInfoCache(apiKey.CompanyId);

                return Ok(
                    new ResponseViewModel
                    {
                        code = 200, message = $"Staff deleted. Id: {staffUserId}"
                    });
            }
            catch (Exception e)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Error on creating staff."
                    });
            }
        }

        #endregion

        #region Teams

        /// <summary>
        /// Get teams.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/teams")]
        public async Task<ActionResult<List<PublicApiStaffTeamDto>>> GetTeams(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var teams = await _appDbContext
                .CompanyStaffTeams
                .AsNoTracking()
                .Include(x => x.Members)
                .ThenInclude(x => x.Staff)
                .Where(x => x.CompanyId == apiKey.CompanyId)
                .Select(
                    x => new PublicApiStaffTeamDto
                    {
                        TeamId = x.Id,
                        TeamName = x.TeamName,
                        MemberStaffIds = x.Members.Select(m => m.StaffId).ToList(),
                        LastUpdatedAt = x.LastUpdated
                    })
                .ToListAsync(HttpContext.RequestAborted);

            return Ok(teams);
        }

        /// <summary>
        /// Get team by teamId.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/teams/{teamId}")]
        public async Task<ActionResult<PublicApiStaffTeamDto>> GetTeam(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute, Required]
            int teamId,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var isTeamExist = await _companyTeamService.IsTeamExist(apiKey.CompanyId, teamId);

            if (!isTeamExist)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Team Not found. Id: {teamId}"
                    });
            }

            var team = await _appDbContext
                .CompanyStaffTeams
                .Where(x => x.Id == teamId)
                .AsNoTracking()
                .Include(x => x.Members)
                .ThenInclude(x => x.Staff)
                .Where(x => x.CompanyId == apiKey.CompanyId)
                .Select(
                    x => new PublicApiStaffTeamDto
                    {
                        TeamId = x.Id,
                        TeamName = x.TeamName,
                        MemberStaffIds = x.Members.Select(m => m.StaffId).ToList(),
                        LastUpdatedAt = x.LastUpdated
                    })
                .FirstOrDefaultAsync();

            if (team == null)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Team Not found. Id: {teamId}"
                    });
            }

            return Ok(team);
        }

        /// <summary>
        /// Create team.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("api/teams")]
        public async Task<ActionResult> CreateTeam(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            CreateNewTeamViewModel teamToCreate,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var newTeam = new CompanyTeam
            {
                CompanyId = apiKey.CompanyId,
                TeamName = teamToCreate.TeamName,
                DefaultChannels = teamToCreate.DefaultChannels
            };

            if (teamToCreate.StaffIds != null)
            {
                foreach (var staffId in teamToCreate.StaffIds)
                {
                    var staff = await _appDbContext.UserRoleStaffs
                        .Include(x => x.Identity)
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == apiKey.CompanyId
                                && x.IdentityId == staffId);

                    if (staff == null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                code = 400, message = "No member found"
                            });
                    }

                    newTeam.Members.Add(
                        new TeamMember
                        {
                            Staff = staff
                        });

                    await _staffHooks.OnStaffAddedToTeamsAsync(
                        newTeam.CompanyId,
                        staff.Id,
                        new List<CompanyTeam>()
                        {
                            newTeam
                        });
                }
            }

            // Add QRCodeContent
            newTeam.QRCodeIdentity = await _companyService.GetTeamQRCodeContent(newTeam);

            _appDbContext.CompanyStaffTeams.Add(newTeam);
            await _appDbContext.SaveChangesAsync();

            var assignedTeamField = await _appDbContext.CompanyCustomUserProfileFields
                .Where(x => x.CompanyId == apiKey.CompanyId && x.FieldName.ToLower() == "assignedteam")
                .FirstOrDefaultAsync();

            if (assignedTeamField == null)
            {
                var newTeamField = new CompanyCustomUserProfileField
                {
                    CompanyId = apiKey.CompanyId,
                    FieldName = "AssignedTeam",
                    Type = FieldDataType.Options,
                    Order = 99,
                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                    {
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "Assigned Team", Language = "en"
                        },
                        new CustomUserProfileFieldLingual
                        {
                            DisplayName = "團隊", Language = "zh-hk"
                        }
                    },
                    IsDeletable = false,
                    IsEditable = false,
                };
                _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                await _appDbContext.SaveChangesAsync();
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(apiKey.CompanyId);

            var response = _mapper.Map<CompanyTeamResponse>(newTeam);

            return Ok(response);
        }

        /// <summary>
        /// Delete team by team id.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpDelete]
        [Route("api/teams/{teamId}")]
        public async Task<ActionResult<ResponseViewModel>> DeleteTeam(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute, Required]
            int teamId,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            // Implement delete team by teamId
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var isTeamExist = await _companyTeamService.IsTeamExist(apiKey.CompanyId, teamId);

            if (!isTeamExist)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Team Not found. Id: {teamId}"
                    });
            }

            await _appDbContext.Conversations
                .Where(x => teamId == x.AssignedTeamId)
                .ExecuteUpdateAsync(
                    calls => calls.SetProperty(
                        p => p.AssignedTeamId,
                        (long?) null));

            await _appDbContext.CompanyAutomationActions
                .Where(x => x.CompanyId == apiKey.CompanyId && x.AssignedTeamId == teamId)
                .ExecuteUpdateAsync(
                    calls => calls.SetProperty(
                        p => p.AssignedTeamId,
                        (long?) null));

            await _appDbContext.CompanyAutomationActionRecords.Where(x => x.AssignedTeamId == teamId)
                .ExecuteUpdateAsync(
                    calls => calls.SetProperty(
                        p => p.AssignedTeamId,
                        (long?) null));

            await _appDbContext.CompanyTeamMembers.Where(x => x.CompanyTeamId == teamId).ExecuteDeleteAsync();
            await _appDbContext.CompanyStaffTeams.Where(x => x.Id == teamId).ExecuteDeleteAsync();
            await _companyInfoCacheService.RemoveCompanyInfoCache(apiKey.CompanyId);

            return Ok(
                new ResponseViewModel
                {
                    code = 200, message = $"Team deleted, Id: {teamId}"
                });
        }

        /// <summary>
        /// Add staffs to team.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("api/teams/{teamId}/addStaffs")]
        public async Task<ActionResult<ResponseViewModel>> AddStaffToTeam(
            [FromRoute, Required]
            int teamId,
            [FromBody]
            CreateNewTeamViewModel teamStaffsToAdd,
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var isTeamExist = await _companyTeamService.IsTeamExist(apiKey.CompanyId, teamId);

            if (!isTeamExist)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Team Not found. Id: {teamId}"
                    });
            }

            try
            {
                await _companyTeamService.AddToTeam(apiKey.CompanyId, teamId, teamStaffsToAdd);

                return Ok(
                    new ResponseViewModel
                    {
                        code = 200, message = $"Staffs added to team successfully. TeamId: {teamId}"
                    });
            }
            catch
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Failed to add staffs to team. TeamId: {teamId}"
                    });
            }
        }

        /// <summary>
        /// Remove staffs from team.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("api/teams/{teamId}/removeStaffs")]
        public async Task<ActionResult<ResponseViewModel>> RemoveStaffFromTeam(
            [FromRoute, Required]
            int teamId,
            [FromBody]
            CreateNewTeamViewModel teamStaffsToRemove,
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var isTeamExist = await _companyTeamService.IsTeamExist(apiKey.CompanyId, teamId);

            if (!isTeamExist)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Team Not found. Id: {teamId}"
                    });
            }

            try
            {
                await _companyTeamService.RemoveFromTeam(apiKey.CompanyId, teamId, teamStaffsToRemove);

                var companyTeamAssignmentQueue =
                    await _companyTeamAssignmentQueueService.GetCompanyTeamAssignmentQueueByTeamIdAsync(teamId);

                foreach (var staffIdToRemove in teamStaffsToRemove.StaffIds)
                {
                    await _companyTeamAssignmentQueueService.RemoveStaffFromCompanyTeamAssignmentQueueAsync(
                        staffIdToRemove,
                        companyTeamAssignmentQueue);
                }

                return Ok(
                    new ResponseViewModel
                    {
                        code = 200, message = $"Staffs removed from team successfully. TeamId: {teamId}"
                    });
            }
            catch
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Failed to remove staffs from team. TeamId: {teamId}"
                    });
            }
        }

        /// <summary>
        /// Get staff conversation summary by staffUserId.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/staffs/{staffUserId}/conversations/summary")]
        public async Task<IActionResult> GetStaffConversationSummary(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromRoute, Required]
            string staffUserId,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var companyUser = await _appDbContext.UserRoleStaffs.FirstOrDefaultAsync(x => x.IdentityId == staffUserId);

            if (companyUser == null)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        code = 404, message = $"Staff not found. Id: {staffUserId}"
                    });
            }

            var conversations = await _conversationMessageService.GetConversations(
                apiKey.CompanyId,
                companyUser,
                "all",
                staffUserId,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null);

            var lastContactTime = await conversations
                .Include(x => x.UserProfile)
                .OrderByDescending(x => x.UserProfile.LastContact)
                .Select(x => x.UserProfile.LastContact)
                .FirstOrDefaultAsync();

            var lastContactFromCustomersTime = await conversations
                .Include(x => x.UserProfile)
                .OrderByDescending(x => x.UserProfile.LastContactFromCustomers)
                .Select(x => x.UserProfile.LastContactFromCustomers)
                .FirstOrDefaultAsync();

            var conversationStatusCounts = await _conversationSummaryService.GetConversationSummaryCountAsync(
                conversations,
                "all",
                staffUserId,
                null,
                null);

            var response = new StaffConversationSummaryResponse
            {
                StaffUserId = staffUserId,
                ConversationStatusCounts = conversationStatusCounts,
                LastContact = lastContactTime?.ToString("s", CultureInfo.InvariantCulture) ?? string.Empty,
                LastContactFromCustomers =
                    lastContactFromCustomersTime?.ToString("s", CultureInfo.InvariantCulture) ?? string.Empty
            };

            return Ok(response);
        }

        #endregion

        #region Analytic

        /// <summary>
        /// Get Analytic.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("api/analytic")]
        public async Task<ActionResult<PublicApiAnalyticsResponse>> GetAnalyticsData(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery(Name = "startDate"), Required]
            DateTime startDate,
            [FromQuery(Name = "endDate"), Required]
            DateTime endDate,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            // Check Date
            if (startDate > endDate)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Invalid date range, date from cannot be later than date to."
                    });
            }

            var dateLimit = DateTime.UtcNow.Date.AddDays(-1);

            if (startDate > dateLimit || endDate > dateLimit)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400,
                        message =
                            $"Invalid date range, date from or to should be earlier or equal to {dateLimit:yyyy-MM-dd}."
                    });
            }

            if ((endDate - startDate).TotalDays > 31)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Date different must less than 31 day"
                    });
            }

            var result = await _analyticsService.GetSummaryData(apiKey.CompanyId, null, null, startDate, endDate, null);
            var response = _mapper.Map<AnalyticsResponse>(result);

            if (response.DailyLogs == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400,
                        message = "Your Plan is not eligible to use this API, consider upgrade to Premium Plan"
                    });
            }

            return Ok(
                new PublicApiAnalyticsResponse
                {
                    From = response.StartDate, To = response.EndDate, DailyAnalytics = response.DailyLogs
                });
        }

        #endregion

        #region Helpers

        private async Task<List<JObject>> ToZapierResponse(Company company, List<UserProfile> userProfiles)
        {
            var results = new List<JObject>();

            var userProfilePictureFileIds = userProfiles
                .Where(p => p.UserProfilePictureFileId != null)
                .Select(u => u.UserProfilePictureFileId);

            var userProfilePicFiles = await _appDbContext.UserProfilePictureFiles
                .AsNoTracking()
                .Where(x => userProfilePictureFileIds.Contains(x.Id))
                .ToDictionaryAsync(u => u.Id, u => u);

            foreach (var userProfile in userProfiles)
            {
                var response = await _userProfileService.NormalizeUserProfileResponse(
                    company.CustomUserProfileFields,
                    userProfile);

                if (userProfile.UserProfilePictureFileId != null)
                {
                    var file = userProfilePicFiles.TryGetValue(
                        userProfile.UserProfilePictureFileId.Value,
                        out var value)
                        ? value
                        : null;

                    if (file != null)
                    {
                        var userProfileUrl = _azureBlobStorageService.GetAzureBlobSasUri(
                            file.Filename,
                            file.BlobContainer,
                            7 * 24);
                        response["_userProfilePictureUrl"] = userProfileUrl;
                    }
                }

                response["CreatedAt"] = userProfile.CreatedAt;
                response["UpdatedAt"] = userProfile.UpdatedAt;

                results.Add(response);
            }

            return results;
        }

        private async Task<JObject> ToZapierResponse(Company company, UserProfile userProfile)
        {
            var userProfileId = userProfile.Id;

            UserProfilePictureFile userProfilePicFile = null;

            if (userProfile.UserProfilePictureFileId.HasValue)
            {
                userProfilePicFile = await _appDbContext.UserProfilePictureFiles
                    .AsNoTracking()
                    .Where(x => x.Id == userProfile.UserProfilePictureFileId)
                    .FirstOrDefaultAsync();
            }

            JObject response = await _userProfileService.NormalizeUserProfileResponse(
                company.CustomUserProfileFields,
                userProfile);

            if (userProfilePicFile != null)
            {
                var userProfileUrl = _azureBlobStorageService.GetAzureBlobSasUri(
                    userProfilePicFile.Filename,
                    userProfilePicFile.BlobContainer,
                    7 * 24);
                response["_userProfilePictureUrl"] = userProfileUrl;
            }

            response["CreatedAt"] = userProfile.CreatedAt;
            response["UpdatedAt"] = userProfile.UpdatedAt;

            return response;
        }

        private static string RemoveSpecialCharacters(string str)
        {
            return Regex.Replace(str, "[^a-zA-Z0-9_.]+", string.Empty, RegexOptions.Compiled);
        }

        /// <summary>
        /// To keep the consistency of endpoint's behavior,
        /// Manually add Userprofile Field "PhoneNumber" and "Email" if user sets NewProfileViewModel.PhoneNumber/Email but not adds to NewProfileViewModel.UserProfileFields.
        /// Because these 2 field is is updated through  _userProfileService.UpdateUserProfileCustomFields.
        /// </summary>
        /// <param name="_newProfileViewModel">NewProfileViewModel</param>
        private static void ArrangeUserProfileFields(NewProfileViewModel _newProfileViewModel)
        {
            if (!string.IsNullOrEmpty(_newProfileViewModel.WhatsAppPhoneNumber) &&
                !_newProfileViewModel.UserProfileFields.Any(x => x.CustomFieldName.ToLower() == "phonenumber"))
            {
                _newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "phonenumber", CustomValue = _newProfileViewModel.WhatsAppPhoneNumber
                    });
            }

            if (!string.IsNullOrEmpty(_newProfileViewModel.Email) &&
                !_newProfileViewModel.UserProfileFields.Any(x => x.CustomFieldName.ToLower() == "email"))
            {
                _newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "email", CustomValue = _newProfileViewModel.Email
                    });
            }
        }

        private async Task MaskConversationNoCompanyResponseViewModelAsync(
            ConversationNoCompanyResponseViewModel vm,
            string companyId,
            StaffUserRole role)
        {
            if (vm is null
                || !await _piiMaskingService.IsConfiguredAsync(companyId)
                || !Enum.TryParse(role.ToString(), out MaskingRoles maskingRole))
            {
                return;
            }

            if (vm.LastMessage is not null)
            {
                foreach (var message in vm.LastMessage)
                {
                    if (message is not null
                        && !message.IsSentFromSleekflow
                        && message.MessageContent is not null)
                    {
                        message.MessageContent = await _piiMaskingService.MaskPiiIfEnabledAsync(
                            companyId,
                            message.MessageContent,
                            MaskingLocations.IncomingMessage,
                            maskingRole,
                            isFromPlatformApi: true);
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(vm.UserProfile?.FirstName))
            {
                vm.UserProfile.FirstName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    vm.UserProfile.FirstName,
                    MaskingLocations.Contact,
                    maskingRole,
                    isFromPlatformApi: true);
            }

            if (!string.IsNullOrWhiteSpace(vm.UserProfile?.LastName))
            {
                vm.UserProfile.LastName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    vm.UserProfile.LastName,
                    MaskingLocations.Contact,
                    maskingRole,
                    isFromPlatformApi: true);
            }

            if (vm.UserProfile?.CustomFields is null)
            {
                return;
            }

            var companyCustomUserProfileFieldIdToType = await _appDbContext.CompanyCustomUserProfileFields
                .Where(x => x.CompanyId == companyId)
                .ToDictionaryAsync(x => x.Id, x => x.Type);

            foreach (var field in vm.UserProfile.CustomFields)
            {
                var type = companyCustomUserProfileFieldIdToType.GetValueOrDefault(field.CompanyDefinedFieldId);

                if (field.Value is null
                    || (type != FieldDataType.SingleLineText
                        && type != FieldDataType.MultiLineText
                        && type != FieldDataType.Number
                        && type != FieldDataType.PhoneNumber
                        && type != FieldDataType.Email))
                {
                    continue;
                }

                field.Value = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    field.Value,
                    MaskingLocations.Contact,
                    maskingRole,
                    isFromPlatformApi: true);
            }
        }

        private async Task MaskConversationMessageWebhookResponsesAsync(
            List<ConversationMessageWebhookResponse> responses,
            string companyId,
            StaffUserRole role)
        {
            if (!await _piiMaskingService.IsConfiguredAsync(companyId)
                || !Enum.TryParse(role.ToString(), out MaskingRoles maskingRole))
            {
                return;
            }

            foreach (var response in responses)
            {
                if (!response.IsSentFromSleekflow && response.MessageContent is not null)
                {
                    response.MessageContent = await _piiMaskingService.MaskPiiIfEnabledAsync(
                        companyId,
                        response.MessageContent,
                        MaskingLocations.IncomingMessage,
                        maskingRole,
                        isFromPlatformApi: true);
                }
            }
        }

        private async Task MaskUserProfileNoCompanyResponseAsync(
            UserProfileNoCompanyResponse response,
            string companyId,
            StaffUserRole role)
        {
            if (!await _piiMaskingService.IsConfiguredAsync(companyId)
                || !Enum.TryParse(role.ToString(), out MaskingRoles maskingRole))
            {
                return;
            }

            if (!string.IsNullOrWhiteSpace(response.FirstName))
            {
                response.FirstName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    response.FirstName,
                    MaskingLocations.Contact,
                    maskingRole,
                    isFromPlatformApi: true);
            }

            if (!string.IsNullOrWhiteSpace(response.LastName))
            {
                response.LastName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    response.LastName,
                    MaskingLocations.Contact,
                    maskingRole,
                    isFromPlatformApi: true);
            }

            var companyCustomUserProfileFieldIdToType = await _appDbContext.CompanyCustomUserProfileFields
                .Where(x => x.CompanyId == companyId)
                .ToDictionaryAsync(x => x.Id, x => x.Type);

            foreach (var field in response.CustomFields)
            {
                var type = companyCustomUserProfileFieldIdToType.GetValueOrDefault(field.CompanyDefinedFieldId);

                if (field.Value is null
                    || (type != FieldDataType.SingleLineText
                        && type != FieldDataType.MultiLineText
                        && type != FieldDataType.Number
                        && type != FieldDataType.PhoneNumber
                        && type != FieldDataType.Email))
                {
                    continue;
                }

                field.Value = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    field.Value,
                    MaskingLocations.Contact,
                    maskingRole,
                    isFromPlatformApi: true);
            }
        }

        #endregion

        private static bool IsEmailValid(string email)
        {
            var isValid = true;

            try
            {
                var emailAddress = new MailAddress(email);
            }
            catch
            {
                isValid = false;
            }

            return isValid;
        }

        #region Quick reply

        [HttpGet]
        [Route("api/quick-replies")]
        public async Task<ActionResult<List<PublicQuickReplyViewModel>>> GetQuickReplies(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            try
            {
                if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Apikey from query param is different from header."
                        });
                }

                apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
                var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
                await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

                var quickReplies = await _appDbContext.CompanyQuickReplies
                    .Where(x => x.CompanyId == apiKey.CompanyId)
                    .Include(x => x.CompanyQuickReplyLinguals)
                    .OrderBy(x => x.Order)
                    .Skip(offset)
                    .Take(limit)
                    .Select(
                        x => new PublicQuickReplyViewModel()
                        {
                            Id = x.Id,
                            Name = x.Value,
                            Text = x.CompanyQuickReplyLinguals.Select(l => l.Value).FirstOrDefault(),
                            Order = x.Order
                        })
                    .ToListAsync(HttpContext.RequestAborted);

                return Ok(quickReplies);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(GetQuickReplies),
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Error on getting Quick Reply."
                    });
            }
        }

        [HttpPost]
        [Route("api/quick-replies")]
        public async Task<ActionResult<PublicQuickReplyViewModel>> AddQuickReplies(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            CreateQuickReplyViewModel createQuickReplyViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            try
            {
                if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Apikey from query param is different from header."
                        });
                }

                apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
                var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
                await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

                var quickReply = new CompanyQuickReply
                {
                    Order = createQuickReplyViewModel.Order,
                    Value = createQuickReplyViewModel.Name,
                    CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                    {
                        new ()
                        {
                            Language = "en", Value = createQuickReplyViewModel.Text
                        }
                    },
                    CompanyId = apiKey.CompanyId
                };
                _appDbContext.CompanyQuickReplies.Add(quickReply);
                await _appDbContext.SaveChangesAsync();

                return Ok(
                    new PublicQuickReplyViewModel()
                    {
                        Id = quickReply.Id,
                        Name = quickReply.Value,
                        Text = quickReply.CompanyQuickReplyLinguals.Select(l => l.Value).FirstOrDefault(),
                        Order = quickReply.Order
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(AddQuickReplies),
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Error on creating Quick Reply."
                    });
            }
        }

        [HttpPut]
        [Route("api/quick-replies")]
        public async Task<ActionResult<PublicQuickReplyViewModel>> UpdateQuickReplies(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            UpdateQuickReplyViewModel updateQuickReplyViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            try
            {
                if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Apikey from query param is different from header."
                        });
                }

                apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
                var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
                await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

                var quickReply = await _appDbContext.CompanyQuickReplies
                    .Where(x => x.Id == updateQuickReplyViewModel.Id)
                    .Include(x => x.CompanyQuickReplyLinguals)
                    .FirstOrDefaultAsync();

                if (quickReply == null)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = $"Quick Reply not found."
                        });
                }

                if (string.IsNullOrEmpty(updateQuickReplyViewModel.Name))
                {
                    quickReply.Value = updateQuickReplyViewModel.Name;
                }

                if (string.IsNullOrEmpty(updateQuickReplyViewModel.Text))
                {
                    quickReply.CompanyQuickReplyLinguals.First().Value = updateQuickReplyViewModel.Text;
                }

                if (updateQuickReplyViewModel.Order.HasValue)
                {
                    quickReply.Order = updateQuickReplyViewModel.Order.Value;
                }

                quickReply.UpdatedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                return Ok(quickReply);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(UpdateQuickReplies),
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Error on updating Quick Reply"
                    });
            }
        }

        [HttpDelete]
        [Route("api/quick-replies")]
        public async Task<ActionResult<ResponseViewModel>> DeleteQuickReplies(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            DeleteQuickReplyViewModel deleteQuickReplyViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            try
            {
                if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Apikey from query param is different from header."
                        });
                }

                apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
                var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
                await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

                var quickReply = await _appDbContext.CompanyQuickReplies
                    .Where(x => x.Id == deleteQuickReplyViewModel.Id)
                    .Include(x => x.CompanyQuickReplyLinguals)
                    .Include(x => x.QuickReplyFile)
                    .FirstOrDefaultAsync();

                if (quickReply == null)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = $"Quick Reply not found."
                        });
                }

                _appDbContext.CompanyQuickReplies.Remove(quickReply);
                await _appDbContext.SaveChangesAsync();

                return Ok(
                    new ResponseViewModel
                    {
                        code = 200, message = $"Quick Reply with ID: {deleteQuickReplyViewModel.Id} deleted"
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(DeleteQuickReplies),
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = $"Error on deleting Quick Replies."
                    });
            }
        }

        #endregion

        #region CustomObjects

        [HttpGet]
        [Route("api/customObjects/{objectKey}")]
        [TypeFilter(typeof(CustomObjectExceptionFilter))]
        public async Task<ActionResult<PublicApiSchemaDto>> GetSchema(
            [FromRoute(Name = "objectKey"), Required]
            string objectKey,
            [FromQuery(Name = "apiKey")]
            string apikey,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var schema = await _customObjectService.GetSchemaByUniqueName(apiKey.CompanyId, objectKey);

            if (schema is null)
            {
                return BadRequest($"Custom Object not found. Object Key: {objectKey}");
            }

            var publicApiSchemaDto = CustomObjectPublicApiExtension.ConvertToPublicApiSchemaDto(schema);

            return Ok(publicApiSchemaDto);
        }

        [HttpPost]
        [Route("api/customObjects/{objectKey}/records")]
        [TypeFilter(typeof(CustomObjectExceptionFilter))]
        public async Task<ActionResult<PublicApiSchemafulObjectDto>> CreateSchemafulObject(
            [FromRoute(Name = "objectKey"), Required]
            string objectKey,
            [FromQuery(Name = "apiKey")]
            string apikey,
            [FromBody]
            SchemafulObjectViewModel schemafulObjectViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.AddUsageAsync(apiKey);

            var schema = await _customObjectService.GetSchemaByUniqueName(apiKey.CompanyId, objectKey);

            if (schema is null)
            {
                return BadRequest($"Custom Object not found. Object Key: {objectKey}");
            }

            Dictionary<string, object> propertyValues;

            try
            {
                propertyValues = PropertyNamingMapper.SanitizePropertyValues(
                    schema,
                    schemafulObjectViewModel.PropertyValues);
            }
            catch (Exception)
            {
                return BadRequest("Invalid Property Unique Identifier.");
            }

            if (!string.IsNullOrEmpty(schemafulObjectViewModel.ReferencedUserProfileId) &&
                !_appDbContext.UserProfiles.Any(
                    up => up.CompanyId == apiKey.CompanyId &&
                          up.Id == schemafulObjectViewModel.ReferencedUserProfileId &&
                          up.ActiveStatus == ActiveStatus.Active))
            {
                return BadRequest("Invalid User Profile Id.");
            }

            var schemafulObject =
                (await _schemafulObjectsApi.SchemafulObjectsCreateSchemafulObjectPostAsync(
                    createSchemafulObjectInput: new CreateSchemafulObjectInput(
                        schema.Id,
                        apiKey.CompanyId,
                        schemafulObjectViewModel.PrimaryPropertyValue,
                        propertyValues,
                        schemafulObjectViewModel.ReferencedUserProfileId,
                        string.Empty,
                        createdVia: CustomObjectAuditSources.PublicApi)))
                .Data
                .SchemafulObject;

            return Ok(
                new PublicApiSchemafulObjectDto
                {
                    PrimaryPropertyValue = schemafulObject.PrimaryPropertyValue,
                    PropertyValues = PropertyNamingMapper.FormatPropertyValues(schema, schemafulObject.PropertyValues),
                    ReferencedUserProfileId = schemafulObject.SleekflowUserProfileId,
                    CreatedAt = schemafulObject.CreatedAt
                });
        }

        [HttpGet]
        [Route("api/customObjects/{objectKey}/records/{primaryPropertyValue}")]
        [TypeFilter(typeof(CustomObjectExceptionFilter))]
        public async Task<ActionResult<PublicApiSchemafulObjectDto>> GetSchemafulObject(
            [FromRoute(Name = "objectKey"), Required]
            string objectKey,
            [FromRoute(Name = "primaryPropertyValue"), Required]
            string primaryPropertyValue,
            [FromQuery(Name = "apiKey")]
            string apikey,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var schema = await _customObjectService.GetSchemaByUniqueName(apiKey.CompanyId, objectKey);

            if (schema is null)
            {
                return BadRequest($"Custom Object not found. Object Key: {objectKey}");
            }

            var schemafulObject = await _customObjectService.GetSchemafulObjectByPrimaryPropertyValue(
                apiKey.CompanyId,
                schema.Id,
                primaryPropertyValue);

            if (schemafulObject is null)
            {
                return BadRequest($"Custom Object Record not found. Primary Property Value: {primaryPropertyValue}");
            }

            var result = new PublicApiSchemafulObjectDto
            {
                PrimaryPropertyValue = primaryPropertyValue,
                PropertyValues = PropertyNamingMapper.FormatPropertyValues(schema, schemafulObject.PropertyValues),
                ReferencedUserProfileId = schemafulObject.SleekflowUserProfileId,
                CreatedAt = schemafulObject.CreatedAt,
                UpdatedAt = schemafulObject.UpdatedAt
            };

            await MaskPublicApiSchemafulObjectDtosAsync(
                schema,
                new List<PublicApiSchemafulObjectDto> { result },
                apiKey.CompanyId,
                StaffUserRole.Admin);

            return Ok(result);
        }

        [HttpPut]
        [Route("api/customObjects/{objectKey}/records/{primaryPropertyValue}/partialUpdate")]
        [TypeFilter(typeof(CustomObjectExceptionFilter))]
        public async Task<ActionResult<PublicApiSchemafulObjectDto>> PartialUpdateSchemafulObject(
            [FromRoute(Name = "objectKey"), Required]
            string objectKey,
            [FromRoute(Name = "primaryPropertyValue"), Required]
            string primaryPropertyValue,
            [FromQuery(Name = "apiKey")]
            string apikey,
            [FromBody]
            SchemafulObjectViewModel schemafulObjectViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.AddUsageAsync(apiKey);

            var schema = await _customObjectService.GetSchemaByUniqueName(apiKey.CompanyId, objectKey);

            if (schema is null)
            {
                return BadRequest($"Custom Object not found. Object Key: {objectKey}");
            }

            var schemafulObject = await _customObjectService.GetSchemafulObjectByPrimaryPropertyValue(
                apiKey.CompanyId,
                schema.Id,
                primaryPropertyValue);

            if (schemafulObject is null)
            {
                return BadRequest($"Custom Object Record not found. Primary Property Value: {primaryPropertyValue}");
            }

            Dictionary<string, object?> receivedPropertyValues;
            try
            {
                receivedPropertyValues = PropertyNamingMapper.SanitizePropertyValues(
                    schema,
                    schemafulObjectViewModel.PropertyValues);
            }
            catch (Exception)
            {
                return BadRequest("Invalid Property Unique Identifier.");
            }

            schemafulObject = (await _schemafulObjectsApi.SchemafulObjectsPartialUpdatePropertyValuesPostAsync(
                    partialUpdatePropertyValuesInput: new PartialUpdatePropertyValuesInput(
                        schemafulObject.Id,
                        schema.Id,
                        apiKey.CompanyId,
                        receivedPropertyValues,
                        string.Empty,
                        updatedVia: CustomObjectAuditSources.PublicApi)))
                .Data
                .SchemafulObject;

            return Ok(
                new PublicApiSchemafulObjectDto
                {
                    PrimaryPropertyValue = primaryPropertyValue,
                    PropertyValues = PropertyNamingMapper.FormatPropertyValues(schema, schemafulObject.PropertyValues),
                    ReferencedUserProfileId = schemafulObject.SleekflowUserProfileId,
                    CreatedAt = schemafulObject.CreatedAt,
                    UpdatedAt = schemafulObject.UpdatedAt
                });
        }

        [HttpPut]
        [Route("api/customObjects/{objectKey}/records/{primaryPropertyValue}")]
        [TypeFilter(typeof(CustomObjectExceptionFilter))]
        public async Task<ActionResult<PublicApiSchemafulObjectDto>> UpdateSchemafulObject(
            [FromRoute(Name = "objectKey"), Required]
            string objectKey,
            [FromRoute(Name = "primaryPropertyValue"), Required]
            string primaryPropertyValue,
            [FromQuery(Name = "apiKey")]
            string apikey,
            [FromBody]
            SchemafulObjectViewModel schemafulObjectViewModel,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.AddUsageAsync(apiKey);

            var schema = await _customObjectService.GetSchemaByUniqueName(apiKey.CompanyId, objectKey);

            if (schema is null)
            {
                return BadRequest($"Custom Object not found. Object Key: {objectKey}");
            }

            var schemafulObject = await _customObjectService.GetSchemafulObjectByPrimaryPropertyValue(
                apiKey.CompanyId,
                schema.Id,
                primaryPropertyValue);

            if (schemafulObject is null)
            {
                return BadRequest($"Custom Object Record not found. Primary Property Value: {primaryPropertyValue}");
            }

            Dictionary<string, object> propertyValues;

            try
            {
                propertyValues = PropertyNamingMapper.SanitizePropertyValues(
                    schema,
                    schemafulObjectViewModel.PropertyValues);
            }
            catch (Exception)
            {
                return BadRequest("Invalid Property Unique Identifier.");
            }

            if (!string.IsNullOrEmpty(schemafulObjectViewModel.ReferencedUserProfileId) &&
                !_appDbContext.UserProfiles.Any(
                    up => up.CompanyId == apiKey.CompanyId &&
                          up.Id == schemafulObjectViewModel.ReferencedUserProfileId &&
                          up.ActiveStatus == ActiveStatus.Active))
            {
                return BadRequest("Invalid User Profile Id.");
            }

            schemafulObject = (await _schemafulObjectsApi.SchemafulObjectsUpdateSchemafulObjectPostAsync(
                    updateSchemafulObjectInput: new UpdateSchemafulObjectInput(
                        schemafulObject.Id,
                        schema.Id,
                        apiKey.CompanyId,
                        propertyValues,
                        schemafulObjectViewModel.ReferencedUserProfileId,
                        string.Empty,
                        updatedVia: CustomObjectAuditSources.PublicApi)))
                .Data
                .SchemafulObject;

            return Ok(
                new PublicApiSchemafulObjectDto
                {
                    PrimaryPropertyValue = primaryPropertyValue,
                    PropertyValues = PropertyNamingMapper.FormatPropertyValues(schema, schemafulObject.PropertyValues),
                    ReferencedUserProfileId = schemafulObject.SleekflowUserProfileId,
                    CreatedAt = schemafulObject.CreatedAt,
                    UpdatedAt = schemafulObject.UpdatedAt
                });
        }

        [HttpDelete]
        [Route("api/customObjects/{objectKey}/records/{primaryPropertyValue}")]
        [TypeFilter(typeof(CustomObjectExceptionFilter))]
        public async Task<ActionResult> DeleteSchemafulObject(
            [FromRoute(Name = "objectKey"), Required]
            string objectKey,
            [FromRoute(Name = "primaryPropertyValue"), Required]
            string primaryPropertyValue,
            [FromQuery(Name = "apiKey")]
            string apikey,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.AddUsageAsync(apiKey);

            var schema = await _customObjectService.GetSchemaByUniqueName(apiKey.CompanyId, objectKey);

            if (schema is null)
            {
                return BadRequest($"Custom Object not found. Object Key: {objectKey}");
            }

            var schemafulObject = await _customObjectService.GetSchemafulObjectByPrimaryPropertyValue(
                apiKey.CompanyId,
                schema.Id,
                primaryPropertyValue);

            if (schemafulObject is null)
            {
                return BadRequest($"Custom Object Record not found. Primary Property Value: {primaryPropertyValue}");
            }

            var deletedCount = (await _schemafulObjectsApi.SchemafulObjectsDeleteSchemafulObjectsPostAsync(
                    deleteSchemafulObjectsInput: new DeleteSchemafulObjectsInput(
                        new List<string>
                        {
                            schemafulObject.Id
                        },
                        schema.Id,
                        apiKey.CompanyId)))
                .Data
                .DeletedCount;

            if (deletedCount != 1)
            {
                return StatusCode(
                    500,
                    "Errors occurred while deleting the Custom Object Record. Please contact for support.");
            }

            return Ok();
        }

        [HttpGet]
        [Route("api/customObjects/{objectKey}/records")]
        [TypeFilter(typeof(CustomObjectExceptionFilter))]
        public async Task<ActionResult<GetSchemafulObjectsResponse>> GetSchemafulObjects(
            [FromRoute(Name = "objectKey"), Required]
            string objectKey,
            [FromQuery(Name = "apiKey")]
            string apikey,
            [FromQuery(Name = "limit"), Required] [Range(0, 1000)]
            int limit,
            [FromQuery(Name = "sortBy")]
            [RegularExpression("^(created_at|updated_at|primary_property_value|referenced_user_profile_id)$")]
            string sortBy = "created_at",
            [FromQuery(Name = "direction")] [RegularExpression("^(asc|desc|ASC|DESC)$")]
            string direction = "desc",
            [FromQuery(Name = "referenced_user_profile_id")]
            string referencedUserProfileId = null,
            [FromHeader(Name = "ContinuationToken")]
            string continuationToken = null,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var schema = await _customObjectService.GetSchemaByUniqueName(apiKey.CompanyId, objectKey);

            if (schema is null)
            {
                return BadRequest($"Custom Object not found. Object Key: {objectKey}");
            }

            var filterGroups = string.IsNullOrWhiteSpace(referencedUserProfileId)
                ? new List<GetSchemafulObjectsFilterGroup>()
                : new List<GetSchemafulObjectsFilterGroup>
                {
                    new GetSchemafulObjectsFilterGroup(
                        new List<SchemafulObjectFilter>
                        {
                            new SchemafulObjectFilter(
                                "sleekflow_user_profile_id",
                                "=",
                                referencedUserProfileId)
                        })
                };

            var getSchemafulObjectsOutputOutput =
                await _schemafulObjectsApi.SchemafulObjectsGetSchemafulObjectsPostAsync(
                    getSchemafulObjectsInput: new GetSchemafulObjectsInput(
                        apiKey.CompanyId,
                        schema.Id,
                        SanitizeContinuationToken(continuationToken),
                        limit,
                        true,
                        filterGroups,
                        new List<SchemafulObjectSort>
                        {
                            new SchemafulObjectSort(
                                sortBy,
                                direction,
                                false)
                        }));

            var publicApiSchemafulObjectDtos = getSchemafulObjectsOutputOutput.Data.SchemafulObjects
                .Select(
                    so => new PublicApiSchemafulObjectDto
                    {
                        PrimaryPropertyValue = so.PrimaryPropertyValue,
                        PropertyValues = PropertyNamingMapper.FormatPropertyValues(schema, so.PropertyValues),
                        ReferencedUserProfileId = so.SleekflowUserProfileId,
                        CreatedAt = so.CreatedAt,
                        UpdatedAt = so.UpdatedAt
                    })
                .ToList();

            await MaskPublicApiSchemafulObjectDtosAsync(
                schema,
                publicApiSchemafulObjectDtos,
                apiKey.CompanyId,
                StaffUserRole.Admin);

            return Ok(
                new GetSchemafulObjectsResponse
                {
                    Records = publicApiSchemafulObjectDtos,
                    NextContinuationToken = getSchemafulObjectsOutputOutput.Data.ContinuationToken
                });
        }

        [HttpGet]
        [Route("api/customObjects/contact/{userProfileId}")]
        [TypeFilter(typeof(CustomObjectExceptionFilter))]
        public async Task<ActionResult<List<PublicApiCustomObjectDto>>> GetUserProfileReferencedSchemafulObjects(
            [FromRoute(Name = "userProfileId"), Required]
            string userProfileId,
            [FromQuery(Name = "apiKey")]
            string apikey,
            [FromQuery(Name = "limitPerCustomObject")] [Range(0, 100)]
            int limitPerCustomObject = 10,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.ValidateUsageAsync(apiKey);

            var customObjects = await _customObjectService.GetUserprofileReferencedCustomObjectsAsync(
                apiKey.CompanyId,
                userProfileId,
                limitPerCustomObject,
                false);

            var publicApiCustomObjectDtos = new List<PublicApiCustomObjectDto>();

            foreach (var customObject in customObjects)
            {
                var publicApiSchemafulObjectDtos = customObject.SchemafulObjects.Select(
                        so => new PublicApiSchemafulObjectDto
                        {
                            PrimaryPropertyValue = so.PrimaryPropertyValue,
                            PropertyValues =
                                PropertyNamingMapper.FormatPropertyValues(customObject.Schema, so.PropertyValues),
                            ReferencedUserProfileId = so.SleekflowUserProfileId,
                            CreatedAt = so.CreatedAt,
                            UpdatedAt = so.UpdatedAt
                        })
                    .ToList();

                await MaskPublicApiSchemafulObjectDtosAsync(
                    customObject.Schema,
                    publicApiSchemafulObjectDtos,
                    apiKey.CompanyId,
                    StaffUserRole.Admin);

                publicApiCustomObjectDtos.Add(
                    new PublicApiCustomObjectDto
                    {
                        CustomObject = CustomObjectPublicApiExtension.ConvertToPublicApiSchemaDto(customObject.Schema),
                        Records = publicApiSchemafulObjectDtos,
                        HasMoreRecords = !string.IsNullOrEmpty(customObject.ContinuationToken)
                    });
            }

            return Ok(publicApiCustomObjectDtos);
        }

        private async Task MaskPublicApiSchemafulObjectDtosAsync(
            SchemaDto schema,
            List<PublicApiSchemafulObjectDto> dtos,
            string companyId,
            StaffUserRole role)
        {
            if (!await _piiMaskingService.IsConfiguredAsync(companyId)
                || !Enum.TryParse(role.ToString(), out MaskingRoles maskingRole)
                || schema?.Properties is null)
            {
                return;
            }

            var schemaPropertyUniqueNameToDataType = schema.Properties
                .ToDictionary(x=> x.UniqueName, x=> x.DataType);

            foreach(var dto in dtos)
            {
                var updatedPropertyValues = new Dictionary<string, object>();

                foreach (var propertyValue in dto.PropertyValues)
                {
                    var type = schemaPropertyUniqueNameToDataType.GetValueOrDefault(propertyValue.Key);
                    if (type is null
                        || (type.Name != SchemaPropertyDataTypes.SingleLineText
                            && type.Name != SchemaPropertyDataTypes.Numeric
                            && type.Name != SchemaPropertyDataTypes.Decimal))
                    {
                        updatedPropertyValues[propertyValue.Key] = propertyValue.Value;
                        continue;
                    }

                    var maskedValue = await _piiMaskingService.MaskPiiIfEnabledAsync(
                        companyId,
                        propertyValue.Value?.ToString(),
                        schema.Id,
                        maskingRole,
                        isFromPlatformApi: true);

                    updatedPropertyValues[propertyValue.Key] = maskedValue;
                }

                dto.PropertyValues = updatedPropertyValues;
            }
        }

        private static string SanitizeContinuationToken(string continuationToken)
        {
            return string.IsNullOrEmpty(continuationToken)
                ? null
                : continuationToken.Replace(@"\""", @"""").Replace(@"\\", @"\");
        }

        #endregion

        private ActionResult<T> GetValidationErrorResponse<T>(ModelStateDictionary modelState, string key)
        {
            if (modelState.TryGetValue(key, out var errors) && errors.Errors.Any())
            {
                var errorMessage = errors.Errors.First().ErrorMessage;
                var parts = errorMessage.Split(':', 2);

                string errorCode = parts.Length == 2 ? parts[0] : "400";
                string userFriendlyMessage = parts.Length == 2 ? parts[1].Trim() : errorMessage;

                return BadRequest(new
                {
                    ErrorCode = errorCode,
                    Message = userFriendlyMessage
                });
            }

            return null;
        }

        [HttpPost]
        [Route("api/notifications/integration-disconnected")]
        public async Task<IActionResult> SendIntegrationDisconnectedNotification(
            [FromQuery(Name = "apiKey")]
            string apikey,
            [FromBody]
            IntegrationDisconnectedNotificationViewModel vm,
            [FromHeader(Name = "X-Sleekflow-Api-Key")]
            string apikeyHeader = "")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
            }

            if (!apikey.IsNullOrEmpty() && !apikeyHeader.IsNullOrEmpty() && apikey != apikeyHeader)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Apikey from query param is different from header."
                    });
            }

            apikey = apikey.IsNullOrEmpty() ? apikeyHeader : apikey;
            var apiKey = await _publicApiKeyResolver.AuthenticateAsync(apikey);
            await _publicApiKeyResolver.AddUsageAsync(apiKey);

            var toPhoneNumbers = new List<string>();
            foreach(var pn in vm.ToPhoneNumbers)
            {
                if (string.IsNullOrWhiteSpace(pn))
                {
                    return BadRequest("Phone number cannot be empty or whitespace");
                }

                try
                {
                    toPhoneNumbers.Add(PhoneNumberHelper.NormalizePhoneNumber(pn));
                }
                catch(Exception)
                {
                    return BadRequest("Failed to format phone number");
                }
            }

            await _whatsAppMessageNotificationService.IntegrationDisconnected(
                vm.IntegrationName,
                toPhoneNumbers);

            return Ok();
        }

        private async Task<UserProfileNoCompanyResponse> ProcessSingleContactAsync(CompanyAPIKey companyApiKey, NewProfileViewModel userProfile)
        {
            // Create a new scope to get fresh service instances for parallel processing
            using var scope = _serviceProvider.CreateScope();
            var scopedServices = scope.ServiceProvider;

            var scopedAppDbContext = scopedServices.GetRequiredService<ApplicationDbContext>();
            var scopedLockService = scopedServices.GetRequiredService<ILockService>();
            var scopedContactWhatsappSenderLockService = scopedServices.GetRequiredService<IContactWhatsappSenderLockService>();
            var scopedUserProfileService = scopedServices.GetRequiredService<IUserProfileService>();
            var scopedContactUpsertService = scopedServices.GetRequiredService<IContactUpsertService>();
            var scopedMapper = scopedServices.GetRequiredService<IMapper>();

            ILockService.Lock myLock = null;
            ILockService.Lock whatsappPhoneNumberLock = null;

            try
            {
                if (!string.IsNullOrEmpty(userProfile.WhatsAppPhoneNumber))
                {
                    if (!await scopedAppDbContext.UserProfiles
                            .AnyAsync(
                                x =>
                                    x.CompanyId == companyApiKey.CompanyId
                                    && x.PhoneNumber == userProfile.WhatsAppPhoneNumber
                                    && x.ActiveStatus == ActiveStatus.Active))
                    {
                        myLock = await scopedLockService.AcquireLockAsync(
                            AddNewContactLockKey(companyApiKey.CompanyId, userProfile.WhatsAppPhoneNumber),
                            TimeSpan.FromSeconds(5));
                    }

                    try
                    {
                        whatsappPhoneNumberLock =
                            await scopedContactWhatsappSenderLockService.GetContactWhatsappPhoneNumberLockAsync(
                                companyApiKey.CompanyId,
                                userProfile.WhatsAppPhoneNumber);
                    }
                    catch (ContactWhatsappSenderLockException contactWhatsappSenderLockException)
                    {
                        _logger.LogError(
                            contactWhatsappSenderLockException,
                            "[Rejected] [PublicAPI] {MethodName}: {CompanyId} cant acquire lock for contact whatsapp phone number {WhatsappPhoneNumber}",
                            nameof(ProcessSingleContactAsync),
                            companyApiKey.CompanyId,
                            userProfile.WhatsAppPhoneNumber);

                        throw new InvalidOperationException("Server busy");
                    }
                }

                if (string.IsNullOrEmpty(userProfile.UpsertMode))
                {
                    userProfile.UpsertMode = _configuration.GetValue<bool>("SqlPerformance:IsPublicApiNewUpsertEnabled") ? "new" : "old";
                }

                switch (userProfile.UpsertMode)
                {
                    case "old":
                        var legacyUpsertResults =
                            await scopedUserProfileService.AddOrUpdateUserProfile(
                                companyApiKey.CompanyId,
                                userProfile,
                                userProfileSource: UserProfileSource.PublicApi);
                        return scopedMapper.Map<UserProfileNoCompanyResponse>(legacyUpsertResults.FirstOrDefault());
                    default:
                        var upsertResult =
                            await scopedContactUpsertService.UpsertUserProfileAsync(
                                companyApiKey.CompanyId,
                                userProfile,
                                userProfileSource: UserProfileSource.PublicApi);
                        return scopedMapper.Map<UserProfileNoCompanyResponse>(upsertResult);
                }
            }
            catch (InvalidPhoneNumberFormatException ex)
            {
                return new UserProfileNoCompanyResponse
                {
                    PhoneNumber = userProfile.WhatsAppPhoneNumber,
                    Email = userProfile.Email,
                    Id = userProfile.ContactId,
                    ErrorMessage = ex.Message
                };
            }
            catch (InvalidContactOwnerException ex)
            {
                return new UserProfileNoCompanyResponse
                {
                    PhoneNumber = userProfile.WhatsAppPhoneNumber,
                    Email = userProfile.Email,
                    Id = userProfile.ContactId,
                    ErrorMessage = ex.Message
                };
            }
            catch (InvalidTeamAssignmentException ex)
            {
                return new UserProfileNoCompanyResponse
                {
                    PhoneNumber = userProfile.WhatsAppPhoneNumber,
                    Email = userProfile.Email,
                    Id = userProfile.ContactId,
                    ErrorMessage = ex.Message
                };
            }
            catch (ArgumentException argumentException)
            {
                return new UserProfileNoCompanyResponse
                {
                    PhoneNumber = userProfile.WhatsAppPhoneNumber,
                    Email = userProfile.Email,
                    Id = userProfile.ContactId,
                    ErrorMessage = argumentException.Message
                };
            }
            finally
            {
                if (myLock != null)
                {
                    await scopedLockService.ReleaseLockAsync(myLock);
                }

                if (whatsappPhoneNumberLock != null)
                {
                    await scopedLockService.ReleaseLockAsync(whatsappPhoneNumberLock);
                }
            }
        }


    }
}