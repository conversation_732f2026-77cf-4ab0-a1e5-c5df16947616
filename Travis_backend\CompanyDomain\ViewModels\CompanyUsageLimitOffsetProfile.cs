namespace Travis_backend.CompanyDomain.ViewModels;

public class CompanyUsageLimitOffsetProfile
{
    public bool IsEnabled { get; set; } = true;

    public int ApiCallLimitOffSet { get; set; } = 0;

    public int AgentsLimitOffset { get; set; } = 0;

    public int WhatsappInstanceLimitOffset { get; set; } = 0;

    public int ContactsLimitOffset { get; set; } = 0;

    public int AutomatedMessagesLimitOffset { get; set; } = 0;

    public int AutomationsLimitOffset { get; set; } = 0;

    public int ChannelLimitOffset { get; set; } = 0;
}