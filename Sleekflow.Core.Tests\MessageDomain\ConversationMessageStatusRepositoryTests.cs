using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Sleekflow.Core.Tests.Tools;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Repositories;

namespace Sleekflow.Core.Tests.MessageDomain;

[TestFixture]
public class ConversationMessageStatusRepositoryTests
{
    private Mock<IDbContextService> _dbContextServiceMock;
    private DbContextOptionsBuilder<ApplicationDbContext> _dbContextOptions;

    [SetUp]
    public void Setup()
    {
        var  _configuration = AppConfigure.InitConfiguration();
        _dbContextOptions = new DbContextOptionsBuilder<ApplicationDbContext>()
            .EnableSensitiveDataLogging()
            .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
            .UseSqlServer(
                _configuration.GetConnectionString("DefaultConnection")); // If you want to use UAT database, enable it

        _dbContextServiceMock = new Mock<IDbContextService>();
    }

    private ApplicationDbContext CreateDbContext()
    {
        return new ApplicationDbContext(_dbContextOptions.Options);
    }

    [Test]
    [TestCase(MessageStatus.Sending, MessageStatus.Sent)]
    [TestCase(MessageStatus.Sending, MessageStatus.Received)]
    [TestCase(MessageStatus.Sending, MessageStatus.Read)]
    [TestCase(MessageStatus.Sending, MessageStatus.Deleted)]
    [TestCase(MessageStatus.Sending, MessageStatus.Failed)]
    [TestCase(MessageStatus.Sending, MessageStatus.Undelivered)]
    [TestCase(MessageStatus.Sent, MessageStatus.Received)]
    [TestCase(MessageStatus.Sent, MessageStatus.Read)]
    [TestCase(MessageStatus.Sent, MessageStatus.Deleted)]
    [TestCase(MessageStatus.Sent, MessageStatus.Failed)]
    [TestCase(MessageStatus.Sent, MessageStatus.Undelivered)]
    [TestCase(MessageStatus.Received, MessageStatus.Read)]
    [TestCase(MessageStatus.Received, MessageStatus.Deleted)]
    [TestCase(MessageStatus.Received, MessageStatus.Failed)]
    [TestCase(MessageStatus.Received, MessageStatus.Undelivered)]
    [TestCase(MessageStatus.Read, MessageStatus.Deleted)]
    [TestCase(MessageStatus.Read, MessageStatus.Failed)]
    [TestCase(MessageStatus.Read, MessageStatus.Undelivered)]
    public async Task UpdateConversationStatusWithConditionAsync_ShouldUpdateStatusCorrectly(
        MessageStatus initialStatus,
        MessageStatus newStatus)
    {
        // Arrange
        var testConversationMessage = new ConversationMessage
        {
            MessageUniqueID = $"{nameof(UpdateConversationStatusWithConditionAsync_ShouldUpdateStatusCorrectly)}_MessageUniqueID",
            Status = initialStatus
        };
        using (var context = CreateDbContext())
        {
            context.ConversationMessages.Add(testConversationMessage);
            await context.SaveChangesAsync();
        }

        var service = new ConversationMessageStatusRepository(
            new NullLogger<ConversationMessageStatusRepository>(),
            _dbContextServiceMock.Object);
        _dbContextServiceMock.Setup(m => m.GetDbContext()).Returns(CreateDbContext());

        // Update the status
        using (var context = CreateDbContext())
        {
            _dbContextServiceMock.Setup(m => m.GetDbContext()).Returns(context);

            // Act
            var result = await service.UpdateConversationStatusWithConditionAsync(testConversationMessage.Id, newStatus);

            // Assert
            Assert.IsTrue(result);
            var updatedMessage = await context.ConversationMessages.FindAsync(testConversationMessage.Id);

            Assert.That(updatedMessage, Is.Not.Null);
            Assert.That(updatedMessage.Status, Is.EqualTo(newStatus));
        }
    }

    [Test]
    [TestCase(MessageStatus.Sent, MessageStatus.Sending)]
    [TestCase(MessageStatus.Sent, MessageStatus.Scheduled)]
    [TestCase(MessageStatus.Received, MessageStatus.Sending)]
    [TestCase(MessageStatus.Received, MessageStatus.Sent)]
    [TestCase(MessageStatus.Received, MessageStatus.Scheduled)]
    [TestCase(MessageStatus.Read, MessageStatus.Sending)]
    [TestCase(MessageStatus.Read, MessageStatus.Sent)]
    [TestCase(MessageStatus.Read, MessageStatus.Received)]
    [TestCase(MessageStatus.Read, MessageStatus.Scheduled)]
    public async Task UpdateConversationStatusWithConditionAsync_ShouldNotUpdateStatusCorrectly(
        MessageStatus initialStatus,
        MessageStatus newStatus)
    {
        // Arrange
        var testConversationMessage = new ConversationMessage
        {
            MessageUniqueID = $"{nameof(UpdateConversationStatusWithConditionAsync_ShouldUpdateStatusCorrectly)}_MessageUniqueID",
            Status = initialStatus
        };
        using (var context = CreateDbContext())
        {
            context.ConversationMessages.Add(testConversationMessage);
            await context.SaveChangesAsync();
        }

        var service = new ConversationMessageStatusRepository(
            new NullLogger<ConversationMessageStatusRepository>(),
            _dbContextServiceMock.Object);
        _dbContextServiceMock.Setup(m => m.GetDbContext()).Returns(CreateDbContext());

        // Update the status
        using (var context = CreateDbContext())
        {
            _dbContextServiceMock.Setup(m => m.GetDbContext()).Returns(context);

            // Act
            var result = await service.UpdateConversationStatusWithConditionAsync(testConversationMessage.Id, newStatus);

            // Assert
            Assert.IsFalse(result);
            var updatedMessage = await context.ConversationMessages.FindAsync(testConversationMessage.Id);

            Assert.That(updatedMessage, Is.Not.Null);
            Assert.That(updatedMessage.Status, Is.EqualTo(initialStatus));
        }
    }

    [TearDown]
    public async Task TearDown()
    {
        await RemoveTestMessages();
    }

    private async Task RemoveTestMessages()
    {
        using (var context = CreateDbContext())
        {
            await context.ConversationMessages
                .Where(x =>
                    x.MessageUniqueID == $"{nameof(UpdateConversationStatusWithConditionAsync_ShouldUpdateStatusCorrectly)}_MessageUniqueID"
                    || x.MessageUniqueID == $"{nameof(UpdateConversationStatusWithConditionAsync_ShouldNotUpdateStatusCorrectly)}_MessageUniqueID")
                .ExecuteDeleteAsync();
        }
    }
}
