using System.Collections.Generic;
using Newtonsoft.Json;
using Travis_backend.AnalyticsDomain.Models;

namespace Travis_backend.AnalyticsDomain.ViewModels;

public class TopicCommonMetricViewModel
{
    public string TopicId { get; set; }

    [JsonProperty(NullValueHandling = NullValueHandling.Include)]
    public int? Rank { get; set; }
    public List<TopicAnalyticsMetricDto> DailyLogs { get; set; }

    public TopicAnalyticsMetricDto Summary { get; set; }

    public TopicCommonMetricViewModel(
        string topicId,
        int? rank,
        List<TopicAnalyticsMetricDto> dailyLogs,
        TopicAnalyticsMetricDto summary)
    {
        TopicId = topicId;
        Rank = rank;
        DailyLogs = dailyLogs;
        Summary = summary;
    }
}