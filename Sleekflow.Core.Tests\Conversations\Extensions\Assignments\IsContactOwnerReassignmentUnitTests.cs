using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Extensions.Assignments;

[TestFixture]
public class IsContactOwnerReassignmentUnitTests
{
    [Test]
    public void IsContactOwnerReassignment_ShouldReturnFalse_WhenConversationIsNull()
    {
        Conversation conversation = null;
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 1 };

        bool result = conversation.IsContactOwnerReassignment(newContactOwner);

        Assert.IsFalse(result);
    }

    [Test]
    public void IsContactOwnerReassignment_ShouldReturnFalse_WhenAssigneeIdIsNull()
    {
        var conversation = new Conversation { AssigneeId = null };
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 1 };

        bool result = conversation.IsContactOwnerReassignment(newContactOwner);

        Assert.IsFalse(result);
    }

    [Test]
    public void IsContactOwnerReassignment_ShouldReturnFalse_WhenNewContactOwnerStaffIdIsNull()
    {
        var conversation = new Conversation { AssigneeId = 1 };
        StaffAccessControlAggregate newContactOwner = null;

        bool result = conversation.IsContactOwnerReassignment(newContactOwner);

        Assert.IsFalse(result);
    }

    [Test]
    public void IsContactOwnerReassignment_ShouldReturnFalse_WhenAssigneeIdEqualsNewContactOwnerStaffId()
    {
        var conversation = new Conversation { AssigneeId = 1 };
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 1 };

        bool result = conversation.IsContactOwnerReassignment(newContactOwner);

        Assert.IsFalse(result);
    }

    [Test]
    public void IsContactOwnerReassignment_ShouldReturnTrue_WhenAssigneeIdNotEqualsNewContactOwnerStaffId()
    {
        var conversation = new Conversation { AssigneeId = 1 };
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 2 };

        bool result = conversation.IsContactOwnerReassignment(newContactOwner);

        Assert.IsTrue(result);
    }
}