﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Enums;
using Travis_backend.FlowHubs.Models;
using Travis_backend.Utils;

namespace Sleekflow.Core.Tests.Utils;

public class JsonUtilTests
{
    [Test]
    public void GenerateSampleObjectFromType_Should_ReturnSampleObject()
    {
        // Act
        var testObject = JsonUtils.GenerateSampleObjectFromType<TestObject>();

        // Assert
        Assert.Multiple(
            () =>
            {
                // Check values of basic properties populated by NJsonSchema
                Assert.That(testObject, Is.Not.Null);
                Assert.That(testObject.ContactId, Is.Not.Null.And.EqualTo("ContactId"));
                Assert.That(testObject.Channel, Is.Not.Null.And.EqualTo("Channel"));
                Assert.That(testObject.ChannelId, Is.Not.Null.And.EqualTo("ChannelId"));
                Assert.That(testObject.ConversationId, Is.Not.Null.And.EqualTo("ConversationId"));
                Assert.That(testObject.MessageId, Is.Not.Null.And.EqualTo("MessageId"));
                Assert.That(testObject.MessageUniqueId, Is.Not.Null.And.EqualTo("MessageUniqueId"));
                Assert.That(testObject.Contact, Is.Not.Null.And.Empty);
                Assert.That(testObject.TextMessage, Is.Not.Null);
                Assert.That(testObject.TextMessage.Text, Is.Not.Null.And.EqualTo("Text"));
            });
    }

    [Test]
    public void SimplifyJsonData_Should_ReturnSimplifiedJsonData()
    {
        // Arrange
        var testObject = JsonUtils.GenerateSampleObjectFromType<TestObject>();

        testObject.Contact = new Dictionary<string, object>()
        {
            ["first_name"] = "John",
            ["last_name"] = "Doe",
            ["email"] = "<EMAIL>",
            ["labels"] = new List<LabelData>()
            {
                new LabelData("normal label", HashTagColor.Cyan.ToString(), HashTagType.Normal.ToString()),
                new LabelData("shopify label", HashTagColor.Red.ToString(), HashTagType.Shopify.ToString())
            },
            ["Last Channel"] = "whatsappcloudapi"
        };

        string expectedJson =
            $$"""{"contact_id":"ContactId","channel":"Channel","channel_id":"ChannelId","conversation_id":"ConversationId","message_id":"MessageId","message_unique_id":"MessageUniqueId","contact":{"first_name":"John","last_name":"Doe","email":"<EMAIL>","labels":[{"LabelValue":"shopify label","LabelColor":"Red","LabelType":"Shopify"}],"Last Channel":"whatsappcloudapi"},"text_message":{"text":"Text"},"created_at":"{{testObject.CreatedAt.LocalDateTime:yyyy-MM-ddTHH:mm:ss.fffZ}}"}""";

        // Act
        var jToken = JsonUtils.SimplifyJsonData(testObject.ToJson());
        var simplifiedJson = jToken.ToString(Formatting.None);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(jToken, Is.Not.Null);
                Assert.That(simplifiedJson, Is.EqualTo(expectedJson));
            });
    }

    [Test]
    public void GetFieldMetadata_Should_ReturnAllFieldMetadata_WithUiCopy()
    {
        // Arrange
        var testObject = JsonUtils.GenerateSampleObjectFromType<TestObject>();

        var expectedFieldMetadata = new HashSet<FieldMetadata>()
        {
            new FieldMetadata(
                "contact_id",
                "contact_id",
                JTokenType.String.ToString(),
                null,
                null,
                "ContactId",
                string.Empty),
            new FieldMetadata("channel", "channel", JTokenType.String.ToString(), null, null, "Channel", string.Empty),
            new FieldMetadata(
                "channel_id",
                "channel_id",
                JTokenType.String.ToString(),
                null,
                null,
                "ChannelId",
                string.Empty),
            new FieldMetadata(
                "conversation_id",
                "conversation_id",
                JTokenType.String.ToString(),
                null,
                null,
                "ConversationId",
                string.Empty),
            new FieldMetadata(
                "message_id",
                "message_id",
                JTokenType.String.ToString(),
                null,
                null,
                "MessageId",
                string.Empty),
            new FieldMetadata(
                "message_unique_id",
                "message_unique_id",
                JTokenType.String.ToString(),
                null,
                null,
                "MessageUniqueId",
                string.Empty),
            new FieldMetadata(
                "contact.first_name",
                "contact/first_name",
                JTokenType.String.ToString(),
                null,
                null,
                "John",
                string.Empty),
            new FieldMetadata(
                "contact.last_name",
                "contact/last_name",
                JTokenType.String.ToString(),
                null,
                null,
                "Doe",
                string.Empty),
            new FieldMetadata(
                "contact.email",
                "contact/email",
                JTokenType.String.ToString(),
                null,
                null,
                "<EMAIL>",
                string.Empty),
            new FieldMetadata(
                "contact.labels[*].LabelValue",
                "contact/labels/[*]/LabelValue",
                JTokenType.String.ToString(),
                null,
                null,
                "shopify label",
                string.Empty),
            new FieldMetadata(
                "contact.labels[*].LabelColor",
                "contact/labels/[*]/LabelColor",
                JTokenType.String.ToString(),
                null,
                null,
                "Red",
                string.Empty),
            new FieldMetadata(
                "contact.labels[*].LabelType",
                "contact/labels/[*]/LabelType",
                JTokenType.String.ToString(),
                null,
                null,
                "Shopify",
                string.Empty),
            new FieldMetadata(
                "contact['Last Channel']",
                "contact/Last Channel",
                JTokenType.String.ToString(),
                null,
                null,
                "whatsappcloudapi",
                string.Empty),
            new FieldMetadata(
                "contact['Last Order Item Count']",
                "contact/Last Order Item Count",
                JTokenType.Integer.ToString(),
                null,
                null,
                10,
                string.Empty),
            new FieldMetadata(
                "contact['Last Order Amount']",
                "contact/Last Order Amount",
                JTokenType.Float.ToString(),
                null,
                null,
                352.13f,
                string.Empty),
            new FieldMetadata(
                "contact['Last Order Date']",
                "contact/Last Order Date",
                JTokenType.Date.ToString(),
                null,
                null,
                "2024-10-22T13:38:49.312Z",
                string.Empty),
            new FieldMetadata(
                "text_message.text",
                "text_message/text",
                JTokenType.String.ToString(),
                null,
                null,
                "Text",
                string.Empty),
            new FieldMetadata(
                "created_at",
                "created_at",
                JTokenType.Date.ToString(),
                null,
                null,
                testObject.CreatedAt.LocalDateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                string.Empty)
        };

        testObject.Contact = new Dictionary<string, object>()
        {
            ["first_name"] = "John",
            ["last_name"] = "Doe",
            ["email"] = "<EMAIL>",
            ["labels"] = new List<LabelData>()
            {
                new LabelData("normal label", HashTagColor.Cyan.ToString(), HashTagType.Normal.ToString()),
                new LabelData("shopify label", HashTagColor.Red.ToString(), HashTagType.Shopify.ToString())
            },
            ["Last Channel"] = "whatsappcloudapi",
            ["Last Order Item Count"] = 10,
            ["Last Order Amount"] = 352.13f,
            ["Last Order Date"] = "2024-10-22T13:38:49.312Z"
        };

        // Act
        var jToken = JsonUtils.SimplifyJsonData(testObject.ToJson());
        var fieldMetadata = JsonUtils.GetFieldsMetadata(jToken);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(jToken, Is.Not.Null);
                Assert.That(fieldMetadata, Is.Not.Empty);
                Assert.That(fieldMetadata, Has.Count.EqualTo(expectedFieldMetadata.Count));

                foreach (var fm in fieldMetadata)
                {
                    var expectedField = expectedFieldMetadata.FirstOrDefault(f => f.FieldPath == fm.FieldPath);

                    Assert.That(expectedField, Is.Not.Null);
                    Assert.That(fm.DisplayPath, Is.EqualTo(expectedField!.DisplayPath));
                    Assert.That(fm.FieldType, Is.EqualTo(expectedField.FieldType));
                    Assert.That(fm.SampleValue, Is.EqualTo(expectedField.SampleValue));
                    Assert.That(fm.UiCopy, Is.EqualTo(string.Empty)); // Default UiCopy should be empty
                }
            });
    }

    [Test]
    public void GetFilteredFieldMetadata_Should_ReturnOnlyAllowedFields()
    {
        // Arrange
        var testObject = JsonUtils.GenerateSampleObjectFromType<TestObject>();

        testObject.Contact = new Dictionary<string, object>()
        {
            ["first_name"] = "John",
            ["last_name"] = "Doe",
            ["email"] = "<EMAIL>",
            ["labels"] = new List<LabelData>()
            {
                new LabelData("shopify label", HashTagColor.Red.ToString(), HashTagType.Shopify.ToString())
            }
        };

        var allowedFieldPaths = new HashSet<string>
        {
            "contact_id", "contact.first_name", "contact.email", "contact.labels[*].LabelValue"
        };

        var expectedFieldMetadata = new HashSet<FieldMetadata>()
        {
            new FieldMetadata(
                "contact_id",
                "contact_id",
                JTokenType.String.ToString(),
                null,
                null,
                "ContactId",
                string.Empty),
            new FieldMetadata(
                "contact.first_name",
                "contact/first_name",
                JTokenType.String.ToString(),
                null,
                null,
                "John",
                string.Empty),
            new FieldMetadata(
                "contact.email",
                "contact/email",
                JTokenType.String.ToString(),
                null,
                null,
                "<EMAIL>",
                string.Empty),
            new FieldMetadata(
                "contact.labels[*].LabelValue",
                "contact/labels/[*]/LabelValue",
                JTokenType.String.ToString(),
                null,
                null,
                "shopify label",
                string.Empty)
        };

        // Act
        var jToken = JsonUtils.SimplifyJsonData(testObject.ToJson());
        var filteredFieldMetadata = JsonUtils.GetFilteredFieldMetadata(jToken, allowedFieldPaths);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(filteredFieldMetadata, Is.Not.Empty);
                Assert.That(filteredFieldMetadata, Has.Count.EqualTo(expectedFieldMetadata.Count));

                foreach (var fm in filteredFieldMetadata)
                {
                    var expectedField = expectedFieldMetadata.FirstOrDefault(f => f.FieldPath == fm.FieldPath);

                    Assert.That(expectedField, Is.Not.Null, $"Field {fm.FieldPath} should be in expected results");
                    Assert.That(fm.DisplayPath, Is.EqualTo(expectedField!.DisplayPath));
                    Assert.That(fm.FieldType, Is.EqualTo(expectedField.FieldType));
                    Assert.That(fm.SampleValue, Is.EqualTo(expectedField.SampleValue));
                    Assert.That(fm.UiCopy, Is.EqualTo(string.Empty)); // GetFilteredFieldMetadata returns empty UiCopy
                }

                // Verify that non-allowed fields are not included
                Assert.That(filteredFieldMetadata.Any(f => f.FieldPath == "channel"), Is.False);
                Assert.That(filteredFieldMetadata.Any(f => f.FieldPath == "contact.last_name"), Is.False);
                Assert.That(filteredFieldMetadata.Any(f => f.FieldPath == "text_message.text"), Is.False);
            });
    }

    [Test]
    public void GetFilteredFieldMetadata_Should_ReturnEmptySet_WhenNoAllowedPaths()
    {
        // Arrange
        var testObject = JsonUtils.GenerateSampleObjectFromType<TestObject>();

        testObject.Contact = new Dictionary<string, object>()
        {
            ["first_name"] = "John", ["email"] = "<EMAIL>"
        };

        var allowedFieldPaths = new HashSet<string>(); // Empty allowed paths

        // Act
        var jToken = JsonUtils.SimplifyJsonData(testObject.ToJson());
        var filteredFieldMetadata = JsonUtils.GetFilteredFieldMetadata(jToken, allowedFieldPaths);

        // Assert
        Assert.That(filteredFieldMetadata, Is.Empty);
    }

    [Test]
    public void GetFilteredFieldMetadata_Should_HandleNestedObjects()
    {
        // Arrange
        var testObject = JsonUtils.GenerateSampleObjectFromType<TestObject>();

        testObject.Contact = new Dictionary<string, object>()
        {
            ["first_name"] = "John",
            ["nested_object"] = new Dictionary<string, object>
            {
                ["property1"] = "value1", ["property2"] = "value2"
            }
        };

        var allowedFieldPaths = new HashSet<string>
        {
            "contact.nested_object.property1"
        };

        // Act
        var jToken = JsonUtils.SimplifyJsonData(testObject.ToJson());
        var filteredFieldMetadata = JsonUtils.GetFilteredFieldMetadata(jToken, allowedFieldPaths);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(filteredFieldMetadata, Has.Count.EqualTo(1));
                var field = filteredFieldMetadata.First();
                Assert.That(field.FieldPath, Is.EqualTo("contact.nested_object.property1"));
                Assert.That(field.SampleValue, Is.EqualTo("value1"));
            });
    }

    [Test]
    public void GetFilteredFieldMetadata_Should_HandleArrays()
    {
        // Arrange
        var testObject = JsonUtils.GenerateSampleObjectFromType<TestObject>();

        testObject.Contact = new Dictionary<string, object>()
        {
            ["labels"] = new List<LabelData>()
            {
                new LabelData("label1", HashTagColor.Red.ToString(), HashTagType.Normal.ToString()),
                new LabelData("label2", HashTagColor.Blue.ToString(), HashTagType.Shopify.ToString())
            }
        };

        var allowedFieldPaths = new HashSet<string>
        {
            "contact.labels[*].LabelValue", "contact.labels[*].LabelColor"
        };

        // Act
        var jToken = JsonUtils.SimplifyJsonData(testObject.ToJson());
        var filteredFieldMetadata = JsonUtils.GetFilteredFieldMetadata(jToken, allowedFieldPaths);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(filteredFieldMetadata, Has.Count.EqualTo(2));
                Assert.That(filteredFieldMetadata.Any(f => f.FieldPath == "contact.labels[*].LabelValue"), Is.True);
                Assert.That(filteredFieldMetadata.Any(f => f.FieldPath == "contact.labels[*].LabelColor"), Is.True);
                Assert.That(filteredFieldMetadata.Any(f => f.FieldPath == "contact.labels[*].LabelType"), Is.False);
            });
    }

    [Test]
    public void GetFilteredFieldMetadata_WithUiCopyApplication_Should_ReturnCorrectUiCopy()
    {
        // Arrange
        var testObject = JsonUtils.GenerateSampleObjectFromType<TestObject>();

        testObject.Contact = new Dictionary<string, object>()
        {
            ["first_name"] = "John",
            ["last_name"] = "Doe",
            ["email"] = "<EMAIL>",
            ["labels"] = new List<LabelData>()
            {
                new LabelData("shopify label", HashTagColor.Red.ToString(), HashTagType.Shopify.ToString())
            }
        };

        var allowedFieldPaths = new HashSet<string>
        {
            "contact_id", "contact.first_name", "contact.email", "contact.labels[*].LabelValue"
        };

        // Mock UI copy mapping (this would come from your configuration service)
        var uiCopyMapping = new Dictionary<string, string>
        {
            ["contact_id"] = "Contact ID updated",
            ["contact.first_name"] = "First name updated",
            ["contact.email"] = "Email updated",
            ["contact.labels[*].LabelValue"] = "Label updated"
        };

        // Act
        var jToken = JsonUtils.SimplifyJsonData(testObject.ToJson());
        var filteredFieldMetadata = JsonUtils.GetFilteredFieldMetadata(jToken, allowedFieldPaths);

        // Apply UI copy mapping (simulating what the base generator does)
        foreach (var metadata in filteredFieldMetadata)
        {
            if (uiCopyMapping.TryGetValue(metadata.FieldPath, out var uiCopy))
            {
                metadata.UiCopy = uiCopy;
            }
            else
            {
                metadata.UiCopy = metadata.DisplayPath; // Fallback
            }
        }

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(filteredFieldMetadata, Is.Not.Empty);
                Assert.That(filteredFieldMetadata, Has.Count.EqualTo(4));

                foreach (var fm in filteredFieldMetadata)
                {
                    if (fm.FieldPath == "contact_id")
                    {
                        Assert.That(fm.UiCopy, Is.EqualTo("Contact ID updated"));
                    }
                    else if (fm.FieldPath == "contact.first_name")
                    {
                        Assert.That(fm.UiCopy, Is.EqualTo("First name updated"));
                    }
                    else if (fm.FieldPath == "contact.email")
                    {
                        Assert.That(fm.UiCopy, Is.EqualTo("Email updated"));
                    }
                    else if (fm.FieldPath == "contact.labels[*].LabelValue")
                    {
                        Assert.That(fm.UiCopy, Is.EqualTo("Label updated"));
                    }
                }

                // Verify that non-allowed fields are not included
                Assert.That(filteredFieldMetadata.Any(f => f.FieldPath == "channel"), Is.False);
                Assert.That(filteredFieldMetadata.Any(f => f.FieldPath == "contact.last_name"), Is.False);
                Assert.That(filteredFieldMetadata.Any(f => f.FieldPath == "text_message.text"), Is.False);
            });
    }

    private class TestObject
    {
        [JsonProperty("contact_id")]
        public string ContactId { get; set; }

        [JsonProperty("channel")]
        public string Channel { get; set; }

        [JsonProperty("channel_id")]
        public string ChannelId { get; set; }

        [JsonProperty("conversation_id")]
        public string ConversationId { get; set; }

        [JsonProperty("message_id")]
        public string MessageId { get; set; }

        [JsonProperty("message_unique_id")]
        public string MessageUniqueId { get; set; }

        [JsonProperty("contact")]
        public Dictionary<string, object> Contact { get; set; }

        [JsonProperty("text_message")]
        public TextMessageObject TextMessage { get; set; }

        [JsonProperty("created_at")]
        public DateTimeOffset CreatedAt { get; set; }

        [JsonConstructor]
        public TestObject(
            string contactId,
            string channel,
            string channelId,
            string conversationId,
            string messageId,
            string messageUniqueId,
            Dictionary<string, object> contact,
            TextMessageObject textMessage,
            DateTimeOffset createdAt)
        {
            ContactId = contactId;
            Channel = channel;
            ChannelId = channelId;
            ConversationId = conversationId;
            MessageId = messageId;
            MessageUniqueId = messageUniqueId;
            Contact = contact;
            TextMessage = textMessage;
            CreatedAt = createdAt;
        }

        public string ToJson()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }
    }
}