namespace Sleekflow.Core.Infra.Constants;

public static class ServiceNames
{
    public const string SleekPay = "SleekPay";
    public const string SleekflowCore = "SleekflowCore";
    public const string SleekflowPowerflow = "SleekflowPowerflow";
    public const string SleekflowCoreWorker = "SleekflowCoreWorker";

    public static string GetShortName(string serviceName)
    {
        return serviceName switch
        {
            SleekPay => "sleek-pay",
            SleekflowCore => "core",
            SleekflowPowerflow => "powerflow",
            SleekflowCoreWorker => "core-worker",
            _ => throw new Exception("ServiceNames")
        };
    }

    public static string GetSleekflowPrefixedShortName(string serviceName)
    {
        return $"sleekflow-{GetShortName(serviceName)}";
    }
}