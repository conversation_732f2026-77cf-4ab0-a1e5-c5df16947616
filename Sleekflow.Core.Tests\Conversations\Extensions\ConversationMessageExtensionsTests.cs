using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.MessageDomain.Models;
using Travis_backend.Constants;

namespace Sleekflow.Core.Tests.Conversations.Extensions
{
    [TestFixture]
    public class ConversationMessageExtensionsTests
    {
        [Test]
        public void IsDefaultChannelMessage_ShouldReturnFalse_WhenConversationMessageIsNull()
        {
            // Arrange
            ConversationMessage conversationMessage = null;
            var staff = new StaffAccessControlAggregate();

            // Act
            var result = conversationMessage.IsDefaultChannelMessage(staff);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public void IsDefaultChannelMessage_ShouldReturnFalse_WhenAssociatedTeamsIsNull()
        {
            // Arrange
            var conversationMessage = new ConversationMessage();
            var staff = new StaffAccessControlAggregate
            {
                AssociatedTeams = null
            };

            // Act
            var result = conversationMessage.IsDefaultChannelMessage(staff);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public void IsDefaultChannelMessage_ShouldReturnFalse_WhenAllTeamsHaveNullTeamDefaultChannels()
        {
            // Arrange
            var conversationMessage = new ConversationMessage { ChannelIdentityId = "channel1" };
            var staff = new StaffAccessControlAggregate
            {
                AssociatedTeams = new List<TeamAccessControlAggregate>
                {
                    new TeamAccessControlAggregate { TeamDefaultChannels = null },
                    new TeamAccessControlAggregate { TeamDefaultChannels = null }
                }
            };

            // Act
            var result = conversationMessage.IsDefaultChannelMessage(staff);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public void IsDefaultChannelMessage_ShouldReturnTrue_WhenChannelIdentityIdIsInDefaultChannels()
        {
            // Arrange
            var conversationMessage = new ConversationMessage { ChannelIdentityId = "channel1" };
            var staff = new StaffAccessControlAggregate
            {
                AssociatedTeams = new List<TeamAccessControlAggregate>
                {
                    new TeamAccessControlAggregate
                    {
                        TeamDefaultChannels = new TeamDefaultChannels
                        {
                            ChannelTypeToChannelIdentityIds = new Dictionary<string, List<string>>
                            {
                                { ChannelTypes.WhatsappTwilio, new List<string> { "channel1" } }
                            }
                        }
                    }
                }
            };

            // Act
            var result = conversationMessage.IsDefaultChannelMessage(staff);

            // Assert
            Assert.IsTrue(result);
        }

        [Test]
        public void IsDefaultChannelMessage_ShouldReturnFalse_WhenChannelIdentityIdIsNotInDefaultChannels()
        {
            // Arrange
            var conversationMessage = new ConversationMessage { ChannelIdentityId = "channel2" };
            var staff = new StaffAccessControlAggregate
            {
                AssociatedTeams = new List<TeamAccessControlAggregate>
                {
                    new TeamAccessControlAggregate
                    {
                        TeamDefaultChannels = new TeamDefaultChannels
                        {
                            ChannelTypeToChannelIdentityIds = new Dictionary<string, List<string>>
                            {
                                { ChannelTypes.WhatsappTwilio, new List<string> { "channel1" } }
                            }
                        }
                    }
                }
            };

            // Act
            var result = conversationMessage.IsDefaultChannelMessage(staff);

            // Assert
            Assert.IsFalse(result);
        }
    }
}