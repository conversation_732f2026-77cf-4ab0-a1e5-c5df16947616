using System.Text;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Sleekflow.Powerflow.Apis.Services;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.Configuration;
using Travis_backend.SleekflowCrmHubDomain.Filters;
using Travis_backend.SleekflowCrmHubDomain.Services;

namespace Sleekflow.Powerflow.Apis.Controllers;

[TypeFilter(typeof(CrmHubExceptionFilter))]
[Route("/internal/crm-hub/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
#if DEBUG
[AllowAnonymous]
#endif
public class InternalCrmHubController : InternalControllerBase
{
    private readonly ISchemafulObjectsApi _schemafulObjectsApi;
    private readonly IInternalCrmHubService _internalCrmHubService;
    private readonly ICrmHubConfigsApi _crmHubConfigsApi;
    private readonly IUserRoleStaffRepository _userRoleStaffRepository;
    private readonly ICrmHubService _crmHubService;

    public InternalCrmHubController(
        UserManager<ApplicationUser> userManager,
        ISchemafulObjectsApi schemafulObjectsApi,
        IInternalCrmHubService internalCrmHubService,
        ICrmHubConfigsApi crmHubConfigsApi,
        IUserRoleStaffRepository userRoleStaffRepository,
        ICrmHubService crmHubService)
        : base(userManager)
    {
        _schemafulObjectsApi = schemafulObjectsApi;
        _internalCrmHubService = internalCrmHubService;
        _crmHubConfigsApi = crmHubConfigsApi;
        _userRoleStaffRepository = userRoleStaffRepository;
        _crmHubService = crmHubService;
    }

    public record GetSchemafulObjectCsvTemplateRequest(string SleekflowCompanyId, string SchemaId);

    [HttpPost]
    [Produces("text/csv")]
    public async Task<IActionResult> GetSchemafulObjectCsvTemplate(
        [FromBody] GetSchemafulObjectCsvTemplateRequest request)
    {
        var getSchemafulObjectCsvTemplateOutputOutput =
            await _schemafulObjectsApi.SchemafulObjectsGetSchemafulObjectCsvTemplatePostAsync(
                getSchemafulObjectCsvTemplateInput: new GetSchemafulObjectCsvTemplateInput(
                    request.SchemaId,
                    request.SleekflowCompanyId));

        return File(
            Encoding.UTF8.GetBytes(getSchemafulObjectCsvTemplateOutputOutput.Data.CsvString),
            "text/csv",
            "csv_template.csv");
    }

    public record ProcessSchemafulObjectCsvBatchRequest(
        IFormFile File,
        string CompanyId,
        string SchemaId,
        int BatchSize = 100);

    public record ProcessSchemafulObjectCsvBatchResponse(
        PublicBlob RawCsvPublicBlob,
        PublicBlob LoggerCsvPublicBlob,
        string BackGroundJobId
    );

    [HttpPost]
    public async Task<ActionResult<ProcessSchemafulObjectCsvBatchResponse>> ProcessSchemafulObjectCsvBatch(
        [FromForm]
        ProcessSchemafulObjectCsvBatchRequest request)
    {
        var processedFile = await _internalCrmHubService.PreprocessSchemafulObjectCsvAsync(
            request.File,
            request.CompanyId,
            request.SchemaId);

        // upload file to share hub, get blob download sas uri
        var publicBlobForDownLoad = await _internalCrmHubService.UploadFileAndGetPublicBlobAsync(
            request.CompanyId,
            processedFile);

        var backgroundJobId = BackgroundJob.Enqueue(() => _internalCrmHubService.ProcessSchemafulObjectCsvBatchAsync(
            request.SchemaId,
            request.CompanyId,
            publicBlobForDownLoad,
            request.BatchSize));

        // todo implement csv logger in next phase
        return Ok(new ProcessSchemafulObjectCsvBatchResponse(
            publicBlobForDownLoad,
            null,
            backgroundJobId));
    }

    public record UpdateCrmHubConfigRequest(
        string CompanyId,
        UsageLimit UsageLimit,
        FeatureAccessibilitySettings FeatureAccessibilitySettings);

    [HttpPost]
    public async Task<ActionResult<CrmHubConfig>> UpdateCrmHubConfig(
        [FromBody]
        UpdateCrmHubConfigRequest updateCrmHubConfigRequest)
    {
        var getCrmHubConfigOutputOutput =
            await _crmHubConfigsApi.CrmHubConfigsGetCrmHubConfigPostAsync(
                getCrmHubConfigInput: new GetCrmHubConfigInput(updateCrmHubConfigRequest.CompanyId));

        var crmHubConfig = getCrmHubConfigOutputOutput.Data.CrmHubConfig;

        if (crmHubConfig.Id == "NOT_INITIALIZED")
        {
            return BadRequest("CrmHubConfig is not initialized");
        }

        var staff = await _userRoleStaffRepository.GetFirstAdmin(updateCrmHubConfigRequest.CompanyId);

        var updateCrmHubConfigOutputOutput =
            await _crmHubConfigsApi.CrmHubConfigsUpdateCrmHubConfigPostAsync(
                updateCrmHubConfigInput: new UpdateCrmHubConfigInput(
                    crmHubConfig.Id,
                    updateCrmHubConfigRequest.CompanyId,
                    updateCrmHubConfigRequest.UsageLimit,
                    updateCrmHubConfigRequest.FeatureAccessibilitySettings,
                    staff?.Id.ToString() ?? string.Empty));

        return Ok(updateCrmHubConfigOutputOutput.Data.CrmHubConfig);
    }

    public record RefreshCrmHubConfigRequest(string CompanyId);

    [HttpPost]
    public async Task<ActionResult<CrmHubConfig>> GetAndRefreshCrmHubConfig(
        [FromBody]
        RefreshCrmHubConfigRequest refreshCrmHubConfigRequest)
    {
        var staff = await _userRoleStaffRepository.GetFirstAdmin(refreshCrmHubConfigRequest.CompanyId);

        var getCrmHubConfigOutputOutput =
            await _crmHubConfigsApi.CrmHubConfigsGetCrmHubConfigPostAsync(
                getCrmHubConfigInput: new GetCrmHubConfigInput(refreshCrmHubConfigRequest.CompanyId));

        if (await _crmHubService.IsCrmHubConfigRefreshed(
                refreshCrmHubConfigRequest.CompanyId,
                getCrmHubConfigOutputOutput.Data.CrmHubConfig))
        {
            return Ok(getCrmHubConfigOutputOutput.Data.CrmHubConfig);
        }

        await _crmHubService.RefreshCrmHubConfigAsync(refreshCrmHubConfigRequest.CompanyId, staff);

        getCrmHubConfigOutputOutput =
            await _crmHubConfigsApi.CrmHubConfigsGetCrmHubConfigPostAsync(
                getCrmHubConfigInput: new GetCrmHubConfigInput(refreshCrmHubConfigRequest.CompanyId));

        return Ok(getCrmHubConfigOutputOutput.Data.CrmHubConfig);
    }

    public record UpdateCrmHubConfigUsageLimitOffsetRequest(
        string CompanyId,
        UsageLimitOffset UsageLimitOffset);

    [HttpPost]
    public async Task<ActionResult<CrmHubConfig>> UpdateCrmHubConfigUsageLimitOffset(
        [FromBody]
        UpdateCrmHubConfigUsageLimitOffsetRequest request)
    {
        var getCrmHubConfigOutputOutput =
            await _crmHubConfigsApi.CrmHubConfigsGetCrmHubConfigPostAsync(
                getCrmHubConfigInput: new GetCrmHubConfigInput(request.CompanyId));

        var crmHubConfig = getCrmHubConfigOutputOutput.Data.CrmHubConfig;

        if (crmHubConfig.Id == "NOT_INITIALIZED")
        {
            return BadRequest("CrmHubConfig is not initialized");
        }

        var updateCrmHubConfigUsageLimitOffsetOutputOutput =
            await _crmHubConfigsApi.CrmHubConfigsUpdateCrmHubConfigUsageLimitOffsetPostAsync(
                updateCrmHubConfigUsageLimitOffsetInput: new UpdateCrmHubConfigUsageLimitOffsetInput(
                    crmHubConfig.Id,
                    request.CompanyId,
                    request.UsageLimitOffset));

        return Ok(updateCrmHubConfigUsageLimitOffsetOutputOutput.Data.CrmHubConfig);
    }
}