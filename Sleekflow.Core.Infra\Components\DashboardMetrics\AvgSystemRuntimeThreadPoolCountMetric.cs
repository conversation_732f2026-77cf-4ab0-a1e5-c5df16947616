﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;
using Sleekflow.Core.Infra.Utils;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class AvgSystemRuntimeThreadPoolCountMetric : IDashboardMetric
{
    private readonly Output<string>? _sleekflowCoreName;
    private readonly Output<string>? _sleekflowCoreAppInsightResourceId;
    private readonly Output<string>? _sleekflowCoreAppInsightName;

    public AvgSystemRuntimeThreadPoolCountMetric(
        Output<string>? sleekflowCoreName,
        Output<string>? sleekflowCoreAppInsightResourceId,
        Output<string>? sleekflowCoreAppInsightName)
    {
        _sleekflowCoreName = sleekflowCoreName;
        _sleekflowCoreAppInsightResourceId = sleekflowCoreAppInsightResourceId;
        _sleekflowCoreAppInsightName = sleekflowCoreAppInsightName;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs =
                    new[]
                    {
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "options"
                            },
                            {
                                "value", new Dictionary<string, object>()
                                {
                                    {
                                        "chart", new Dictionary<string, object>()
                                        {
                                            {
                                                "filterCollection", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "filters", new[]
                                                        {
                                                            new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "key", "cloud/roleName"
                                                                },
                                                                {
                                                                    "operator", 0
                                                                },
                                                                {
                                                                    "values", new[]
                                                                    {
                                                                        _sleekflowCoreName!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            {
                                                "grouping", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "dimension", "cloud/roleInstance"
                                                    },
                                                    {
                                                        "sort", 2
                                                    },
                                                    {
                                                        "top", 10
                                                    }
                                                }
                                            },
                                            {
                                                "metrics", new[]
                                                {
                                                    new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "aggregationType", 4
                                                        },
                                                        {
                                                            "metricVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "displayName",
                                                                    "System.Runtime|ThreadPool Thread Count"
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "name",
                                                            "customMetrics/System.Runtime|ThreadPool Thread Count"
                                                        },
                                                        {
                                                            "namespace", "microsoft.insights/components/kusto"
                                                        },
                                                        {
                                                            "resourceMetadata", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "id", _sleekflowCoreAppInsightResourceId!
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            {
                                                "timespan", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "grain", 2
                                                    },
                                                    {
                                                        "relative", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "duration", 259200000
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "showUTCTime", false
                                                    }
                                                }
                                            },
                                            {
                                                "title", Output.Tuple(
                                                        _sleekflowCoreAppInsightName!,
                                                        _sleekflowCoreName!)
                                                    .Apply(
                                                        a =>
                                                            $"Avg System.Runtime|ThreadPool Thread Count for {a.Item1!} by cloud/roleInstance where cloud/roleName = {a.Item2}")
                                            },
                                            {
                                                "titleKind", 1
                                            },
                                            {
                                                "visualization", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "axisVisualization", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "x", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "axisType", 2
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "y", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "axisType", 1
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "chartType", 2
                                                    },
                                                    {
                                                        "legendVisualization", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "hideHoverCard", false
                                                            },
                                                            {
                                                                "hideLabelNames", true
                                                            },
                                                            {
                                                                "hideSubtitle", false
                                                            },
                                                            {
                                                                "isVisible", true
                                                            },
                                                            {
                                                                "position", 2
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "sharedTimeRange"
                            },
                            {
                                "isOptional", true
                            }
                        }
                    },
                Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                Settings =
                {
                    new Dictionary<string, object>()
                    {
                        {
                            "content", new Dictionary<string, object>()
                            {
                                {
                                    "options", new Dictionary<string, object>()
                                    {
                                        {
                                            "chart", new Dictionary<string, object>()
                                            {
                                                {
                                                    "grouping", new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "dimension", "cloud/roleInstance"
                                                        },
                                                        {
                                                            "sort", 2
                                                        },
                                                        {
                                                            "top", 10
                                                        }
                                                    }
                                                },
                                                {
                                                    "metrics", new[]
                                                    {
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 4
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName",
                                                                        "System.Runtime|ThreadPool Thread Count"
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name",
                                                                "customMetrics/System.Runtime|ThreadPool Thread Count"
                                                            },
                                                            {
                                                                "namespace", "microsoft.insights/components/kusto"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreAppInsightResourceId!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    "title", Output.Tuple(
                                                            _sleekflowCoreAppInsightName!,
                                                            _sleekflowCoreName!)
                                                        .Apply(
                                                            a =>
                                                                $"Avg System.Runtime|ThreadPool Thread Count for {a.Item1!} by cloud/roleInstance where cloud/roleName = {a.Item2}")
                                                },
                                                {
                                                    "titleKind", 1
                                                },
                                                {
                                                    "visualization", new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "axisVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "x", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 2
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    "y", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 1
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "chartType", 2
                                                        },
                                                        {
                                                            "disablePinning", true
                                                        },
                                                        {
                                                            "legendVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "hideHoverCard", false
                                                                },
                                                                {
                                                                    "hideLabelNames", true
                                                                },
                                                                {
                                                                    "hideSubtitle", false
                                                                },
                                                                {
                                                                    "isVisible", true
                                                                },
                                                                {
                                                                    "position", 2
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}