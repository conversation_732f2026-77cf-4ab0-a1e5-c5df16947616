using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.Cache;
using Travis_backend.MessageDomain.ChannelWebhookProvider;
using Travis_backend.MessageDomain.Exceptions;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Repositories;
using Travis_backend.SignalR;

namespace Sleekflow.Core.Tests.MessageDomain;

[TestFixture]
public class BaseChannelMessageStatusHandlerTests
{
    private Mock<ILogger<BaseChannelMessageStatusHandler>> _loggerMock;
    private Mock<IConversationMessageStatusRepository> _repositoryMock;
    private Mock<ISignalRService> _signalRServiceMock;
    private BaseChannelMessageStatusHandler _handler;

    [SetUp]
    public void SetUp()
    {
        _loggerMock = new Mock<ILogger<BaseChannelMessageStatusHandler>>();
        _repositoryMock = new Mock<IConversationMessageStatusRepository>();
        _signalRServiceMock = new Mock<ISignalRService>();

        _handler = new TestChannelMessageStatusHandler(
            _loggerMock.Object,
            _repositoryMock.Object,
            _signalRServiceMock.Object);
    }

    [Test]
    public async Task OnMessageStatusUpdateAsync_MessageNotFound_ThrowsMessageNotFoundException()
    {
        // Arrange
        var companyId = "company1";
        var messageUniqueId = "msg1";
        var messageStatus = MessageStatus.Read;

        _repositoryMock.Setup(r => r.GetConversationMessageForStatusUpdateAsync(companyId, messageUniqueId, _handler.ChannelType))
            .ReturnsAsync((ConversationMessage)null);

        // Act & Assert
        Assert.ThrowsAsync<MessageNotFoundException>(
            async () => await _handler.OnMessageStatusUpdateAsync(companyId, messageUniqueId, messageStatus));
    }

    [Test]
    [TestCase(MessageStatus.Sending, MessageStatus.Sent)]
    [TestCase(MessageStatus.Sending, MessageStatus.Received)]
    [TestCase(MessageStatus.Sending, MessageStatus.Read)]
    [TestCase(MessageStatus.Sending, MessageStatus.Deleted)]
    [TestCase(MessageStatus.Sending, MessageStatus.Failed)]
    [TestCase(MessageStatus.Sending, MessageStatus.Undelivered)]
    [TestCase(MessageStatus.Sent, MessageStatus.Received)]
    [TestCase(MessageStatus.Sent, MessageStatus.Read)]
    [TestCase(MessageStatus.Sent, MessageStatus.Deleted)]
    [TestCase(MessageStatus.Sent, MessageStatus.Failed)]
    [TestCase(MessageStatus.Sent, MessageStatus.Undelivered)]
    [TestCase(MessageStatus.Received, MessageStatus.Read)]
    [TestCase(MessageStatus.Received, MessageStatus.Deleted)]
    [TestCase(MessageStatus.Received, MessageStatus.Failed)]
    [TestCase(MessageStatus.Received, MessageStatus.Undelivered)]
    [TestCase(MessageStatus.Read, MessageStatus.Deleted)]
    [TestCase(MessageStatus.Read, MessageStatus.Failed)]
    [TestCase(MessageStatus.Read, MessageStatus.Undelivered)]
    public async Task OnMessageStatusUpdateAsync_ValidStatusUpdate_CallsExpectedMethods(MessageStatus initialMessageStatus, MessageStatus newMessageStatus)
    {
        // Arrange
        var companyId = "company1";
        var messageUniqueId = "msg1";
        var conversationMessage = new ConversationMessage { Id = 1, Status = initialMessageStatus };

        _repositoryMock.Setup(r => r.GetConversationMessageForStatusUpdateAsync(companyId, messageUniqueId, _handler.ChannelType))
            .ReturnsAsync(conversationMessage);
        _repositoryMock.Setup(r => r.UpdateConversationStatusWithConditionAsync(conversationMessage.Id, newMessageStatus))
            .ReturnsAsync(true);
        _signalRServiceMock.Setup(s => s.SignalROnMessageStatusChanged(It.IsAny<ConversationMessage>())).Returns(Task.CompletedTask);

        // Act
        await _handler.OnMessageStatusUpdateAsync(companyId, messageUniqueId, newMessageStatus);

        // Assert
        _repositoryMock.Verify(r => r.UpdateConversationStatusWithConditionAsync(conversationMessage.Id, newMessageStatus), Times.Once);
        _signalRServiceMock.Verify(s => s.SignalROnMessageStatusChanged(conversationMessage), Times.Once);
    }

    [Test]
    [TestCase(MessageStatus.Sent, MessageStatus.Sending)]
    [TestCase(MessageStatus.Sent, MessageStatus.Scheduled)]
    [TestCase(MessageStatus.Received, MessageStatus.Sending)]
    [TestCase(MessageStatus.Received, MessageStatus.Sent)]
    [TestCase(MessageStatus.Received, MessageStatus.Scheduled)]
    [TestCase(MessageStatus.Read, MessageStatus.Sending)]
    [TestCase(MessageStatus.Read, MessageStatus.Sent)]
    [TestCase(MessageStatus.Read, MessageStatus.Received)]
    [TestCase(MessageStatus.Read, MessageStatus.Scheduled)]
    public async Task OnMessageStatusUpdateAsync_StatusNotValid_NoUpdatePerformed(MessageStatus initialMessageStatus, MessageStatus newMessageStatus)
    {
        // Arrange
        var companyId = "company1";
        var messageUniqueId = "msg1";
        var conversationMessage = new ConversationMessage { Id = 1, Status = initialMessageStatus };

        _repositoryMock.Setup(r => r.GetConversationMessageForStatusUpdateAsync(companyId, messageUniqueId, _handler.ChannelType))
            .ReturnsAsync(conversationMessage);
        _repositoryMock.Setup(r => r.UpdateConversationStatusWithConditionAsync(conversationMessage.Id, newMessageStatus))
            .ReturnsAsync(true);
        _signalRServiceMock.Setup(s => s.SignalROnMessageStatusChanged(It.IsAny<ConversationMessage>())).Returns(Task.CompletedTask);

        // Act
        await _handler.OnMessageStatusUpdateAsync(companyId, messageUniqueId, newMessageStatus);

        // Assert
        _signalRServiceMock.Verify(s => s.SignalROnMessageStatusChanged(conversationMessage), Times.Never);
    }

    private class TestChannelMessageStatusHandler : BaseChannelMessageStatusHandler
    {
        public TestChannelMessageStatusHandler(
            ILogger<BaseChannelMessageStatusHandler> logger,
            IConversationMessageStatusRepository conversationMessageStatusRepository,
            ISignalRService signalRService)
            : base(logger, conversationMessageStatusRepository, signalRService)
        {
        }

        public override string ChannelType => "TestChannel";
    }
}