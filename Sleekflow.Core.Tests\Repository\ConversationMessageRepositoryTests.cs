using System.Linq.Expressions;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.Database;
using Travis_backend.Database.DataAccessLayer;
using Travis_backend.Database.Services;
using Travis_backend.MessageDomain.Models;
using Z.EntityFramework.Extensions;

namespace Sleekflow.Core.Tests.Repository;

[TestFixture]
public class ConversationMessageRepositoryTests
{
    private ApplicationDbContext _appDbContext;
    private Mock<IDbContextService> _dbContextServiceMock;
    private IRepository<ConversationMessage> _repository;

    [SetUp]
    public async Task Setup()
    {
        var dbName = Guid.NewGuid().ToString();

        // Initialize in-memory DbContexts with the same database name to share data
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: dbName)
            .Options;
        _appDbContext = new ApplicationDbContext(options);

        // Seed data
        await SeedDataAsync();

        // Setup the DbContextService mock
        _dbContextServiceMock = new Mock<IDbContextService>();
        _dbContextServiceMock.Setup(s => s.GetDbContext()).Returns(_appDbContext);

        // Initialize the repository
        var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });

        _repository = new Repository<ConversationMessage>(
            _dbContextServiceMock.Object,
            loggerFactory.CreateLogger<Repository<ConversationMessage>>());
    }

    private async Task SeedDataAsync()
    {
        await _appDbContext.ConversationMessages.AddRangeAsync(
            new List<ConversationMessage>
            {
                new ConversationMessage
                {
                    Id = 1, MessageContent = "Alice"
                },
                new ConversationMessage
                {
                    Id = 2, MessageContent = "Bob"
                },
                new ConversationMessage
                {
                    Id = 3, MessageContent = "Charlie"
                },
                new ConversationMessage
                {
                    Id = 4, MessageContent = "David"
                },
                new ConversationMessage
                {
                    Id = 5, MessageContent = "Eve"
                }
            });
        await _appDbContext.SaveChangesAsync();
    }

    [TearDown]
    public async Task TearDown()
    {
        // Dispose of the DbContext to free resources
        await _appDbContext.DisposeAsync();
    }

    #region CRUD Tests

    [Test]
    public async Task AddAsync_AddsEntityAndSavesChanges()
    {
        // Arrange
        var entity = new ConversationMessage
        {
            Id = 100, MessageContent = "Test"
        };

        // Act
        await _repository.AddAsync(entity);

        // Assert
        var addedEntity = await _appDbContext.ConversationMessages.FindAsync(entity.Id);
        Assert.IsNotNull(addedEntity);
        Assert.That(addedEntity.MessageContent, Is.EqualTo("Test"));
    }

    #region AddRangeAsync Tests

    [Test]
    public async Task AddRangeAsync_AddsEntitiesAndSavesChanges()
    {
        // Arrange
        var entitiesToAdd = new List<ConversationMessage>
        {
            new ConversationMessage
            {
                Id = 6, MessageContent = "Frank", ConversationId = "C3"
            },
            new ConversationMessage
            {
                Id = 7, MessageContent = "Grace", ConversationId = "C3"
            }
        };

        // Act
        await _repository.AddRangeAsync(entitiesToAdd);

        // Assert
        _appDbContext.ConversationMessages.Should().Contain(e => e.Id == 6 && e.MessageContent == "Frank");
        _appDbContext.ConversationMessages.Should().Contain(e => e.Id == 7 && e.MessageContent == "Grace");
        // Verify that SaveChangesAsync was called once
        _appDbContext.ChangeTracker.HasChanges().Should().BeFalse();
    }

    [Test]
    public async Task AddRangeAsync_WithEmptyCollection_DoesNotSaveChanges()
    {
        // Arrange
        var emptyEntities = new List<ConversationMessage>();

        // Act
        await _repository.AddRangeAsync(emptyEntities);

        // Assert
        // No new entities should be added
        _appDbContext.ConversationMessages.Count().Should().Be(5);

        // Verify that SaveChangesAsync was not called
        _appDbContext.ChangeTracker.HasChanges().Should().BeFalse();
    }

    [Test]
    public void AddRangeAsync_WithNullCollection_ThrowsArgumentNullException()
    {
        // Arrange
        IEnumerable<ConversationMessage> nullEntities = null;

        // Act
        Func<Task> act = async () => await _repository.AddRangeAsync(nullEntities);

        // Assert
        act.Should().ThrowAsync<ArgumentNullException>()
            .WithMessage("*entities*"); // Ensure the parameter name is mentioned
    }

    #endregion

    [Test]
    public async Task GetByIdAsync_ReturnsEntity_WhenEntityExists()
    {
        // Arrange
        const long existingId = 1;
        var result = await _repository.GetByIdAsync(existingId);

        // Assert
        Assert.IsNotNull(result);
        Assert.That(result.MessageContent, Is.EqualTo("Alice"));
    }

    [Test]
    public async Task GetByIdAsync_ReturnsNull_WhenEntityDoesNotExist()
    {
        // Act
        const long nonExistentId = 999;
        var result = await _repository.GetByIdAsync(nonExistentId);

        // Assert
        Assert.IsNull(result);
    }

    [Test]
    public async Task GetAllAsync_ReturnsFilteredOrderedPagedEntities()
    {
        // Act
        var result = await _repository.GetAllAsync(
            predicate: x => x.MessageContent.StartsWith("A") || x.MessageContent.StartsWith("B"),
            orderBy: q => q.OrderBy(x => x.MessageContent),
            includeProperties: null,
            page: 1,
            pageSize: 2);

        // Assert
        var conversationMessages = result as ConversationMessage[] ?? result.ToArray();
        Assert.That(conversationMessages.Count, Is.EqualTo(2));
        Assert.That(conversationMessages.ElementAt(0).MessageContent, Is.EqualTo("Alice"));
        Assert.That(conversationMessages.ElementAt(1).MessageContent, Is.EqualTo("Bob"));
    }

    [Test]
    public async Task UpdateAsync_UpdatesEntityAndSavesChanges()
    {
        // Arrange
        const long existingEntityId = 1;
        var existingEntity = await _repository.GetByIdAsync(existingEntityId);
        Assert.IsNotNull(existingEntity);

        existingEntity.MessageContent = "Alice Updated";

        // Act
        await _repository.UpdateAsync(existingEntity);

        // Assert
        var retrievedEntity = await _appDbContext.ConversationMessages.FindAsync(existingEntityId);
        Assert.IsNotNull(retrievedEntity);
        Assert.That(retrievedEntity.MessageContent, Is.EqualTo("Alice Updated"));
    }

    #region UpdateRangeAsync Tests

    [Test]
    public async Task UpdateRangeAsync_UpdatesEntitiesAndSavesChanges()
    {
        // Arrange
        var entitiesToUpdate = _appDbContext.ConversationMessages
            .Where(e => e.Id <= 3)
            .ToList();

        foreach (var entity in entitiesToUpdate)
        {
            entity.MessageContent += " Updated";
        }

        // Act
        await _repository.UpdateRangeAsync(entitiesToUpdate);

        // Assert
        var updatedEntities = _appDbContext.ConversationMessages
            .Where(e => e.Id <= 3)
            .ToList();
        updatedEntities.Should().OnlyContain(e => e.MessageContent.EndsWith(" Updated"));
    }

    [Test]
    public async Task UpdateRangeAsync_WithEmptyCollection_DoesNotSaveChanges()
    {
        // Arrange
        var emptyEntities = new List<ConversationMessage>();

        // Act
        await _repository.UpdateRangeAsync(emptyEntities);

        // Assert
        // No entities should be updated
        var existingEntities = _appDbContext.ConversationMessages.ToList();
        existingEntities.Should().HaveCount(5);
    }

    [Test]
    public void UpdateRangeAsync_WithNullCollection_ThrowsArgumentNullException()
    {
        // Arrange
        IEnumerable<ConversationMessage> nullEntities = null;

        // Act
        Func<Task> act = async () => await _repository.UpdateRangeAsync(nullEntities);

        // Assert
        act.Should().ThrowAsync<ArgumentNullException>()
            .WithMessage("*entities*");
    }

    [Test]
    public void UpdateRangeAsync_WhenSaveChangesFails_ThrowsException()
    {
        // Arrange
        var entitiesToUpdate = new List<ConversationMessage>
        {
            new ConversationMessage
            {
                Id = 3, MessageContent = "Charlie Updated", ConversationId = "C1"
            }
        };

        // Act
        Func<Task> act = async () => await _repository.UpdateRangeAsync(entitiesToUpdate);

        // Assert
        act.Should().ThrowAsync<Exception>()
            .WithMessage("Database error.");
    }

    #endregion

    [Test]
    public async Task DeleteAsync_RemovesEntityAndSavesChanges()
    {
        // Arrange
        const long deleteEntityId = 2;
        var entity = await _repository.GetByIdAsync(deleteEntityId);
        Assert.IsNotNull(entity);

        // Act
        await _repository.DeleteAsync(entity);

        // Assert
        var deletedEntity = await _appDbContext.ConversationMessages.FindAsync(deleteEntityId);
        Assert.IsNull(deletedEntity);
    }

    #region DeleteRangeAsync Tests

    [Test]
    public async Task DeleteRangeAsync_DeletesEntitiesAndSavesChanges()
    {
        // Arrange
        var entitiesToDelete = _appDbContext.ConversationMessages
            .Where(e => e.Id >= 4)
            .ToList();

        // Act
        await _repository.DeleteRangeAsync(entitiesToDelete);

        // Assert
        var remainingEntities = _appDbContext.ConversationMessages.ToList();
        remainingEntities.Should().HaveCount(3);
        remainingEntities.Should().NotContain(e => e.Id >= 4);
    }

    [Test]
    public async Task DeleteRangeAsync_WithEmptyCollection_DoesNotSaveChanges()
    {
        // Arrange
        var emptyEntities = new List<ConversationMessage>();

        // Act
        await _repository.DeleteRangeAsync(emptyEntities);

        // Assert
        // All original entities should remain
        var remainingEntities = _appDbContext.ConversationMessages.ToList();
        remainingEntities.Should().HaveCount(5);
    }

    [Test]
    public void DeleteRangeAsync_WithNullCollection_ThrowsArgumentNullException()
    {
        // Arrange
        IEnumerable<ConversationMessage>? nullEntities = null;

        // Act
        Func<Task> act = async () => await _repository.DeleteRangeAsync(nullEntities);

        // Assert
        act.Should().ThrowAsync<ArgumentNullException>()
            .WithMessage("*entities*");
    }

    [Test]
    public void DeleteRangeAsync_WhenSaveChangesFails_ThrowsException()
    {
        // Arrange
        var entitiesToDelete = new List<ConversationMessage>
        {
            new ConversationMessage
            {
                Id = 3, MessageContent = "Charlie", ConversationId = "C1"
            }
        };

        // Act
        Func<Task> act = async () => await _repository.DeleteRangeAsync(entitiesToDelete);

        // Assert
        act.Should().ThrowAsync<Exception>()
            .WithMessage("Database error.");
    }

    #endregion

    #endregion

    #region Delete By Batch Operation Tests

    [Test]
    public async Task BatchDeleteAsync_DeletesEntitiesInBatches()
    {
        // Arrange
        Expression<Func<ConversationMessage, bool>> predicate = x => x.Id <= 3; // Delete Alice, Bob, Charlie

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchAsync(predicate, x => x.Id, batchDeleteAction);


        // Assert
        Assert.That(deletedCount, Is.EqualTo(3));

        var remainingEntities = await _appDbContext.ConversationMessages.ToListAsync();
        Assert.That(remainingEntities.Count, Is.EqualTo(2));
        Assert.That(remainingEntities.Any(e => e.Id <= 3), Is.False);
    }

    #endregion

}