﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Sleekflow.Powerflow.Apis.Utils.Slack;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FileDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.Services;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.InternalIntegrationHubDomain.Services;
using CreateSalesPaymentRecordRequest = Sleekflow.Powerflow.Apis.ViewModels.CreateSalesPaymentRecordRequest;
using DeleteSalesPaymentRecordRequest = Sleekflow.Powerflow.Apis.ViewModels.DeleteSalesPaymentRecordRequest;
using GetAllSalesPaymentRecordsResponse = Sleekflow.Powerflow.Apis.ViewModels.GetAllSalesPaymentRecordsResponse;
using GetSalesPaymentRecordFileUrlRequest = Sleekflow.Powerflow.Apis.ViewModels.GetSalesPaymentRecordFileUrlRequest;
using SetCompanyContactOwnerRequest = Sleekflow.Powerflow.Apis.ViewModels.SetCompanyContactOwnerRequest;
using UpdateSalesPaymentRecordRequest = Sleekflow.Powerflow.Apis.ViewModels.UpdateSalesPaymentRecordRequest;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Internal CMS Contact Owner api controller; Special Access Token required.
/// </summary>
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)] // Basic Role Requirement
[Route("/internal/contact-owner/[action]")]
public class InternalCmsContactOwnerController : InternalControllerBase
{
    private const string CMS_BLOB_CONTAINER = "internalcmspayment";
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly IUploadService _uploadService;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly IInternalHubSpotService _internalHubSpotService;
    private readonly IInternalAnalyticService _internalAnalyticService;
    private readonly IWebHostEnvironment _env;
    private readonly ILogger<InternalCmsContactOwnerController> _logger;

    public InternalCmsContactOwnerController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IUploadService uploadService,
        IAzureBlobStorageService azureBlobStorageService,
        IInternalHubSpotService internalHubSpotService,
        IInternalAnalyticService internalAnalyticService,
        IWebHostEnvironment env,
        ILogger<InternalCmsContactOwnerController> logger
        )
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _uploadService = uploadService;
        _azureBlobStorageService = azureBlobStorageService;
        _internalHubSpotService = internalHubSpotService;
        _internalAnalyticService = internalAnalyticService;
        _env = env;
        _logger = logger;
    }

    /// <summary>
    /// Get All Cms Sales Payment Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetAllSalesPaymentRecordsResponse>> GetAllSalesPaymentRecords()
    {
        var result = new GetAllSalesPaymentRecordsResponse();

        var cmsSalesPayments = await _appDbContext.CmsSalesPaymentRecords
            .Include(x => x.Files)
            .OrderByDescending(x => x.CreatedAt)
            .ProjectTo<CmsSalesPaymentRecordDto>(_mapper.ConfigurationProvider)
            .ToListAsync();

        result.CmsSalesPaymentRecords = cmsSalesPayments;

        var companyIds = result.CmsSalesPaymentRecords.Select(x => x.CompanyId).Distinct();

        var companyNameDict = await _appDbContext.CompanyCompanies.Where(x => companyIds.Contains(x.Id))
            .ToDictionaryAsync(x => x.Id, x => x.CompanyName);

        foreach (var r in result.CmsSalesPaymentRecords)
        {
            r.CompanyName = companyNameDict[r.CompanyId];
        }

        return Ok(result);
    }

    /// <summary>
    /// Set Company Contact Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> SetCompanyContactOwner(
        [FromBody]
        SetCompanyContactOwnerRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest("Company Not Found.");
        }

        if (!string.IsNullOrWhiteSpace(request.CompanyOwnerId))
        {
            ApplicationUser contactOwnerUser = null;

            string fromContactOwner = company.CmsCompanyOwnerId;

            if (request.CompanyOwnerId == "unassigned")
            {
                company.CmsCompanyOwnerId = null;
            }
            else
            {
                contactOwnerUser =
                    await _userManager.Users.FirstOrDefaultAsync(x => x.Id == request.CompanyOwnerId);

                if (contactOwnerUser == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Owner Id is not valid."
                        });
                }

                if (!(await _userManager.GetRolesAsync(contactOwnerUser)).Any(
                        x => x == ApplicationUserRole.InternalCmsSalesUser ||
                             x == ApplicationUserRole.InternalCmsCustomerSuccessUser))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Owner Id is not valid."
                        });
                }

                company.CmsCompanyOwnerId = contactOwnerUser.Id;
            }

            if (company.CmsCompanyOwnerId != fromContactOwner)
            {
                _appDbContext.CmsContactOwnerAssignLogs.Add(
                    new CmsContactOwnerAssignLog()
                    {
                        CompanyId = company.Id,
                        ContactOwnerType = CmsContactOwnerType.CompanyOwner,
                        FromContactOwnerId = fromContactOwner,
                        ToContactOwnerId = contactOwnerUser?.Id ?? null,
                        AssignedByUserId = user.Id
                    });

                await _appDbContext.SaveChangesAsync();
            }
        }

        if (!string.IsNullOrWhiteSpace(request.ActivationOwnerId))
        {
            ApplicationUser contactOwnerUser = null;

            string fromContactOwner = company.CmsActivationOwnerId;

            if (request.ActivationOwnerId == "unassigned")
            {
                company.CmsActivationOwnerId = null;
            }
            else
            {
                contactOwnerUser =
                    await _userManager.Users.FirstOrDefaultAsync(x => x.Id == request.ActivationOwnerId);

                if (contactOwnerUser == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Owner Id is not valid."
                        });
                }

                if (!(await _userManager.GetRolesAsync(contactOwnerUser)).Any(
                        x => x == ApplicationUserRole.InternalCmsSalesUser ||
                             x == ApplicationUserRole.InternalCmsCustomerSuccessUser))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Owner Id is not valid."
                        });
                }

                company.CmsActivationOwnerId = contactOwnerUser.Id;
            }

            if (company.CmsActivationOwnerId != fromContactOwner)
            {
                _appDbContext.CmsContactOwnerAssignLogs.Add(
                    new CmsContactOwnerAssignLog()
                    {
                        CompanyId = company.Id,
                        ContactOwnerType = CmsContactOwnerType.ActivationOwner,
                        FromContactOwnerId = fromContactOwner,
                        ToContactOwnerId = contactOwnerUser?.Id ?? null,
                        AssignedByUserId = user.Id
                    });

                await _appDbContext.SaveChangesAsync();
            }
        }

        if (!string.IsNullOrWhiteSpace(request.CsOwnerId))
        {
            ApplicationUser csOwnerUser = null;

            string fromCsOwner = company.CmsCsOwnerId;

            if (request.CsOwnerId == "unassigned")
            {
                company.CmsCsOwnerId = null;
            }
            else
            {
                csOwnerUser =
                    await _userManager.Users.FirstOrDefaultAsync(x => x.Id == request.CsOwnerId);

                if (csOwnerUser == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Owner Id is not valid."
                        });
                }

                if (!(await _userManager.GetRolesAsync(csOwnerUser)).Any(
                        x => x == ApplicationUserRole.InternalCmsSalesUser ||
                             x == ApplicationUserRole.InternalCmsCustomerSuccessUser))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Owner Id is not valid."
                        });
                }

                company.CmsCsOwnerId = csOwnerUser.Id;
            }

            if (company.CmsCsOwnerId != fromCsOwner)
            {
                _appDbContext.CmsContactOwnerAssignLogs.Add(
                    new CmsContactOwnerAssignLog()
                    {
                        CompanyId = company.Id,
                        ContactOwnerType = CmsContactOwnerType.CsOwner,
                        FromContactOwnerId = fromCsOwner,
                        ToContactOwnerId = csOwnerUser?.Id ?? null,
                        AssignedByUserId = user.Id
                    });

                await _appDbContext.SaveChangesAsync();
            }
        }

        await _internalHubSpotService.SyncCompany(request.CompanyId);

        return Ok(
            new ResponseViewModel()
            {
                message = "Contact Owner Changed"
            });
    }

    /// <summary>
    /// Get a File SAS Url from Cms Sales Payment Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<CmsSalesPaymentRecordFileDto>> GetSalesPaymentRecordFileUrl(
        [FromBody]
        GetSalesPaymentRecordFileUrlRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var file = await _appDbContext.CmsSalesPaymentRecordFiles
            .AsNoTracking()
            .Where(x => x.CmsSalesPaymentRecordId == request.CmsSalesPaymentRecordId && x.Id == request.FileId)
            .FirstOrDefaultAsync();

        if (file == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "File Not Found."
                });
        }

        var result = new CmsSalesPaymentRecordFileDto
        {
            Id = file.Id,
            Filename = file.Filename,
            MIMEType = file.MIMEType,
            CreatedAt = file.CreatedAt,
            Url = _azureBlobStorageService.GetAzureBlobSasUri(file.Filename, file.BlobContainer)
        };

        return Ok(result);
    }

    /// <summary>
    /// Create Sales Payment Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> CreateSalesPaymentRecord(
        [FromForm]
        CreateSalesPaymentRecordRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest("Company Not Found.");
        }

        var billRecord = await _appDbContext.CompanyBillRecords.Where(x => x.Id == request.BillRecordId)
            .FirstOrDefaultAsync();

        if (billRecord == null)
        {
            return BadRequest("Bill Record Not Found.");
        }

        if (string.IsNullOrEmpty(request.InvoiceId))
        {
            var timestamp = ((DateTimeOffset) DateTime.Now).ToUnixTimeMilliseconds();
            request.InvoiceId = $"INV-{request.BillRecordId}-{timestamp}";
        }

        var cmsSalesPayment = new CmsSalesPaymentRecord
        {
            BillRecordId = request.BillRecordId,
            SubscriptionFee = request.SubscriptionFee,
            OneTimeSetupFee = request.OneTimeSetupFee,
            WhatsappCreditAmount = request.WhatsappCreditAmount,
            Currency = request.Currency,
            PaymentMethod = request.PaymentMethod,
            PaymentTermInt = request.PaymentTerm,
            Remark = request.Remark,
            PaidAt = request.PaidAt,
            InvoiceId = request.InvoiceId,
            CreateUserId = user.Id,
            CompanyId = request.CompanyId,
            Discount = request.Discount,
            PeriodInMonths = request.PeriodInMonths,
        };

        if (request.Files != null)
        {
            foreach (var file in request.Files)
            {
                var fileUploadName = $"{billRecord.Id}-{DateTime.UtcNow:yyyyMMddHHmmss}-{file.FileName}";

                var uploadResult = await _uploadService.UploadFileBySteam(
                    CMS_BLOB_CONTAINER,
                    fileUploadName,
                    file.OpenReadStream());

                var fileRecord = new CmsSalesPaymentRecordFile()
                {
                    BlobContainer = CMS_BLOB_CONTAINER,
                    Filename = fileUploadName,
                    Url = uploadResult.Url,
                    MIMEType = MimeTypeMap.GetMimeType(file.FileName),
                    CmsSalesPaymentRecord = cmsSalesPayment,
                };

                _appDbContext.CmsSalesPaymentRecordFiles.Add(fileRecord);
            }
        }

        _appDbContext.CmsSalesPaymentRecords.Add(cmsSalesPayment);

        await _appDbContext.SaveChangesAsync();

        if (request.PaymentMethod == PaymentMethod.Bank)
        {
            // Create Netsuite invoice in background job
            var jobId = BackgroundJob.Enqueue<IInternalIntegrationService>(x => x.CreateInvoiceAsync(
                    request.InvoiceId,
                    request.CompanyId,
                    request.SubscriptionFee,
                    request.OneTimeSetupFee,
                    request.WhatsappCreditAmount,
                    billRecord.PeriodStart,
                    billRecord.PeriodEnd,
                    request.PaymentTerm,
                    request.Currency,
                    null,
                    null
            ));

            _logger.LogInformation("Created bank transfer invoice background job. JobId: {JobId}, InvoiceId: {InvoiceId}, CompanyId: {CompanyId}",
                jobId, request.InvoiceId, request.CompanyId);

            var companyBillRecords = await _appDbContext.CompanyBillRecords
                .Include(x => x.CmsSalesPaymentRecords)
                .Where(
                    x => x.CompanyId == request.CompanyId &&
                         x.Status != BillStatus.Inactive)
                .AsNoTracking()
                .ToListAsync();

            var isContractUploaded = request.Files != null && request.Files.Count > 0;
            var mrr =
                BillRecordRevenueCalculator.SumMonthlyRecurringRevenue(companyBillRecords);
            await SendBankTransferSlackMessageAsync(company.CompanyName, company.Id, request.BillRecordId.ToString(), isContractUploaded, user, mrr);
        }
        else
        {
            // Sum up all fees as paid amount
            var paidAmount = request.SubscriptionFee + request.OneTimeSetupFee + request.WhatsappCreditAmount;

            // Create Netsuite invoice in background job
            var jobId = BackgroundJob.Enqueue<IInternalIntegrationService>(x => x.CreateInvoiceAsync(
                request.InvoiceId,
                    request.CompanyId,
                    request.SubscriptionFee,
                    request.OneTimeSetupFee,
                    request.WhatsappCreditAmount,
                    billRecord.PeriodStart,
                    billRecord.PeriodEnd,
                    request.PaymentTerm,
                    request.Currency,
                    paidAmount, //With paid amount for create payment
                    request.PaidAt
            ));

            _logger.LogInformation("Created paid invoice background job. JobId: {JobId}, InvoiceId: {InvoiceId}, CompanyId: {CompanyId}, PaidAmount: {PaidAmount}",
                jobId, request.InvoiceId, request.CompanyId, paidAmount);
        }

        await _internalAnalyticService.UpdateCompanyAllTimeRevenueAnalyticInfo(
            new List<string>()
            {
                request.CompanyId
            });

        return Ok(
            new ResponseViewModel()
            {
                message =
                    $"Payment Record (ID:{cmsSalesPayment.Id}) Created for Bill Record(ID:{cmsSalesPayment.BillRecordId})."
            });
    }

    /// <summary>
    /// Update Sales Payment Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdateSalesPaymentRecord(
        [FromForm]
        UpdateSalesPaymentRecordRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var cmsSalesPayment =
            await _appDbContext.CmsSalesPaymentRecords.FirstOrDefaultAsync(
                x => x.Id == request.CmsSalesPaymentRecordId);

        if (cmsSalesPayment == null)
        {
            return BadRequest("Payment Records Not Found.");
        }

        cmsSalesPayment.SubscriptionFee = request.SubscriptionFee;
        cmsSalesPayment.OneTimeSetupFee = request.OneTimeSetupFee;
        cmsSalesPayment.WhatsappCreditAmount = request.WhatsappCreditAmount;
        cmsSalesPayment.Currency = request.Currency;
        cmsSalesPayment.PaymentMethod = request.PaymentMethod;
        cmsSalesPayment.PaymentTermInt = request.PaymentTerm;
        cmsSalesPayment.Remark = request.Remark;
        cmsSalesPayment.PaidAt = request.PaidAt;
        cmsSalesPayment.InvoiceId = request.InvoiceId;
        cmsSalesPayment.LastModifiedByUserId = user.Id;
        cmsSalesPayment.LastModifiedDate = DateTime.UtcNow;
        cmsSalesPayment.Discount = request.Discount;
        cmsSalesPayment.PeriodInMonths = request.PeriodInMonths;

        if (request.AdditionalFiles != null)
        {
            foreach (var file in request.AdditionalFiles)
            {
                var fileUploadName =
                    $"{cmsSalesPayment.BillRecordId}-{DateTime.UtcNow:yyyyMMddHHmmss}-{file.FileName}";
                var uploadResult = await _uploadService.UploadFileBySteam(
                    CMS_BLOB_CONTAINER,
                    fileUploadName,
                    file.OpenReadStream());

                var fileRecord = new CmsSalesPaymentRecordFile()
                {
                    BlobContainer = CMS_BLOB_CONTAINER,
                    Filename = fileUploadName,
                    Url = uploadResult.Url,
                    MIMEType = MimeTypeMap.GetMimeType(file.FileName),
                    CmsSalesPaymentRecord = cmsSalesPayment,
                };

                _appDbContext.CmsSalesPaymentRecordFiles.Add(fileRecord);
            }
        }

        if (request.RemoveFileIds != null)
        {
            foreach (var fileIdToRemove in request.RemoveFileIds)
            {
                var file = await _appDbContext.CmsSalesPaymentRecordFiles.FirstOrDefaultAsync(
                    x => x.Id == fileIdToRemove);

                if (file != null)
                {
                    await _azureBlobStorageService.DeleteFromAzureBlob(file.Filename, file.BlobContainer);
                    _appDbContext.CmsSalesPaymentRecordFiles.Remove(file);
                }
            }
        }

        await _appDbContext.SaveChangesAsync();

        await _internalAnalyticService.UpdateCompanyAllTimeRevenueAnalyticInfo(
            new List<string>()
            {
                cmsSalesPayment.CompanyId
            });

        return Ok(
            new ResponseViewModel()
            {
                message = $"Payment Record (ID:{cmsSalesPayment.Id}) Updated."
            });
    }

    /// <summary>
    /// Delete Sales Payment Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> DeleteSalesPaymentRecord(
        [FromBody]
        DeleteSalesPaymentRecordRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var cmsSalesPayment = await _appDbContext.CmsSalesPaymentRecords
            .Include(x => x.Files)
            .FirstOrDefaultAsync(
                x => x.Id == request.CmsSalesPaymentRecordId && x.BillRecordId == request.BillRecordId);

        if (cmsSalesPayment == null)
        {
            return BadRequest("Payment Records Not Found.");
        }

        var companyId = cmsSalesPayment.CompanyId;

        _appDbContext.CmsSalesPaymentRecordFiles.RemoveRange(cmsSalesPayment.Files);
        _appDbContext.CmsSalesPaymentRecords.Remove(cmsSalesPayment);

        await _appDbContext.SaveChangesAsync();

        await _internalAnalyticService.UpdateCompanyAllTimeRevenueAnalyticInfo(
            new List<string>()
            {
                companyId
            });

        return Ok(
            new ResponseViewModel()
            {
                message = $"Payment Record (ID:{request.CmsSalesPaymentRecordId}) deleted"
            });
    }

    private async Task SendBankTransferSlackMessageAsync(string companyName, string companyId, string billRecordId, bool isContractUploaded, ApplicationUser user, decimal mrr)
    {
        if (!_env.IsProduction()) return;
        var slackClient = new SlackClient(SlackChannel.PowerFlowBankTransfer);
        SlackMessageBuilder slackMessageBuilder = new SlackMessageBuilder();
        slackMessageBuilder.Add("*New bank transfer record created*");
        slackMessageBuilder.Add($"*Date*: {DateTime.Now:yyyy-MM-dd}");
        slackMessageBuilder.Add($"*Bill record id*: {billRecordId}");
        slackMessageBuilder.Add($"*Company *: <https://powerflow.sleekflow.io/companies/detail/{companyId}|{companyName}>");
        slackMessageBuilder.Add($"*MRR*: {mrr}");
        slackMessageBuilder.Add($"*Contract file *: {(isContractUploaded ? "Uploaded" : "Empty")}");
        slackMessageBuilder.Add($"*Created by*: {user.Email}({user.DisplayName})");
        slackMessageBuilder.AddDivider();
        slackMessageBuilder.Add("Please check the bank transfer record");
        await slackClient.SendMessageAsync(slackMessageBuilder.BuildSlackMessage());
    }
}