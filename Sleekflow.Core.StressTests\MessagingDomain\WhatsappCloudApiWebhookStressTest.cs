using System.Net.Http.Headers;
using System.Net.Mime;
using System.Text;
using Abstracta.JmeterDsl.Core.PreProcessors;
using Abstracta.JmeterDsl.Core.Timers;
using Newtonsoft.Json;
using Polly;
using Serilog;
using Sleekflow.Core.StressTests.Constants;
using Sleekflow.Core.StressTests.Helpers;
using Sleekflow.Core.StressTests.Logging;
using static Abstracta.JmeterDsl.JmeterDsl;

namespace Sleekflow.Core.StressTests.MessagingDomain;

public class WhatsappCloudApiWebhookStressTest
{
    private readonly ILogger _logger = LoggerConfig.CreateLogger();
    private string AccessToken;
    private HttpClient _http = new ();

    [OneTimeSetUp]
    public async Task Setup()
    {
        _logger.Information("Starting test setup");

        // Same username for dev and staging
        var loginCredential = GetLoginCredential();
        AccessToken = await TravisBackendHelper.GetSleekflowAccessTokenAsync(
            loginCredential.username,
            loginCredential.password);

        _http.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken);
        _http.DefaultRequestHeaders.Add(StressTestSignatureHelper.StressTestHeader, "true");
        _http.DefaultRequestHeaders.Add(
            StressTestSignatureHelper.StressTestSignatureHeader,
            StressTestSignatureHelper.GenerateSignature(AccessToken));

        await SetupChannel();
        await DeleteContacts();

        _logger.Information("Test setup completed");
    }

    [Test]
    [TestCase(50, 10)]
    public async Task WhatsappCloudApiReceiveMessageStressTest(
        int numberOfUniqueContacts,
        int numberOfMessagesPerUniqueContact)
    {
        var startTime = DateTime.UtcNow;

        _logger.Information(
            "Starting WhatsApp webhook stress test at {StartTime} with {UniqueContacts} contacts and {MessagesPerContact} messages each",
            startTime,
            numberOfUniqueContacts,
            numberOfMessagesPerUniqueContact);

        // Configure and run the test plan
        var stats = TestPlan(
            ThreadGroup(
                numberOfUniqueContacts,
                numberOfMessagesPerUniqueContact,
                HttpSampler(
                        $"{StressTestEnvironment.GetSleekflowCoreApiServerPath()}/whatsapp/cloudapi/webhook/{GetCompanyId()}/stPhoneNumId")
                    .Post("${REQUEST_BODY}", new MediaTypeHeaderValue(MediaTypeNames.Application.Json))
                    .Header("X-Sleekflow-Stress-Test", "true")
                    .Children(GenerateJsr223PreProcessorScript()),
                new DslUniformRandomTimer(TimeSpan.FromMilliseconds(100), TimeSpan.FromMilliseconds(500))
            )
        ).Run();

        // Generate test report
        _logger.Information(
            """
            Test Report:
            ----------------
            TestDuration: {@TestDuration}
            TotalRequests: {@TotalRequests}
            SuccessfulRequests: {@SuccessfulRequests}
            FailedRequests: {@FailedRequests}
            AverageResponseTime: {@AverageResponseTime}
            MinResponseTime: {@MinResponseTime}
            MaxResponseTime: {@MaxResponseTime}
            ResponseTime90Percentile: {@ResponseTime90Percentile}
            ResponseTime95Percentile: {@ResponseTime95Percentile}
            ResponseTime99Percentile: {@ResponseTime99Percentile}
            """,
            DateTime.UtcNow - startTime,
            stats.Overall.SamplesCount,
            stats.Overall.SamplesCount - stats.Overall.ErrorsCount,
            stats.Overall.ErrorsCount,
            stats.Overall.SampleTime.Mean,
            stats.Overall.SampleTime.Min,
            stats.Overall.SampleTime.Max,
            stats.Overall.SampleTime.Perc90,
            stats.Overall.SampleTime.Perc95,
            stats.Overall.SampleTime.Perc99);


        // Assertions
        Assert.That(stats.Overall.ErrorsCount, Is.EqualTo(0), "Should have no failed requests");

        // Custom Assertions - Verify the message count in the database
        _logger.Information("Waiting 10 seconds before database verification");
        await Task.Delay(TimeSpan.FromSeconds(10));
        _logger.Information("Starting message count verification");

        // Wait 10 seconds before checking the database
        await Task.Delay(TimeSpan.FromSeconds(10));

        _logger.Information("Message Count Report:");

        var retryPolicy = Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(
                20, // Number of retries
                retryAttempt => TimeSpan.FromSeconds(5), // Delay between retries
                onRetry: (exception, timeSpan, retryCount, context) =>
                {
                    _logger.Warning(
                        exception,
                        "Attempt {RetryCount}: Message count verification failed. Waiting {RetryDelay} seconds before next retry",
                        retryCount,
                        timeSpan.TotalSeconds);
                }
            );

        await retryPolicy.ExecuteAsync(
            async () =>
            {
                var messageCount = await GetMessageCountAsync(startTime);

                _logger.Information(
                    "Message Count: {ActualCount}, Expected: {ExpectedCount}",
                    messageCount,
                    numberOfUniqueContacts * numberOfMessagesPerUniqueContact);

                if (messageCount != numberOfUniqueContacts * numberOfMessagesPerUniqueContact)
                {
                    throw new Exception("Message count does not match the expected value");
                }

                Assert.That(
                    messageCount,
                    Is.EqualTo(numberOfUniqueContacts * numberOfMessagesPerUniqueContact),
                    "Should have messages in the database");

                return messageCount;
            });

        _logger.Information("WhatsApp webhook stress test completed successfully");
    }

    private DslJsr223PreProcessor GenerateJsr223PreProcessorScript()
    {
        _logger.Debug("Generating JMeter preprocessor script");
        var script = $$""""
                       import groovy.json.JsonOutput
                       import groovy.json.JsonSlurper

                       // Get the iteration and thread number
                       def iteration = vars.getIteration()
                       def userIndex = ctx.getThreadNum()
                       def phoneNumber = 85266660000 + userIndex
                       def uniqueId = UUID.randomUUID().toString()

                       // Store values in JMeter variables
                       vars.putObject('userIndex', String.valueOf(userIndex))
                       vars.putObject('phoneNumber', String.valueOf(phoneNumber))

                       // Create the JSON payload using string interpolation
                       def jsonString = """
                       {
                         "waba_id": "{{TestConstants.MessagingHubWabaId}}",
                         "waba_phone_number": "{{TestConstants.MessagingHubWabaPhoneNumberId}}",
                         "value": {
                           "messaging_product": "whatsapp",
                           "metadata": {
                             "display_phone_number": "{{TestConstants.MessagingHubWabaPhoneNumber}}",
                             "phone_number_id": "{{TestConstants.FacebookPhoneNumberId}}"
                           },
                           "contacts": [
                             {
                               "profile": {
                                 "name": "Stress Testing Contact - ${userIndex}"
                               },
                               "wa_id": "${phoneNumber}"
                             }
                           ],
                           "messages": [
                             {
                               "from": "${phoneNumber}",
                               "id": "wamid.{{TestConstants.MessageGuid}}.${phoneNumber}.${uniqueId}",
                               "timestamp": "${__time(/1000)}",
                               "type": "text",
                               "text": {
                                 "body": "Stress Testing - iteration: ${iteration}"
                               }
                             }
                           ]
                         }
                       }
                       """

                       // Parse the JSON string to an object
                       def jsonSlurper = new JsonSlurper()
                       def object = jsonSlurper.parseText(jsonString)

                       // Store the JSON object as a string in a JMeter variable
                       vars.put('REQUEST_BODY', JsonOutput.toJson(object))
                       """";
        return Jsr223PreProcessor(script);
    }

    [TearDown]
    public async Task TearDown()
    {
        await DeleteContacts();

        _http.Dispose();
    }

    private static class TestConstants
    {
        public const string MessagingHubWabaId = "stWabaId";
        public const string MessagingHubWabaPhoneNumberId = "stPhoneNumId";
        public const string MessagingHubWabaPhoneNumber = "***********";
        public const string WabaId = "100000000000001";
        public const string FacebookPhoneNumberId = "100000000000002";
        public const string FacebookBusinessId = "100000000000003";
        public static readonly string MessageGuid = Guid.NewGuid().ToString();
    }

    private string GetCompanyId()
    {
        if (StressTestEnvironment.IsDevelopment())
        {
            return "59600ba8-2eea-4d68-a4eb-6f8e9939d42e";
        }

        if (StressTestEnvironment.IsStaging())
        {
            return "d52842ed-8486-4c7c-b1b3-c2464dc15168";
        }

        throw new Exception("Unknown environment");
    }

    private (string username, string password) GetLoginCredential()
    {
        if (StressTestEnvironment.IsDevelopment())
        {
            return ("<EMAIL>", "TGYCpr.7LHWsPrrnz2tq");
        }

        if (StressTestEnvironment.IsStaging())
        {
            return ("<EMAIL>", "TGYCpr.7LHWsPrrnz2tq");
        }

        throw new Exception("Unknown environment");
    }

    private async Task<int> GetMessageCountAsync(DateTime startTime)
    {
        _logger.Debug("Querying message count from database");

        var response = await _http.PostAsync(
            $"{StressTestEnvironment.GetSleekflowCoreApiServerPath()}/StressTest/Internals/Messaging/GetMessageCount",
            new StringContent(
                JsonConvert.SerializeObject(
                    new
                    {
                        start_date = startTime.AddSeconds(-10), end_date = DateTime.UtcNow
                    }),
                Encoding.UTF8,
                "application/json"));

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception("Failed to setup WhatsApp channel with status code: " + response.StatusCode);
        }

        var responseContent = await response.Content.ReadAsStringAsync();
        var responseJson = JsonConvert.DeserializeObject<dynamic>(responseContent);
        var count = responseJson?.messageCount;

        return (int) count;
    }

    private async Task SetupChannel()
    {
        _logger.Information("Setting up WhatsApp channel");

        var response = await _http.PostAsync(
            $"{StressTestEnvironment.GetSleekflowCoreApiServerPath()}/StressTest/Internals/Messaging/SetupWhatsappCloudApiStressTestChannel",
            new StringContent("{}", Encoding.UTF8, "application/json"));

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception("Failed to setup WhatsApp channel with status code: " + response.StatusCode);
        }

        _logger.Information("WhatsApp channel setup completed");
    }

    private async Task DeleteContacts()
    {
        _logger.Information("Cleaning up existing contacts and related data");

        var response = await _http.PostAsync(
            $"{StressTestEnvironment.GetSleekflowCoreApiServerPath()}/StressTest/Internals/Messaging/TeardownDeleteContacts",
            new StringContent("{}", Encoding.UTF8, "application/json"));

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception("Failed to setup WhatsApp channel with status code: " + response.StatusCode);
        }

        _logger.Information("Contact cleanup completed");
    }
}