#nullable enable
using System.Threading.Tasks;

namespace Travis_backend.CompanyDomain.Services;

/// <summary>
/// Company Service related to company offer or promotion.
/// </summary>
public interface ICompanyOfferService
{
    /// <summary>
    /// Check whether a company is entitled to flow enrollments incentive when purchase add-on.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="stripeInvoiceLineItemId">Current flow enrollments add-on InvoiceLineItemId.</param>
    /// <returns>Boolean indicate whether company entitled to the incentives.</returns>
    Task<bool> IsEntitleToFlowEnrollmentAddOnPurchaseIncentiveAsync(string companyId, string? stripeInvoiceLineItemId = null);
}