using System;
using System.Collections.Generic;
using System.Linq;
using Travis_backend.Commons.Models;

namespace Travis_backend.CompanyDomain.Models;

public class GetCompanyStaffRequestQuery
{
    public string SearchString { get; set; }

    public IEnumerable<string> SearchRoles { get; set; }

    public IEnumerable<string> ExcludeRoles { get; set; }

    public IEnumerable<long> Teams { get; set; }

    public DateTimeRange JoinDateTime { get; set; }

    public string? SortBy { get; set; }

    public bool IsDesc { get; set; } = false;

    public bool? IsInvitePending { get; set; }

    public StaffSortField? GetSortField()
    {
        if (string.IsNullOrEmpty(SortBy))
        {
            return null;
        }

        return SortBy.ToLower() switch
        {
            "display_name" or "displayName" or "name" => StaffSortField.DisplayName,
            "email" => StaffSortField.Email,
            "role" => StaffSortField.Role,
            "status" => StaffSortField.Status,
            "position" or "pos" => StaffSortField.Position,
            "created_at" or "createdAt" or "date" => StaffSortField.CreatedAt,
            "is_invite_pending" or "invite_pending" or "invite" => StaffSortField.InvitePending,
            "is_invite_expired" or "invite_expired" or "expired" => StaffSortField.InviteExpired,
            _ => null
        };
    }

    public List<StaffSortField> GetSortFields()
    {
        if (string.IsNullOrEmpty(SortBy))
        {
            return new List<StaffSortField>();
        }

        return SortBy.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(s => s.Trim().ToLower() switch
            {
                "display_name" or "displayName" or "name" => StaffSortField.DisplayName,
                "email" => StaffSortField.Email,
                "role" => StaffSortField.Role,
                "status" => StaffSortField.Status,
                "position" or "pos" => StaffSortField.Position,
                "created_at" or "createdAt" or "date" => StaffSortField.CreatedAt,
                "is_invite_pending" or "invite_pending" or "invite" => StaffSortField.InvitePending,
                "is_invite_expired" or "invite_expired" or "expired" => StaffSortField.InviteExpired,
                _ => StaffSortField.None
            })
            .ToList();
    }
}

public enum StaffSortField
{
    DisplayName,
    Email,
    Role,
    Status,
    Position,
    CreatedAt,
    InvitePending,
    InviteExpired,
    None
}