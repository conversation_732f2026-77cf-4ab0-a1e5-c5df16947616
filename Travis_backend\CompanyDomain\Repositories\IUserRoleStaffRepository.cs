using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Enums;

namespace Travis_backend.CompanyDomain.Repositories;

/// <summary>
/// Repository for access UserRoleStaffs table.
/// </summary>
public interface IUserRoleStaffRepository
{
    /// <summary>
    /// Get first admin staff of company.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <returns>Staff.</returns>
    Task<Staff> GetFirstAdmin(string companyId);

    /// <summary>
    /// Query and count company staff for given conditions.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="searchString">Search String.</param>
    /// <param name="roles">Roles.</param>
    /// <param name="teamIds">TeamIds.</param>
    /// <param name="createDateTimeFrom">From Created DateTime of Staff.</param>
    /// <param name="createDateTimeTo">To Created DateTime of Staff.</param>
    /// <param name="offset">Query Offset.</param>
    /// <param name="limit">Query Limit.</param>
    /// <returns>Tuple of Staffs and Count.</returns>
    Task<(IEnumerable<GetCompanyStaffsQueryResult> Staffs, int Count)> GetCompanyStaffAsync(
        string companyId,
        string searchString,
        IEnumerable<StaffUserRole> roles,
        IEnumerable<long> teamIds,
        DateTime? createDateTimeFrom,
        DateTime? createDateTimeTo,
        int? offset,
        int? limit);

    /// <summary>
    /// Count company staff for given conditions.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="searchString">Search String.</param>
    /// <param name="roles">Roles.</param>
    /// <param name="teamIds">TeamIds.</param>
    /// <param name="createDateTimeFrom">From Created DateTime of Staff.</param>
    /// <param name="createDateTimeTo">To Created DateTime of Staff.</param>
    /// <returns>Total number of staffs.</returns>
    Task<int> CountCompanyStaffAsync(
        string companyId,
        string searchString,
        IEnumerable<StaffUserRole> roles,
        IEnumerable<long> teamIds,
        DateTime? createDateTimeFrom,
        DateTime? createDateTimeTo);

    /// <summary>
    /// Query and count reseller company staff for given conditions.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="searchString">Search String.</param>
    /// <param name="roles">Roles.</param>
    /// <param name="teamIds">TeamIds.</param>
    /// <param name="createDateTimeFrom">From Created DateTime of Staff.</param>
    /// <param name="createDateTimeTo">To Created DateTime of Staff.</param>
    /// <param name="offset">Query Offset.</param>
    /// <param name="limit">Query Limit.</param>
    /// <returns>Tuple of Staffs and Count.</returns>
    Task<(IEnumerable<GetCompanyStaffsQueryResult> Staffs, int Count)> GetResellerCompanyStaffAsync(
        string companyId,
        string searchString,
        IEnumerable<StaffUserRole> roles,
        IEnumerable<long> teamIds,
        DateTime? createDateTimeFrom,
        DateTime? createDateTimeTo,
        int? offset,
        int? limit);

    /// <summary>
    /// Count reseller company staff for given conditions.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="searchString">Search String.</param>
    /// <param name="roles">Roles.</param>
    /// <param name="teamIds">TeamIds.</param>
    /// <param name="createDateTimeFrom">From Created DateTime of Staff.</param>
    /// <param name="createDateTimeTo">To Created DateTime of Staff.</param>
    /// <returns>Total number of staffs.</returns>
    Task<int> CountResellerCompanyStaffAsync(
        string companyId,
        string searchString,
        IEnumerable<StaffUserRole> roles,
        IEnumerable<long> teamIds,
        DateTime? createDateTimeFrom,
        DateTime? createDateTimeTo);

    /// <summary>
    /// Get company owner.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <returns>Staff.</returns>
    Task<Staff> GetCompanyOwnerAsync(string companyId);

    /// <summary>
    /// ResellerClient company owner.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <returns>Staff.</returns>
    Task<Staff> GetResellerClientCompanyOwnerAsync(string companyId);

    Task<Staff> GetUserRoleStaffByStaffIdAsync(string companyId, string staffId);

    Task<int> UpdateUserRoleStaffAsync(Staff staff);
}