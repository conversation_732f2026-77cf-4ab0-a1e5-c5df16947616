﻿using System.Linq.Expressions;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.Database;
using Travis_backend.Database.DataAccessLayer;
using Travis_backend.Database.Services;
using Travis_backend.MessageDomain.Models;
using Z.EntityFramework.Extensions;

namespace Sleekflow.Core.Tests.Repository;


[TestFixture]
public class ConversationMessageRepositoryNoOrderTests
{
    private ApplicationDbContext _appDbContext;
    private Mock<IDbContextService> _dbContextServiceMock;
    private IRepository<ConversationMessage> _repository;
    private string _dbName;
    private DbContextOptions<ApplicationDbContext> _dbOptions;

    [SetUp]
    public async Task Setup()
    {
        _dbName = Guid.NewGuid().ToString();

        // Initialize in-memory DbContexts with the same database name to share data
        _dbOptions = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: _dbName)
            .Options;
        _appDbContext = new ApplicationDbContext(_dbOptions);

        // Seed data
        await SeedMoreDataAsync();

        // Setup the DbContextService mock
        _dbContextServiceMock = new Mock<IDbContextService>();
        _dbContextServiceMock.Setup(s => s.GetDbContext()).Returns(_appDbContext);

        // Initialize the repository
        var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });

        _repository = new Repository<ConversationMessage>(
            _dbContextServiceMock.Object,
            loggerFactory.CreateLogger<Repository<ConversationMessage>>());
    }

    private ApplicationDbContext CreateNewDbContext()
    {
        return new ApplicationDbContext(_dbOptions);
    }

    private async Task SeedMoreDataAsync()
    {
        var conversationMessages = new List<ConversationMessage>();

        // Add original test data
        conversationMessages.AddRange(new List<ConversationMessage>
        {
            new ConversationMessage { Id = 1, MessageContent = "Alice" },
            new ConversationMessage { Id = 2, MessageContent = "Bob" },
            new ConversationMessage { Id = 3, MessageContent = "Charlie" },
            new ConversationMessage { Id = 4, MessageContent = "David" },
            new ConversationMessage { Id = 5, MessageContent = "Eve" }
        });

        // Add more test data for batch operations (IDs 6-50)
        for (int i = 6; i <= 50; i++)
        {
            conversationMessages.Add(new ConversationMessage
            {
                Id = i,
                MessageContent = $"User{i}",
                ConversationId = i <= 25 ? "Conv1" : "Conv2" // Split into two conversations
            });
        }

        // Add additional data with specific patterns for testing
        conversationMessages.AddRange(new List<ConversationMessage>
        {
            // Batch test data with specific prefixes (IDs 51-100)
            new ConversationMessage { Id = 51, MessageContent = "Batch_Test_1", ConversationId = "BatchConv1" },
            new ConversationMessage { Id = 52, MessageContent = "Batch_Test_2", ConversationId = "BatchConv1" },
            new ConversationMessage { Id = 53, MessageContent = "Batch_Test_3", ConversationId = "BatchConv1" },
            new ConversationMessage { Id = 54, MessageContent = "Keep_This_1", ConversationId = "KeepConv1" },
            new ConversationMessage { Id = 55, MessageContent = "Keep_This_2", ConversationId = "KeepConv1" },
        });

        // Add more batch test data
        for (int i = 56; i <= 100; i++)
        {
            conversationMessages.Add(new ConversationMessage
            {
                Id = i,
                MessageContent = $"Batch_Test_{i}",
                ConversationId = "BatchConv2"
            });
        }

        await _appDbContext.ConversationMessages.AddRangeAsync(conversationMessages);
        await _appDbContext.SaveChangesAsync();
    }

    [TearDown]
    public async Task TearDown()
    {
        // Dispose of the DbContext to free resources
        await _appDbContext.DisposeAsync();
    }


    #region BulkDeleteByBatchNoOrderAsync Tests

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_DeletesEntitiesInBatches()
    {
        // Arrange
        Expression<Func<ConversationMessage, bool>> predicate = x => x.MessageContent.StartsWith("Batch_Test_");

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(predicate, x => x.Id, batchDeleteAction, batchSize: 10);

        // Assert
        Assert.That(deletedCount, Is.EqualTo(48)); // 48 entities with "Batch_Test_" prefix (IDs 51-53 and 56-100)

        var remainingBatchTestEntities = await _appDbContext.ConversationMessages
            .Where(x => x.MessageContent.StartsWith("Batch_Test_"))
            .ToListAsync();
        Assert.That(remainingBatchTestEntities.Count, Is.EqualTo(0));

        // Verify other entities remain
        var totalRemaining = await _appDbContext.ConversationMessages.CountAsync();
        Assert.That(totalRemaining, Is.EqualTo(52)); // 100 - 48 deleted entities
    }

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_WithDeleteSizeLimit_DeletesOnlySpecifiedAmount()
    {
        // Arrange
        Expression<Func<ConversationMessage, bool>> predicate = x => x.Id >= 6 && x.Id <= 50; // 45 entities

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(
            predicate,
            x => x.Id,
            batchDeleteAction,
            batchSize: 10,
            deleteSize: 25); // Limit to 25 deletions

        // Assert
        Assert.That(deletedCount, Is.EqualTo(25));

        var remainingEntitiesInRange = await _appDbContext.ConversationMessages
            .Where(x => x.Id >= 6 && x.Id <= 50)
            .CountAsync();
        Assert.That(remainingEntitiesInRange, Is.EqualTo(20)); // 45 - 25 deleted
    }

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_WithSmallBatchSize_ProcessesInMultipleBatches()
    {
        // Arrange
        Expression<Func<ConversationMessage, bool>> predicate = x => x.ConversationId == "Conv1"; // 20 entities (IDs 6-25)

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(
            predicate,
            x => x.Id,
            batchDeleteAction,
            batchSize: 5); // Small batch size to force multiple batches

        // Assert
        Assert.That(deletedCount, Is.EqualTo(20));

        var remainingConv1Entities = await _appDbContext.ConversationMessages
            .Where(x => x.ConversationId == "Conv1")
            .CountAsync();
        Assert.That(remainingConv1Entities, Is.EqualTo(0));
    }

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_WithNoMatchingEntities_ReturnsZero()
    {
        // Arrange
        Expression<Func<ConversationMessage, bool>> predicate = x => x.MessageContent == "NonExistentMessage";

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(predicate, x => x.Id, batchDeleteAction);

        // Assert
        Assert.That(deletedCount, Is.EqualTo(0));

        // Verify all entities remain
        var totalCount = await _appDbContext.ConversationMessages.CountAsync();
        Assert.That(totalCount, Is.EqualTo(100));
    }

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_WithSingleEntity_DeletesSuccessfully()
    {
        // Arrange
        Expression<Func<ConversationMessage, bool>> predicate = x => x.Id == 1; // Single entity

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(predicate, x => x.Id, batchDeleteAction);

        // Assert
        Assert.That(deletedCount, Is.EqualTo(1));

        var entity = await _appDbContext.ConversationMessages.FindAsync(1L);
        Assert.IsNull(entity);
    }

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_WithLargeBatchSize_ProcessesAllAtOnce()
    {
        // Arrange
        Expression<Func<ConversationMessage, bool>> predicate = x => x.ConversationId == "Conv2"; // 25 entities (IDs 26-50)

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(
            predicate,
            x => x.Id,
            batchDeleteAction,
            batchSize: 1000); // Large batch size

        // Assert
        Assert.That(deletedCount, Is.EqualTo(25));

        var remainingConv2Entities = await _appDbContext.ConversationMessages
            .Where(x => x.ConversationId == "Conv2")
            .CountAsync();
        Assert.That(remainingConv2Entities, Is.EqualTo(0));
    }

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_WithComplexPredicate_DeletesCorrectEntities()
    {
        // Arrange
        Expression<Func<ConversationMessage, bool>> predicate = x =>
            x.MessageContent.Contains("Keep_This_") ||
            (x.Id >= 10 && x.Id <= 15 && x.ConversationId == "Conv1");

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(predicate, x => x.Id, batchDeleteAction);

        // Assert - Should delete 2 "Keep_This_" entities and 6 entities with IDs 10-15
        Assert.That(deletedCount, Is.EqualTo(8));

        var keepThisEntities = await _appDbContext.ConversationMessages
            .Where(x => x.MessageContent.Contains("Keep_This_"))
            .CountAsync();
        Assert.That(keepThisEntities, Is.EqualTo(0));

        var rangeEntities = await _appDbContext.ConversationMessages
            .Where(x => x.Id >= 10 && x.Id <= 15)
            .CountAsync();
        Assert.That(rangeEntities, Is.EqualTo(0));
    }

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_WithDeleteSizeSmallerThanBatchSize_DeletesCorrectAmount()
    {
        // Arrange
        Expression<Func<ConversationMessage, bool>> predicate = x => x.Id >= 80 && x.Id <= 100; // 21 entities

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(
            predicate,
            x => x.Id,
            batchDeleteAction,
            batchSize: 15,
            deleteSize: 10); // Delete size smaller than batch size

        // Assert
        Assert.That(deletedCount, Is.EqualTo(10));

        var remainingEntitiesInRange = await _appDbContext.ConversationMessages
            .Where(x => x.Id >= 80 && x.Id <= 100)
            .CountAsync();
        Assert.That(remainingEntitiesInRange, Is.EqualTo(11)); // 21 - 10 deleted
    }

    #endregion

    #region Non-Existing Entries Tests

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_WithInvalidIds_ReturnsZero()
    {
        // Arrange
        Expression<Func<ConversationMessage, bool>> predicate = x => x.Id < 0 || x.Id > 10000; // Invalid range

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(predicate, x => x.Id, batchDeleteAction);

        // Assert
        Assert.That(deletedCount, Is.EqualTo(0));

        // Verify all original entities remain
        var totalCount = await _appDbContext.ConversationMessages.CountAsync();
        Assert.That(totalCount, Is.EqualTo(100));
    }

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_WithNonExistentConversationId_ReturnsZero()
    {
        // Arrange
        Expression<Func<ConversationMessage, bool>> predicate = x => x.ConversationId == "NonExistentConversation";

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(predicate, x => x.Id, batchDeleteAction);

        // Assert
        Assert.That(deletedCount, Is.EqualTo(0));

        // Verify all original entities remain
        var totalCount = await _appDbContext.ConversationMessages.CountAsync();
        Assert.That(totalCount, Is.EqualTo(100));
    }

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_WithMixedExistingAndNonExisting_DeletesOnlyExisting()
    {
        // Arrange - Mix of existing IDs (1-5) and non-existing IDs (1001-1005)
        var existingIds = new[] { 1L, 2L, 3L, 4L, 5L };
        var nonExistingIds = new[] { 1001L, 1002L, 1003L, 1004L, 1005L };
        var allIds = existingIds.Concat(nonExistingIds).ToArray();

        Expression<Func<ConversationMessage, bool>> predicate = x => allIds.Contains(x.Id);

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(predicate, x => x.Id, batchDeleteAction);

        // Assert - Only 5 existing entities should be deleted
        Assert.That(deletedCount, Is.EqualTo(5));

        // Verify the existing entities were actually deleted
        var deletedEntities = await _appDbContext.ConversationMessages
            .Where(x => existingIds.Contains(x.Id))
            .ToListAsync();
        Assert.That(deletedEntities.Count, Is.EqualTo(0));

        // Verify total count decreased by 5
        var totalCount = await _appDbContext.ConversationMessages.CountAsync();
        Assert.That(totalCount, Is.EqualTo(95)); // 100 - 5 deleted
    }

    [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_WithComplexNonExistentCriteria_ReturnsZero()
    {
        // Arrange - Complex predicate that should match nothing
        Expression<Func<ConversationMessage, bool>> predicate = x =>
            x.MessageContent.StartsWith("ZZZ_") &&
            x.ConversationId == "NonExistent" &&
            x.Id > 10000;

        Action<BatchDelete> batchDeleteAction = delete =>
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
            delete.InMemoryDbContextFactory = () => _appDbContext;
        };

        // Act
        int deletedCount = await _repository.BulkDeleteByBatchNoOrderAsync(predicate, x => x.Id, batchDeleteAction);

        // Assert
        Assert.That(deletedCount, Is.EqualTo(0));

        // Verify all original entities remain
        var totalCount = await _appDbContext.ConversationMessages.CountAsync();
        Assert.That(totalCount, Is.EqualTo(100));
    }

    #endregion

    #region High Concurrency Tests

        [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_ConcurrentDeletionsOnDifferentDataSets_ProcessesSuccessfully()
    {
        // Arrange
        var tasks = new List<Task<int>>();

        // Task 1: Delete Conv1 entities (IDs 6-25)
        var task1 = Task.Run(async () =>
        {
            using var dbContext = CreateNewDbContext();
            var dbContextServiceMock = new Mock<IDbContextService>();
            dbContextServiceMock.Setup(s => s.GetDbContext()).Returns(dbContext);
            var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
            var repository = new Repository<ConversationMessage>(dbContextServiceMock.Object, loggerFactory.CreateLogger<Repository<ConversationMessage>>());

            Expression<Func<ConversationMessage, bool>> predicate1 = x => x.ConversationId == "Conv1";
            Action<BatchDelete> batchDeleteAction1 = delete =>
            {
                delete.UseTableLock = false;
                delete.IgnoreInMemoryAsNoTracking = true;
                delete.InMemoryDbContextFactory = () => dbContext;
            };

            return await repository.BulkDeleteByBatchNoOrderAsync(predicate1, x => x.Id, batchDeleteAction1, batchSize: 5);
        });

        // Task 2: Delete Conv2 entities (IDs 26-50)
        var task2 = Task.Run(async () =>
        {
            await Task.Delay(10); // Small delay to increase concurrency likelihood
            using var dbContext = CreateNewDbContext();
            var dbContextServiceMock = new Mock<IDbContextService>();
            dbContextServiceMock.Setup(s => s.GetDbContext()).Returns(dbContext);
            var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
            var repository = new Repository<ConversationMessage>(dbContextServiceMock.Object, loggerFactory.CreateLogger<Repository<ConversationMessage>>());

            Expression<Func<ConversationMessage, bool>> predicate2 = x => x.ConversationId == "Conv2";
            Action<BatchDelete> batchDeleteAction2 = delete =>
            {
                delete.UseTableLock = false;
                delete.IgnoreInMemoryAsNoTracking = true;
                delete.InMemoryDbContextFactory = () => dbContext;
            };

            return await repository.BulkDeleteByBatchNoOrderAsync(predicate2, x => x.Id, batchDeleteAction2, batchSize: 5);
        });

        // Task 3: Delete BatchConv1 entities
        var task3 = Task.Run(async () =>
        {
            await Task.Delay(20); // Small delay to increase concurrency likelihood
            using var dbContext = CreateNewDbContext();
            var dbContextServiceMock = new Mock<IDbContextService>();
            dbContextServiceMock.Setup(s => s.GetDbContext()).Returns(dbContext);
            var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
            var repository = new Repository<ConversationMessage>(dbContextServiceMock.Object, loggerFactory.CreateLogger<Repository<ConversationMessage>>());

            Expression<Func<ConversationMessage, bool>> predicate3 = x => x.ConversationId == "BatchConv1";
            Action<BatchDelete> batchDeleteAction3 = delete =>
            {
                delete.UseTableLock = false;
                delete.IgnoreInMemoryAsNoTracking = true;
                delete.InMemoryDbContextFactory = () => dbContext;
            };

            return await repository.BulkDeleteByBatchNoOrderAsync(predicate3, x => x.Id, batchDeleteAction3, batchSize: 2);
        });

        tasks.AddRange(new[] { task1, task2, task3 });

        // Act
        var results = await Task.WhenAll(tasks);

        // Assert
        Assert.That(results[0], Is.EqualTo(20)); // Conv1 entities
        Assert.That(results[1], Is.EqualTo(25)); // Conv2 entities
        Assert.That(results[2], Is.EqualTo(3));  // BatchConv1 entities

        // Verify entities were actually deleted
        var remainingConv1 = await _appDbContext.ConversationMessages.Where(x => x.ConversationId == "Conv1").CountAsync();
        var remainingConv2 = await _appDbContext.ConversationMessages.Where(x => x.ConversationId == "Conv2").CountAsync();
        var remainingBatchConv1 = await _appDbContext.ConversationMessages.Where(x => x.ConversationId == "BatchConv1").CountAsync();

        Assert.That(remainingConv1, Is.EqualTo(0));
        Assert.That(remainingConv2, Is.EqualTo(0));
        Assert.That(remainingBatchConv1, Is.EqualTo(0));
    }

            [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_ConcurrentDeletionsWithOverlappingCriteria_HandlesGracefully()
    {
        // Arrange - Both tasks will target overlapping ranges
        // First, count how many entities actually exist in the target ranges
        var entitiesInRange60To80 = await _appDbContext.ConversationMessages
            .Where(x => x.Id >= 60 && x.Id <= 80)
            .CountAsync();
        var entitiesInRange70To90 = await _appDbContext.ConversationMessages
            .Where(x => x.Id >= 70 && x.Id <= 90)
            .CountAsync();
        var entitiesInCombinedRange60To90 = await _appDbContext.ConversationMessages
            .Where(x => x.Id >= 60 && x.Id <= 90)
            .CountAsync();

        var tasks = new List<Task<int>>();

        // Task 1: Delete entities with IDs 60-80
        var task1 = Task.Run(async () =>
        {
            using var dbContext = CreateNewDbContext();
            var dbContextServiceMock = new Mock<IDbContextService>();
            dbContextServiceMock.Setup(s => s.GetDbContext()).Returns(dbContext);
            var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
            var repository = new Repository<ConversationMessage>(dbContextServiceMock.Object, loggerFactory.CreateLogger<Repository<ConversationMessage>>());

            Expression<Func<ConversationMessage, bool>> predicate1 = x => x.Id >= 60 && x.Id <= 80;
            Action<BatchDelete> batchDeleteAction1 = delete =>
            {
                delete.UseTableLock = false;
                delete.IgnoreInMemoryAsNoTracking = true;
                delete.InMemoryDbContextFactory = () => dbContext;
            };

            return await repository.BulkDeleteByBatchNoOrderAsync(predicate1, x => x.Id, batchDeleteAction1, batchSize: 5);
        });

        // Task 2: Delete entities with IDs 70-90 (overlaps with Task 1)
        var task2 = Task.Run(async () =>
        {
            await Task.Delay(5); // Small delay
            using var dbContext = CreateNewDbContext();
            var dbContextServiceMock = new Mock<IDbContextService>();
            dbContextServiceMock.Setup(s => s.GetDbContext()).Returns(dbContext);
            var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
            var repository = new Repository<ConversationMessage>(dbContextServiceMock.Object, loggerFactory.CreateLogger<Repository<ConversationMessage>>());

            Expression<Func<ConversationMessage, bool>> predicate2 = x => x.Id >= 70 && x.Id <= 90;
            Action<BatchDelete> batchDeleteAction2 = delete =>
            {
                delete.UseTableLock = false;
                delete.IgnoreInMemoryAsNoTracking = true;
                delete.InMemoryDbContextFactory = () => dbContext;
            };

            return await repository.BulkDeleteByBatchNoOrderAsync(predicate2, x => x.Id, batchDeleteAction2, batchSize: 5);
        });

        tasks.AddRange(new[] { task1, task2 });

        // Act
        var results = await Task.WhenAll(tasks);

        // Assert - Total deleted should not exceed the actual entities that existed in the combined range
        var totalDeleted = results.Sum();
        Assert.That(totalDeleted, Is.LessThanOrEqualTo(entitiesInCombinedRange60To90));

        // The sum of individual task results might be higher than actual deletions due to overlap
        // but should not exceed the sum of entities in their individual ranges
        Assert.That(totalDeleted, Is.LessThanOrEqualTo(entitiesInRange60To80 + entitiesInRange70To90));

        // Verify all entities in the combined range are deleted
        var remainingInRange = await _appDbContext.ConversationMessages
            .Where(x => x.Id >= 60 && x.Id <= 90)
            .CountAsync();
        Assert.That(remainingInRange, Is.EqualTo(0));

        // Verify that the actual number of entities deleted from the database equals the combined range count
        var entitiesDeletedFromDb = entitiesInCombinedRange60To90 - remainingInRange;
        Assert.That(entitiesDeletedFromDb, Is.EqualTo(entitiesInCombinedRange60To90));
    }

        [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_HighConcurrencyWithDifferentBatchSizes_ProcessesSuccessfully()
    {
        // Arrange - Multiple concurrent operations with different batch sizes
        var tasks = new List<Task<int>>();

        for (int i = 0; i < 5; i++)
        {
            var taskIndex = i;
            var task = Task.Run(async () =>
            {
                await Task.Delay(taskIndex * 5); // Stagger start times

                using var dbContext = CreateNewDbContext();
                var dbContextServiceMock = new Mock<IDbContextService>();
                dbContextServiceMock.Setup(s => s.GetDbContext()).Returns(dbContext);
                var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
                var repository = new Repository<ConversationMessage>(dbContextServiceMock.Object, loggerFactory.CreateLogger<Repository<ConversationMessage>>());

                // Each task targets a different range of 5 entities
                var startId = 80 + (taskIndex * 4); // 80, 84, 88, 92, 96
                var endId = startId + 3; // 4 entities each

                Expression<Func<ConversationMessage, bool>> predicate = x => x.Id >= startId && x.Id <= endId;
                Action<BatchDelete> batchDeleteAction = delete =>
                {
                    delete.UseTableLock = false;
                    delete.IgnoreInMemoryAsNoTracking = true;
                    delete.InMemoryDbContextFactory = () => dbContext;
                };

                // Use different batch sizes for each task
                var batchSize = taskIndex + 1; // 1, 2, 3, 4, 5

                return await repository.BulkDeleteByBatchNoOrderAsync(predicate, x => x.Id, batchDeleteAction, batchSize: batchSize);
            });

            tasks.Add(task);
        }

        // Act
        var results = await Task.WhenAll(tasks);

        // Assert
        var totalDeleted = results.Sum();
        Assert.That(totalDeleted, Is.EqualTo(20)); // 5 tasks × 4 entities each

        // Verify entities in the target ranges are deleted
        var remainingInTargetRange = await _appDbContext.ConversationMessages
            .Where(x => x.Id >= 80 && x.Id <= 99)
            .CountAsync();
        Assert.That(remainingInTargetRange, Is.EqualTo(0));
    }

            [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_ConcurrentDeletionAttemptOnSameEntity_HandlesGracefully()
    {
        // Arrange - Multiple tasks trying to delete the same entities
        var tasks = new List<Task<int>>();

        for (int i = 0; i < 3; i++)
        {
            var taskIndex = i;
            var task = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(taskIndex * 2); // Small staggered delays

                    using var dbContext = CreateNewDbContext();
                    var dbContextServiceMock = new Mock<IDbContextService>();
                    dbContextServiceMock.Setup(s => s.GetDbContext()).Returns(dbContext);
                    var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
                    var repository = new Repository<ConversationMessage>(dbContextServiceMock.Object, loggerFactory.CreateLogger<Repository<ConversationMessage>>());

                    // All tasks target the same entities (IDs 1-5)
                    Expression<Func<ConversationMessage, bool>> predicate = x => x.Id >= 1 && x.Id <= 5;
                    Action<BatchDelete> batchDeleteAction = delete =>
                    {
                        delete.UseTableLock = false;
                        delete.IgnoreInMemoryAsNoTracking = true;
                        delete.InMemoryDbContextFactory = () => dbContext;
                    };

                    return await repository.BulkDeleteByBatchNoOrderAsync(predicate, x => x.Id, batchDeleteAction, batchSize: 2);
                }
                catch (DbUpdateConcurrencyException)
                {
                    // This is expected when multiple tasks try to delete the same entities
                    // Return 0 to indicate no entities were deleted by this task due to concurrency
                    return 0;
                }
                catch (Exception ex) when (ex.InnerException is DbUpdateConcurrencyException)
                {
                    // Handle cases where the concurrency exception is wrapped
                    return 0;
                }
            });

            tasks.Add(task);
        }

        // Act
        var results = await Task.WhenAll(tasks);

        // Assert - Total deleted across all tasks should be <= 5 (some tasks may fail due to concurrency)
        var totalDeleted = results.Sum();
        Assert.That(totalDeleted, Is.LessThanOrEqualTo(5));
        Assert.That(totalDeleted, Is.GreaterThan(0)); // At least one task should succeed

        // Verify the entities are actually deleted (most important check)
        var remainingEntities = await _appDbContext.ConversationMessages
            .Where(x => x.Id >= 1 && x.Id <= 5)
            .CountAsync();
        Assert.That(remainingEntities, Is.EqualTo(0));

        // Verify total count decreased appropriately
        var totalCount = await _appDbContext.ConversationMessages.CountAsync();
        Assert.That(totalCount, Is.EqualTo(95)); // 100 - 5 deleted

        // Verify that despite concurrency exceptions, all target entities were successfully deleted
        Assert.That(totalDeleted + remainingEntities, Is.LessThanOrEqualTo(5));
    }

        [Test]
    public async Task BulkDeleteByBatchNoOrderAsync_ConcurrentOperationsWithNonExistentData_ReturnsZeroGracefully()
    {
        // Arrange - Multiple tasks trying to delete non-existent entities concurrently
        var tasks = new List<Task<int>>();

        for (int i = 0; i < 4; i++)
        {
            var taskIndex = i;
            var task = Task.Run(async () =>
            {
                await Task.Delay(taskIndex * 3); // Small staggered delays

                using var dbContext = CreateNewDbContext();
                var dbContextServiceMock = new Mock<IDbContextService>();
                dbContextServiceMock.Setup(s => s.GetDbContext()).Returns(dbContext);
                var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
                var repository = new Repository<ConversationMessage>(dbContextServiceMock.Object, loggerFactory.CreateLogger<Repository<ConversationMessage>>());

                // Each task targets a different non-existent range
                var startId = 2000 + (taskIndex * 100); // 2000, 2100, 2200, 2300
                Expression<Func<ConversationMessage, bool>> predicate = x => x.Id >= startId && x.Id < startId + 50;
                Action<BatchDelete> batchDeleteAction = delete =>
                {
                    delete.UseTableLock = false;
                    delete.IgnoreInMemoryAsNoTracking = true;
                    delete.InMemoryDbContextFactory = () => dbContext;
                };

                return await repository.BulkDeleteByBatchNoOrderAsync(predicate, x => x.Id, batchDeleteAction, batchSize: 10);
            });

            tasks.Add(task);
        }

        // Act
        var results = await Task.WhenAll(tasks);

        // Assert - All tasks should return 0
        foreach (var result in results)
        {
            Assert.That(result, Is.EqualTo(0));
        }

        // Verify no entities were deleted
        var totalCount = await _appDbContext.ConversationMessages.CountAsync();
        Assert.That(totalCount, Is.EqualTo(100));
    }

    #endregion

}