using System.Text.RegularExpressions;
using Travis_backend.ConversationDomain.ConversationSpecifications.RbacConversationSpecifications;
using Travis_backend.ConversationDomain.ConversationSpecifications.RbacConversationSpecifications.QueryObjects.Rbac;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Rbac.RbacQueryObjects;

[TestFixture]
public class UnassignedConversationsUnderMyTeamUnitTests
{
    private StaffAccessControlAggregate _staff;
    private StaffAccessControlAggregate _staffWithNoTeam;

    [SetUp]
    public void Setup()
    {
        // Arrange
        _staff = new StaffAccessControlAggregate
        {
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                new ()
                {
                    Id = 1
                },
                new ()
                {
                    Id = 2
                }
            }
        };

        _staffWithNoTeam = new StaffAccessControlAggregate
        {
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            AssociatedTeams = new List<TeamAccessControlAggregate>()
        };
    }

    [Test]
    public void can_generate_valid_sql()
    {
        // Arrange
        var query = new UnassignedConversationsUnderMyTeamQuery(_staff);
        var associatedTeamIds = string.Join(", ", _staff.AssociatedTeams.Select(team => team.Id));
        var alias = "C";

        // Act
        var actualSql = query.ToSql(alias);

        var expectedSql = @$"
            SELECT {alias}.*
            FROM Conversations {alias}
            WHERE {alias}.AssignedTeamId IN ( {associatedTeamIds} )
            AND {alias}.AssigneeId IS NULL
            AND {alias}.CompanyId = '{_staff.CompanyId}'
        ";

        Assert.That(
            Regex.Replace(actualSql.ToLower().Trim(), @"\s+", ""),
            Is.EqualTo(Regex.Replace(expectedSql.ToLower().Trim(), @"\s+", "").Normalize()));

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(actualSql.Trim());
        TestContext.WriteLine("Expected SQL Query:");
        TestContext.WriteLine(expectedSql.Trim());
    }


    [Test]
    public void can_generate_valid_sql_for_staff_with_no_team()
    {
        // Arrange
        var query = new UnassignedConversationsUnderMyTeamQuery(_staffWithNoTeam);
        var alias = "C";

        // Act
        var actualSql = query.ToSql(alias);

        var expectedSql = @$"
            SELECT {alias}.*
            FROM Conversations {alias}
            WHERE {alias}.AssignedTeamId IN ( -1 )
            AND {alias}.AssigneeId IS NULL
	        AND {alias}.CompanyId = '{_staff.CompanyId}'
	    ";

        Assert.That(
            Regex.Replace(actualSql.ToLower().Trim(), @"\s+", ""),
            Is.EqualTo(Regex.Replace(expectedSql.ToLower().Trim(), @"\s+", "")));

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(actualSql.Trim());
        TestContext.WriteLine("Expected SQL Query:");
        TestContext.WriteLine(expectedSql.Trim());
    }
}