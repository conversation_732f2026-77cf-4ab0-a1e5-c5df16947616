using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.Database;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Models;
using Travis_backend.SignalR;

namespace Sleekflow.Core.Tests.ConversationDomain.Services;

[TestFixture]
[NonParallelizable]
public class ConversationAssigneeServiceTests
{
    private ApplicationDbContext _appDbContext;
    private Mock<ILogger<ConversationAssigneeService>> _loggerMock;
    private Mock<ISignalRService> _signalRServiceMock;
    private Mock<IUserProfileHooks> _userProfileHooksMock;
    private ConversationAssigneeService _conversationAssigneeService;
    private string _companyId;
    private string _operatorIdentityId;

    // All conversation IDs used in tests for cleanup
    private static readonly List<string> AllTestConversationIds = new()
    {
        "6a2819cb-87c9-40b6-98ff-93f7d04e07ae",
        "a4dd4013-70a6-421d-96db-9859b46a975b",
        "bc0c44c6-e766-4a3c-804b-669fdd902714",
        "ba88cc1c-cc5c-48b2-b34e-8f50ab05a732",
        "5b9df1f1-42de-4897-93b4-7cb87ef94fb5",
        "236de853-ab85-4a87-bde1-f10288556274",
        "53f48e5e-ecd4-4a94-8251-3851ece239f8",
        "9ba715a2-d4ac-404d-b61a-e01220ea792d",
        "bc1733e0-2b88-4e59-a6a3-b6f307c7f36f",
        "49480c52-6c41-4a4c-90da-665775f82544",
        "3f7d538f-6143-4883-b54c-b1d962ce523e",
        "430edab4-d032-44d1-9f5a-045a69c5816a",
        "d329bb35-9a81-4726-8f5d-e52767fc483f",
        "44e57e39-b5d8-444d-aeee-30505cd3019f",
        "4488357a-344a-4e24-8b7f-0705c2e336c2"
    };

    [SetUp]
    public async Task Setup()
    {
        const string connectionString = "Server=tcp:sleekflow-core-sql-server-eas-dev928ea268.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=s81604a6f;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=100;";
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseSqlServer(connectionString)
            .Options;
        _appDbContext = new ApplicationDbContext(options);

        _loggerMock = new Mock<ILogger<ConversationAssigneeService>>();
        _signalRServiceMock = new Mock<ISignalRService>();
        _userProfileHooksMock = new Mock<IUserProfileHooks>();

        _conversationAssigneeService = new ConversationAssigneeService(
            _appDbContext,
            _loggerMock.Object,
            _signalRServiceMock.Object,
            _userProfileHooksMock.Object);

        _companyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        _operatorIdentityId = "58752a81-e31c-4dd3-ae2f-d02f4cc205c2";
    }

    [OneTimeTearDown]
    public async Task OneTimeTearDown()
    {
        // Clean up all test data after all tests are complete
        await Reset();

        _appDbContext?.Dispose();
    }

    private async Task Reset()
    {
        await _appDbContext.ConversationAdditionalAssignees
            .Where(x => x.CompanyId == _companyId && AllTestConversationIds.Contains(x.ConversationId))
            .ExecuteDeleteAsync();
    }

    #region AddAdditionalAssigneesForUserProfileInBatchAsync Tests

    [Test]
    public async Task AddAdditionalAssigneesForUserProfileInBatchAsync_WithValidData_ShouldAddAssignees()
    {
        await Reset();

        // Arrange
        var userProfileIds = new List<string> { "f00eed34-c317-4a5a-a9fc-3069e3bcef1b", "026593be-f454-4dbf-b716-b0e7780d9276" };
        var conversationIds = new List<string> { "6a2819cb-87c9-40b6-98ff-93f7d04e07ae", "a4dd4013-70a6-421d-96db-9859b46a975b" };
        var assigneeIds = new List<long> { 90, 2, 70 };

        // Act
        await _conversationAssigneeService.AddAdditionalAssigneesForUserProfileInBatchAsync(
            _companyId,
            userProfileIds,
            assigneeIds,
            operatorIdentityId: _operatorIdentityId);

        // Assert
        var addedAssignees = await _appDbContext.ConversationAdditionalAssignees
            .Where(x => x.CompanyId == _companyId && conversationIds.Contains(x.ConversationId))
            .ToListAsync();

        Assert.That(addedAssignees.Count, Is.EqualTo(6)); // 2 conversations × 2 assignees

        // Verify each conversation has both assignees
        var conv1Assignees = addedAssignees.Where(x => x.ConversationId == conversationIds[0]).ToList();
        var conv2Assignees = addedAssignees.Where(x => x.ConversationId == conversationIds[1]).ToList();

        Assert.That(conv1Assignees.Count, Is.EqualTo(3));
        Assert.That(conv2Assignees.Count, Is.EqualTo(3));

        Assert.That(conv1Assignees.Select(x => x.AssigneeId), Is.EquivalentTo(assigneeIds));
        Assert.That(conv2Assignees.Select(x => x.AssigneeId), Is.EquivalentTo(assigneeIds));
    }

    [Test]
    public async Task AddAdditionalAssigneesForUserProfileInBatchAsync_WithExistingAssignees_ShouldNotAddDuplicates()
    {
        await Reset();

        var userProfileIds = new List<string> { "f00eed34-c317-4a5a-a9fc-3069e3bcef1b" };
        var conversationIds = new List<string> { "6a2819cb-87c9-40b6-98ff-93f7d04e07ae" };
        var existingAssigneeIds = new List<long> { 90, 2, 70 };

        // First, add some assignees
        await _conversationAssigneeService.AddAdditionalAssigneesForUserProfileInBatchAsync(
            _companyId,
            userProfileIds,
            existingAssigneeIds,
            operatorIdentityId: _operatorIdentityId);

        // Now try to add overlapping assignees (90 already exists, 433 is new)
        var overlappingAssigneeIds = new List<long> { 90, 433 };

        // Act - try to add overlapping assignees
        await _conversationAssigneeService.AddAdditionalAssigneesForUserProfileInBatchAsync(
            _companyId,
            userProfileIds,
            overlappingAssigneeIds,
            operatorIdentityId: _operatorIdentityId);

        // Assert - should have 4 total (3 original + 1 new, no duplicate of 90)
        var addedAssignees = await _appDbContext.ConversationAdditionalAssignees
            .Where(x => x.ConversationId == conversationIds[0] && x.CompanyId == _companyId)
            .ToListAsync();

        Assert.That(addedAssignees.Count, Is.EqualTo(4)); // 3 original + 1 new (433), no duplicate 90
        Assert.That(addedAssignees.Select(x => x.AssigneeId), Is.EquivalentTo(new List<long> { 90, 2, 70, 433 }));
    }

    #endregion

    #region RemoveAdditionalAssigneesForUserProfileInBatchAsync Tests

    [Test]
    public async Task RemoveAdditionalAssigneesForUserProfileInBatchAsync_WithValidData_ShouldRemoveAssignees()
    {
        await Reset();

        // Arrange
        var userProfileIds = new List<string> { "f00eed34-c317-4a5a-a9fc-3069e3bcef1b", "026593be-f454-4dbf-b716-b0e7780d9276" };
        var conversationIds = new List<string> { "6a2819cb-87c9-40b6-98ff-93f7d04e07ae", "a4dd4013-70a6-421d-96db-9859b46a975b" };
        var existingAssigneeIds = new List<long> { 90, 2, 70, 433 };

        // First, add assignees to both conversations
        await _conversationAssigneeService.AddAdditionalAssigneesForUserProfileInBatchAsync(
            _companyId,
            userProfileIds,
            existingAssigneeIds,
            operatorIdentityId: _operatorIdentityId);

        // Verify they were added (8 total: 4 assignees × 2 conversations)
        var beforeRemoval = await _appDbContext.ConversationAdditionalAssignees
            .Where(x => x.CompanyId == _companyId && conversationIds.Contains(x.ConversationId))
            .ToListAsync();
        Assert.That(beforeRemoval.Count, Is.EqualTo(8));

        // Now remove specific assignees
        var assigneesToRemove = new List<long> { 90, 2 };

        // Act - Remove the assignees
        await _conversationAssigneeService.RemoveAdditionalAssigneesForUserProfileInBatchAsync(
            _companyId,
            userProfileIds,
            assigneesToRemove,
            operatorIdentityId: _operatorIdentityId);

        // Assert - Should have 4 remaining (2 assignees × 2 conversations: 70, 433)
        var remainingAssignees = await _appDbContext.ConversationAdditionalAssignees
            .Where(x => x.CompanyId == _companyId && conversationIds.Contains(x.ConversationId))
            .ToListAsync();

        Assert.That(remainingAssignees.Count, Is.EqualTo(4)); // 2 remaining assignees × 2 conversations

        // Verify each conversation has the correct remaining assignees
        var conv1Assignees = remainingAssignees.Where(x => x.ConversationId == conversationIds[0]).ToList();
        var conv2Assignees = remainingAssignees.Where(x => x.ConversationId == conversationIds[1]).ToList();

        Assert.That(conv1Assignees.Count, Is.EqualTo(2));
        Assert.That(conv2Assignees.Count, Is.EqualTo(2));

        // Should only have 70 and 433 remaining (90 and 2 were removed)
        Assert.That(conv1Assignees.Select(x => x.AssigneeId), Is.EquivalentTo(new List<long> { 70, 433 }));
        Assert.That(conv2Assignees.Select(x => x.AssigneeId), Is.EquivalentTo(new List<long> { 70, 433 }));
    }

    [Test]
    public async Task RemoveAdditionalAssigneesForUserProfileInBatchAsync_WithNonExistentAssignees_ShouldHandleGracefully()
    {
        await Reset();

        // Arrange
        var userProfileIds = new List<string> { "f00eed34-c317-4a5a-a9fc-3069e3bcef1b" };
        var conversationIds = new List<string> { "6a2819cb-87c9-40b6-98ff-93f7d04e07ae" };
        var existingAssigneeIds = new List<long> { 90, 2, 70, 433 };

        // First, add some assignees
        await _conversationAssigneeService.AddAdditionalAssigneesForUserProfileInBatchAsync(
            _companyId,
            userProfileIds,
            existingAssigneeIds,
            operatorIdentityId: _operatorIdentityId);

        // Verify they were added
        var beforeRemoval = await _appDbContext.ConversationAdditionalAssignees
            .Where(x => x.ConversationId == conversationIds[0] && x.CompanyId == _companyId)
            .ToListAsync();
        Assert.That(beforeRemoval.Count, Is.EqualTo(4));

        // Try to remove non-existent assignees
        var nonExistentAssigneeIds = new List<long> { 999 }; // Non-existent assignee

        // Act - Should not throw exception
        await _conversationAssigneeService.RemoveAdditionalAssigneesForUserProfileInBatchAsync(
            _companyId,
            userProfileIds,
            nonExistentAssigneeIds,
            operatorIdentityId: _operatorIdentityId);

        // Assert - All original assignees should still be there (nothing was removed)
        var afterRemoval = await _appDbContext.ConversationAdditionalAssignees
            .Where(x => x.ConversationId == conversationIds[0] && x.CompanyId == _companyId)
            .ToListAsync();

        Assert.That(afterRemoval.Count, Is.EqualTo(4)); // All original assignees should remain
        Assert.That(afterRemoval.Select(x => x.AssigneeId), Is.EquivalentTo(existingAssigneeIds));
    }

    #endregion
}