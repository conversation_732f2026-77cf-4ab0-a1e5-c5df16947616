using System.Collections.Generic;
using System.Linq;

namespace Travis_backend.Cache.Models.CacheKeyPatterns;

public class GetCompanyAllStaffRolesWithPermissionsCacheKeyPattern : ICacheKeyPattern
{
    public string MethodName { get; set; }

    public string CompanyId { get; set; }

    public GetCompanyAllStaffRolesWithPermissionsCacheKeyPattern(
        string methodName,
        string companyId)
    {
        MethodName = methodName;
        CompanyId = companyId;
    }

    public string GenerateKeyPattern()
    {
        var keyName = CacheHelper.BuildKeyNameFromPattern(
            new object[]
            {
                MethodName,
                CompanyId
            },
            "-");

        return keyName;
    }
}