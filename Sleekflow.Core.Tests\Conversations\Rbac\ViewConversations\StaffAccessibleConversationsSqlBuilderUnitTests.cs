using Travis_backend.ConversationDomain.ConversationQueryables;
using <PERSON>_backend.ConversationDomain.ConversationSpecifications.SqlBuilders;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Rbac.ViewConversations;

public class RbacStaffAccessibleConversationsSqlBuilderUnitTests
{
    private StaffAccessControlAggregate _staff;

    [SetUp]
    public void Setup()
    {
        _staff = new StaffAccessControlAggregate
        {
            StaffId = 15201,
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea"
        };
    }

    [Test]
    public void can_generate_valid_sql_for_staff_with_no_permission()
    {
        // Arrange
        var builder = new StaffAccessibleConversationsSqlBuilder(_staff);

        // Act
        var actualSql = builder.Build();

        // Output the SQL query
        TestContext.WriteLine("Generated SQL Query:");
        TestContext.WriteLine(actualSql.Trim());
    }
}