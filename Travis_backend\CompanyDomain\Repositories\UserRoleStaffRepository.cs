using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;

namespace Travis_backend.CompanyDomain.Repositories;

/// <inheritdoc />
public class UserRoleStaffRepository : IUserRoleStaffRepository
{
    /// <summary>
    /// Application DbContext.
    /// </summary>
    private readonly ApplicationDbContext _applicationDbContext;

    /// <summary>
    /// Initializes a new instance of the <see cref="UserRoleStaffRepository"/> class.
    /// </summary>
    /// <param name="applicationDbContext">ApplicationDbContext.</param>
    public UserRoleStaffRepository(ApplicationDbContext applicationDbContext)
    {
        _applicationDbContext = applicationDbContext;
    }

    /// <inheritdoc />
    public async Task<Staff> GetFirstAdmin(string companyId)
    {
        return await _applicationDbContext.UserRoleStaffs.AsNoTracking()
            .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.RoleType == StaffUserRole.Admin);
    }

    /// <inheritdoc />
    public async Task<(IEnumerable<GetCompanyStaffsQueryResult> Staffs, int Count)> GetCompanyStaffAsync(
        string companyId,
        string searchString,
        IEnumerable<StaffUserRole> roles,
        IEnumerable<long> teamIds,
        DateTime? createDateTimeFrom,
        DateTime? createDateTimeTo,
        int? offset,
        int? limit)
    {
        var query = GetCompanyStaffQueryable(companyId, searchString, roles, teamIds, createDateTimeFrom, createDateTimeTo);

        var staffsResult = offset is not null && limit is not null
            ? await query.AsNoTracking().Skip((int)offset).Take((int)limit).ToListAsync()
            : await query.AsNoTracking().ToListAsync();
        var totalCount = await query.CountAsync();

        return (staffsResult, totalCount);
    }

    /// <inheritdoc />
    public Task<int> CountCompanyStaffAsync(
        string companyId,
        string searchString,
        IEnumerable<StaffUserRole> roles,
        IEnumerable<long> teamIds,
        DateTime? createDateTimeFrom,
        DateTime? createDateTimeTo)
    {
        var query = GetCompanyStaffQueryable(companyId, searchString, roles, teamIds, createDateTimeFrom, createDateTimeTo);
        return query.CountAsync();
    }

    /// <inheritdoc />
    public async Task<(IEnumerable<GetCompanyStaffsQueryResult> Staffs, int Count)> GetResellerCompanyStaffAsync(
        string companyId,
        string searchString,
        IEnumerable<StaffUserRole> roles,
        IEnumerable<long> teamIds,
        DateTime? createDateTimeFrom,
        DateTime? createDateTimeTo,
        int? offset,
        int? limit)
    {
        var companyStaffsQuery = GetCompanyStaffQueryable(companyId, searchString, roles, teamIds, createDateTimeFrom, createDateTimeTo);

        var companyStaffs = await companyStaffsQuery.AsNoTracking().ToListAsync();
        var resellerStaffIdentityIds = await GetResellerStaffIdentityIdsAsync(companyId);

        companyStaffs = companyStaffs.Where(x => !resellerStaffIdentityIds.Contains(x.Staff.IdentityId)).ToList();

        var staffsResult = companyStaffs.Skip(offset ?? 0).Take(limit ?? int.MaxValue).ToList();
        var totalCount = companyStaffs.Count;

        return (staffsResult, totalCount);
    }

    /// <inheritdoc />
    public async Task<int> CountResellerCompanyStaffAsync(
        string companyId,
        string searchString,
        IEnumerable<StaffUserRole> roles,
        IEnumerable<long> teamIds,
        DateTime? createDateTimeFrom,
        DateTime? createDateTimeTo)
    {
        var companyStaffsQuery = GetCompanyStaffQueryable(companyId, searchString, roles, teamIds, createDateTimeFrom, createDateTimeTo);

        var companyStaffs = await companyStaffsQuery.AsNoTracking().ToListAsync();
        var resellerStaffIdentityIds = await GetResellerStaffIdentityIdsAsync(companyId);

        companyStaffs = companyStaffs.Where(x => !resellerStaffIdentityIds.Contains(x.Staff.IdentityId)).ToList();

        return companyStaffs.Count;
    }

    /// <inheritdoc />
    public Task<Staff> GetCompanyOwnerAsync(string companyId)
    {
        return _applicationDbContext.UserRoleStaffs.AsNoTracking()
            .OrderBy(x => x.Order)
            .ThenBy(x => x.Id)
            .FirstOrDefaultAsync(x => x.Id != 1 && x.CompanyId == companyId);
    }

    /// <inheritdoc />
    public async Task<Staff> GetResellerClientCompanyOwnerAsync(string companyId)
    {
        var resellerStaffIdentityIds = await GetResellerStaffIdentityIdsAsync(companyId);

        return await _applicationDbContext.UserRoleStaffs.AsNoTracking()
            .OrderBy(x => x.Order)
            .ThenBy(x => x.Id)
            .FirstOrDefaultAsync(x => x.Id != 1 && x.CompanyId == companyId && !resellerStaffIdentityIds.Contains(x.IdentityId));
    }

    public async Task<Staff> GetUserRoleStaffByStaffIdAsync(string companyId, string staffId)
    {
        if (long.TryParse(staffId, out var searchStaffid))
        {
            return await _applicationDbContext.UserRoleStaffs
                .FirstOrDefaultAsync(x => x.Id == searchStaffid && x.CompanyId == companyId);
        }

        return null;
    }

    public async Task<int> UpdateUserRoleStaffAsync(Staff staff)
    {
        _applicationDbContext.UserRoleStaffs.Update(staff);
        return await _applicationDbContext.SaveChangesAsync();
    }

    #region Private Methods

    private IQueryable<GetCompanyStaffsQueryResult> GetCompanyStaffQueryable(
        string companyId,
        string searchString,
        IEnumerable<StaffUserRole> roles,
        IEnumerable<long> teamIds,
        DateTime? createDateTimeFrom,
        DateTime? createDateTimeTo)
    {
        var query = _applicationDbContext.UserRoleStaffs
            .Include(x => x.Identity)
            .Include(x => x.ProfilePicture)
            .GroupJoin(
                _applicationDbContext.CompanyTeamMembers,
                staff => staff.Id,
                teamMember => teamMember.StaffId,
                (staff, teamMember) => new { Staff = staff, Teams = teamMember.DefaultIfEmpty() })
            .Where(x => x.Staff.CompanyId == companyId && x.Staff.Id != 1)
            .WhereIf(
                !string.IsNullOrWhiteSpace(searchString),
                x => x.Staff.Identity.FirstName.Contains(searchString)
                     || x.Staff.Identity.LastName.Contains(searchString)
                     || x.Staff.Identity.Email.Contains(searchString)
                     || x.Staff.Identity.DisplayName.Contains(searchString))
            .WhereIf(roles != null && roles.Any(), x => roles.Contains(x.Staff.RoleType))
            .WhereIf(teamIds != null && teamIds.Any(), x => x.Teams.Any(member => teamIds.Contains(member.CompanyTeamId)))
            .WhereIf(
                createDateTimeFrom.HasValue && createDateTimeTo.HasValue,
                x => x.Staff.Identity.CreatedAt >= createDateTimeFrom
                     && x.Staff.Identity.CreatedAt <= createDateTimeTo)
            .OrderBy(x => x.Staff.Order)
            .ThenBy(x => x.Staff.Id)
            .Select(x => new GetCompanyStaffsQueryResult(x.Staff, x.Teams))
            .AsQueryable();

        return query;
    }

    private async Task<IEnumerable<string>> GetResellerStaffIdentityIdsAsync(string companyId)
    {
        return await _applicationDbContext.ResellerStaffs
            .Where(x => x.ResellerCompanyProfile.ClientCompanyProfiles.Any(y => y.ClientCompanyId == companyId))
            .Select(x => x.IdentityId)
            .ToListAsync();
    }


    #endregion
}