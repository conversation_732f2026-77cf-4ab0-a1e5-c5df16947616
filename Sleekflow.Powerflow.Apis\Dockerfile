﻿ARG IMAGE_TAG
FROM sleekflow-core-common:${IMAGE_TAG:-latest} AS publish

WORKDIR "/src/Sleekflow.Powerflow.Apis"
RUN dotnet publish "Sleekflow.Powerflow.Apis.csproj" -c Release -o /app/publish --self-contained /p:ExcludeBuildDbMigration=TRUE

FROM mcr.microsoft.com/dotnet/aspnet:8.0.7 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Sleekflow.Powerflow.Apis.dll"]

# Installing Dependencies
RUN apt-get update
RUN apt-get install -y ffmpeg