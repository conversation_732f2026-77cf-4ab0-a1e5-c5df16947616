﻿using Pulumi;
using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Core.Infra.Components.DashboardMetrics;

public class AppServiceMemoryPercentageMetric : IDashboardMetric
{
    private readonly Output<string>? _sleekflowCoreLinuxPlanName;
    private readonly Output<string>? _sleekflowCoreLinuxResourceId;

    public AppServiceMemoryPercentageMetric(
        Output<string>? sleekflowCoreLinuxPlanName,
        Output<string>? sleekflowCoreLinuxResourceId)
    {
        _sleekflowCoreLinuxPlanName = sleekflowCoreLinuxPlanName;
        _sleekflowCoreLinuxResourceId = sleekflowCoreLinuxResourceId;
    }

    public DashboardPartsArgs GetDashboardPartsArgs(
        DashboardPartsPositionArgs position)
    {
        return new DashboardPartsArgs
        {
            Position = position,
            Metadata = new DashboardPartMetadataArgs
            {
                Inputs =
                    new[]
                    {
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "options"
                            },
                            {
                                "value", new Dictionary<string, object>()
                                {
                                    {
                                        "chart", new Dictionary<string, object>()
                                        {
                                            {
                                                "metrics", new[]
                                                {
                                                    new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "aggregationType", 4
                                                        },
                                                        {
                                                            "metricVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "color", "#47BDF5"
                                                                },
                                                                {
                                                                    "displayName", "Available memory"
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "name", "performanceCounters/memoryAvailableBytes"
                                                        },
                                                        {
                                                            "namespace", "microsoft.insights/components"
                                                        },
                                                        {
                                                            "resourceMetadata", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "id", _sleekflowCoreLinuxResourceId!
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            {
                                                "title", "Average available memory"
                                            },
                                            {
                                                "visualization", new Dictionary<string, object>()
                                                {
                                                    {
                                                        "axisVisualization", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "x", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "axisType", 2
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "y", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "axisType", 1
                                                                    },
                                                                    {
                                                                        "isVisible", true
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "chartType", 2
                                                    },
                                                    {
                                                        "legendVisualization", new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "hideSubtitle", false
                                                            },
                                                            {
                                                                "isVisible", true
                                                            },
                                                            {
                                                                "position", 2
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "isOptional", true
                            }
                        },
                        new Dictionary<string, object>()
                        {
                            {
                                "name", "sharedTimeRange"
                            },
                            {
                                "isOptional", true
                            }
                        }
                    },
                Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                Settings =
                {
                    new Dictionary<string, object>()
                    {
                        {
                            "content", new Dictionary<string, object>()
                            {
                                {
                                    "options", new Dictionary<string, object>()
                                    {
                                        {
                                            "chart", new Dictionary<string, object>()
                                            {
                                                {
                                                    "metrics", new[]
                                                    {
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 2
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "Memory Percentage"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName",
                                                                        _sleekflowCoreLinuxPlanName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "MemoryPercentage"
                                                            },
                                                            {
                                                                "namespace", "microsoft.web/serverfarms"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreLinuxResourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 3
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "Memory Percentage"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName",
                                                                        _sleekflowCoreLinuxPlanName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "MemoryPercentage"
                                                            },
                                                            {
                                                                "namespace", "microsoft.web/serverfarms"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreLinuxResourceId!
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        new Dictionary<string, object>()
                                                        {
                                                            {
                                                                "aggregationType", 4
                                                            },
                                                            {
                                                                "metricVisualization", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "displayName", "Memory Percentage"
                                                                    },
                                                                    {
                                                                        "resourceDisplayName",
                                                                        _sleekflowCoreLinuxPlanName!
                                                                    }
                                                                }
                                                            },
                                                            {
                                                                "name", "MemoryPercentage"
                                                            },
                                                            {
                                                                "namespace", "microsoft.web/serverfarms"
                                                            },
                                                            {
                                                                "resourceMetadata", new Dictionary<string, object>()
                                                                {
                                                                    {
                                                                        "id", _sleekflowCoreLinuxResourceId!
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                {
                                                    "title", "App Service Memory Percentage"
                                                },
                                                {
                                                    "titleKind", 2
                                                },
                                                {
                                                    "visualization", new Dictionary<string, object>()
                                                    {
                                                        {
                                                            "axisVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "x", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 2
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    "y", new Dictionary<string, object>()
                                                                    {
                                                                        {
                                                                            "axisType", 1
                                                                        },
                                                                        {
                                                                            "isVisible", true
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        },
                                                        {
                                                            "chartType", 2
                                                        },
                                                        {
                                                            "disablePinning", true
                                                        },
                                                        {
                                                            "legendVisualization", new Dictionary<string, object>()
                                                            {
                                                                {
                                                                    "hideSubtitle", false
                                                                },
                                                                {
                                                                    "isVisible", true
                                                                },
                                                                {
                                                                    "position", 2
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            }
        };
    }
}