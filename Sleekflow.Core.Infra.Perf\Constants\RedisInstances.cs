namespace Sleekflow.Core.Infra.Perf.Constants;

public static class RedisInstances
{
    public const string Default = "Default";
    public const string Caching = "Caching";

    public static List<string> GetInstances()
    {
        return [Default, Caching];
    }

    public static string GetShortName(string redisInstance)
    {
        return redisInstance switch
        {
            Default => "default",
            Caching => "caching",
            _ => throw new Exception("RedistInstances")
        };
    }
}