using Microsoft.EntityFrameworkCore;
using Sleekflow.Core.DataMigrator.Migrations;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.DataMigrator.MigrationScripts;

public class ConversationMessagesMigration : BaseMigration
{
    private readonly MigrationHelper<ConversationMessage> _helper;

    public ConversationMessagesMigration(Configurations configurations)
        : base(configurations)
    {
        _helper = new MigrationHelper<ConversationMessage>(OriginalContext, MigrationContext);
    }

    public override async Task<int> ExecuteAsync(string companyId)
    {
        var originItems = await OriginalContext.ConversationMessages.Where(x => x.CompanyId == companyId).ToListAsync();
        Console.WriteLine($"Finish Gathered Entries for Company: {companyId} from Origin ({originItems.Count} Items)");
        var migratedItems = MigrationContext.ConversationMessages.Where(
                c => (c.CompanyId == companyId || c.CompanyId == "c4109b2b-a897-45a6-8f54-30ec71c381c8"))
            .Select(x => x.Id)
            .ToHashSet();
        Console.WriteLine($"Has migrated conversation messages ({migratedItems.Count} entries)");

        var results = new List<ConversationMessage>();

        foreach (var originItem in originItems)
        {
            if (!migratedItems.Contains(originItem.Id))
            {
                results.Add(originItem);
            }
        }

        Console.WriteLine($"Finish Filtering Entries for Migrations ({results.Count} Items)");

        return await TransactionalExecuteAsync(
            nameof(MigrationContext.ConversationMessages),
            async () =>
            {
                var status = await _helper.MigrationSaveChangesAsync(
                    results,
                    MigrationContext.ConversationMessages,
                    MigrationContext);
                return status;
            });
    }
}