using System.ComponentModel.DataAnnotations;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;

namespace Sleekflow.Core.Tests.CompanyDomain.Services;

public class BusinessHourWeeklyHoursTests
{
    [Test]
    public void DailyHoursValidation_WithValidInput_ShouldPass()
    {
        {
            var dailyHours = new List<Period>
            {
                new (TimeOnly.Parse("00:00:00"), TimeOnly.Parse("23:59:59"), false)
            };

            Assert.DoesNotThrow(
                () =>
                {
                    WeeklyHoursUtil.ValidateDailyHours("v1", dailyHours);

                });
        }

        {
            var dailyHours = new List<Period>();

            Assert.DoesNotThrow(
                () =>
                {
                    WeeklyHoursUtil.ValidateDailyHours("v1", dailyHours);
                });
        }
    }

    [Test]
    public void DailyHoursValidation_WithInValidInput_ShouldThrowException()
    {
        {
            var dailyHours = new List<Period>
            {
                new (TimeOnly.Parse("00:00:00"), TimeOnly.Parse("23:59:59"), true)
            };

            Assert.Throws<ValidationException>(
                () =>
                {
                    WeeklyHoursUtil.ValidateDailyHours("v1", dailyHours);
                });
        }

        {
            var dailyHours = new List<Period>
            {
                new (TimeOnly.Parse("01:00:00"), TimeOnly.Parse("00:00:00"), false)
            };

            Assert.Throws<ValidationException>(
                () =>
                {
                    WeeklyHoursUtil.ValidateDailyHours("v1", dailyHours);
                });
        }

        {
            var dailyHours = new List<Period>
            {
                new (TimeOnly.Parse("00:00:00"), TimeOnly.Parse("00:59:59"), false),
                new (TimeOnly.Parse("10:00:00"), TimeOnly.Parse("11:00:00"), false)

            };

            Assert.Throws<ValidationException>(
                () =>
                {
                    WeeklyHoursUtil.ValidateDailyHours("v1", dailyHours);

                });
        }
    }
}