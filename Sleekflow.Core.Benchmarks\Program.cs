﻿using BenchmarkDotNet.Configs;
using BenchmarkDotNet.Jobs;
using BenchmarkDotNet.Running;
using BenchmarkDotNet.Toolchains.InProcess.NoEmit;

namespace Sleekflow.Core.Benchmarks
{
    public class Program
    {
        public static void Main(string[] args) => BenchmarkSwitcher.FromAssembly(typeof(Program).Assembly)
            .Run(
                args,
                DefaultConfig.Instance.With(
                    Job.Default.With(new InProcessNoEmitToolchain(timeout: TimeSpan.FromHours(1), logOutput: false))));
    }
}