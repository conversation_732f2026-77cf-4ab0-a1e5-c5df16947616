﻿using System;
using System.Collections.Generic;
using System.Linq;
using Travis_backend.AnalyticsDomain.Models;

namespace Travis_backend.AnalyticsDomain.Utils;

public static class TopicAnalyticsMetricUtils
{
    /// <summary>
    /// There may exist more than 1 record for the same dimension data per day,
    /// Recognized by <see cref="TopicAnalyticsMetric.ToBeDeleted"/>.
    /// We assume there are at most 1 record with ToBeDeleted = 0, which has the highest priority for this dimension data.
    /// </summary>
    /// <param name="metrics">Metrics.</param>
    public static void Sanitize(ref List<TopicAnalyticsMetric> metrics)
    {
        // var metricDictionary = new Dictionary<string, TopicAnalyticsMetric>(metrics.Count);
        // foreach (var metric in metrics)
        // {
        //     var key = metric.DimensionDataJson;
        //
        //     if (metric.ToBeDeleted == 0)
        //     {
        //         metricDictionary[key] = metric;
        //     }
        //     else
        //     {
        //         metricDictionary.TryAdd(key, metric);
        //     }
        // }
        //
        // metrics = metricDictionary.Values.ToList();
    }

    public static TopicAnalyticsMetric Aggregate(IEnumerable<TopicAnalyticsMetric> metrics)
    {
        return metrics.Aggregate(
            TopicAnalyticsMetric.Empty(),
            (acc, metric) =>
            {
                acc.TotalConversations += metric.TotalConversations ?? 0;

                return acc;
            });
    }

    public static TopicAnalyticsMetricDto Aggregate(
        DateOnly? date,
        IEnumerable<TopicAnalyticsMetricDto> metrics)
    {
        return metrics.Aggregate(
            TopicAnalyticsMetricDto.Default(date),
            (acc, metric) =>
            {
                acc.TotalConversations += metric.TotalConversations;
                return acc;
            });
    }

    public static TopicAnalyticsMetricDto AggregateToDto(
        DateOnly? date,
        IEnumerable<TopicAnalyticsMetric> metrics)
    {
        return metrics.Aggregate(
            TopicAnalyticsMetricDto.Default(date),
            (acc, metric) =>
            {
                acc.TotalConversations += metric.TotalConversations ?? 0;
                return acc;
            });
    }
}