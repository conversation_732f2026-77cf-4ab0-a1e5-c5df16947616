﻿using System.Reflection;
using System.Text;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Travis_backend.CampaignAnalyticsDomain.Models;
using Travis_backend.CampaignAnalyticsDomain.Services;
using Travis_backend.CampaignAnalyticsDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.FileDomain.Services;

namespace Sleekflow.Core.Tests.CampaignAnalytics;

public class CampaignAnalyticsExporterTests
{
    private ServiceProvider _serviceProvider;
    private CampaignAnalyticsExporter _campaignAnalyticsExporter;
    private IAzureBlobStorageService _azureBlobStorageService;

    [SetUp]
    public void Setup()
    {
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "local_dev";
        if (!string.Equals(environment, "local_dev", StringComparison.OrdinalIgnoreCase) &&
            !string.Equals(environment, "dev", StringComparison.OrdinalIgnoreCase))
        {
            Assert.Ignore("Only runs in dev/Development environment");

            return;
        }

        // Build configuration
        IConfiguration configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        var services = new ServiceCollection();

        services.AddSingleton(configuration);
        services.AddHttpClient();

        // Configure DbContext with real SQL Server database
        services.AddDbContext<ApplicationReadDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("ReadConnection")));

        // Register dependencies (you'll need to configure these with actual connection strings)
        services.AddScoped<ILogger<CampaignAnalyticsExporter>>(_ => NullLogger<CampaignAnalyticsExporter>.Instance);
        services.AddScoped<ILogger<CampaignAnalyticsRepository>>(_ => NullLogger<CampaignAnalyticsRepository>.Instance);
        services.AddScoped<ILogger<AzureBlobStorageService>>(_ => NullLogger<AzureBlobStorageService>.Instance);
        services.AddScoped<ILogger<UploadService>>(_ => NullLogger<UploadService>.Instance);
        services.AddScoped<ICampaignAnalyticsRepository, CampaignAnalyticsRepository>();
        services.AddScoped<ICampaignAnalyticsService, CampaignAnalyticsService>();
        services.AddScoped<IAzureBlobStorageService, AzureBlobStorageService>();
        services.AddScoped<IUploadService, UploadService>();
        services.AddScoped<CampaignAnalyticsExporter>();

        _serviceProvider = services.BuildServiceProvider();
        _campaignAnalyticsExporter = _serviceProvider.GetRequiredService<CampaignAnalyticsExporter>();
        _azureBlobStorageService = _serviceProvider.GetRequiredService<IAzureBlobStorageService>();
    }

    [TearDown]
    public void TearDown()
    {
        _serviceProvider.Dispose();
    }

    [Test]
    public async Task UploadCsvStreamToBlobAsync_WithCorrectInput_ShouldReturnValidResult()
    {
        // Arrange
        var companyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        var csvContent = "Name,Email,Date\nJohn Doe,<EMAIL>,2024-01-01\nJane Smith,<EMAIL>,2024-01-02";
        var fileName = "sampleName";
        var csvStream = new MemoryStream(Encoding.UTF8.GetBytes(csvContent));

        var result = await _campaignAnalyticsExporter.UploadCsvStreamToBlobAsync(
            companyId,
            fileName,
            csvStream,
            CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.That(result.FileName, Is.Not.Empty);
        Assert.That(result.FilePath, Is.Not.Empty);
        Assert.That(result.DownloadUri, Is.Not.Empty);
        Assert.True(result.FileSizeInByte > 0);

        // delete
        await _azureBlobStorageService.DeleteFromAzureBlob(result.FilePath, companyId);
    }

    [Test]
    public async Task GetCampaignMessageWithReplyOverviews_WithCorrectInput_ShouldReturnValidResult()
    {
        var request = new ExportCampaignMessageOverviewsByAnalyticTagRequest
        {
            Status = "sent",
            ReplyWindow = new ReplyWindow("day", 3),
            AnalyticTag = "facebook-lead-ads-disconnected",
        };

        var campaignMessageWithReplyOverviews = await _campaignAnalyticsExporter.GetCampaignMessageWithReplyOverviewsAsync(
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            request,
            0,
            CancellationToken.None);

        Assert.That(campaignMessageWithReplyOverviews.Count, Is.GreaterThan(0));
    }

    [Test]
    public void NormalizeFileName_WithInvalidCharacters_ShouldReplaceInvalidCharactersWithUnderscore()
    {
        // Arrange
        var input = "in,valid/some\"  \\strings";
        var expected = "in,valid_some_  _strings";

        // Act
        var methodInfo = typeof(CampaignAnalyticsExporter).GetMethod("NormalizeFileName",
            BindingFlags.NonPublic | BindingFlags.Static);
        var result = (string)methodInfo.Invoke(null, new object[] { input });

        // Assert
        Assert.That(result, Is.EqualTo(expected));
    }
}