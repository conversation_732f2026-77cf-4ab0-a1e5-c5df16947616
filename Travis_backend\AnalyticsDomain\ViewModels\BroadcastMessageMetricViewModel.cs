using System;

namespace Travis_backend.AnalyticsDomain.ViewModels;

public class BroadcastMessageMetricViewModel
{
    public string Date { get; set; }

    public int NumberOfBroadcastSent { get; set; }

    public int NumberOfBroadcastBounced { get; set; }

    public int NumberOfBroadcastDelivered { get; set; }

    public int NumberOfBroadcastRead { get; set; }

    public int NumberOfBroadcastReplied { get; set; }

    public BroadcastMessageMetricViewModel(DateOnly? date, int sent, int bounced, int delivered, int read, int replied)
    {
        Date = date?.ToString("yyyy-MM-dd");
        NumberOfBroadcastSent = sent;
        NumberOfBroadcastBounced = bounced;
        NumberOfBroadcastDelivered = delivered;
        NumberOfBroadcastRead = read;
        NumberOfBroadcastReplied = replied;
    }

    public static BroadcastMessageMetricViewModel Default(DateOnly? date = null)
    {
        return new BroadcastMessageMetricViewModel(date, 0, 0, 0, 0, 0);
    }
}