using Pulumi;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Components.Models;
using Sleekflow.Core.Infra.Constants;
using Sleekflow.Core.Infra.Utils;
using Web = Pulumi.AzureNative.Web;
using Insights = Pulumi.AzureNative.Insights;
using Network = Pulumi.AzureNative.Network;

namespace Sleekflow.Core.Infra.Components.SleekflowCore;

public class SleekflowSleekPay
{
    private readonly MyConfig _myConfig;
    private readonly List<EnvGroup> _envGroups;
    private readonly ServerConfig _serverConfig;

    public SleekflowSleekPay(
        MyConfig myConfig,
        List<EnvGroup> envGroups,
        ServerConfig serverConfig)
    {
        _myConfig = myConfig;
        _envGroups = envGroups;
        _serverConfig = serverConfig;
    }

    public void InitSleekPay()
    {
        foreach (var envGroup in _envGroups)
        {
            var webApps = envGroup.WebApps;
            var resourceGroup = envGroup.ResourceGroup;
            var logAnalyticsWorkspace = envGroup.LogAnalyticsWorkspace;

            var sleekflowCore = webApps.TryGetValue(ServiceNames.SleekflowCore, out var value) ? value.WebApp : null;

            var (storageAccount, storageContainer, blob) =
                new BlobStorage(_myConfig, envGroup.LocationName, resourceGroup).InitFunctionAppBlobStorage(
                    "sleekflow-core-sleek-pay",
                    "../Sleekflow.SleekPay/bin/Release/net8.0/publish");

            var regionalConfig = _serverConfig
                .RegionalConfigs
                .First(s => s.LocationName == envGroup.LocationName);

            var appInsights = new Insights.Component(
                $"sleekflow-core-sleek-pay-app-insight-{LocationNames.GetShortName(envGroup.LocationName)}",
                new Insights.ComponentArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    ApplicationType = Insights.ApplicationType.Web,
                    FlowType = "Redfield",
                    RequestSource = "IbizaWebAppExtensionCreate",
                    Kind = "Web",
                    WorkspaceResourceId = logAnalyticsWorkspace.Id
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var skuConfig = regionalConfig.SkuConfig.SleekflowSleekPay;

            var appServicePlan = new Web.AppServicePlan(
                $"sleekflow-core-sleek-pay-app-service-plan-{LocationNames.GetShortName(envGroup.LocationName)}",
                new Web.AppServicePlanArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    Kind = string.Empty,
                    Sku = new Web.Inputs.SkuDescriptionArgs
                    {
                        Name = skuConfig.Name, Tier = skuConfig.Tier,
                    }
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var webAppName = ResourceUtils.GetName(
                envGroup.FormatAppName(ServiceNames.GetShortName(ServiceNames.SleekPay)),
                _myConfig);

            var appSettings = AppSettingUtils.GetSleekPaySetting(
                webAppName,
                blob,
                envGroup.Redis,
                sleekflowCore!,
                resourceGroup,
                appInsights,
                storageContainer,
                storageAccount,
                regionalConfig.SleekflowCoreConfig,
                envGroup.SqlServerProperties!,
                logAnalyticsWorkspace);

            var app = new Web.WebApp(
                webAppName,
                new Web.WebAppArgs
                {
                    Kind = "FunctionApp",
                    ResourceGroupName = resourceGroup.Name,
                    ServerFarmId = appServicePlan.Id,
                    SiteConfig = new Web.Inputs.SiteConfigArgs
                    {
                        AppSettings = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(appSettings),
                        Use32BitWorkerProcess = true,
                        NetFrameworkVersion = "v8.0"
                    },
                },
                new CustomResourceOptions
                {
                    Parent = appServicePlan
                });

            var diagnosticSetting = new Insights.DiagnosticSetting(
                $"sleekflow-core-sleek-pay-app-diagnostic-setting-{LocationNames.GetShortName(envGroup.LocationName)}",
                new Insights.DiagnosticSettingArgs
                {
                    ResourceUri = app.Id,
                    WorkspaceId = logAnalyticsWorkspace.Id,
                    Logs = new[]
                    {
                        new Insights.Inputs.LogSettingsArgs
                        {
                            Category = "FunctionAppLogs", Enabled = true
                        }
                    }
                });

            var subnet = new Network.Subnet(
                ResourceUtils.GetName(
                    $"sleekflow-core-sleek-pay-subnet-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Network.SubnetArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    VirtualNetworkName = envGroup.VirtualNetwork.Name,
                    AddressPrefix = regionalConfig.VnetConfig.SleekflowSleekPayAddressPrefix,
                    Delegations =
                    {
                        new Network.Inputs.DelegationArgs
                        {
                            Name = "sleekflow-core-sleek-pay-app-subnet-delegation",
                            ServiceName = "Microsoft.Web/serverfarms"
                        }
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var webAppSwiftVirtualNetworkConnection = new Web.WebAppSwiftVirtualNetworkConnection(
                ResourceUtils.GetName(
                    $"sleekflow-core-sleek-pay-app-swift-virtual-network-connection-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Web.WebAppSwiftVirtualNetworkConnectionArgs
                {
                    Name = app.Name, SubnetResourceId = subnet.Id, ResourceGroupName = resourceGroup.Name,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            webApps.Add(ServiceNames.SleekPay, new AppServiceConfiguration(webAppName, app, appInsights, appServicePlan));
        }
    }
}