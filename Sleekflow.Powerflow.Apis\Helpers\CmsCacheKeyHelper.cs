﻿using Travis_backend.Helpers;
using <PERSON>_backend.InternalDomain.Models;

namespace Sleekflow.Powerflow.Apis.Helpers;

public static class CmsCacheKeyHelper
{
    public const string GetSelectionsKey = "Internal_GetAllCmsSelectionsResponse";
    public const string GetAllCompaniesKey = "Internal_GetCompanies_IsGetAllCompany";

    #region Revenue pages

    public static string GetRevenueAnalyticKey(
        AnalyticType analyticType,
        string analyticCategory,
        DateTime start,
        DateTime end,
        int timezoneHourOffset,
        bool excludeMarkupPlan,
        bool isByOwnerTeams = false,
        List<string> ownerIds = null,
        List<string> teamNames = null,
        List<string> groupNames = null)
    {
        switch (analyticType)
        {
            case AnalyticType.All:
                return
                    $"Internal_{analyticCategory}_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}";
            case AnalyticType.ActivationOwner:
            case AnalyticType.CompanyOwner:
            case AnalyticType.CsOwner:
                if (isByOwnerTeams)
                {
                    return teamNames.Count > 0
                        ? $"Internal_{analyticCategory}_{analyticType.ToString()}_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{isByOwnerTeams}_{ListOfStringHash(teamNames)}"
                        : $"Internal_{analyticCategory}_{analyticType.ToString()}_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{isByOwnerTeams}";
                }

                return ownerIds.Count > 0
                    ? $"Internal_{analyticCategory}_{analyticType.ToString()}_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{isByOwnerTeams}_{ListOfStringHash(ownerIds)}"
                    : $"Internal_{analyticCategory}_{analyticType.ToString()}_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{isByOwnerTeams}";
            case AnalyticType.PartnerStackGroup:
                return groupNames is { Count: > 0 }
                    ? $"Internal_{analyticCategory}_{analyticType.ToString()}_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{ListOfStringHash(groupNames)}"
                    : $"Internal_{analyticCategory}_{analyticType.ToString()}_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}";
            default:
                throw new ArgumentOutOfRangeException(nameof(analyticType), analyticType, null);
        }
    }

    public static string GetCmsAccruedAnalyticKey(
        DateTime start,
        DateTime end,
        int timezoneHourOffset,
        bool excludeMarkupPlan)
    {
        return
            $"Internal_GetCmsAccruedAnalytic_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}";
    }

    #endregion

    public static string GetAllPartnerChannelsByClientId(string partnerId)
    {
        return $"Internal_GetAllPartnerChannelsByClientId_{partnerId}";
    }

    public static string GetCmsCompaniesChurnReasonKey(
        List<string> companyIds,
        bool hasChurnReason,
        bool hasTier,
        bool hasAllTimeRevenueAnalyticData)
    {
        if (companyIds is { Count: > 0 })
        {
            return
                $"Internal_GetCompaniesChurnReasonKey_{ListOfStringHash(companyIds)}_{hasChurnReason}_{hasTier}_{hasAllTimeRevenueAnalyticData}";
        }

        return $"Internal_GetCompaniesChurnReasonKey_{hasChurnReason}_{hasTier}_{hasAllTimeRevenueAnalyticData}";
    }

    public static string GetCmsGetSubscriptionPlansKey()
    {
        return $"Internal_GetSubscriptionPlans";
    }

    public static string GetAllWhatsAppCloudApiBalance()
    {
        return $"Internal_GetAllWhatsAppCloudApiBalance";
    }

    public static string GetAllWhatsappCloudApiWabas()
    {
        return $"Internal_GetAllWhatsappCloudApiWabas";
    }

    public static string GetConnectedWabasByCompanyId(string companyId)
    {
        return $"Internal_GetConnectedWabasBy{companyId}";
    }

    private static string ListOfStringHash(List<string> stringList)
    {
        return SHA256Helper.sha256_hash(string.Join(',', stringList.OrderBy(x => x).ToArray()));
    }

    public static string GetCmsCompaniesFlowHubData()
    {
        return "Internal_GetCmsCompaniesFlowHubData";
    }

    public static string GetCmsCompaniesIntelligentHubData()
    {
        return "Internal_GetCmsCompaniesIntelligentHubData";
    }

    public static string GetExpiringCompanySubscriptionPlan(DateOnly today, List<string> ownerIds)
    {
        return
            $"Internal_GetExpiringCompanySubscriptionPlan_{today:yyyyMMdd}_{ListOfStringHash(
                ownerIds)}";
    }
}