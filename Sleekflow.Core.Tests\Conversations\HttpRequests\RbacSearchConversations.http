### Rbac Login - (DEV only)

POST https://localhost:5000/auth0/account/getusertoken
content-type: application/json-patch+json

{
  "username": "<EMAIL>",
  "password": "MP13005926"
}

> {% client.global.set("token", response.body.accessToken); %}

### Rbac Search Conversations By Message
POST https://{{host}}/v2/conversation/assignee/{{conversation_Id}}
content-type: application/json
Authorization: Bearer {{token}}

{
}

### Rbac Search Conversations By Contact
POST https://{{host}}/v2/conversation/assignee/{{conversation_Id}}
content-type: application/json
Authorization: Bearer {{token}}

{
}

### Rbac Get Conversations Summary
GET https://{{host}}/v3/conversation/summary/92638df2-3f30-4a94-81c9-8786280051ef?status=open&behaviourVersion=2
content-type: application/json
Authorization: Bearer {{token}}

{
}

### Rbac Get Conversations
GET https://{{host}}/v3/conversations/all?offset=0&limit=20&status=open&orderBy=desc&isAssigned=true&behaviourVersion=1
content-type: application/json
Authorization: Bearer {{token}}

{
}

### Rbac Get Conversations
GET https://{{host}}/Conversation/a15f8279-0d0f-4d1b-872a-40ddeacb5f5f
content-type: application/json
Authorization: Bearer {{token}}

{
}