using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Travis_backend.ChannelDomain.Models.Interfaces;
using Travis_backend.CompanyDomain.Models;

namespace Travis_backend.ChannelDomain.Models;

public class TikTokConfig : IMessagingChannel
{
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string CompanyId { get; set; }

    [ForeignKey(nameof(CompanyId))]
    public Company Company { get; set; }
    public string ChannelType { get; set; }
    public string ChannelIdentityId { get; set; }
    public string ChannelDisplayName { get; set; }
    public string TikTokConfigId { get; set; }
    public Dictionary<string, object> Metadata { get; set; }
}