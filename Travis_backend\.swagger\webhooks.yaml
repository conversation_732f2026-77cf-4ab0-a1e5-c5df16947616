openapi: 3.0.1
info:
  title: Sleekflow Api
  description: Sleekflow Api
  version: webhooks
paths:
  '/email/webhook/{companyId}':
    post:
      tags:
        - EmailWebhooks
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
  /facebook/ads/connect:
    get:
      tags:
        - FacebookAdsWebhooks
      parameters:
        - name: code
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
  /facebook/ads/connect/access_token:
    get:
      tags:
        - FacebookAdsWebhooks
      parameters:
        - name: access_token
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
  /facebook/ads/ConnectionURL:
    get:
      tags:
        - FacebookAdsWebhooks
      parameters:
        - name: appDomainName
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
  /facebook/ads/subscrible:
    post:
      tags:
        - FacebookAdsWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.CompanyDomain.ViewModels.ConnectFacebookViewModel'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.CompanyDomain.ViewModels.ConnectFacebookViewModel'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.CompanyDomain.ViewModels.ConnectFacebookViewModel'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.CompanyDomain.ViewModels.ConnectFacebookViewModel'
      responses:
        '200':
          description: OK
  '/facebook/ads/unsubscribe/{pageId}':
    delete:
      tags:
        - FacebookAdsWebhooks
      parameters:
        - name: pageId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
  '/facebook/ads/{pageId}':
    post:
      tags:
        - FacebookAdsWebhooks
      parameters:
        - name: pageId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.CompanyDomain.ViewModels.UpdateFacebookViewModel'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.CompanyDomain.ViewModels.UpdateFacebookViewModel'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.CompanyDomain.ViewModels.UpdateFacebookViewModel'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.CompanyDomain.ViewModels.UpdateFacebookViewModel'
      responses:
        '200':
          description: OK
  /facebook/ads/all/status:
    post:
      tags:
        - FacebookAdsWebhooks
      responses:
        '200':
          description: OK
  /facebook/ads/getnotificationconfig:
    post:
      tags:
        - FacebookAdsWebhooks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookAdsWebhooksController.GetNotificationConfigInput'
      responses:
        '200':
          description: OK
  /facebook/ads/createnotificationconfig:
    post:
      tags:
        - FacebookAdsWebhooks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookAdsWebhooksController.CreateNotificationConfigInput'
      responses:
        '200':
          description: OK
  /facebook/ads/updatenotificationconfig:
    post:
      tags:
        - FacebookAdsWebhooks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookAdsWebhooksController.UpdateNotificationConfigInput'
      responses:
        '200':
          description: OK
  /facebook/ads/deletenotificationconfig:
    post:
      tags:
        - FacebookAdsWebhooks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookAdsWebhooksController.DeleteNotificationConfigInput'
      responses:
        '200':
          description: OK
  /facebook/ads/adhocleadgencheck:
    post:
      tags:
        - FacebookAdsWebhooks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookAdsWebhooksController.TriggerAdHocLeadGenCheckInput'
      responses:
        '200':
          description: OK
  /facebook/Webhook:
    get:
      tags:
        - FacebookWebhooks
      parameters:
        - name: hub.mode
          in: query
          schema:
            type: string
        - name: hub.challenge
          in: query
          schema:
            type: string
        - name: hub.verify_token
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                type: string
            application/json:
              schema:
                type: string
            text/json:
              schema:
                type: string
    post:
      tags:
        - FacebookWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema: { }
          application/json:
            schema: { }
          text/json:
            schema: { }
          application/*+json:
            schema: { }
      responses:
        '200':
          description: OK
  /facebook/webhook/register-all:
    post:
      tags:
        - FacebookWebhooks
      parameters:
        - name: IsIg
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: OK
  /facebook/webhook/register:
    post:
      tags:
        - FacebookWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.RegisterWebhookRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.RegisterWebhookRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.RegisterWebhookRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.RegisterWebhookRequest'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.RegisterWebhookResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.RegisterWebhookResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.RegisterWebhookResponse'
  /facebook/webhook/Deregister:
    post:
      tags:
        - FacebookWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.DeregisterWebhookRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.DeregisterWebhookRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.DeregisterWebhookRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.DeregisterWebhookRequest'
      responses:
        '200':
          description: OK
  /facebook/webhook/ForwardWebhook:
    post:
      tags:
        - FacebookWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema: { }
          application/json:
            schema: { }
          text/json:
            schema: { }
          application/*+json:
            schema: { }
      responses:
        '200':
          description: OK
  /facebook/webhook/ReplayCompanyFailedWebhookEvents:
    post:
      tags:
        - FacebookWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayCompanyFailedWebhookEventsRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayCompanyFailedWebhookEventsRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayCompanyFailedWebhookEventsRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayCompanyFailedWebhookEventsRequest'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayCompanyFailedWebhookEventsResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayCompanyFailedWebhookEventsResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayCompanyFailedWebhookEventsResponse'
  /facebook/webhook/GetAllFailedFacebookWebhookEventIds:
    post:
      tags:
        - FacebookWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetAllFailedFacebookWebhookEventIdsRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetAllFailedFacebookWebhookEventIdsRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetAllFailedFacebookWebhookEventIdsRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetAllFailedFacebookWebhookEventIdsRequest'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetAllFailedFacebookWebhookEventIdsResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetAllFailedFacebookWebhookEventIdsResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetAllFailedFacebookWebhookEventIdsResponse'
  /facebook/webhook/ReplayWebhookEvents:
    post:
      tags:
        - FacebookWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayWebhookEventsRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayWebhookEventsRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayWebhookEventsRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayWebhookEventsRequest'
      responses:
        '200':
          description: OK
  /facebook/webhook/GetCompanyActiveWebhooks:
    post:
      tags:
        - FacebookWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksRequest'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksResponse'
  /facebook/webhook/GetCompanyInactiveWebhooks:
    post:
      tags:
        - FacebookWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyInactiveWebhooksRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyInactiveWebhooksRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyInactiveWebhooksRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyInactiveWebhooksRequest'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksResponse'
  /facebook/webhook/HandleFacebookWebhookPayload:
    post:
      tags:
        - FacebookWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema: { }
          application/json:
            schema: { }
          text/json:
            schema: { }
          application/*+json:
            schema: { }
      responses:
        '200':
          description: OK
  /facebook/webhook/HandleInstagramWebhookPayload:
    post:
      tags:
        - FacebookWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema: { }
          application/json:
            schema: { }
          text/json:
            schema: { }
          application/*+json:
            schema: { }
      responses:
        '200':
          description: OK
  /facebook/connect:
    get:
      tags:
        - FacebookWebhooks
      parameters:
        - name: code
          in: query
          schema:
            type: string
        - name: appDomainName
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
  /instagram/connect:
    get:
      tags:
        - FacebookWebhooks
      parameters:
        - name: code
          in: query
          schema:
            type: string
        - name: appDomainName
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
  /facebook/connect/access_token:
    get:
      tags:
        - FacebookWebhooks
      parameters:
        - name: access_token
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
  /FlowHub/Webhooks/Events/IncomingMessage/Sample:
    get:
      tags:
        - FlowHubWebhookSamples
      responses:
        '200':
          description: OK
  /FlowHub/Webhooks/Events/OutgoingMessage/Sample:
    get:
      tags:
        - FlowHubWebhookSamples
      responses:
        '200':
          description: OK
  /FlowHub/Webhooks/Events/IncomingCtwaMessage/Sample:
    get:
      tags:
        - FlowHubWebhookSamples
      responses:
        '200':
          description: OK
  /FlowHub/Webhooks/Events/ContactCreated/Sample:
    get:
      tags:
        - FlowHubWebhookSamples
      responses:
        '200':
          description: OK
  /FlowHub/Webhooks/Events/ContactFieldsUpdated/Sample:
    get:
      tags:
        - FlowHubWebhookSamples
      responses:
        '200':
          description: OK
  /FlowHub/Webhooks/Events/ConversationLabelAdded/Sample:
    get:
      tags:
        - FlowHubWebhookSamples
      responses:
        '200':
          description: OK
  /FlowHub/Webhooks/Events/ConversationLabelRemoved/Sample:
    get:
      tags:
        - FlowHubWebhookSamples
      responses:
        '200':
          description: OK
  /FlowHub/Webhooks/Events/ContactAddedToList/Sample:
    get:
      tags:
        - FlowHubWebhookSamples
      responses:
        '200':
          description: OK
  /FlowHub/Webhooks/Events/ContactRemovedFromList/Sample:
    get:
      tags:
        - FlowHubWebhookSamples
      responses:
        '200':
          description: OK
  /FlowHub/Webhooks/Events/ConversationStatusChanged/Sample:
    get:
      tags:
        - FlowHubWebhookSamples
      responses:
        '200':
          description: OK
  /FlowHub/Webhooks/Events/Sample:
    post:
      tags:
        - FlowHubWebhookSamples
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.FlowHubs.Models.WebhookSampleInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.FlowHubs.Models.WebhookSampleInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.FlowHubs.Models.WebhookSampleInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.FlowHubs.Models.WebhookSampleInput'
      responses:
        '200':
          description: OK
  '/telegram/webhook/{companyId}':
    post:
      tags:
        - TelegramWebhooks
      summary: Telegram Webhook for receiving Telegram bot callback.
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: telegramBotId
          in: query
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Telegram.Bot.Types.Update'
          application/json:
            schema:
              $ref: '#/components/schemas/Telegram.Bot.Types.Update'
          text/json:
            schema:
              $ref: '#/components/schemas/Telegram.Bot.Types.Update'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Telegram.Bot.Types.Update'
      responses:
        '200':
          description: OK
  /sms/twilio/webhook:
    post:
      tags:
        - TwilioSMSWebhooks
      parameters:
        - name: companyId
          in: query
          schema:
            type: string
        - name: SmsSid
          in: query
          schema:
            type: string
        - name: Body
          in: query
          schema:
            type: string
        - name: MessageStatus
          in: query
          schema:
            type: string
        - name: OptOutType
          in: query
          schema:
            type: string
        - name: MessagingServiceSid
          in: query
          schema:
            type: string
        - name: AccountSid
          in: query
          schema:
            type: string
        - name: From
          in: query
          schema:
            type: string
        - name: To
          in: query
          schema:
            type: string
        - name: FromCity
          in: query
          schema:
            type: string
        - name: FromState
          in: query
          schema:
            type: string
        - name: FromZip
          in: query
          schema:
            type: string
        - name: FromCountry
          in: query
          schema:
            type: string
        - name: ToCity
          in: query
          schema:
            type: string
        - name: ToState
          in: query
          schema:
            type: string
        - name: ToZip
          in: query
          schema:
            type: string
        - name: ToCountry
          in: query
          schema:
            type: string
        - name: numMedia
          in: query
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
  /twilio/usage:
    post:
      tags:
        - TwilioWebhooks
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.ChannelDomain.ViewModels.TwilioResponseViewModel'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.ChannelDomain.ViewModels.TwilioResponseViewModel'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.ChannelDomain.ViewModels.TwilioResponseViewModel'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.ChannelDomain.ViewModels.TwilioResponseViewModel'
      responses:
        '200':
          description: OK
  /whatsapp/twilio/webhook:
    post:
      tags:
        - TwilioWebhooks
      parameters:
        - name: companyId
          in: query
          schema:
            type: string
        - name: SmsSid
          in: query
          schema:
            type: string
        - name: Body
          in: query
          schema:
            type: string
        - name: MessageStatus
          in: query
          schema:
            type: string
        - name: OptOutType
          in: query
          schema:
            type: string
        - name: MessagingServiceSid
          in: query
          schema:
            type: string
        - name: AccountSid
          in: query
          schema:
            type: string
        - name: From
          in: query
          schema:
            type: string
        - name: To
          in: query
          schema:
            type: string
        - name: FromCity
          in: query
          schema:
            type: string
        - name: FromState
          in: query
          schema:
            type: string
        - name: FromZip
          in: query
          schema:
            type: string
        - name: FromCountry
          in: query
          schema:
            type: string
        - name: ToCity
          in: query
          schema:
            type: string
        - name: ToState
          in: query
          schema:
            type: string
        - name: ToZip
          in: query
          schema:
            type: string
        - name: ToCountry
          in: query
          schema:
            type: string
        - name: numMedia
          in: query
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
  /twilio/webhook/status:
    post:
      tags:
        - TwilioWebhooks
      parameters:
        - name: ErrorCode
          in: query
          schema:
            type: string
        - name: ChannelInstallSid
          in: query
          schema:
            type: string
        - name: ChannelStatusMessage
          in: query
          schema:
            type: string
        - name: ChannelPrefix
          in: query
          schema:
            type: string
        - name: EventType
          in: query
          schema:
            type: string
        - name: SmsSid
          in: query
          schema:
            type: string
        - name: Body
          in: query
          schema:
            type: string
        - name: MessageStatus
          in: query
          schema:
            type: string
        - name: OptOutType
          in: query
          schema:
            type: string
        - name: MessagingServiceSid
          in: query
          schema:
            type: string
        - name: AccountSid
          in: query
          schema:
            type: string
        - name: From
          in: query
          schema:
            type: string
        - name: To
          in: query
          schema:
            type: string
        - name: FromCity
          in: query
          schema:
            type: string
        - name: FromState
          in: query
          schema:
            type: string
        - name: FromZip
          in: query
          schema:
            type: string
        - name: FromCountry
          in: query
          schema:
            type: string
        - name: ToCity
          in: query
          schema:
            type: string
        - name: ToState
          in: query
          schema:
            type: string
        - name: ToZip
          in: query
          schema:
            type: string
        - name: ToCountry
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
  '/viber/webhook/{companyId}':
    post:
      tags:
        - ViberWebhooks
      summary: Viber Webhook Api for receiving Viber bot callback.
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: viberBotId
          in: query
          schema:
            type: string
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.ChannelDomain.ViewModels.ViberWebhookCallback'
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.ChannelDomain.ViewModels.ViberWebhookCallback'
          text/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.ChannelDomain.ViewModels.ViberWebhookCallback'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Travis_backend.ChannelDomain.ViewModels.ViberWebhookCallback'
      responses:
        '200':
          description: OK
  '/wavecell/webhook/{companyId}':
    get:
      tags:
        - WavecellWebhooks
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
    post:
      tags:
        - WavecellWebhooks
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json-patch+json:
            schema: { }
          application/json:
            schema: { }
          text/json:
            schema: { }
          application/*+json:
            schema: { }
      responses:
        '200':
          description: OK
  '/wechat/Webhook/{companyId}':
    get:
      tags:
        - WeChatWebhooks
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: Signature
          in: query
          schema:
            type: string
        - name: Timestamp
          in: query
          schema:
            type: string
        - name: Nonce
          in: query
          schema:
            type: string
        - name: Echostr
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
    post:
      tags:
        - WeChatWebhooks
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
  '/wechat/ExperimentalWebhook/{companyId}':
    get:
      tags:
        - WeChatWebhooks
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: Signature
          in: query
          schema:
            type: string
        - name: Timestamp
          in: query
          schema:
            type: string
        - name: Nonce
          in: query
          schema:
            type: string
        - name: Echostr
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
    post:
      tags:
        - WeChatWebhooks
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
  /whatsapp/360dialog/partner/webhook:
    post:
      tags:
        - WhatsApp360DialogPartnerWebhooks
      parameters:
        - name: partnerId
          in: query
          schema:
            type: string
        - name: verifyKey
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
  /whatsapp/360dialog/webhook:
    post:
      tags:
        - WhatsApp360DialogWebhooks
      responses:
        '200':
          description: OK
  '/whatsapp/cloudapi/businessbalance/webhook/{companyId}/{facebookBusinessId}':
    post:
      tags:
        - WhatsappCloudApiBusinessBalanceWebhooks
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: facebookBusinessId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
  '/whatsapp/cloudapi/webhook/{companyId}/{messagingHubPhoneNumberId}':
    post:
      tags:
        - WhatsappCloudApiWebhooks
      parameters:
        - name: companyId
          in: path
          required: true
          schema:
            type: string
        - name: messagingHubPhoneNumberId
          in: path
          required: true
          schema:
            type: string
        - name: verify_code
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
components:
  schemas:
    Sleekflow.Apis.WebhookHub.Model.WebhookProperty:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        entity_type_name:
          type: string
          nullable: true
        event_type_name:
          type: string
          nullable: true
        webhook_url:
          type: string
          nullable: true
        is_active:
          type: boolean
        additional_context:
          type: object
          additionalProperties: { }
          nullable: true
        max_retry_count:
          type: integer
          format: int32
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.Animation:
      required:
        - duration
        - file_id
        - file_unique_id
        - height
        - width
      type: object
      properties:
        width:
          type: integer
          format: int32
        height:
          type: integer
          format: int32
        duration:
          type: integer
          format: int32
        thumb:
          $ref: '#/components/schemas/Telegram.Bot.Types.PhotoSize'
        file_name:
          type: string
          nullable: true
        mime_type:
          type: string
          nullable: true
        file_id:
          type: string
        file_unique_id:
          type: string
        file_size:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.Audio:
      required:
        - duration
        - file_id
        - file_unique_id
      type: object
      properties:
        duration:
          type: integer
          format: int32
        performer:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        file_name:
          type: string
          nullable: true
        mime_type:
          type: string
          nullable: true
        thumb:
          $ref: '#/components/schemas/Telegram.Bot.Types.PhotoSize'
        file_id:
          type: string
        file_unique_id:
          type: string
        file_size:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.CallbackGame:
      type: object
      additionalProperties: false
    Telegram.Bot.Types.CallbackQuery:
      required:
        - chat_instance
        - from
        - id
      type: object
      properties:
        id:
          type: string
        from:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        message:
          $ref: '#/components/schemas/Telegram.Bot.Types.Message'
        inline_message_id:
          type: string
          nullable: true
        chat_instance:
          type: string
        data:
          type: string
          nullable: true
        game_short_name:
          type: string
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.Chat:
      required:
        - id
        - type
      type: object
      properties:
        id:
          type: integer
          format: int64
        type:
          $ref: '#/components/schemas/Telegram.Bot.Types.Enums.ChatType'
        title:
          type: string
          nullable: true
        username:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        photo:
          $ref: '#/components/schemas/Telegram.Bot.Types.ChatPhoto'
        bio:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        invite_link:
          type: string
          nullable: true
        pinned_message:
          $ref: '#/components/schemas/Telegram.Bot.Types.Message'
        permissions:
          $ref: '#/components/schemas/Telegram.Bot.Types.ChatPermissions'
        slow_mode_delay:
          type: integer
          format: int32
          nullable: true
        sticker_set_name:
          type: string
          nullable: true
        can_set_sticker_set:
          type: boolean
          nullable: true
        linked_chat_id:
          type: integer
          format: int64
        location:
          $ref: '#/components/schemas/Telegram.Bot.Types.ChatLocation'
      additionalProperties: false
    Telegram.Bot.Types.ChatInviteLink:
      required:
        - creator
        - invite_link
      type: object
      properties:
        invite_link:
          type: string
        creator:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        is_primary:
          type: boolean
          nullable: true
        is_revoked:
          type: boolean
          nullable: true
        expire_date:
          type: string
          format: date-time
          nullable: true
        member_limit:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.ChatLocation:
      required:
        - location
        - string
      type: object
      properties:
        location:
          $ref: '#/components/schemas/Telegram.Bot.Types.Location'
        string:
          type: string
      additionalProperties: false
    Telegram.Bot.Types.ChatMember:
      required:
        - status
        - user
      type: object
      properties:
        user:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        status:
          $ref: '#/components/schemas/Telegram.Bot.Types.Enums.ChatMemberStatus'
        custom_title:
          type: string
          nullable: true
        is_anonymous:
          type: boolean
        until_date:
          type: string
          format: date-time
          nullable: true
        can_be_edited:
          type: boolean
          nullable: true
        can_change_info:
          type: boolean
          nullable: true
        can_post_messages:
          type: boolean
          nullable: true
        can_edit_messages:
          type: boolean
          nullable: true
        can_delete_messages:
          type: boolean
          nullable: true
        can_invite_users:
          type: boolean
          nullable: true
        can_restrict_members:
          type: boolean
          nullable: true
        can_pin_messages:
          type: boolean
          nullable: true
        can_promote_members:
          type: boolean
          nullable: true
        is_member:
          type: boolean
          nullable: true
        can_send_messages:
          type: boolean
          nullable: true
        can_send_media_messages:
          type: boolean
          nullable: true
        can_send_polls:
          type: boolean
          nullable: true
        can_send_other_messages:
          type: boolean
          nullable: true
        can_add_web_page_previews:
          type: boolean
          nullable: true
        can_manage_chat:
          type: boolean
          nullable: true
        can_manage_voice_chats:
          type: boolean
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.ChatMemberUpdated:
      required:
        - chat
        - date
        - from
        - new_chat_member
        - old_chat_member
      type: object
      properties:
        chat:
          $ref: '#/components/schemas/Telegram.Bot.Types.Chat'
        from:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        date:
          type: string
          format: date-time
        old_chat_member:
          $ref: '#/components/schemas/Telegram.Bot.Types.ChatMember'
        new_chat_member:
          $ref: '#/components/schemas/Telegram.Bot.Types.ChatMember'
        invite_link:
          $ref: '#/components/schemas/Telegram.Bot.Types.ChatInviteLink'
      additionalProperties: false
    Telegram.Bot.Types.ChatPermissions:
      type: object
      properties:
        can_send_messages:
          type: boolean
          nullable: true
        can_send_media_messages:
          type: boolean
          nullable: true
        can_send_polls:
          type: boolean
          nullable: true
        can_send_other_messages:
          type: boolean
          nullable: true
        can_add_web_page_previews:
          type: boolean
          nullable: true
        can_change_info:
          type: boolean
          nullable: true
        can_invite_users:
          type: boolean
          nullable: true
        can_pin_messages:
          type: boolean
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.ChatPhoto:
      required:
        - big_file_id
        - big_file_unique_id
        - small_file_id
        - small_file_unique_id
      type: object
      properties:
        big_file_id:
          type: string
        big_file_unique_id:
          type: string
        small_file_id:
          type: string
        small_file_unique_id:
          type: string
      additionalProperties: false
    Telegram.Bot.Types.ChosenInlineResult:
      required:
        - from
        - query
        - result_id
      type: object
      properties:
        result_id:
          type: string
        from:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        location:
          $ref: '#/components/schemas/Telegram.Bot.Types.Location'
        inline_message_id:
          type: string
          nullable: true
        query:
          type: string
      additionalProperties: false
    Telegram.Bot.Types.Contact:
      required:
        - first_name
        - phone_number
      type: object
      properties:
        phone_number:
          type: string
        first_name:
          type: string
        last_name:
          type: string
          nullable: true
        user_id:
          type: integer
          format: int64
        vcard:
          type: string
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.Dice:
      required:
        - emoji
        - value
      type: object
      properties:
        emoji:
          type: string
        value:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.Document:
      required:
        - file_id
        - file_unique_id
      type: object
      properties:
        thumb:
          $ref: '#/components/schemas/Telegram.Bot.Types.PhotoSize'
        file_name:
          type: string
          nullable: true
        mime_type:
          type: string
          nullable: true
        file_id:
          type: string
        file_unique_id:
          type: string
        file_size:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.Enums.ChatMemberStatus:
      enum:
        - creator
        - administrator
        - member
        - left
        - kicked
        - restricted
      type: string
    Telegram.Bot.Types.Enums.ChatType:
      enum:
        - private
        - group
        - channel
        - supergroup
        - sender
      type: string
    Telegram.Bot.Types.Enums.MaskPositionPoint:
      enum:
        - forehead
        - eyes
        - mouth
        - chin
      type: string
    Telegram.Bot.Types.Enums.MessageEntityType:
      enum:
        - mention
        - hashtag
        - bot_command
        - url
        - email
        - bold
        - italic
        - code
        - pre
        - text_link
        - text_mention
        - phone_number
        - cashtag
        - unknown
        - underline
        - strikethrough
      type: string
    Telegram.Bot.Types.Game:
      required:
        - description
        - photo
        - title
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        photo:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.PhotoSize'
        text:
          type: string
          nullable: true
        text_entities:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.MessageEntity'
          nullable: true
        animation:
          $ref: '#/components/schemas/Telegram.Bot.Types.Animation'
      additionalProperties: false
    Telegram.Bot.Types.InlineQuery:
      required:
        - from
        - id
        - offset
        - query
      type: object
      properties:
        id:
          type: string
        from:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        query:
          type: string
        offset:
          type: string
        chat_type:
          $ref: '#/components/schemas/Telegram.Bot.Types.Enums.ChatType'
        location:
          $ref: '#/components/schemas/Telegram.Bot.Types.Location'
      additionalProperties: false
    Telegram.Bot.Types.Location:
      required:
        - latitude
        - longitude
      type: object
      properties:
        longitude:
          type: number
          format: float
        latitude:
          type: number
          format: float
        horizontal_accuracy:
          type: number
          format: float
        live_period:
          type: integer
          format: int32
        heading:
          type: integer
          format: int32
        proximity_alert_radius:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.LoginUrl:
      required:
        - url
      type: object
      properties:
        url:
          type: string
        forward_text:
          type: string
          nullable: true
        bot_username:
          type: string
          nullable: true
        request_write_access:
          type: boolean
      additionalProperties: false
    Telegram.Bot.Types.MaskPosition:
      required:
        - point
        - scale
        - x_shift
        - y_shift
      type: object
      properties:
        point:
          $ref: '#/components/schemas/Telegram.Bot.Types.Enums.MaskPositionPoint'
        x_shift:
          type: number
          format: float
        y_shift:
          type: number
          format: float
        scale:
          type: number
          format: float
      additionalProperties: false
    Telegram.Bot.Types.Message:
      required:
        - chat
        - date
        - message_id
      type: object
      properties:
        message_id:
          type: integer
          format: int32
        from:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        sender_chat:
          $ref: '#/components/schemas/Telegram.Bot.Types.Chat'
        date:
          type: string
          format: date-time
        chat:
          $ref: '#/components/schemas/Telegram.Bot.Types.Chat'
        forward_from:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        forward_from_chat:
          $ref: '#/components/schemas/Telegram.Bot.Types.Chat'
        forward_from_message_id:
          type: integer
          format: int32
        forward_signature:
          type: string
          nullable: true
        forward_sender_name:
          type: string
          nullable: true
        forward_date:
          type: string
          format: date-time
          nullable: true
        reply_to_message:
          $ref: '#/components/schemas/Telegram.Bot.Types.Message'
        via_bot:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        edit_date:
          type: string
          format: date-time
          nullable: true
        media_group_id:
          type: string
          nullable: true
        author_signature:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        entities:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.MessageEntity'
          nullable: true
        animation:
          $ref: '#/components/schemas/Telegram.Bot.Types.Animation'
        audio:
          $ref: '#/components/schemas/Telegram.Bot.Types.Audio'
        document:
          $ref: '#/components/schemas/Telegram.Bot.Types.Document'
        photo:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.PhotoSize'
          nullable: true
        sticker:
          $ref: '#/components/schemas/Telegram.Bot.Types.Sticker'
        video:
          $ref: '#/components/schemas/Telegram.Bot.Types.Video'
        video_note:
          $ref: '#/components/schemas/Telegram.Bot.Types.VideoNote'
        voice:
          $ref: '#/components/schemas/Telegram.Bot.Types.Voice'
        caption:
          type: string
          nullable: true
        caption_entities:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.MessageEntity'
          nullable: true
        contact:
          $ref: '#/components/schemas/Telegram.Bot.Types.Contact'
        dice:
          $ref: '#/components/schemas/Telegram.Bot.Types.Dice'
        game:
          $ref: '#/components/schemas/Telegram.Bot.Types.Game'
        poll:
          $ref: '#/components/schemas/Telegram.Bot.Types.Poll'
        venue:
          $ref: '#/components/schemas/Telegram.Bot.Types.Venue'
        location:
          $ref: '#/components/schemas/Telegram.Bot.Types.Location'
        new_chat_members:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.User'
          nullable: true
        left_chat_member:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        new_chat_title:
          type: string
          nullable: true
        new_chat_photo:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.PhotoSize'
          nullable: true
        delete_chat_photo:
          type: boolean
        group_chat_created:
          type: boolean
        supergroup_chat_created:
          type: boolean
        channel_chat_created:
          type: boolean
        message_auto_delete_timer_changed:
          $ref: '#/components/schemas/Telegram.Bot.Types.MessageAutoDeleteTimerChanged'
        migrate_to_chat_id:
          type: integer
          format: int64
        migrate_from_chat_id:
          type: integer
          format: int64
        pinned_message:
          $ref: '#/components/schemas/Telegram.Bot.Types.Message'
        invoice:
          $ref: '#/components/schemas/Telegram.Bot.Types.Payments.Invoice'
        successful_payment:
          $ref: '#/components/schemas/Telegram.Bot.Types.Payments.SuccessfulPayment'
        connected_website:
          type: string
          nullable: true
        passport_data:
          $ref: '#/components/schemas/Telegram.Bot.Types.Passport.PassportData'
        proximity_alert_triggered:
          $ref: '#/components/schemas/Telegram.Bot.Types.ProximityAlertTriggered'
        voice_chat_scheduled:
          $ref: '#/components/schemas/Telegram.Bot.Types.VoiceChatScheduled'
        voice_chat_started:
          $ref: '#/components/schemas/Telegram.Bot.Types.VoiceChatStarted'
        voice_chat_ended:
          $ref: '#/components/schemas/Telegram.Bot.Types.VoiceChatEnded'
        voice_chat_participants_invited:
          $ref: '#/components/schemas/Telegram.Bot.Types.VoiceChatParticipantsInvited'
        reply_markup:
          $ref: '#/components/schemas/Telegram.Bot.Types.ReplyMarkups.InlineKeyboardMarkup'
      additionalProperties: false
    Telegram.Bot.Types.MessageAutoDeleteTimerChanged:
      type: object
      properties:
        message_auto_delete_time:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.MessageEntity:
      required:
        - length
        - offset
        - type
      type: object
      properties:
        type:
          $ref: '#/components/schemas/Telegram.Bot.Types.Enums.MessageEntityType'
        offset:
          type: integer
          format: int32
        length:
          type: integer
          format: int32
        url:
          type: string
          nullable: true
        user:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        language:
          type: string
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.Passport.EncryptedCredentials:
      required:
        - data
        - hash
        - secret
      type: object
      properties:
        data:
          type: string
        hash:
          type: string
        secret:
          type: string
      additionalProperties: false
    Telegram.Bot.Types.Passport.EncryptedPassportElement:
      required:
        - hash
        - type
      type: object
      properties:
        type:
          type: string
        hash:
          type: string
        data:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        files:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.Passport.PassportFile'
          nullable: true
        front_side:
          $ref: '#/components/schemas/Telegram.Bot.Types.Passport.PassportFile'
        reverse_side:
          $ref: '#/components/schemas/Telegram.Bot.Types.Passport.PassportFile'
        selfie:
          $ref: '#/components/schemas/Telegram.Bot.Types.Passport.PassportFile'
        translation:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.Passport.PassportFile'
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.Passport.PassportData:
      required:
        - credentials
        - data
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.Passport.EncryptedPassportElement'
        credentials:
          $ref: '#/components/schemas/Telegram.Bot.Types.Passport.EncryptedCredentials'
      additionalProperties: false
    Telegram.Bot.Types.Passport.PassportFile:
      required:
        - file_date
        - file_id
        - file_unique_id
      type: object
      properties:
        file_date:
          type: string
          format: date-time
        file_id:
          type: string
        file_unique_id:
          type: string
        file_size:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.Payments.Invoice:
      required:
        - currency
        - description
        - start_parameter
        - title
        - total_amount
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        start_parameter:
          type: string
        currency:
          type: string
        total_amount:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.Payments.OrderInfo:
      type: object
      properties:
        name:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        shipping_address:
          $ref: '#/components/schemas/Telegram.Bot.Types.Payments.ShippingAddress'
      additionalProperties: false
    Telegram.Bot.Types.Payments.PreCheckoutQuery:
      required:
        - currency
        - from
        - id
        - invoice_payload
        - total_amount
      type: object
      properties:
        id:
          type: string
        from:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        currency:
          type: string
        total_amount:
          type: integer
          format: int32
        invoice_payload:
          type: string
        shipping_option_id:
          type: string
          nullable: true
        order_info:
          $ref: '#/components/schemas/Telegram.Bot.Types.Payments.OrderInfo'
      additionalProperties: false
    Telegram.Bot.Types.Payments.ShippingAddress:
      required:
        - city
        - country_code
        - post_code
        - street_line1
        - street_line2
      type: object
      properties:
        country_code:
          type: string
        state:
          type: string
          nullable: true
        city:
          type: string
        street_line1:
          type: string
        street_line2:
          type: string
        post_code:
          type: string
      additionalProperties: false
    Telegram.Bot.Types.Payments.ShippingQuery:
      required:
        - from
        - id
        - invoice_payload
        - shipping_address
      type: object
      properties:
        id:
          type: string
        from:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        invoice_payload:
          type: string
        shipping_address:
          $ref: '#/components/schemas/Telegram.Bot.Types.Payments.ShippingAddress'
      additionalProperties: false
    Telegram.Bot.Types.Payments.SuccessfulPayment:
      required:
        - currency
        - invoice_payload
        - provider_payment_charge_id
        - telegram_payment_charge_id
        - total_amount
      type: object
      properties:
        currency:
          type: string
        total_amount:
          type: integer
          format: int32
        invoice_payload:
          type: string
        shipping_option_id:
          type: string
          nullable: true
        order_info:
          $ref: '#/components/schemas/Telegram.Bot.Types.Payments.OrderInfo'
        telegram_payment_charge_id:
          type: string
        provider_payment_charge_id:
          type: string
      additionalProperties: false
    Telegram.Bot.Types.PhotoSize:
      required:
        - file_id
        - file_unique_id
        - height
        - width
      type: object
      properties:
        width:
          type: integer
          format: int32
        height:
          type: integer
          format: int32
        file_id:
          type: string
        file_unique_id:
          type: string
        file_size:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.Poll:
      required:
        - allows_multiple_answers
        - id
        - is_anonymous
        - is_closed
        - options
        - question
        - total_voter_count
        - type
      type: object
      properties:
        id:
          type: string
        question:
          type: string
        options:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.PollOption'
        total_voter_count:
          type: integer
          format: int32
        is_closed:
          type: boolean
        is_anonymous:
          type: boolean
        type:
          type: string
        allows_multiple_answers:
          type: boolean
        correct_option_id:
          type: integer
          format: int32
          nullable: true
        explanation:
          type: string
          nullable: true
        explanation_entities:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.MessageEntity'
          nullable: true
        open_period:
          type: integer
          format: int32
          nullable: true
        close_date:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.PollAnswer:
      required:
        - option_ids
        - poll_id
        - user
      type: object
      properties:
        poll_id:
          type: string
        user:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        option_ids:
          type: array
          items:
            type: integer
            format: int32
      additionalProperties: false
    Telegram.Bot.Types.PollOption:
      required:
        - text
        - voter_count
      type: object
      properties:
        text:
          type: string
        voter_count:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.ProximityAlertTriggered:
      required:
        - distance
        - traveler
        - watcher
      type: object
      properties:
        traveler:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        watcher:
          $ref: '#/components/schemas/Telegram.Bot.Types.User'
        distance:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.ReplyMarkups.InlineKeyboardButton:
      required:
        - text
      type: object
      properties:
        text:
          type: string
        url:
          type: string
          nullable: true
        login_url:
          $ref: '#/components/schemas/Telegram.Bot.Types.LoginUrl'
        callback_data:
          type: string
          nullable: true
        switch_inline_query:
          type: string
          nullable: true
        switch_inline_query_current_chat:
          type: string
          nullable: true
        callback_game:
          $ref: '#/components/schemas/Telegram.Bot.Types.CallbackGame'
        pay:
          type: boolean
      additionalProperties: false
    Telegram.Bot.Types.ReplyMarkups.InlineKeyboardMarkup:
      required:
        - inline_keyboard
      type: object
      properties:
        inline_keyboard:
          type: array
          items:
            type: array
            items:
              $ref: '#/components/schemas/Telegram.Bot.Types.ReplyMarkups.InlineKeyboardButton'
      additionalProperties: false
    Telegram.Bot.Types.Sticker:
      required:
        - file_id
        - file_unique_id
        - height
        - is_animated
        - width
      type: object
      properties:
        width:
          type: integer
          format: int32
        height:
          type: integer
          format: int32
        is_animated:
          type: boolean
        thumb:
          $ref: '#/components/schemas/Telegram.Bot.Types.PhotoSize'
        emoji:
          type: string
          nullable: true
        set_name:
          type: string
          nullable: true
        mask_position:
          $ref: '#/components/schemas/Telegram.Bot.Types.MaskPosition'
        file_id:
          type: string
        file_unique_id:
          type: string
        file_size:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.Update:
      required:
        - update_id
      type: object
      properties:
        update_id:
          type: integer
          format: int32
        message:
          $ref: '#/components/schemas/Telegram.Bot.Types.Message'
        edited_message:
          $ref: '#/components/schemas/Telegram.Bot.Types.Message'
        inline_query:
          $ref: '#/components/schemas/Telegram.Bot.Types.InlineQuery'
        chosen_inline_result:
          $ref: '#/components/schemas/Telegram.Bot.Types.ChosenInlineResult'
        callback_query:
          $ref: '#/components/schemas/Telegram.Bot.Types.CallbackQuery'
        channel_post:
          $ref: '#/components/schemas/Telegram.Bot.Types.Message'
        edited_channel_post:
          $ref: '#/components/schemas/Telegram.Bot.Types.Message'
        shipping_query:
          $ref: '#/components/schemas/Telegram.Bot.Types.Payments.ShippingQuery'
        pre_checkout_query:
          $ref: '#/components/schemas/Telegram.Bot.Types.Payments.PreCheckoutQuery'
        poll:
          $ref: '#/components/schemas/Telegram.Bot.Types.Poll'
        poll_answer:
          $ref: '#/components/schemas/Telegram.Bot.Types.PollAnswer'
        my_chat_member:
          $ref: '#/components/schemas/Telegram.Bot.Types.ChatMemberUpdated'
        chat_member:
          $ref: '#/components/schemas/Telegram.Bot.Types.ChatMemberUpdated'
      additionalProperties: false
    Telegram.Bot.Types.User:
      required:
        - first_name
        - id
        - is_bot
      type: object
      properties:
        id:
          type: integer
          format: int64
        is_bot:
          type: boolean
        first_name:
          type: string
        last_name:
          type: string
          nullable: true
        username:
          type: string
          nullable: true
        language_code:
          type: string
          nullable: true
        can_join_groups:
          type: boolean
          nullable: true
        can_read_all_group_messages:
          type: boolean
          nullable: true
        supports_inline_queries:
          type: boolean
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.Venue:
      required:
        - address
        - location
        - title
      type: object
      properties:
        location:
          $ref: '#/components/schemas/Telegram.Bot.Types.Location'
        title:
          type: string
        address:
          type: string
        foursquare_id:
          type: string
          nullable: true
        google_place_id:
          type: string
          nullable: true
        google_place_type:
          type: string
          nullable: true
        foursquare_type:
          type: string
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.Video:
      required:
        - duration
        - file_id
        - file_unique_id
        - height
        - width
      type: object
      properties:
        width:
          type: integer
          format: int32
        height:
          type: integer
          format: int32
        duration:
          type: integer
          format: int32
        thumb:
          $ref: '#/components/schemas/Telegram.Bot.Types.PhotoSize'
        file_name:
          type: string
          nullable: true
        mime_type:
          type: string
          nullable: true
        file_id:
          type: string
        file_unique_id:
          type: string
        file_size:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.VideoNote:
      required:
        - duration
        - file_id
        - file_unique_id
        - length
      type: object
      properties:
        length:
          type: integer
          format: int32
        duration:
          type: integer
          format: int32
        thumb:
          $ref: '#/components/schemas/Telegram.Bot.Types.PhotoSize'
        file_id:
          type: string
        file_unique_id:
          type: string
        file_size:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.Voice:
      required:
        - duration
        - file_id
        - file_unique_id
      type: object
      properties:
        duration:
          type: integer
          format: int32
        mime_type:
          type: string
          nullable: true
        file_id:
          type: string
        file_unique_id:
          type: string
        file_size:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.VoiceChatEnded:
      required:
        - duration
      type: object
      properties:
        duration:
          type: integer
          format: int32
      additionalProperties: false
    Telegram.Bot.Types.VoiceChatParticipantsInvited:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/Telegram.Bot.Types.User'
          nullable: true
      additionalProperties: false
    Telegram.Bot.Types.VoiceChatScheduled:
      required:
        - start_date
      type: object
      properties:
        start_date:
          type: string
          format: date-time
      additionalProperties: false
    Telegram.Bot.Types.VoiceChatStarted:
      type: object
      additionalProperties: false
    Travis_backend.ChannelDomain.Models.ViberContact:
      type: object
      properties:
        name:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.ChannelDomain.Models.ViberLocation:
      type: object
      properties:
        lat:
          type: number
          format: double
        lon:
          type: number
          format: double
      additionalProperties: false
    Travis_backend.ChannelDomain.Models.ViberMessage:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        media:
          type: string
          nullable: true
        contact:
          $ref: '#/components/schemas/Travis_backend.ChannelDomain.Models.ViberContact'
        location:
          $ref: '#/components/schemas/Travis_backend.ChannelDomain.Models.ViberLocation'
        tracking_data:
          type: string
          nullable: true
        file_name:
          type: string
          nullable: true
        file_size:
          type: integer
          format: int64
        duration:
          type: integer
          format: int32
        sticker_id:
          type: integer
          format: int32
      additionalProperties: false
    Travis_backend.ChannelDomain.Models.ViberUser:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        avatar:
          type: string
          nullable: true
        country:
          type: string
          nullable: true
        language:
          type: string
          nullable: true
        primary_device_os:
          type: string
          nullable: true
        api_version:
          type: integer
          format: int32
        viber_version:
          type: string
          nullable: true
        mcc:
          type: integer
          format: int32
        mnc:
          type: integer
          format: int32
        device_type:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.ChannelDomain.ViewModels.TwilioResponseViewModel:
      type: object
      properties:
        accountSid:
          type: string
          nullable: true
        usageTriggerSid:
          type: string
          nullable: true
        dateFired:
          type: string
          format: date-time
        recurring:
          type: string
          nullable: true
        usageCategory:
          type: string
          nullable: true
        triggerBy:
          type: string
          nullable: true
        triggerValue:
          type: string
          nullable: true
        currentValue:
          type: string
          nullable: true
        usageRecordUri:
          type: string
          nullable: true
        idempotencyToken:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.ChannelDomain.ViewModels.ViberWebhookCallback:
      type: object
      properties:
        event:
          type: string
          nullable: true
        timestamp:
          type: integer
          format: int64
        message_token:
          type: integer
          format: int64
        type:
          type: string
          nullable: true
        context:
          type: string
          nullable: true
        subscribed:
          type: boolean
        user_id:
          type: string
          nullable: true
        desc:
          type: string
          nullable: true
        user:
          $ref: '#/components/schemas/Travis_backend.ChannelDomain.Models.ViberUser'
        sender:
          $ref: '#/components/schemas/Travis_backend.ChannelDomain.Models.ViberUser'
        message:
          $ref: '#/components/schemas/Travis_backend.ChannelDomain.Models.ViberMessage'
      additionalProperties: false
    Travis_backend.CompanyDomain.ViewModels.ConnectFacebookViewModel:
      type: object
      properties:
        id:
          type: string
          nullable: true
        access_token:
          type: string
          nullable: true
        page_name:
          type: string
          nullable: true
        business_integration_system_user_access_token:
          type: string
          nullable: true
        is_v2_connection:
          type: boolean
      additionalProperties: false
    Travis_backend.CompanyDomain.ViewModels.UpdateFacebookViewModel:
      type: object
      properties:
        page_name:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookAdsWebhooksController.CreateNotificationConfigInput:
      type: object
      properties:
        facebook_config_id:
          type: integer
          format: int64
        notification_emails:
          type: array
          items:
            type: string
          nullable: true
        notification_phone_numbers:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookAdsWebhooksController.DeleteNotificationConfigInput:
      type: object
      properties:
        id:
          type: integer
          format: int64
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookAdsWebhooksController.GetNotificationConfigInput:
      type: object
      properties:
        facebook_config_id:
          type: integer
          format: int64
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookAdsWebhooksController.TriggerAdHocLeadGenCheckInput:
      type: object
      properties:
        page_id:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookAdsWebhooksController.UpdateNotificationConfigInput:
      type: object
      properties:
        id:
          type: integer
          format: int64
        notification_emails:
          type: array
          items:
            type: string
          nullable: true
        notification_phone_numbers:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.DeregisterWebhookRequest:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        webhook_url:
          type: string
          nullable: true
        page_id:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetAllFailedFacebookWebhookEventIdsRequest:
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        limit:
          type: integer
          format: int32
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetAllFailedFacebookWebhookEventIdsResponse:
      type: object
      properties:
        failed_facebook_webhook_event_ids:
          type: array
          items:
            type: string
          nullable: true
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksRequest:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        page_id:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyActiveWebhooksResponse:
      type: object
      properties:
        webhooks:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.WebhookHub.Model.WebhookProperty'
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.GetCompanyInactiveWebhooksRequest:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        page_id:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.RegisterWebhookRequest:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        page_id:
          type: string
          nullable: true
        is_ig:
          type: boolean
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.RegisterWebhookResponse:
      type: object
      properties:
        webhook_url:
          type: string
          nullable: true
        page_id:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayCompanyFailedWebhookEventsRequest:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        limit:
          type: integer
          format: int32
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayCompanyFailedWebhookEventsResponse:
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    Travis_backend.Controllers.WebhookControllers.FacebookWebhooksController.ReplayWebhookEventsRequest:
      type: object
      properties:
        webhook_event_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Travis_backend.FlowHubs.Models.WebhookSampleInput:
      type: object
      properties:
        trigger_id:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
  securitySchemes:
    Bearer:
      type: http
      description: Please insert JWT with Bearer into field
      scheme: bearer
      bearerFormat: Bearer _YourToken_
security:
  - Bearer: [ ]
tags:
  - name: TelegramWebhooks
    description: Telegram Webhook.
  - name: ViberWebhooks
    description: Viber Webhook.