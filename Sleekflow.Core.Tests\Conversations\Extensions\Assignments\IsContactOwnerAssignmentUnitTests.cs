using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Extensions.Assignments;

[TestFixture]
public class IsContactOwnerAssignmentUnitTests
{
    [Test]
    public void IsContactOwnerAssignment_ShouldReturnFalse_WhenConversationIsNull()
    {
        Conversation conversation = null;
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 1 };

        bool result = conversation.IsContactOwnerAssignment(newContactOwner);

        Assert.IsFalse(result);
    }

    [Test]
    public void IsContactOwnerAssignment_ShouldReturnFalse_WhenAssigneeIdIsNotNull()
    {
        var conversation = new Conversation { AssigneeId = 1 };
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 2 };

        bool result = conversation.IsContactOwnerAssignment(newContactOwner);

        Assert.IsFalse(result);
    }

    [Test]
    public void IsContactOwnerAssignment_ShouldReturnTrue_WhenAssigneeIdIsNullAndNewContactOwnerIsNotNull()
    {
        var conversation = new Conversation { AssigneeId = null };
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 1 };

        bool result = conversation.IsContactOwnerAssignment(newContactOwner);

        Assert.IsTrue(result);
    }
}