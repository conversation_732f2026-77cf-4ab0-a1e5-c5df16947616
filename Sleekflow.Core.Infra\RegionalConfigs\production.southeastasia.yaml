location_name: southeastasia
sku_config:
  sleekflow_core:
    name: P3V3
    tier: PremiumV3
  sleekflow_core_db:
    name: HS_PRMS
    tier: Hyperscale
    family: PRMS
    capacity: 2
  sleekflow_analytic_db:
    name: HS_S_Gen5
    tier: Hyperscale
    family: Gen5
    capacity: 2
  sleekflow_powerflow:
    name: P1V3
    tier: PremiumV3
  sleekflow_sleek_pay:
    name: P0V3
    tier: PremiumV3
  redis:
    default:
      name: Standard
      family: C
      capacity: 1
    caching:
      name: Standard
      family: C
      capacity: 1
sql_db_config:
  administrator_login_random_secret: sP12aRdpXok723wSXihVX+fvcoX3ib+H6l59MVtfVormPMEFLDTJlOI0bkaS99FNDW4lJb045knRvqAI
  administrator_login_password_random_secret: Zzo+bM0Q/OMW/rYOf1adAPtQjnDMCv4Z0XhBcmQZmeeGWyk56dmlJ02sW1qfRmCGJ842FWYyGpYrggdo
  whitelist_ip_ranges:
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: *************
      end_ip_address: *************
    - start_ip_address: *************
      end_ip_address: *************
    - start_ip_address: **************
      end_ip_address: **************
  is_read_scale_enable: "false"
  high_availability_replica_count: 0
vnet:
  default_address_space: ********/16
  default_subnet_address_prefix: ********/24
  sleekflow_core_db_address_prefix: ********/24
  sleekflow_core_address_prefix: ********/24
  sleekflow_powerflow_address_prefix: ********/24
  sleekflow_sleek_pay_address_prefix: ********/24
  sleekflow_core_worker_address_prefix: ********/24
auto_scale_config:
  sleekflow_core:
    capacity:
      default: "3"
      maximum: "10"
      minimum: "2"
    scale_out_instances: "1"
    scale_down_instances: "1"
sleekflow_core_config:
  aspnetcore_environment: production
  logger:
    gcp_is_enabled: "TRUE"
    gcp_project_id: my-production-project-405815
    gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  audit_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/audit-hub
    key: GNDR0hF2jyusJiUfgBK6zHcH6G1Dt3HetUnyMDq0UO2246QeRUVryn0jsdd72Mtj6Sj0d7QaPzSZo1I2rzV0n52kaK6wNZNKKr01SAdln3M=yfJTVtKJAqOeFvoN
  auth0:
    action_audience: https://api.sleekflow.io/
    action_issuer: https://sso.sleekflow.io/
    audience: https://api.sleekflow.io
    client_id: txLGs6X2eN17XOXuxZpDakoxpyxoOlHW
    client_secret: 3tDkYdwKc9t76vSQikRDJ7J8TNsMCfFR3IRYEwXUtYHMCjkfKxJVSy3/+PPhVTS72lImgx5I2Ylk8Pe7ZtApVjQElKnNpvWKxd7ki/4nNCo=Snc4icqFJGlTUDM1
    database_connection_name: Sleekflow-Username-Password-Authentication
    domain: sleekflow.eu.auth0.com
    http_retries: 10
    issuers:
      - https://sso.sleekflow.io/
      - https://sleekflow.eu.auth0.com
    namespace: https://app.sleekflow.io/
    role_claim_type: roles
    user_email_claim_type: email
    user_id_claim_type: user_id
    username_claim_type: user_name
    tenant_hub_secret_key: q9uJ9qoFOZu6SuoKCcJRKwyDTWq60XZSQtJn4eEZ/0ZiM0d4TORc3CBBJs5bAMMtIa0O9SXenNOYc7RlO6sBC4TekSDIzD1a6j40S6KP3Y8=GUVJPSum4OTsVYzh
    health_check:
      is_enabled: "false"
      client_id: OrxDhNRbAyWXKoSVoMuS0hIWNo3v2An0
      client_secret: duj+1/UxLnSY+0FPip3X35gyOK+P905GoHcVlziRHTTsaO9OPHfmG1qU9nOjhZ5yWYWgleQIBW4z/e84ZDmMTfztxWYRuD0ByDu41RzMG5I=eHRuYIYesyxHZkyb
      username: *******
      password: ogihsaw38090SDFn4sdglnloksd3
  azure:
    media_service:
      account_name: sfmediaproduction
      client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
      client_secret: GUm/dr3IuUN3q/VMZrXaAYQIdTde7FyIV3IzzmNYXf1N7ftvewSP09SJDKfSbWVw8w3Cez3VUGxPOmr=
      resource_group: sleekflow-resource-group-production853b96c8
      subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
      tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
    text_analytics_credentials: 226f2d8217704dd6b223b4a1fd6e51c6
    text_analytics_url: https://sleekflow-prod-sp-text-analytics.cognitiveservices.azure.com/
  application_insights:
    is_telemetry_tracer_enabled: "true"
    is_sampling_disabled: "false"
  beamer:
    api_key: 8mEGcvQS0q4DXuDYrLYFcs52ezsy63K+SX5gZnXKmunBNcRfjyCopJ0cd/9LQoYQhEvD=dPSsnlpTqcl
    api_url: https://api.getbeamer.com/v0/nps
    webhook_verify_key: Wy2iaWboR9wHWGVlPgYsAxq++NWx5UX/pYHfxrLUtLmptCGEZr5EH3wuMfKUYFYmX71/yVC+OkU2duuzidsXvcnbEZKGRdE5VoKZMki7Z+o=mDlXRztPd2CvCcNS
  chat_api:
    api_key: NqAGalx6sPZu6IJBQT0hCt0Kj5V0oMrTV9GcnzceF/A=SD1ui2mUKOEVO5wD
    api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
  commerce_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/commerce-hub
    key: UbMLDARYoq3RnRMLaA8Hfi09YMXSDShneCGXyI+azvs7CwER/+96HsHyD8VLuoK6pusgLXMGFcZQFc5bXbAe/bI2r8W241TH0ZwSa/VcCCP65VD0yPBHETFNxGQ4IT9Q0ZuAt6dKG+ydjie2eq2+DZHNzEOIjn1FRSYyZdIz9qWj78gnEANE0kfwxslqeX5kgz8ZzMJBoTSFj0iR
  crm_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/crm-hub
    key: FajXGbCIGjlLzXIWG5+MUXf6eguHX/rLIpTnqhAmybBi4qTPwrzM+JEEmTHpeytTzObbhovA1kh22uXg
  data_snapshot:
    is_enable: "true"
  development:
    redirect_fb_webhook: https://9633-42-***********.ngrok.io/facebook/Webhook
  epplus:
    excel_package:
      license_context: NonCommercial
  facebook:
    client_id: 812364635796464
    client_secret: K5vppn37kEF1cRgbYBXbRb1ncBuMtY0t4eeiAC6D9AlpQDA1BBA7+ws7U7ZRFaVUr17pLQyUmoYsDBcl
  environment_features:
    is_recurring_job_enabled: "true"
  ffmpeg:
    ffmpeg_exe_name: /usr/bin/ffmpeg
  flow_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/flow-hub
    key: l/2+mOh1z0HD/YY7Qq+pMVdyWQZ++xdqB+K31sGqJ1pLYN/j01AoQGj1id0ZeoqshYKlrHPjzN/nTOJU5b3g1mT9YhSmKMbQGNYVgJnPRHg=FQtZgvrFx3MJOpKT
  hub_spot:
    internal_hub_spot_api_key: y3d2WqM+wCbalpUJ/3OHHLTFtU6mjCpRWjDIlQRkhbP49Cu0wcO/wtGkE0PK+fjmpeYQGpkI1hxM=wkP
    is_enable: "true"
  instrumentation_engine_extension_version: disabled
  internal_google_cloud:
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  general_google_cloud:
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    project_id: my-production-project-405815
    server_location: asia-southeast1
    google_storage_bucket_name: sleekflow-transcoder-prod-asia
  ip_look_up:
    key: UOyjYtjuqs5gSr5yurj/prIZwRNral/o9viavtnWDzHbEO5/tYaStsX58blxvHjJmMmwH2=9Fco4vU6s
  intelligent_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/intelligent-hub
    key: mAlR9NfVGwEWqR9Gjvv8mIB7N677mOUfggQkpPApxqd4Yj+DFRh6h8HAlJzAFDWlBAmZfV30Ewi9UJ7rXlAdV5uxQBHtwFjTITOL+/u2b7g=tpWcTujySMF0eV0h
  webhook_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/webhook-hub
    key: Zl9esxiuFhYaM52g56EfzD3kcpPxJ0b3OGhB74HqGoalfsB3FzsjvDFSv/ZMQd9/t6ru7n86QFQKtejdcRmlkiGt/NXBUBEvqCG92G/i4Ls=T6Lvd4p33qOZdeBU
    auth_secret_key: 4WsDo6rNnEABVHdsf0K5Aottw7oGMjefDczfiAMUSajzFPyHH3ubFA3n3p1ZkOcYfKVx+e4aq8ZIFXOMwIIyKlB/Ck5RE6XIYJmLQuWX1yo=wRAqqoMtqZqG019g
  message_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/messaging-hub
    key: VXeptgdOJoNJl4sLWXkucfUI32t+OgO0DXfEuhHEevv/vFTa2Lv09oeowLawpTkRFYHbon8jkxG5cAAf
  mixpanel:
    token: ef72dd13a0ffccb584cfdf75d3160e25
  mobile_app_management_extension_version: latest
  notification_hub:
    connection_string: Endpoint=sb://sleekflowproduction.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=pbzIBQQoNB8rGgrHdt1vmCIuTIB/Aaey5iION3eiCbQ=
    hub_name: SleekflowProduction
  public_api_gateway:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/public-api-gateway
    key: jV3AlNLF/C14ENltJIwF3+C9+hvUYKh9QBlZ8hXQ4rDJTxhxjnPzray767Bk9bdLhZVzWrrvsF3IkV4k
  reseller:
    domain_name: https://partner.sleekflow.io
  rewardful:
    api_secret: KoEtqOxYyH+ZotXIWLBGsp7AnQjXhi14nbH3A5lTlY3zqazOhGV+fC3HtpJLOj3aJiyROoXwqyRutM7L
  salesforce:
    custom_active_web_app: https://sfmc-custom-activity.vercel.app
  share_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/share-hub
    key: z7Vmc92fQ/KD+pHRAzYook3K+ifPor/Ag7DYgjzNZ/RWJP/slB4rcFr0hxOmjQaVLfkGvuOVJJIEoCJE
  shopify:
    shopify_api_key: YJZ6P8dTYjzw5oy5Q6eppGbD0WIhUKOHq0hMXHX9X2w8trxpFcfK6jcvZuzxgmYsEdpqCKVFmperosyN
    shopify_secret_key: xTumPGRVC4AyXJ/mMzfsdu1RHxrB0YCmBYiFATZ9UWoIRhq1ueto0cuLwYkDfns5AL5yjgPwSYahBB4V
  sleek_pay:
    domain_name: https://pay-seas.sleekflow.io
    shopify_graphql_admin_api_version: 2022-10
    stripe:
      public_keys:
        default: AVNdnCVOakBxLBih0LMM4WBSy8bhb8ZQw46rgQIwCh7gHm8r57qVKmbPSOpdjsyAyN1bGDqouOTfeGAVMw/jdxbSdHhTERi+vYaBf3aYhmvoPe9380F3+yvHuQMC5uj0eKiC0aj7IkVdXCNrLVZeig==VuajKouFP1NoCPRS
        gb: sY7CThxrbXiAF9nk/XlYKO8dD8r8C7GojtH005CjUCtnmjbzky7G5d1aV06/wfuoUYc+qtorTUOg1N1QGJmQnw0Wezs7RDNjSocJWMEHKwPbz+Z1LP6N/OoFmMj57R8r9qLwyXYwe6bVjl2PFsbYGA==AqtRFMxHZjYgCghP
        hk: aKVHgO1JMzvmvGEN759hqy67Qbv0LxQ8X7wMKWG89zSa9LtLV5sEmGqUYFckTyYiPg6S9hc8v9lpPgDDgDKRjarxIz4+BO9NkuZ8+xSqxgtiLMAQWZSEqrQYf26mymdZylglDX9YedDuzI7thkyGDw==LdaXUBf2P8HterX8
        my: A6zY5Wb3CN5P/crqCoXogUVRRjo6M7gA29GXLzQxrvgDD/RB9pQ8qOcq9BOtTAGpC6ol2AMzWfR4lRhqXCrQnNHu04U/ShsSGjfDxvptajt/V+ek5/j7++jhksuVmAkiwx1Fmab6Bx/dI3WVXexTvA==BDq0yOdMFLBnWa5w
        sg: 9vOfDS+Eh/6Ns/ZPMuWgwKkDD9ZqNXQ3P9i6vUgtrDCVvf8qde91+0lnHHIoJVhbSzJxFHecgqgD3yDtfZgp+AGuek6xPPuuG19/RNWmGs0atiq6b0WRbb3zlUZhkTivB4LLgqPwsMJRhcxYnKOTmQ==6ntI63et7xmlkhua
      secret_keys:
        default: YBn7f+bqd3pkDlyXPm0a0wakVoF5oczdSlW9GX72rVMnyrrMYSeQSRojaPFU+ntj95fEScphUPt1ydBk89kTtm7NwYeHK5Nv/tkhMPCepMukm4oY7I3QoMMb9rK/WK13zAZgphe1zQDcnhxewNlItA==tjtLdR6673FnrNeH
        gb: xsEarCpSBp1TagwfYN9tVLga+I0jr1eCb0o9jIVFEtubkTpllg2X+zEDNJ1jgaWU/mabm3vvxr95z+3pxNq+Gd1jCoJKEDzDHGg+7uBsmiTxzpDRcF2GuD0B9hv7tYE0DfpFc3/uainNkHBH58aIaw==6sVHo1f6nHAq9WtV
        hk: KLMl4rGxHs1rZZGNeOZLRtIT53Nfo3TvJmylsU3R4z+BvrppnNWFY0n2MeImMRsauekvs0xpb0oxttBtqw0qXHhjrbD6fhj6m7NWOglZoSHwYoYrV4LD3dWpMFiiJo4tcxMN8Qlg/OQultfM4p8/zg==0vARwJpsmvdWZP3u
        my: 8f6ZSGM5lqei5JjlZWCMZrmKH4Mt3AqPpDAzc0DBdL5W8OIJtwNmjO5/XEI9K0P25LHYmFWKBTw1uvYE7vvOBkI+DxvdNuPGnohJepw7QwWBrk2wlkr5msxmQgQquqU1yagkkcvmuyQW5DvO728Qtw===m5X06KkzbUwScMc
        sg: dvYEiP3pzD8H66slUAQyXWE9o57EjciKpqQ21QShMVjqTJYovU0nx1FSrEI/wuc0wa9FxY14ut31asVoZYve24+1+SW0nVamfLQlqwIsHrE7bQotiXnoaiuwwUdA20+u8dTcHg6FMuLn3B6k/Ub+fQ==JBqDUhpHbO7BmMSS
      connect_webhook_secrets:
        gb: 5Ie5EXmoUOhAr7vfhPEKRI9OmZG4/o9AEbtVmcwnPd+wX6ZeAulCabfcAhxCzmQxv6ZdgkPNKtytsMOH
        hk: kSdPo6KyxM6y0JwBvUADecmKk5ZgXbtR1trq/hznNLK26kX3g6i8NmZaQTSflmNflweCKCWuqF2YHNUU
        my: AFj9Advw98eUydGnatl+gRl8l6XMOGBM72FzyfgEttGEelLz30k+CWxaghzocjBtOvqSzL9zIUDtADam
        sg: 4P7TexrT02VVj/xvX9yt1NZISDfsVwQmi7DvCq+c8MPtVzE9JH4Aaj60X1a9Tjp8cLWYnovlt1a7xOYv
      report_webhook_secrets:
        gb: COnIYC/CM5/Yl095rImWm5laWY/vLvqVX4WftX96kzX7kDE2aPTIMBcXwUstRLBkRkjUexyDatN4Cnid
        hk: Quf0xSS8DjqViqhd6h4O4xhUfvmEKWBVeGJ1CAKImhVF5uN4gAgMIqYK8OdPS+D4eHdo2zNoaKrNMxkL
        my: G4GppinWDRMmRTYGEYfelPV4XwhspbYIoD6L+u/c1BYsoNQNu2LYncotghzrfr55QNIjsydHDIQTEGMC
        sg: N/y3iRcT7ldTPBDn3sZIVQcZX5OeeTov8NnVbAMnYfUVDwQFiQxqXky/qF/r6hxwIQoRiPV5zEnbvles
      webhook_secrets:
        default: EwO/6JHZI9JiRXKexJBYAPZAZFSB1S236gEGYemBb45nG7lLy6/3QSHz7pf9uk/C8PYsW0G4DDxCwFbY
        gb: E58KYoko41POiaSNTubuLx8mErY4EJLXBk4x0fTGKt2gZh22rPqOhQI02kul0d3EJunJ8TLELYk5RqWk
        hk: sXya2xdAUlcd+iUsL8vEzI7diq9lKDi7ARR8lBBQN5xWrHGrmi33dRAXwzAjSQR2SwlXdFCRJwI6tmEB
        my: pj7Iw4FOJsEvOMLEA7VgdMiUAxW6r4jQGtzYmigtR+JhzEo4Tqzj+avUrII/hsZG5yEM0BCbvKpmXBPo
        sg: m1vDtP48Tf2FXkKqaxOqIXlw2L1AlzP8CWqBWQzpOoRosvGy5ISQ4tdJLPXdZDLBo8WTlqMrnOhwAmWW
  snapshot_debugger_extension_version: disabled
  sql_performance:
    from_raw_sql: "true"
    is_and_condition_enabled: "true"
    is_or_condition_enabled: "true"
    is_conversation_analytics_condition_enabled: "false"
    is_shopify_order_statistics_enabled: "false"
    is_sales_performance_enabled: "false"
    is_public_api_new_upsert_enabled: "false"
    is_zapier_new_upsert_enabled: "false"
  stripe:
    stripe_public_key: ttWl6bbwLX8FMYDDIEmU1DWHUiOVswTVz9En89mxTG+yjlrccuWsMIgTiVPI1kWwtK7lg=x4z8BEN=wT
    stripe_report_key: 2w4/uV//rMwM/d4yGwaBNi8aT7Lvo9k44NzVafOll8sYsyZKTJ9xxkTQd1YfYOCoQwnEtBpo4J3mOfEKsGLiAL2sOn/uZU9XFGeutWSo70FcXaDbGwZFedAds3Je/bM69F37YRdKcbgpXumj371xkQ==03kpqIZ9R1NLM9C4
    stripe_secret_key: ccuKxKowPJnCLfn9Ad7KzcniJR7uCaI2fxXM5G4dnaZjWkd76glfm2IanHJ0pxJ0nia7cA8WKtkTB9xl
    stripe_webhook_secret: snnllSoEPzb0+3PZlKR9i27A4IW4EYezU/3avYNaNDRR8Uc3AD1ipi1GcIJ6IjDJESi4XF5XJWZNysbi
  stripe_payment:
    stripe_payment_secret_key_gb: E26/L7LHqDiThRSRl54NO4Jvy8BSpAa5P/HEWJY598SBDgUPu4cdD9XIX3nimOtMXZKiVZnayqWKvxM/gfu0OzshPNMACzjmZefXNrbEJYzC9pIHAdm7e/8Zf8iw5tOxDk1aCKCIo9HTZpgyE+Bo+w==3MM87L=QWGg8ma3H
    stripe_payment_secret_key_hk: hPsXgZ1OzwDf6gcefAJI1RSs7E6zY1g5UZNlLltAkS/bljIow+I70U2IYsh+LmV+C1jP3aOAmumSj81MhktbpI03ob7eVr+YT7NYvoh1Z+SCICJwM6j2L6sWp3fzT0uGSi7xD5nGH4rtOSD10aFiEw===TR=RMRuAT48cUHk
    stripe_payment_secret_key_my: XQS6/qisQ0DQD1weGEtyyJC76GQhuM0NB1a38dYI26UH7DgbHXcjpqykrq9n5IAaQ9taxkgcchP2R/Sm/iWC5LPuUU7TxFzM0jMt3jlXmPxgQlscLGA9EQoxVMbpu2nsQ6/ssctsIl6SaGCgBRyhnw==blBA3T6CYkPLTgTj
    stripe_payment_secret_key_sg: rAtf2FAkCN3CWGAwrlhiygWHTLTjbOQwUhQ3jYLYUhJGzsuMmJ6EEfp+qnnyjgpGZHqEM1lmQcBGX5sqMrrkpkJ31EQDFNLT/2LS82HS7QFuBqvnOwXGhrgk5gSXo21P3simdTXqQVz74lJThbAL3Q==D2GqhuK7kmE9eMFv
  stripe_report:
    stripe_report_webhook_secret_gb: uLM07UnCfNCoG4fJzXb7VeeUvn0+exR7uZI697N5N3NjcsK5VbIEn33bGMad+k1d8DEGCT7PVyE=KqBk
    stripe_report_webhook_secret_hk: vyPIcjRzvnNHIlKhgDWd9NMkJ3sbUbXyIZUoq4W9mjk1Iwc6lr4/awJCZMzARsQiAgkP5T84cZP9d9ZI
    stripe_report_webhook_secret_my: mCKtERhCIvM4saewFVDf1wuwryDcM7E47VbcWYjOYT7z5xjTWdeuWjhi0DNa50UgikhqwVRqKP1JwPQG
    stripe_report_webhook_secret_sg: pndUe8iFn/aoLs/ySMVV3o3x+GOT/S9yzEIrITu3z8PwLeAT9LHeCJPfihp+f6B91wXF7=RLAxS=9IJ7
  tenant_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/tenant-hub
    key: ffwZDbRQCgDQ+nof/y1XWoGG0jyxir7qLWiinLNdk1ZDnDsi50bKvr0/C91psoRNX4r1jTvY8VeNwz8TPiFKx94goDzpbkQpHYpToVcZ60I=TubvsSY7VGctwkju
    is_enable_tenant_logic: "true"
  ticketing_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/ticketing-hub
    key: //BVdlxY8Cp61HbzMiFaehjcqMB93dHT1PWgAlaJZ4GjRdGTcnortY1MMcl5Qq86Hvet/cyHk0hgP+jya71SosqrBpNyFtFCGNqTR/EAEwI=DKLb=Dsxa4FTks6C
    is_enable_ticketing_logic: "false"
  test_swaping: 1
  token:
    audience: https://sleekflow-prod-api.azurewebsites.net
  tokens:
    audience: https://travis-crm-api-hk.azurewebsites.net
    issuer: https://sleekflow-prod-api.azurewebsites.net
    key: B/DGefym1cpeWWhfVgb0S7nm0XYd5Pc2IEGWd9e9sKpOxpKOMwaIhKO+Fi3a+AvdN/Pr1cTvOxmQbL+BvlkrK0KHjJYwmlVif24TP35/ZTw=oStKMyHpXOx8T81w
    lifetime: 60
  user_event_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/user-event-hub
    key: gmcx9neoF33LxN1X+x5YsmmbdKoTAgQDP4kJFl3bRQx/TEXc8frJM3AJr+STJt70DgdBIIZw2HcI7C1Etgm/Ly5PF9MVrQnbRwlkH9m+tAI=uWf8MuJYK5RgIUHN
  values:
    app_domain_name: https://app.sleekflow.io
    app_domain_name_v1: https://v1.sleekflow.io
    app_domain_name_v2: https://app.sleekflow.io
    share_link_function: https://share.sleekflow.io
    sleekflow_api_gateway: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net
    sleekflow_company_id: 471a6289-b9b7-43c3-b6ad-395a1992baea
    sleekflow_public_api_url: https://api.sleekflow.io
    sleekflow_public_api_key: p8H4G4uLacBwomfkvwStGAfmMgr4nRTYrzzSps0bwbVQimmnX9xABBp8JeMio2jsReopENe0TmsGROMb
    sleekflow_company_should_use_public_api: "true"
  website_http_logging_retention_days: 5
  website_node_default_version: 6.9.1
  whatsapp_cloud_api_template:
    default_image_blob_id: sleekflow.png
  xdt_microsoft_application_insights_base_extensions: disabled
  xdt_microsoft_application_insight_mode: default
  global_pricing:
    is_feature_enabled: "true"
    plan_migration_incentives_start_date: "2024-11-20"
    plan_migration_incentives_end_date: "2025-03-10"
  feature_flags:
    - feature_name: FlowBuilderMonetisation
      is_enabled: "true"
    - feature_name: CancelledSubscriptionTermination
      is_enabled: "true"
    - feature_name: Rbac
      is_enabled: "true"
  contact_safe_deletion:
    is_feature_enabled: "true"
  partner_stack:
    public_key: LH/3B/5he7CRkEeoCyjS55Rk4RbFGORHL+A8qf1QS07kQYciwC0rggtki1sGBg+Eft46FMucucr76sia
    secret_key: RBp3N3imb399HXgSieXfSMyb/2t+LF8THgSTEjjgRwbg2aGf/3QuL2QUXM0I8g7vBlN4XNv0aBER6rdv
  hangfire_worker:
    worker_count: 20
  internal_integration_hub:
    endpoint: "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/internal-integration-hub"
    key: pp7aFo+i0y+t1cuWhRNfekg7fzFTYDfvdv+F6wtgoCZqPR4LWjzC6qgZRzB7fqAYC4dOij4iiQnntMB+8NBR4ATrutJSNySxcOn8Nm/nsmg=gJQS6FxPl53oUPBJ
  hangfire_queues:
    disable_instances: ""
  integration_alert:
    endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
    api_key: "9n/YYF7fphaepA9DB4OamdvWKTv0GHgFs+g8Q7xuIyA2LwXaPcGgoEhzCtupLadXpgBFpAvESyDxlJAE"
    host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
    from_phone_number: "17209612030"
    template_name: "integration_disconnect_noti"
    facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
  rbac:
    is_middleware_verification_enabled: "false"
  flow_builder_flow_enrollments_incentives:
    period_start: "2025-02-07"
    period_end: "2025-06-25"
  hub_spot_smtp:
    username: "*******"
    password: "yus47rNx7M7Ca0xqdiQNZVGEOXb1yh"
    send_execution_usage_reached_threshold_email:
      username: "*******"
      password: "k4nCTQARqae2a2i3yX2d2qVuiJIvTz"
  legacy_premium_opt_in_upgrade_incentives:
    period_start: "2025-03-10"
    period_end: "2025-06-10"