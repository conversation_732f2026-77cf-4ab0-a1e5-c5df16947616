config:
  pulumi:template: azure-csharp
  sleekflow:name: performance
  sleekflow:cors:
    domains:
      - https://perf.sleekflow.io
      - https://perf-revamp.sleekflow.io
  sleekflow:default_sleekflow_core_domain: api.sleekflow.io
  sleekflow:default_sleekflow_core_front_door_domain: https://sleekflow-core-performance-dcc9ckckcubhd4g9.z01.azurefd.net
  sleekflow:regional_configs:
    - location_name: eastasia
      sku_config:
        sleekflow_core:
          name: P3V3
          tier: PremiumV3
        redis:
          default:
            name: Standard
            family: C
            capacity: 1
          caching:
            name: Standard
            family: C
            capacity: 1
      sql_db_config:
        connection_string: Server=tcp:sleekflow-core-sql-server-eas-productiond2a4d949.database.windows.net,1433;Initial Catalog=sleekflow-crm-prod;Persist Security Info=False;User ID=sf3cd3f3a;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=100;
        read_connection_string: Server=tcp:sleekflow-core-sql-server-eas-productiond2a4d949.database.windows.net,1433;Initial Catalog=sleekflow-crm-prod;Persist Security Info=False;User ID=sf3cd3f3a;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=200;Connection Lifetime=120;ApplicationIntent=ReadOnly;
        analytic_db_connection_string: Server=tcp:sleekflow-core-sql-server-eas-productiond2a4d949.database.windows.net,1433;Initial Catalog=sleekflow-analytic-sql-db-eas-production;Persist Security Info=False;User ID=sf3cd3f3a;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=200;Connection Lifetime=120;ApplicationIntent=ReadOnly;
      storage_config:
        connection_string: DefaultEndpointsProtocol=https;AccountName=sleekflowstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
      sleekflow_core_config:
        aspnetcore_environment: development
        logger:
          gcp_is_enabled: "TRUE"
          gcp_project_id: my-production-project-405815
          gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        application_insights:
          is_telemetry_tracer_enabled: "true"
          is_sampling_disabled: "false"
        audit_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/audit-hub
          key: 3Ri7RqtKBR1sXHCGXLoGiasTBVqE23pzPXUQVnKPw3v5BX4zr2SM9HcXFGlUkWMq
        auth0:
          action_audience: https://api.sleekflow.io/
          action_issuer: https://sso.sleekflow.io/
          audience: https://api.sleekflow.io
          client_id: txLGs6X2eN17XOXuxZpDakoxpyxoOlHW
          client_secret: FNsmpbetisCz7xg9aALvE3v1WXdZzxhoY6-fDf4RDSbroZ_zHIYsxb31oUs20GfO
          database_connection_name: Sleekflow-Username-Password-Authentication
          domain: sleekflow.eu.auth0.com
          http_retries: 10
          issuers:
            - https://sso.sleekflow.io/
            - https://sleekflow.eu.auth0.com
          namespace: https://app.sleekflow.io/
          role_claim_type: roles
          user_email_claim_type: email
          user_id_claim_type: user_id
          username_claim_type: user_name
          tenant_hub_secret_key: 6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o
          health_check:
            is_enabled: "false"
            client_id: OrxDhNRbAyWXKoSVoMuS0hIWNo3v2An0
            client_secret: NXaeck_K8gPII9HngFq3RsWKJ4QIaKq5bZM2YmkNxnq07lDEzS05Rei6l9lK14Oj
            username: *******
            password: ********************************
        azure:
          media_service:
            account_name: sfmediaproduction
            client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
            client_secret: ****************************************
            resource_group: sleekflow-resource-group-production853b96c8
            subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
            tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
          signal_r:
            connection_string: Endpoint=https://sleekflow-core-signal-r-eas-productione02ea3d1.service.signalr.net;AccessKey=8dYHSz9p4neH8mcd4eLRofr5orgBcSnevXNkmpxl4FE=;Version=1.0;
          text_analytics_credentials: 226f2d8217704dd6b223b4a1fd6e51c6
          text_analytics_url: https://sleekflow-prod-sp-text-analytics.cognitiveservices.azure.com/
        beamer:
          api_key: ""
          api_url: ""
          webhook_verify_key: ""
        chat_api:
          api_key: qVH3fqZufwPZaPlAmsIJMwcxuHI2
          api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
        commerce_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/commerce-hub
          key: w7zJUyirdVnNom4M6YTyxgtbTQDQL2gxHPRbKy9sSXaraX6GC4Ru8I1ejjpFS0LnSnJrRxAtO9YxxrRMGqj2H5EycUywMgQc0RhKWmQvmAe93j5jMJqunIG8aYilMp8Q
        crm_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/crm-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        data_snapshot:
          is_enable: "true"
        development:
          redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
        environment_features:
          is_recurring_job_enabled: "false"
        epplus:
          excel_package:
            license_context: NonCommercial
        facebook:
          client_id: 812364635796464
          client_secret: ********************************
        ffmpeg:
          ffmpeg_exe_name: /usr/bin/ffmpeg
        flow_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/flow-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        hub_spot:
          internal_hub_spot_api_key: ********************************************
          is_enable: "true"
        instrumentation_engine_extension_version: disabled
        internal_google_cloud:
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        general_google_cloud:
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          project_id: my-production-project-405815
          server_location: asia-east1
          google_storage_bucket_name: sleekflow-transcoder-prod-asia
        ip_look_up:
          key: a5ab6f45a1384dd394acb780b620cab0
        intelligent_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/intelligent-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        webhook_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/webhook-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          auth_secret_key: O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1
        message_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/messaging-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        mixpanel:
          token: ef72dd13a0ffccb584cfdf75d3160e25
        mobile_app_management_extension_version: latest
        notification_hub:
          connection_string: Endpoint=sb://sleekflowproduction.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=pbzIBQQoNB8rGgrHdt1vmCIuTIB/Aaey5iION3eiCbQ=
          hub_name: SleekflowProduction
        public_api_gateway:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/public-api-gateway
          key: a12da7c775d00cada5b1ee611d3f6dca
        reseller:
          domain_name: https://partner.sleekflow.io
        rewardful:
          api_secret: ac7a0f566451bc405148d186bf0301c1
        salesforce:
          custom_active_web_app: https://sfmc-custom-activity.vercel.app
        share_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/share-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        shopify:
          shopify_api_key: b1781c9e361cfc851ee8a857fd046cca
          shopify_secret_key: 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
        snapshot_debugger_extension_version: disabled
        sql_performance:
          from_raw_sql: "true"
          is_and_condition_enabled: "true"
          is_or_condition_enabled: "true"
          is_conversation_analytics_condition_enabled: "false"
          is_shopify_order_statistics_enabled: "false"
          is_sales_performance_enabled: "false"
        stripe:
          stripe_public_key: pk_live_J0gdFoKSpThotTX5jHLKf5OJ00AMt1g3j8
          stripe_report_key: ***********************************************************************************************************
          stripe_secret_key: ******************************************
          stripe_webhook_secret: whsec_p9bgQl6uYXmlgRQf8vYkdaFuh8WiJIlK
        stripe_payment:
          stripe_payment_secret_key_gb: ***********************************************************************************************************
          stripe_payment_secret_key_hk: ***********************************************************************************************************
          stripe_payment_secret_key_my: ***********************************************************************************************************
          stripe_payment_secret_key_sg: ***********************************************************************************************************
        stripe_report:
          stripe_report_webhook_secret_gb: whsec_3W17qqv4KOaD3MrUYydhh0S69Kfq8e0s
          stripe_report_webhook_secret_hk: whsec_p9bgQl6uYXmlgRQf8vYkdaFuh8WiJIlK
          stripe_report_webhook_secret_my: whsec_x0HOEz7Wx3OSvddWSFAIKsIDJYRdiPrC
          stripe_report_webhook_secret_sg: whsec_X7m1uFBMi0soo65ZAMfBHfGIQaCO582s
        tenant_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/tenant-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          is_enable_tenant_logic: "true"
        ticketing_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/ticketing-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
          is_enable_ticketing_logic: "false"
        test_swaping: 1
        token:
          audience: https://sleekflow-prod-api.azurewebsites.net
        tokens:
          audience: https://travis-crm-api-hk.azurewebsites.net
          issuer: https://sleekflow-prod-api.azurewebsites.net
          key: 9C91A5D4BF0A3D803FE4A07550C1A5D9D55BEFE1F1226C2C5D22F29D9CED8036
          lifetime: 60
        user_event_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/user-event-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
        values:
          app_domain_name: https://perf.sleekflow.io
          app_domain_name_v1: https://perf.sleekflow.io
          share_link_function: https://share.sleekflow.io
          sleekflow_api_gateway: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net
          sleekflow_company_id: 471a6289-b9b7-43c3-b6ad-395a1992baea
          sleekflow_public_api_url: https://api.sleekflow.io
          sleekflow_public_api_key: t0MPHYgy390Ou32mOlab2wQfdQm8IVLXKOSQkoROLPg
          sleekflow_company_should_use_public_api: "true"
          sleek_pay_function: https://pay-eas.sleekflow.io
        website_http_logging_retention_days: 5
        website_node_default_version: 6.9.1
        whatsapp_cloud_api_template:
          default_image_blob_id: sleekflow.png
        xdt_microsoft_application_insights_base_extensions: disabled
        xdt_microsoft_application_insight_mode: default
        contact_safe_deletion:
          is_feature_enabled: "true"
        global_pricing:
          is_feature_enabled: "true"
          plan_migration_incentives_start_date: "2024-11-20"
          plan_migration_incentives_end_date: "2025-02-20"
        feature_flags:
          - feature_name: FlowBuilderMonetisation
            is_enabled: "false"
          - feature_name: Rbac
            is_enabled: "false"
        partner_stack:
          public_key: pk_bvEwuCDvoz9Qh11y48006zWXdCgsvInk
          secret_key: sk_KnZllhAJOasP09CPsMOOoiUqKAO81nMy
        hangfire_worker:
          worker_count: 5
        internal_integration_hub:
          endpoint: "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/internal-integration-hub"
          key: LxiRidS8SN9XY2rqu2GpQPih9kuxfNbcn5nxewNwmHmxdMDundJVbf4BfwZVMcjX
        hangfire_queues:
          disable_instances: ""
        integration_alert:
          endpoint: ""
          api_key: ""
          host_company_id: ""
          from_phone_number: ""
          template_name: ""
          facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
        rbac:
          is_middleware_verification_enabled: "false"
        flow_builder_flow_enrollments_incentives:
          period_start: "2025-01-24"
          period_end: "2025-07-24"
        hub_spot_smtp:
          username: "*******"
          password: "yus47rNx7M7Ca0xqdiQNZVGEOXb1yh"
        legacy_premium_opt_in_upgrade_incentives:
          period_start: "2025-02-10"
          period_end: "2025-06-10"