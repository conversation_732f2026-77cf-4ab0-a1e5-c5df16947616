﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;

namespace Travis_backend.AnalyticsDomain.Models;

[Table("conversation_analytics_metrics")]
public class ConversationAnalyticsMetric
{
    private bool _isDimensionDataCached = false;
    private ConversationAnalyticsDimensionData _dimensionData;

    [Column("id")]
    public string Id { get; set; }

    [Column("period_type")]
    public string PeriodType { get; set; }

    [Column("period_start_date")]
    public DateOnly? PeriodStartDate { get; set; }

    [Column("sleekflow_company_id")]
    public string CompanyId { get; set; }

    [Column("user_profile_id")]
    public string UserProfileId { get; set; }

    [Column("dim_json")]
    public string DimensionDataJson { get; set; }

    public ConversationAnalyticsDimensionData DimensionData
    {
        get
        {
            if (!_isDimensionDataCached)
            {
                try
                {
                    _dimensionData = JsonConvert.DeserializeObject<ConversationAnalyticsDimensionData>(
                        DimensionDataJson,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });
                }
                catch (JsonException ex)
                {
                    throw new InvalidOperationException("Failed to deserialize DimensionDataJson", ex);
                }

                _isDimensionDataCached = true;
            }

            return _dimensionData;
        }
    }

    [Column("dim_json_array")]
    public string DimensionKey { get; set; }

    [Column("total_conversations")]
    public int? TotalConversations { get; set; }

    [Column("total_active_conversations")]
    public int? TotalActiveConversations { get; set; }

    [Column("messages_sent")]
    public int? MessagesSent { get; set; }

    [Column("messages_received")]
    public int? MessagesReceived { get; set; }

    [Column("messages_send_failed")]
    public int? MessagesSendFailed { get; set; }

    [Column("new_enquiries")]
    public int? NewEnquiries { get; set; }

    [Column("new_contacts")]
    public int? NewContacts { get; set; }

    [Column("resolution_time_in_seconds_numerator")]
    public int? ResolutionTimeInSecondsNumerator { get; set; }

    [Column("resolution_time_in_seconds_denominator")]
    public int? ResolutionTimeInSecondsDenominator { get; set; }

    [Column("first_response_time_in_seconds_numerator")]
    public int? FirstResponseTimeInSecondsNumerator { get; set; }

    [Column("first_response_time_in_seconds_denominator")]
    public int? FirstResponseTimeInSecondsDenominator { get; set; }

    [Column("response_time_in_seconds_numerator")]
    public int? ResponseTimeInSecondsNumerator { get; set; }

    [Column("response_time_in_seconds_denominator")]
    public int? ResponseTimeInSecondsDenominator { get; set; }

    [Column("to_be_deleted")]
    public byte ToBeDeleted { get; set; }

    public static ConversationAnalyticsMetric Empty()
    {
        return new ConversationAnalyticsMetric
        {
            DimensionDataJson = string.Empty,
            PeriodType = string.Empty,
            PeriodStartDate = null,
            CompanyId = string.Empty,
            UserProfileId = string.Empty,
            DimensionKey = string.Empty,
            TotalConversations = 0,
            TotalActiveConversations = 0,
            MessagesSent = 0,
            MessagesReceived = 0,
            MessagesSendFailed = 0,
            ResolutionTimeInSecondsNumerator = 0,
            ResolutionTimeInSecondsDenominator = 0,
            FirstResponseTimeInSecondsNumerator = 0,
            FirstResponseTimeInSecondsDenominator = 0,
            ResponseTimeInSecondsNumerator = 0,
            ResponseTimeInSecondsDenominator = 0,
            NewEnquiries = 0,
            NewContacts = 0
        };
    }

    public void SetTimeRelatedDataPointsToZero()
    {
        ResolutionTimeInSecondsNumerator = 0;
        ResolutionTimeInSecondsDenominator = 0;
        FirstResponseTimeInSecondsNumerator = 0;
        FirstResponseTimeInSecondsDenominator = 0;
        ResponseTimeInSecondsNumerator = 0;
        ResponseTimeInSecondsDenominator = 0;
    }
}