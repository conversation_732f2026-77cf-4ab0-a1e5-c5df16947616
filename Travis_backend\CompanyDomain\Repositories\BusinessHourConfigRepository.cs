using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Database;

namespace Travis_backend.CompanyDomain.Repositories;

public class BusinessHourConfigRepository : IBusinessHourConfigRepository
{
    private readonly ApplicationDbContext _applicationDbContext;

    public BusinessHourConfigRepository(ApplicationDbContext applicationDbContext)
    {
        _applicationDbContext = applicationDbContext;
    }

    public async Task<BusinessHourConfig> GetAsync(string companyId)
    {
        return await _applicationDbContext.BusinessHourConfigs
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.CompanyId == companyId);
    }

    public async Task<BusinessHourConfig> CreateAndGetAsync(string companyId, bool isEnabled, WeeklyHours weeklyHours)
    {
        var businessHourConfig = new BusinessHourConfig
        {
            CompanyId = companyId,
            IsEnabled = isEnabled,
            WeeklyHours = weeklyHours,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var result= await _applicationDbContext.BusinessHourConfigs.AddAsync(businessHourConfig);
        await _applicationDbContext.SaveChangesAsync();

        return result.Entity;
    }

    public async Task<int> UpdateAsync(string companyId, bool isEnabled, WeeklyHours weeklyHours)
    {
        return await _applicationDbContext.BusinessHourConfigs
            .Where(x => x.CompanyId == companyId)
            .ExecuteUpdateAsync(
                entity => entity
                    .SetProperty(b => b.IsEnabled, isEnabled)
                    .SetProperty(b => b.WeeklyHours, weeklyHours)
                    .SetProperty(b => b.UpdatedAt, DateTime.UtcNow));
    }
}