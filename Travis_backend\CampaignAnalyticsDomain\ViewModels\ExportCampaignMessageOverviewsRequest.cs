﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Travis_backend.CampaignAnalyticsDomain.Constants;
using Travis_backend.CampaignAnalyticsDomain.Models;

namespace Travis_backend.CampaignAnalyticsDomain.ViewModels;

public abstract class ExportCampaignMessageOverviewsRequest : IValidatableObject
{
    [Required]
    public string Status { get; set; }

    public ReplyWindow ReplyWindow { get; set; }

    public string OrderBy { get; set; } = GetMessageOverviewSortableFields.CreatedAt;

    [RegularExpression("^(asc|ASC|desc|DESC)$")]
    public string Direction { get; set; } = "desc";

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (!CampaignMessageStatuses.All.Contains(Status))
        {
            yield return new ValidationResult(
                "Illegal message status.",
                new[] { nameof(Status) });
        }

        if (Status != CampaignMessageStatuses.Bounced && ReplyWindow is null)
        {
            yield return new ValidationResult(
                $"ReplyWindow is required for status: {Status}.",
                new[] { nameof(ReplyWindow) });
        }

        if (!GetMessageOverviewSortableFields.All.Contains(OrderBy))
        {
            yield return new ValidationResult(
                "Illegal order by field.",
                new[] { nameof(OrderBy) });
        }
    }
}

public class ExportCampaignMessageOverviewsByBroadcastRequest : ExportCampaignMessageOverviewsRequest
{
    [Required]
    public string BroadcastCampaignId { get; set; }
}

public class ExportCampaignMessageOverviewsByAnalyticTagRequest : ExportCampaignMessageOverviewsRequest
{
    [Required]
    [MinLength(1)]
    public string AnalyticTag { get; set; }
}