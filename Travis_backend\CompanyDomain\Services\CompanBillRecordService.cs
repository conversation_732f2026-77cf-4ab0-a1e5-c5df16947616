using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Services;

namespace Travis_backend.CompanyDomain.Services;

/// <inheritdoc />
public class CompanyBillRecordService : ICompanyBillRecordService
{
    #region Dependencies & Constructor

    /// <summary>
    /// ISubscriptionPlanService.
    /// </summary>
    private readonly ISubscriptionPlanService _subscriptionPlanService;

    /// <summary>
    /// ICompanyBillRecordRepository.
    /// </summary>
    private readonly ICompanyBillRecordRepository _companyBillRecordRepository;

    /// <summary>
    /// IUsageCycleCalculator.
    /// </summary>
    private readonly IUsageCycleCalculator _usageCycleCalculator;

    /// <summary>
    /// Initializes a new instance of the <see cref="CompanyBillRecordService"/> class.
    /// </summary>
    /// <param name="subscriptionPlanService">ISubscriptionPlanService.</param>
    /// <param name="companyBillRecordRepository">ICompanyBillRecordRepository.</param>
    /// <param name="usageCycleCalculator">IUsageCycleCalculator.</param>
    public CompanyBillRecordService(
        ISubscriptionPlanService subscriptionPlanService,
        ICompanyBillRecordRepository companyBillRecordRepository,
        IUsageCycleCalculator usageCycleCalculator)
    {
        _subscriptionPlanService = subscriptionPlanService;
        _companyBillRecordRepository = companyBillRecordRepository;
        _usageCycleCalculator = usageCycleCalculator;
    }

    #endregion

    /// <inheritdoc />
    public async Task CreateBillRecordAsync(BillRecord billRecord)
    {
        var subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanAsync(billRecord.SubscriptionPlanId);

        if (subscriptionPlan == null)
        {
            throw new ApplicationException($"SubscriptionPlan \"{billRecord.SubscriptionPlanId}\" Not Exists.");
        }

        billRecord.SubscriptionPlan = subscriptionPlan;
        billRecord.SubscriptionTier = subscriptionPlan.SubscriptionTier;

        if (subscriptionPlan.PlanTypeCode == PlanTypeCodes.BasePlan)
        {
            var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
            billRecord.UsageCycleStart = usageCycle.From;
            billRecord.UsageCycleEnd = usageCycle.To;
        }

        await _companyBillRecordRepository.CreateBillRecordAsync(billRecord);
    }

    /// <inheritdoc />
    public Task<bool> HasBillRecordAsync(string companyId, Expression<Func<BillRecord, bool>> predicate)
    {
        return _companyBillRecordRepository.HasBillRecordAsync(companyId, predicate);
    }

    /// <inheritdoc />
    public Task<bool> HasValidBillRecordAsync(string companyId, Expression<Func<BillRecord, bool>> predicate)
    {
        return _companyBillRecordRepository.HasValidBillRecordAsync(companyId, predicate);
    }

    /// <inheritdoc />
    public Task<BillRecord?> GetLastBillRecordAsync(string companyId, Expression<Func<BillRecord, bool>> extraPredication = null)
    {
        return _companyBillRecordRepository.GetLastBillRecordAsync(companyId, extraPredication);
    }

    public Task<IReadOnlyCollection<BillRecord>> GetValidBillRecordsAsync(string companyId, Expression<Func<BillRecord, bool>> extraPredication = null)
    {
        return _companyBillRecordRepository.GetValidBillRecordsAsync(companyId, extraPredication);
    }

    /// <inheritdoc />
    public Task<int> DeleteBillRecordsAsync(string companyId, Expression<Func<BillRecord, bool>> predicate)
    {
        return _companyBillRecordRepository.DeleteBillRecordsAsync(companyId, predicate);
    }
}