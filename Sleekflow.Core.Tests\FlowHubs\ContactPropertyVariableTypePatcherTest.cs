using System.Runtime.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.FieldMetadataGenerator.VariableTypeHelper;
using Travis_backend.FlowHubs.Models;
using NUnit.Framework;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Sleekflow.Core.Tests.FlowHubs;

[TestFixture]
public class ContactPropertyVariableTypePatcherTest
{
    private Mock<IDbContextService> _dbContextService;
    private Mock<ILogger<ContactPropertyVariableTypePatcher>> _logger;
    private ContactPropertyVariableTypePatcher _patcher;
    private BaseDbContext _dbContext;
    private List<CompanyCustomUserProfileField> _customFields;
    private string companyId = "CompanyId";

    [SetUp]
    public async Task Setup()
    {
        _customFields = new List<CompanyCustomUserProfileField>
        {
            new() {
                CompanyId = companyId,
                FieldName = "Birthday",
                Type = FieldDataType.Date,
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
            },
            new() {
                CompanyId = companyId,
                FieldName = "labels",
                Type = FieldDataType.Labels,
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
            },
            new() {
                CompanyId = companyId,
                FieldName = "L'Birthday",
                Type = FieldDataType.Date,
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
            },
            new() {
                CompanyId = companyId,
                FieldName = "Enrollment Time",
                Type = FieldDataType.DateTime,
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
            },
            new() {
                CompanyId = companyId,
                FieldName = "best score",
                Type = FieldDataType.Options,
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
            },
        };

        // Use real in-memory database
        var options = new DbContextOptionsBuilder<BaseDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _dbContext = new BaseDbContext(options);

        // Seed the database with test data
        _dbContext.CompanyCustomUserProfileFields.AddRange(_customFields);
        await _dbContext.SaveChangesAsync();

        _dbContextService = new Mock<IDbContextService>();
        _logger = new Mock<ILogger<ContactPropertyVariableTypePatcher>>();
        _dbContextService.Setup(x => x.GetDbContext()).Returns(_dbContext);
        _patcher = new ContactPropertyVariableTypePatcher(_dbContextService.Object, _logger.Object);
    }

    [Test]
    public async Task PatchFieldMetadataSet_ShouldUpdateFieldTypes()
    {
        // Arrange
        var fieldMetadataSet = new HashSet<FieldMetadata>
        {
            new(fieldPath: "contact.age", displayPath: "contact/age", fieldType: "Integer", options: null, optionsRequestPath: null, sampleValue: "25"),
            new(fieldPath: "contact.isActive", displayPath: "contact/isActive", fieldType: "Boolean", options: null, optionsRequestPath: null, sampleValue: "true"),
            new(fieldPath: "contact.Birthday", displayPath: "contact/Birthday", fieldType: "String", options: null, optionsRequestPath: null, sampleValue: "2000-01-01"),
            new(fieldPath: "contact['labels']", displayPath: "contact/labels", fieldType: "String", options: null, optionsRequestPath: null, sampleValue: "label1,label2"),
            new(fieldPath: "contact['L\\'Birthday']", displayPath: "contact/labels", fieldType: "Date", options: null, optionsRequestPath: null, sampleValue: "label1,label2"),
            new(fieldPath: "contact['Enrollment Time']", displayPath: "contact/Enrollment Time", fieldType: "String", options: null, optionsRequestPath: null, sampleValue: "value"),
            new(fieldPath: "contact['best score']", displayPath: "contact/score", fieldType: "Integer", options: null, optionsRequestPath: null, sampleValue: "value"),
            new(fieldPath: "other.field", displayPath: "other/field", fieldType: "Boolean", options: null, optionsRequestPath: null, sampleValue: "value")
        };

        // Act
        await _patcher.PatchTypeReferToContactProperty(companyId, typeof(TestEventBody), "Contact", fieldMetadataSet);

        // Assert
        var ageField = fieldMetadataSet.First(x => x.FieldPath == "contact.age");
        var isActiveField = fieldMetadataSet.First(x => x.FieldPath == "contact.isActive");
        var birthdayField = fieldMetadataSet.First(x => x.FieldPath == "contact.Birthday");
        var labelsField = fieldMetadataSet.First(x => x.FieldPath == "contact['labels']");
        var lBirthday = fieldMetadataSet.First(x => x.FieldPath == "contact['L\\'Birthday']");
        var enrollmentTimeField = fieldMetadataSet.First(x => x.FieldPath == "contact['Enrollment Time']");
        var scoreField = fieldMetadataSet.First(x => x.FieldPath == "contact['best score']");
        var otherField = fieldMetadataSet.First(x => x.FieldPath == "other.field");

        Assert.That(ageField.FieldType, Is.EqualTo(PredefineDataType.Integer));
        Assert.That(isActiveField.FieldType, Is.EqualTo(PredefineDataType.Boolean));
        Assert.That(birthdayField.FieldType, Is.EqualTo(PredefineDataType.DateOnly));
        Assert.That(labelsField.FieldType, Is.EqualTo(PredefineDataType.Options));
        Assert.That(lBirthday.FieldType, Is.EqualTo(PredefineDataType.DateOnly));
        Assert.That(enrollmentTimeField.FieldType, Is.EqualTo(PredefineDataType.Date));
        Assert.That(scoreField.FieldType, Is.EqualTo(PredefineDataType.Options));
        Assert.That(otherField.FieldType, Is.EqualTo(PredefineDataType.Boolean));
    }

    [TearDown]
    public void TearDown()
    {
        _dbContext?.Dispose();
    }

    private class TestEventBody
    {
        [DataMember(Name = "contact")]
        public string Contact { get; set; } = string.Empty;
    }
}