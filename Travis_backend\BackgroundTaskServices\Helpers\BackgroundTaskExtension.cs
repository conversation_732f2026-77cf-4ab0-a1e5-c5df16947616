﻿using Newtonsoft.Json;
using Travis_backend.BackgroundTaskServices.Models;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Models.BackgroundTask;

namespace Travis_backend.BackgroundTaskServices.Helpers;

public static class BackgroundTaskExtension
{
    public static BackgroundTaskViewModel MapToResultViewModel(
        this BackgroundTask backgroundTask,
        BackgroundTaskStatus? progress = null)
    {
        var backgroundTaskViewModel = new BackgroundTaskViewModel
        {
            Id = backgroundTask.Id,
            CompanyId = backgroundTask.CompanyId,
            StaffId = backgroundTask.StaffId,
            UserId = backgroundTask.UserId,
            Total = backgroundTask.Total,
            Progress = backgroundTask.Progress,
            IsCompleted = backgroundTask.IsCompleted,
            IsDismissed = backgroundTask.IsDismissed,
            StartedAt = backgroundTask.StartedAt,
            CompletedAt = backgroundTask.CompletedAt,
            TaskType = backgroundTask.TaskType,
            UpdatedAt = backgroundTask.UpdatedAt,
            CreatedAt = backgroundTask.CreatedAt,
            ErrorMessage = backgroundTask.ErrorMessage,
        };

        switch (backgroundTaskViewModel.TaskType)
        {
            case BackgroundTaskType.ExportContactsListToCsv:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<ContactListBackgroundTaskTargetPayload>(
                            backgroundTask.TargetPayload);
                }

                if (backgroundTask.ResultPayload != null)
                {
                    backgroundTaskViewModel.Result =
                        JsonConvert.DeserializeObject<ExportContactsListToCsvBackgroundTaskResultPayload>(
                            backgroundTask.ResultPayload);
                }

                break;
            case BackgroundTaskType.AddContactsToList:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<ContactListBackgroundTaskTargetPayload>(
                            backgroundTask.TargetPayload);
                }

                break;
            case BackgroundTaskType.ImportContacts:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<ContactListBackgroundTaskTargetPayload>(
                            backgroundTask.TargetPayload);
                }

                break;
            case BackgroundTaskType.ImportContactsV2:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<ContactListBackgroundTaskTargetPayload>(
                            backgroundTask.TargetPayload);
                }

                break;
            case BackgroundTaskType.BulkImportContacts:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<ContactListBackgroundTaskTargetPayload>(
                            backgroundTask.TargetPayload);
                }

                break;
            case BackgroundTaskType.BulkUpdateContactsCustomFields:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<ContactBackgroundTaskTargetPayload>(backgroundTask.TargetPayload);
                }

                break;
            case BackgroundTaskType.ExportBroadcastStatusListToCsv:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<CampaignBackgroundTaskTargetPayload>(
                            backgroundTask.TargetPayload);
                }

                if (backgroundTask.ResultPayload != null)
                {
                    backgroundTaskViewModel.Result =
                        JsonConvert.DeserializeObject<ExportBroadcastStatusListToCsvBackgroundTaskResultPayload>(
                            backgroundTask.ResultPayload);
                }

                break;
            case BackgroundTaskType.ExportAnalyticToCsv:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<AnalyticBackgroundTaskTargetPayload>(
                            backgroundTask.TargetPayload);
                }

                if (backgroundTask.ResultPayload != null)
                {
                    backgroundTaskViewModel.Result =
                        JsonConvert.DeserializeObject<ExportAnalyticListToCsvBackgroundTaskResultPayload>(
                            backgroundTask.ResultPayload);
                }

                break;

            case BackgroundTaskType.ImportWhatsAppHistory:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<WhatsAppHistoryBackgroundTaskTargetPayload>(
                            backgroundTask.TargetPayload);
                }

                break;
            case BackgroundTaskType.ConvertCampaignLeadsToContactList:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<ConvertCampaignLeadsToContactListTaskTargetPayLoad>(
                            backgroundTask.TargetPayload);
                }

                break;
            case BackgroundTaskType.ExportFlowHubWorkflowExecutionUsagesToCsv:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<ExportFlowHubWorkflowExecutionUsagesToCsvTaskTargetPayload>(
                            backgroundTask.TargetPayload);
                }

                if (backgroundTask.ResultPayload != null)
                {
                    backgroundTaskViewModel.Result =
                        JsonConvert.DeserializeObject<ExportFlowHubWorkflowExecutionUsagesTaskResultPayload>(
                            backgroundTask.ResultPayload);
                }

                break;
            case BackgroundTaskType.ExportTicketsCsv:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<ExportTicketsCsvPayload>(
                            backgroundTask.TargetPayload);
                }

                if (backgroundTask.ResultPayload != null)
                {
                    backgroundTaskViewModel.Result =
                        JsonConvert.DeserializeObject<ExportTicketsCsvTaskResultPayload>(
                            backgroundTask.ResultPayload);
                }

                break;
            case BackgroundTaskType.ExportConversationAnalyticsToCsv:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<ConversationAnalyticsBackgroundTaskTargetPayload>(
                            backgroundTask.TargetPayload);
                }

                if (backgroundTask.ResultPayload != null)
                {
                    backgroundTaskViewModel.Result =
                        JsonConvert.DeserializeObject<ExportConversationAnalyticListToCsvBackgroundTaskResultPayload>(
                            backgroundTask.ResultPayload);
                }

                break;

            case BackgroundTaskType.ExportCampaignAnalyticsMessageOverviewsToCsv:
                if (backgroundTask.TargetPayload != null)
                {
                    backgroundTaskViewModel.Target =
                        JsonConvert.DeserializeObject<CampaignAnalyticsBackgroundTaskTargetPayload>(
                            backgroundTask.TargetPayload);
                }

                if (backgroundTask.ResultPayload != null)
                {
                    backgroundTaskViewModel.Result =
                        JsonConvert.DeserializeObject<ExportCampaignAnalyticMessageOverviewsToCsvBackgroundTaskResultPayload>(
                            backgroundTask.ResultPayload);
                }

                break;
        }

        if (progress != null)
        {
            backgroundTaskViewModel.TaskStatus = progress.Value;
        }
        else
        {
            if (backgroundTaskViewModel.IsCompleted)
            {
                backgroundTaskViewModel.TaskStatus = BackgroundTaskStatus.Completed;
            }
            else if (!string.IsNullOrEmpty(backgroundTaskViewModel.ErrorMessage))
            {
                backgroundTaskViewModel.TaskStatus = BackgroundTaskStatus.Error;
            }
            else if (backgroundTaskViewModel.Progress > 0)
            {
                backgroundTaskViewModel.TaskStatus = BackgroundTaskStatus.Processing;
            }
            else if (backgroundTaskViewModel.StartedAt.HasValue && backgroundTaskViewModel.Progress == 0)
            {
                backgroundTaskViewModel.TaskStatus = BackgroundTaskStatus.Started;
            }
            else
            {
                backgroundTaskViewModel.TaskStatus = BackgroundTaskStatus.Queued;
            }
        }

        return backgroundTaskViewModel;
    }
}