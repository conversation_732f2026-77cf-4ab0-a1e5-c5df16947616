﻿using System.Linq.Expressions;
using BenchmarkDotNet.Attributes;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Models;
using Travis_backend.Database;
using Travis_backend.Database.DataAccessLayer;
using Travis_backend.Database.Services;
using Z.EntityFramework.Extensions;

namespace Sleekflow.Core.Benchmarks;

public class UserprofileCustomFieldsBenchmark
{
    private bool _firstCall = true;
    private readonly ILogger<ConversationHashtagsBenchmark> _logger;
    private readonly ApplicationDbContext _applicationDbContext;
    private readonly ApplicationReadDbContext _applicationReadDbContext;
    private readonly AnalyticDbContext _analyticDbContext;
    private const string BenchmarkCompanyId = "da5e436d-db50-4f21-9753-b0b1586eac36";
    private IRepository<UserProfileCustomField> _userProfileCustomFieldRepository;
    private IRepository<CompanyCustomUserProfileField> _companyCustomUserProfileFieldRepository;
    private const int BatchSize = 500;
    private const int DeleteSize = 5000;


    public UserprofileCustomFieldsBenchmark()
    {
        var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
        _logger = loggerFactory.CreateLogger<ConversationHashtagsBenchmark>();

        _applicationDbContext = new ApplicationDbContext(
            new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseSqlServer(new SqlConnection(DbConfig.ReadWriteConnStr))
                .LogTo(Console.WriteLine, LogLevel.Information).Options);

        _applicationReadDbContext = new ApplicationReadDbContext(
            new DbContextOptionsBuilder<ApplicationReadDbContext>()
                .UseSqlServer(new SqlConnection(DbConfig.ReadOnlyConnStr))
                .LogTo(Console.WriteLine, LogLevel.Information).Options);

        _analyticDbContext = new AnalyticDbContext(
            new DbContextOptionsBuilder<AnalyticDbContext>()
                .UseSqlServer(new SqlConnection(DbConfig.AnalyticDbConnStr))
                .LogTo(Console.WriteLine, LogLevel.Information).Options);

    }

    [GlobalSetup]
    public Task GlobalSetup()
    {
        var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });

        // Initialize Repositories
        var dbContextService = new DbContextService(new PersistenceContext(), _applicationDbContext, _applicationReadDbContext, _analyticDbContext); // Assuming DbContextService implementation

        _userProfileCustomFieldRepository = new Repository<UserProfileCustomField>(
            dbContextService,
            loggerFactory.CreateLogger<Repository<UserProfileCustomField>>());

        _companyCustomUserProfileFieldRepository = new Repository<CompanyCustomUserProfileField>(
            dbContextService,
            loggerFactory.CreateLogger<Repository<CompanyCustomUserProfileField>>());

        return Task.CompletedTask;
    }

    [GlobalCleanup]
    public async Task GlobalCleanup()
    {
        await _applicationDbContext.DisposeAsync();
        await _applicationReadDbContext.DisposeAsync();
    }

    [Benchmark(Baseline = true)]
    public async Task BulkDeleteCustomerUserprofileFieldsOneTimeExecution()
    {
        var queryable = _applicationDbContext.UserProfileCustomFields.Where(
            x => _applicationDbContext.CompanyCustomUserProfileFields.Where(cu => cu.CompanyId == BenchmarkCompanyId)
                .Select(cu => cu.Id).Contains(x.CompanyDefinedFieldId)).OrderBy(x => x.Id).Take(DeleteSize);

        if (_firstCall)
        {
            var queryString = queryable.ToQueryString();
            _logger.LogInformation(nameof(BulkDeleteCustomerUserprofileFieldsOneTimeExecution) + " " + queryString);
            _firstCall = false;
        }

        await queryable.ExecuteDeleteAsync();
    }

    [Benchmark]
    public async Task BulkDeleteCustomUserprofileFieldsByBatchesAsync()
    {

        Expression<Func<CompanyCustomUserProfileField, bool>> companyCustomUserProfileFieldsPredicate =
            x => x.CompanyId == BenchmarkCompanyId;
        Expression<Func<UserProfileCustomField, bool>> predicate = x =>
            _companyCustomUserProfileFieldRepository.GetAll(companyCustomUserProfileFieldsPredicate)
                .Select(ccupf => ccupf.Id).Contains(x.CompanyDefinedFieldId);

        if (_firstCall)
        {
            var queryString = _applicationDbContext.Set<UserProfileCustomField>().Where(predicate).ToQueryString();
            _logger.LogInformation(nameof(BulkDeleteCustomUserprofileFieldsByBatchesAsync) + " " + queryString);
            _firstCall = false;
        }

        void BatchDeleteAction(BatchDelete delete)
        {
            delete.UseTableLock = false;
            delete.IgnoreInMemoryAsNoTracking = true;
        }

        await _userProfileCustomFieldRepository.BulkDeleteByBatchAsync(
            predicate,
            x => x.Id,
            BatchDeleteAction,
            batchSize: BatchSize,
            deleteSize: DeleteSize);
    }

}