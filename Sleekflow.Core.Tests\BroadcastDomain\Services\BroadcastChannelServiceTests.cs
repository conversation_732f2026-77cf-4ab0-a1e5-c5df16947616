using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.BroadcastDomain.Services;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Repositories;
using Travis_backend.MessageDomain.ViewModels;

namespace Sleekflow.Core.Tests.BroadcastDomain.Services;

[TestFixture]
public class BroadcastChannelServiceTests
{
    private Mock<ILogger<BroadcastChannelService>> _loggerMock;
    private Mock<IChannelIdentityIdRepository> _channelIdentityIdRepositoryMock;
    private Mock<IWhatsappCloudApiService> _whatsappCloudApiServiceMock;
    private BroadcastChannelService _service;

    [SetUp]
    public void SetUp()
    {
        _loggerMock = new Mock<ILogger<BroadcastChannelService>>();
        _channelIdentityIdRepositoryMock = new Mock<IChannelIdentityIdRepository>();
        _whatsappCloudApiServiceMock = new Mock<IWhatsappCloudApiService>();

        _service = new BroadcastChannelService(
            _loggerMock.Object,
            _channelIdentityIdRepositoryMock.Object,
            _whatsappCloudApiServiceMock.Object);
    }

    [Test]
    public async Task PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync_WithWhatsappCloudApiMessagesAndMedia_ShouldUploadMediaAndReturnTrue()
    {
        // Arrange
        var companyId = "testCompanyId";
        var channelIdentityId = "testChannelIdentityId";
        var documentLink = "http://example.com/document.pdf";
        var imageLink = "http://example.com/image.jpg";
        var videoLink = "http://example.com/video.mp4";
        var documentId = "docId";
        var imageId = "imgId";
        var videoId = "vidId";

        var companyMessageTemplate = new CompanyMessageTemplate
        {
            CompanyId = companyId,
            CampaignChannelMessages = new List<CampaignChannelMessage>
            {
                new CampaignChannelMessage
                {
                    TargetedChannel = new TargetedChannel
                    {
                        ChannelType = ChannelTypes.WhatsappCloudApi, ChannelIdentityId = channelIdentityId
                    },
                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail
                    {
                        WhatsappCloudApiTemplateMessageObject = new WhatsappCloudApiTemplateMessageViewModel()
                        {
                            Components = new List<WhatsappCloudApiTemplateMessageComponentObject>
                            {
                                new ()
                                {
                                    Type = "header",
                                    Parameters = new List<WhatsappCloudApiParameterObject>
                                    {
                                        new ()
                                        {
                                            Type = "document",
                                            Document = new WhatsappCloudApiMediaObject
                                            {
                                                Link = documentLink
                                            }
                                        },
                                        new ()
                                        {
                                            Type = "image",
                                            Image = new WhatsappCloudApiMediaObject
                                            {
                                                Link = imageLink
                                            }
                                        },
                                        new ()
                                        {
                                            Type = "video",
                                            Video = new WhatsappCloudApiMediaObject
                                            {
                                                Link = videoLink
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        };

        _whatsappCloudApiServiceMock.Setup(x => x.UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(companyId, channelIdentityId, documentLink))
            .ReturnsAsync(documentId);
        _whatsappCloudApiServiceMock.Setup(x => x.UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(companyId, channelIdentityId, imageLink))
            .ReturnsAsync(imageId);
        _whatsappCloudApiServiceMock.Setup(x => x.UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(companyId, channelIdentityId, videoLink))
            .ReturnsAsync(videoId);

        // Act
        var result = await _service.PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync(companyMessageTemplate);

        // Assert
        Assert.IsTrue(result);
        var parameters = companyMessageTemplate.CampaignChannelMessages.First()
            .ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.Components.First().Parameters;

        Assert.AreEqual(documentId, parameters.First(p => p.Type == "document").Document.Id);
        Assert.AreEqual(imageId, parameters.First(p => p.Type == "image").Image.Id);
        Assert.AreEqual(videoId, parameters.First(p => p.Type == "video").Video.Id);

        _whatsappCloudApiServiceMock.Verify(x => x.UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(companyId, channelIdentityId, documentLink), Times.Once);
        _whatsappCloudApiServiceMock.Verify(x => x.UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(companyId, channelIdentityId, imageLink), Times.Once);
        _whatsappCloudApiServiceMock.Verify(x => x.UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(companyId, channelIdentityId, videoLink), Times.Once);
    }

    [Test]
    public async Task PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync_NoWhatsappCloudApiMessages_ShouldReturnFalse()
    {
        // Arrange
        var companyMessageTemplate = new CompanyMessageTemplate
        {
            CompanyId = "testCompanyId",
            CampaignChannelMessages = new List<CampaignChannelMessage>
            {
                new CampaignChannelMessage { TargetedChannel = new TargetedChannel { ChannelType = ChannelTypes.WhatsappTwilio } } // Different channel type
            }
        };

        // Act
        var result = await _service.PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync(companyMessageTemplate);

        // Assert
        Assert.IsFalse(result);
        _whatsappCloudApiServiceMock.Verify(x => x.UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Test]
    public async Task PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync_NoHeaderComponent_ShouldReturnFalse()
    {
        // Arrange
        var companyMessageTemplate = new CompanyMessageTemplate
        {
            CompanyId = "testCompanyId",
            CampaignChannelMessages = new List<CampaignChannelMessage>
            {
                new CampaignChannelMessage
                {
                    TargetedChannel = new TargetedChannel { ChannelType = ChannelTypes.WhatsappCloudApi, ChannelIdentityId = "id" },
                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail
                    {
                        WhatsappCloudApiTemplateMessageObject = new WhatsappCloudApiTemplateMessageViewModel
                        {
                            Components = new List<WhatsappCloudApiTemplateMessageComponentObject>() // Empty components
                        }
                    }
                }
            }
        };

        // Act
        var result = await _service.PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync(companyMessageTemplate);

        // Assert
        Assert.IsFalse(result);
        _whatsappCloudApiServiceMock.Verify(x => x.UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Test]
    public async Task PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync_HeaderComponentNoParameters_ShouldReturnFalse()
    {
        // Arrange
        var companyMessageTemplate = new CompanyMessageTemplate
        {
            CompanyId = "testCompanyId",
            CampaignChannelMessages = new List<CampaignChannelMessage>
            {
                new CampaignChannelMessage
                {
                    TargetedChannel = new TargetedChannel { ChannelType = ChannelTypes.WhatsappCloudApi, ChannelIdentityId = "id" },
                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail
                    {
                        WhatsappCloudApiTemplateMessageObject = new WhatsappCloudApiTemplateMessageViewModel
                        {
                            Components = new List<WhatsappCloudApiTemplateMessageComponentObject>
                            {
                                new()
                                {
                                    Type = "header",
                                    Parameters = null // No parameters
                                }
                            }
                        }
                    }
                }
            }
        };

        // Act
        var result = await _service.PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync(companyMessageTemplate);

        // Assert
        Assert.IsFalse(result);
        _whatsappCloudApiServiceMock.Verify(x => x.UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Test]
    public async Task PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync_UploadThrowsException_ShouldLogAndReturnFalse()
    {
        // Arrange
        var companyId = "testCompanyId";
        var channelIdentityId = "testChannelIdentityId";
        var documentLink = "http://example.com/document.pdf";
        var exception = new Exception("Upload failed");

        var companyMessageTemplate = new CompanyMessageTemplate
        {
            CompanyId = companyId,
            CampaignChannelMessages = new List<CampaignChannelMessage>
            {
                new CampaignChannelMessage
                {
                    TargetedChannel = new TargetedChannel { ChannelType = ChannelTypes.WhatsappCloudApi, ChannelIdentityId = channelIdentityId },
                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail
                    {
                        WhatsappCloudApiTemplateMessageObject = new WhatsappCloudApiTemplateMessageViewModel
                        {
                            Components = new List<WhatsappCloudApiTemplateMessageComponentObject>
                            {
                                new ()
                                {
                                    Type = "header",
                                    Parameters = new List<WhatsappCloudApiParameterObject>
                                    {
                                        new WhatsappCloudApiParameterObject { Type = "document", Document = new WhatsappCloudApiMediaObject() { Link = documentLink } }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        };

        _whatsappCloudApiServiceMock.Setup(x => x.UploadWhatsappCloudApiMediaByExtendedMessageFileUrlAsync(companyId, channelIdentityId, documentLink))
            .ThrowsAsync(exception);

        // Act
        var result = await _service.PopulateWhatsappCloudApiMediaIdToExtendedMessagePayloadDetailAsync(companyMessageTemplate);

        // Assert
        Assert.IsFalse(result);
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error populating Whatsapp Cloud API media")),
                exception,
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }
}