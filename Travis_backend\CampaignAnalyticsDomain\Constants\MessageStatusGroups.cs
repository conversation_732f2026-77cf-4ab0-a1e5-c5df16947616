﻿using System.Collections.Immutable;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.CampaignAnalyticsDomain.Constants;

public static class MessageStatusGroups
{
    public static ImmutableList<MessageStatus> Sent = ImmutableList.Create(
        MessageStatus.Sent,
        MessageStatus.Received,
        MessageStatus.Read,
        MessageStatus.Failed,
        MessageStatus.Undelivered,
        MessageStatus.OutOfCredit);

    public static ImmutableList<MessageStatus> Delivered = ImmutableList.Create(
        MessageStatus.Received,
        MessageStatus.Read);

    public static ImmutableList<MessageStatus> Bounced = ImmutableList.Create(
        MessageStatus.Failed,
        MessageStatus.Undelivered,
        MessageStatus.OutOfCredit);

    public static ImmutableList<MessageStatus> Read = ImmutableList.Create(
        MessageStatus.Read);
}