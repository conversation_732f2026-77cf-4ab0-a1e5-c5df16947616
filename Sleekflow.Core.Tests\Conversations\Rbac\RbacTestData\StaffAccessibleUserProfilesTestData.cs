using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.Models;

namespace Sleekflow.Core.Tests.Conversations.Rbac.RbacTestData;

public class StaffAccessibleUserProfilesTestData
{
    private const string CompanyId = "sleekflow";

    public class StaffAccessibleUserProfilesTestCaseData
    {
        public string Key { get; set; }
        public UserProfile UserProfile { get; set; }
        public Conversation Conversation { get; set; }
    }

    private static UserProfile CreateUserProfile(string key, string firstName, string lastName, string phone) =>
        new()
        {
            Id = $"{key}_userprofile",
            CompanyId = CompanyId,
            FirstName = firstName,
            LastName = lastName,
            PhoneNumber = phone
        };

    private static Conversation CreateConversation(string key, string userProfileId, long? assigneeId = null, long? teamId = null) =>
        new()
        {
            Id = $"{key}_conversation",
            CompanyId = CompanyId,
            UserProfileId = userProfileId,
            AssigneeId = assigneeId,
            AssignedTeamId = teamId
        };

    private static StaffAccessibleUserProfilesTestCaseData CreateAssignedToMeCase()
    {
        var key = "assigned_to_me";
        var userProfile = CreateUserProfile(key, "Assigned To", "Me", "1111111111");

        return new StaffAccessibleUserProfilesTestCaseData
        {
            Key = key, UserProfile = userProfile, Conversation = CreateConversation(key, userProfile.Id, assigneeId: 1)
        };
    }

    private static StaffAccessibleUserProfilesTestCaseData CreateAssignedToOtherCase()
    {
        var key = "assigned_to_other";
        var userProfile = CreateUserProfile(key, "Assigned To", "Me", "1111111111");

        return new StaffAccessibleUserProfilesTestCaseData
        {
            Key = key, UserProfile = userProfile, Conversation = CreateConversation(key, userProfile.Id, assigneeId: 100)
        };
    }

    private static StaffAccessibleUserProfilesTestCaseData CreateAssignedToMyTeamCase()
    {
        var key = "assigned_to_my_team";
        var userProfile = CreateUserProfile(key, "Assigned To", "My Team", "111111111");

        return new StaffAccessibleUserProfilesTestCaseData
        {
            Key = key, UserProfile = userProfile, Conversation = CreateConversation(key, userProfile.Id, null, 1)
        };
    }

    private static StaffAccessibleUserProfilesTestCaseData CreateAssignedToNonAssociatedTeamCase()
    {
        var key = "assigned_to_non_associated_team";
        var userProfile = CreateUserProfile(key, "Assigned To", "Non Associated Team", "111111111");

        return new StaffAccessibleUserProfilesTestCaseData
        {
            Key = key, UserProfile = userProfile, Conversation = CreateConversation(key, userProfile.Id, null, 2)
        };
    }

    private static StaffAccessibleUserProfilesTestCaseData CreateUnassignedCase()
    {
        var key = "unassigned";
        var userProfile = CreateUserProfile(key, "Uassigned", "Non Associated Team", "111111111");

        return new StaffAccessibleUserProfilesTestCaseData
        {
            Key = key, UserProfile = userProfile, Conversation = CreateConversation(key, userProfile.Id, null, null)
        };

    }
}

