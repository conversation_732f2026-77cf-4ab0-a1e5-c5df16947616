using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;
using Sleekflow.Core.StressTests.Constants;

namespace Sleekflow.Core.StressTests.Helpers;

public static class TravisBackendHelper
{
    public static async Task<string> GetSleekflowAccessTokenAsync(string username, string password)
    {
        var client = new HttpClient();
        var request = new HttpRequestMessage(
            HttpMethod.Post,
            $"{StressTestEnvironment.GetSleekflowCoreApiServerPath()}/auth0/account/GetUserToken");

        request.Content = new StringContent(
            JsonConvert.SerializeObject(
                new
                {
                    username, password
                }),
            Encoding.UTF8,
            "application/json");

        var response = await client.SendAsync(request);
        if (!response.IsSuccessStatusCode)
        {
            throw new Exception("Failed to get access token");
        }

        var responseContent = await response.Content.ReadAsStringAsync();
        var responseJson = JsonConvert.DeserializeObject<dynamic>(responseContent);
        return responseJson?.accessToken.ToString()!;
    }
}

public class StressTestSignatureHelper
{
    private const string SecretKey = "iklI4XkYHIKfDoLsTC5gV9XPq5YGQKat";

    public const string StressTestHeader = "X-Sleekflow-Stress-Test";
    public const string StressTestSignatureHeader = "X-Sleekflow-Stress-Test-Signature";

    public static string GenerateSignature(string accessToken)
    {
        // Create HMACSHA256 for signing
        using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(SecretKey)))
        {
            var hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(accessToken));
            return Convert.ToBase64String(hashBytes);
        }
    }

    public static bool VerifySignature(
        string receivedSignature,
        string accessToken)
    {
        // Re-generate the signature using the same method
        var expectedSignature = StressTestSignatureHelper.GenerateSignature(accessToken);

        // Compare the received signature with the expected signature
        return receivedSignature.Equals(expectedSignature, StringComparison.Ordinal);
    }
}