namespace Travis_backend.Cache.Models.CacheKeyPatterns;

public class WebClientGetCompanyInfoCacheKeyPattern : ICacheKeyPattern
{
    public string CompanyId { get; set; }

    public WebClientGetCompanyInfoCacheKeyPattern(string companyId)
    {
        CompanyId = companyId;
    }

    public string GenerateKeyPattern()
    {
        var keyName = CacheHelper.BuildKeyNameFromPattern(
            new object[]
            {
                CompanyId
            });
        return keyName;
    }
} 