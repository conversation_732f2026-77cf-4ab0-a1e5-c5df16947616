﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.Core.Tests.Tools;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.FlowHubs;

namespace Sleekflow.Core.Tests.CompanyTeams;

public class TeamsDefaultChannelsTest
{
    private IConfiguration _configuration;
    private ApplicationDbContext _appDbContext;
    private IDbContextService _dbContextService;
    private BaseDbContext _baseDbContext;
    private ICompanyTeamService _companyTeamService;
    private const string CompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
    private const string StaffIdentityId = "4ffaf80c-76bb-4aa6-9548-9e773c6908fa";

    [SetUp]
    public void Setup()
    {
        _configuration = AppConfigure.InitConfiguration();
        var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>()
            .EnableSensitiveDataLogging()
            .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
            .UseSqlServer(
                _configuration.GetConnectionString("DefaultConnection")); // If you want to use UAT database, enable it

        var baseOptionsBuilder = new DbContextOptionsBuilder<BaseDbContext>()
            .EnableSensitiveDataLogging()
            .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
            .UseSqlServer(
                _configuration.GetConnectionString("DefaultConnection")); // If you want to use UAT database, enable it

        _baseDbContext = new BaseDbContext(baseOptionsBuilder.Options);
        _appDbContext = new ApplicationDbContext(optionsBuilder.Options);

        var mockDbContextService = new Mock<IDbContextService>();

        mockDbContextService.Setup(x => x.GetDbContext()).Returns(_baseDbContext);
        _dbContextService = mockDbContextService.Object;

        _companyTeamService = new CompanyTeamService(
            new Mock<IMapper>().Object,
            _configuration,
            new Mock<ILogger<CompanyTeamService>>().Object,
            _dbContextService,
            new Mock<IStaffHooks>().Object,
            new Mock<IAccessControlAggregationService>().Object);
    }

    [Test]
    public async Task TestMessageBelongToDefaultChannels()
    {
        // Stable Phone Number Channel Message
        var message = await _appDbContext.ConversationMessages
            .Where(x => x.CompanyId == "b6d7e442-38ae-4b9a-b100-2951729768bc" && x.ChannelIdentityId == "18454069890")
            .OrderByDescending(x => x.Timestamp).FirstOrDefaultAsync();

        Assert.IsNotNull(message);

        var teams = await _appDbContext.CompanyStaffTeams.Where(
            x => x.CompanyId == CompanyId && x.Members != null &&
                 x.Members.Any(m => m.Staff.IdentityId == StaffIdentityId)).ToListAsync();

        var (whatsappTwilioDefaultChannelIds, teamWhatsapp360dialogDefaultChannelIds,
            teamWhatsappCloudDefaultChannelIds, teamFacebookDefaultChannelIds,
            teamInstagramDefaultChannelIds) = await _companyTeamService.GetTeamsDefaultChannelIdentityIdsAsync(teams);

        var isBelongToDefaultChannel = _companyTeamService.IsMessageBelongsToTeamDefaultChannel(
            message,
            whatsappTwilioDefaultChannelIds,
            teamWhatsapp360dialogDefaultChannelIds,
            teamWhatsappCloudDefaultChannelIds,
            teamFacebookDefaultChannelIds,
            teamInstagramDefaultChannelIds);

        Assert.IsTrue(isBelongToDefaultChannel);
    }

    [Test]
    public async Task TestMessageNotBelongToDefaultChannels()
    {
        // Sleekflow Twilio Channel Message

        var message = await _appDbContext.ConversationMessages
            .Where(x => x.CompanyId == "b6d7e442-38ae-4b9a-b100-2951729768bc" && x.ChannelIdentityId == "15307194031")
            .OrderByDescending(x => x.Timestamp).FirstOrDefaultAsync();

        Assert.IsNotNull(message);

        var teams = await _appDbContext.CompanyStaffTeams.Where(
            x => x.CompanyId == CompanyId && x.Members != null &&
                 x.Members.Any(m => m.Staff.IdentityId == StaffIdentityId)).ToListAsync();

        var (whatsappTwilioDefaultChannelIds, teamWhatsapp360dialogDefaultChannelIds,
            teamWhatsappCloudDefaultChannelIds, teamFacebookDefaultChannelIds,
            teamInstagramDefaultChannelIds) = await _companyTeamService.GetTeamsDefaultChannelIdentityIdsAsync(teams);

        var isBelongToDefaultChannel = _companyTeamService.IsMessageBelongsToTeamDefaultChannel(
            message,
            whatsappTwilioDefaultChannelIds,
            teamWhatsapp360dialogDefaultChannelIds,
            teamWhatsappCloudDefaultChannelIds,
            teamFacebookDefaultChannelIds,
            teamInstagramDefaultChannelIds);

        Assert.IsFalse(isBelongToDefaultChannel);
    }

    // Test case is disabled due to these tests need to be updated
    // Team: Team BugBQ
    // [Test]
    // public async Task TestNoteMessageNotBelongToDefaultChannels()
    // {
    //     // Note Channel Message
    //
    //     var message = await _appDbContext.ConversationMessages
    //         .Where(x => x.CompanyId == "b6d7e442-38ae-4b9a-b100-2951729768bc" && x.ConversationId == "8b0ac00a-1d5a-4fb4-b8a9-d7ec9baac5e5" && x.Channel == ChannelTypes.Note)
    //         .OrderByDescending(x => x.Timestamp).FirstOrDefaultAsync();
    //
    //     Assert.IsNotNull(message);
    //
    //     var teams = await _appDbContext.CompanyStaffTeams.Where(
    //         x => x.CompanyId == CompanyId && x.Members != null &&
    //              x.Members.Any(m => m.Staff.IdentityId == StaffIdentityId)).ToListAsync();
    //
    //     var (whatsappTwilioDefaultChannelIds, teamWhatsapp360dialogDefaultChannelIds,
    //         teamWhatsappCloudDefaultChannelIds, teamFacebookDefaultChannelIds,
    //         teamInstagramDefaultChannelIds) = await _companyTeamService.GetTeamsDefaultChannelIdentityIdsAsync(teams);
    //
    //     var isBelongToDefaultChannel = _companyTeamService.IsMessageBelongsToTeamDefaultChannel(
    //         message,
    //         whatsappTwilioDefaultChannelIds,
    //         teamWhatsapp360dialogDefaultChannelIds,
    //         teamWhatsappCloudDefaultChannelIds,
    //         teamFacebookDefaultChannelIds,
    //         teamInstagramDefaultChannelIds);
    //
    //     Assert.IsFalse(isBelongToDefaultChannel);
    // }

    [Test]
    public async Task TestWeChatMessageNotBelongToDefaultChannels()
    {
        // Note Channel Message

        var message = await _appDbContext.ConversationMessages
            .Where(x => x.CompanyId == "b6d7e442-38ae-4b9a-b100-2951729768bc" && x.ChannelIdentityId == "wxe76aa75daa505121" && x.Channel == ChannelTypes.Wechat)
            .OrderByDescending(x => x.Timestamp).FirstOrDefaultAsync();

        Assert.IsNotNull(message);

        var teams = await _appDbContext.CompanyStaffTeams.Where(
            x => x.CompanyId == CompanyId && x.Members != null &&
                 x.Members.Any(m => m.Staff.IdentityId == StaffIdentityId)).ToListAsync();

        var (whatsappTwilioDefaultChannelIds, teamWhatsapp360dialogDefaultChannelIds,
            teamWhatsappCloudDefaultChannelIds, teamFacebookDefaultChannelIds,
            teamInstagramDefaultChannelIds) = await _companyTeamService.GetTeamsDefaultChannelIdentityIdsAsync(teams);

        var isBelongToDefaultChannel = _companyTeamService.IsMessageBelongsToTeamDefaultChannel(
            message,
            whatsappTwilioDefaultChannelIds,
            teamWhatsapp360dialogDefaultChannelIds,
            teamWhatsappCloudDefaultChannelIds,
            teamFacebookDefaultChannelIds,
            teamInstagramDefaultChannelIds);

        Assert.IsFalse(isBelongToDefaultChannel);
    }
}