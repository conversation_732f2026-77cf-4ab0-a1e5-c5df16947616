{
    "version": "0.2.0",
    "configurations": [
        // === NETCOREDBG DEBUGGING CONFIGURATIONS FOR CURSOR IDE ===
        // Auth0 Backend Configurations
        {
            "name": "<PERSON> Backend Auth0",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-travis-backend-auth0",
            "program": "${workspaceFolder}/Travis_backend.Auth0/bin/Debug/net8.0/Travis_backend.Auth0.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Travis_backend.Auth0",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "ASPNETCORE_URLS": "https://localhost:5000;http://localhost:5001",
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
        },
        {
            "name": "Travis Backend Auth0 - No Browser",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-travis-backend-auth0",
            "program": "${workspaceFolder}/Travis_backend.Auth0/bin/Debug/net8.0/Travis_backend.Auth0.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Travis_backend.Auth0",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "ASPNETCORE_URLS": "https://localhost:5000;http://localhost:5001",
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "launchBrowser": {
                "enabled": false
            }
        },
        {
            "name": "Travis Backend Auth0 - Swagger",
            "type": "node",
            "request": "launch",
            "runtimeExecutable": "open",
            "runtimeArgs": ["https://localhost:5000/thisisonlyforsleekflowdev/swagger/index.html"],
            "osx": {
                "runtimeExecutable": "open"
            }
        },
        {
            "name": "Travis Backend Auth0 - HangFire",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-travis-backend-auth0",
            "program": "${workspaceFolder}/Travis_backend.Auth0/bin/Debug/net8.0/Travis_backend.Auth0.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Travis_backend.Auth0",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "ASPNETCORE_URLS": "https://localhost:5002;http://localhost:5003",
                "ENABLE_HANGFIRE": "true"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "serverReadyAction": {
                "action": "debugWithChrome",
                "pattern": "^.*?Now listening on:\\s+(https?://\\S+)",
                "uriFormat": "%s/HangFire",
                "webRoot": "${workspaceFolder}"
            },
        },

        // Powerflow APIs Configurations
        {
            "name": "Powerflow APIs",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-powerflow-apis",
            "program": "${workspaceFolder}/Sleekflow.Powerflow.Apis/bin/Debug/net8.0/Sleekflow.Powerflow.Apis.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Sleekflow.Powerflow.Apis",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "ASPNETCORE_URLS": "https://localhost:7058;http://localhost:7059",
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "Powerflow APIs - No Browser",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-powerflow-apis",
            "program": "${workspaceFolder}/Sleekflow.Powerflow.Apis/bin/Debug/net8.0/Sleekflow.Powerflow.Apis.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Sleekflow.Powerflow.Apis",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "ASPNETCORE_URLS": "https://localhost:7058;http://localhost:7059",
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "launchBrowser": {
                "enabled": false
            }
        },
        {
            "name": "Powerflow APIs - HangFire",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-powerflow-apis",
            "program": "${workspaceFolder}/Sleekflow.Powerflow.Apis/bin/Debug/net8.0/Sleekflow.Powerflow.Apis.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Sleekflow.Powerflow.Apis",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "ASPNETCORE_URLS": "https://localhost:7060;http://localhost:7061",
                "ENABLE_HANGFIRE": "true"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },

        // Console Applications
        {
            "name": "Data Migrator",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-data-migrator",
            "program": "${workspaceFolder}/Sleekflow.Core.DataMigrator/bin/Debug/net8.0/Sleekflow.Core.DataMigrator.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Sleekflow.Core.DataMigrator",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "console": "integratedTerminal"
        },
        {
            "name": "Core Exporter",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-core-exporter",
            "program": "${workspaceFolder}/Sleekflow.Core.Exporter/bin/Debug/net8.0/Sleekflow.Core.Exporter.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Sleekflow.Core.Exporter",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "console": "integratedTerminal"
        },
        {
            "name": "Core Benchmarks",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-core-benchmarks",
            "program": "${workspaceFolder}/Sleekflow.Core.Benchmarks/bin/Debug/net8.0/Sleekflow.Core.Benchmarks.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Sleekflow.Core.Benchmarks",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "console": "integratedTerminal"
        },
        {
            "name": "Core Stress Tests",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-core-stress-tests",
            "program": "${workspaceFolder}/Sleekflow.Core.StressTests/bin/Debug/net8.0/Sleekflow.Core.StressTests.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Sleekflow.Core.StressTests",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "console": "integratedTerminal"
        },

        // Infrastructure Projects
        {
            "name": "Core Infrastructure",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-core-infra",
            "program": "${workspaceFolder}/Sleekflow.Core.Infra/bin/Debug/net8.0/Sleekflow.Core.Infra.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Sleekflow.Core.Infra",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "console": "integratedTerminal"
        },
        {
            "name": "Core Infrastructure Performance",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-core-infra-perf",
            "program": "${workspaceFolder}/Sleekflow.Core.Infra.Perf/bin/Debug/net8.0/Sleekflow.Core.Infra.Perf.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Sleekflow.Core.Infra.Perf",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "console": "integratedTerminal"
        },

        // Test Configurations
        {
            "name": "Run All Tests",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "test-all",
            "program": "${workspaceFolder}/Sleekflow.Core.Tests/bin/Debug/net8.0/Sleekflow.Core.Tests.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Sleekflow.Core.Tests",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "console": "integratedTerminal"
        },
        {
            "name": "Run SleekPay Tests",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "test-sleekpay",
            "program": "${workspaceFolder}/Sleekflow.SleekPay.Tests/bin/Debug/net8.0/Sleekflow.SleekPay.Tests.dll",
            "args": [],
            "cwd": "${workspaceFolder}/Sleekflow.SleekPay.Tests",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "bash",
                "pipeArgs": ["-c"],
                "debuggerPath": "${workspaceFolder}/.vscode/netcoredbg/netcoredbg",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "console": "integratedTerminal"
        },
    ]
}