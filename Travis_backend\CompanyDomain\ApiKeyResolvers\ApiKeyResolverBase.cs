﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Exceptions;

namespace Travis_backend.CompanyDomain.ApiKeyResolvers;

public abstract class ApiKeyResolverBase : IApiKeyResolver
{
    protected readonly ApplicationDbContext _appDbContext;
    protected readonly ILogger<ApiKeyResolverBase> _logger;

    protected abstract string ApiKeyType { get; }

    protected ApiKeyResolverBase(
        ApplicationDbContext appDbContext,
        ILogger<ApiKeyResolverBase> logger)
    {
        _appDbContext = appDbContext;
        _logger = logger;
    }

    public virtual async Task<CompanyAPIKey> AuthenticateAsync(string apiKey)
    {
        try
        {
            return await _appDbContext.CompanyAPIKeys
                .SingleAsync(x => x.APIKey == apiKey && x.KeyType == ApiKeyType);
        }
        catch(Exception)
        {
            // todo Should be removed after the acknowledge period.
            var (isSuccess, companyApiKey) = await TryGetOldCompanyApiKeyAsync(apiKey);
            if (isSuccess)
            {
                return companyApiKey;
            }

            throw new ApiKeyAuthenticateFailedException(apiKey);
        }
    }

    public virtual async Task<CompanyAPIKey> GenerateAsync(string companyId, int? callLimit, int calls = 0)
    {
        var existedCompanyApiKeys = await GetCompanyApiKeysAsync(companyId);
        if (existedCompanyApiKeys.Any())
        {
            throw new ApiKeyException("Api key already issued.");
        }

        var keySeed = Guid.NewGuid().ToString();
        var newApiKey = string.Empty;

        using (var sha256 = SHA256.Create())
        {
            byte[] saltedPasswordAsBytes = Encoding.UTF8.GetBytes(keySeed);
            newApiKey = RemoveSpecialCharacters(
                Convert.ToBase64String(sha256.ComputeHash(saltedPasswordAsBytes)));
        }

        var companyApiKey = new CompanyAPIKey
        {
            APIKey = newApiKey,
            CompanyId = companyId,
            KeyType = ApiKeyType,
            CallLimit = callLimit,
            Calls = calls
        };

        _appDbContext.CompanyAPIKeys.Add(companyApiKey);
        await _appDbContext.SaveChangesAsync();

        return companyApiKey;
    }

    public virtual async Task<CompanyAPIKey> RefreshAsync(string companyId, int? callLimit)
    {
        var existedCompanyApiKeys = await GetCompanyApiKeysAsync(companyId);
        var currentCalls = existedCompanyApiKeys.Sum(x => x.Calls);

        _logger.LogWarning(
            "Start refresh company Api Key. {CompanyId} {KeyType} {ExistedCompanyApiKeys}",
            companyId,
            ApiKeyType,
            JsonConvert.SerializeObject(existedCompanyApiKeys));

        foreach (var existedCompanyApiKey in existedCompanyApiKeys)
        {
            await DeleteAsync(existedCompanyApiKey);
        }

        return await GenerateAsync(companyId, callLimit, currentCalls);
    }

    public virtual async Task<APIUsage> GetApiUsageAsync(CompanyAPIKey companyApiKey)
    {
        var usageLimitOffsetProfile = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .Where(c => c.Id == companyApiKey.CompanyId)
            .Select(c => c.UsageLimitOffsetProfile)
            .FirstOrDefaultAsync();

        var apiCallLimitOffset = usageLimitOffsetProfile?.ApiCallLimitOffSet ?? 0;

        return await _appDbContext.CompanyAPIKeys
            .Where(x => x.APIKey == companyApiKey.APIKey && x.CompanyId == companyApiKey.CompanyId)
            .Select(x => new APIUsage
            {
                CompanyId = x.CompanyId,
                totalAPICalls = x.Calls,
                maximumAPICalls = (x.CallLimit ?? 0) + apiCallLimitOffset
            })
            .FirstOrDefaultAsync();
    }

    public virtual async Task ValidateUsageAsync(CompanyAPIKey companyApiKey)
    {
        var apiUsage = await GetApiUsageAsync(companyApiKey);
        if (apiUsage.totalAPICalls >= apiUsage.maximumAPICalls)
        {
            throw new ApiKeyCallLimitExceededException(
                companyApiKey.APIKey,
                companyApiKey.KeyType,
                apiUsage.maximumAPICalls);
        }
    }

    public virtual async Task AddUsageAsync(
        CompanyAPIKey companyApiKey)
    {
        await _appDbContext.CompanyAPIKeys
            .Where(x => x.APIKey == companyApiKey.APIKey && x.CompanyId == companyApiKey.CompanyId)
            .ExecuteUpdateAsync(
                calls =>
                    calls.SetProperty(
                        x => x.Calls,
                        x => x.Calls + 1));
    }

    public virtual async Task ResetUsageAndUsageLimitAsync(
        CompanyAPIKey companyApiKey,
        int? callLimit,
        int calls = 0)
    {
        await _appDbContext.CompanyAPIKeys
            .Where(x => x.APIKey == companyApiKey.APIKey && x.CompanyId == companyApiKey.CompanyId)
            .ExecuteUpdateAsync(
                c =>
                    c.SetProperty(p => p.Calls, calls)
                    .SetProperty(p => p.CallLimit, callLimit));
    }

    public virtual async Task<CompanyAPIKey> GetCompanyApiKeyAsync(string companyId)
    {
        return await _appDbContext.CompanyAPIKeys
            .Where(x => x.CompanyId == companyId && x.KeyType == ApiKeyType)
            .OrderByDescending(x => x.CreatedAt)
            .FirstOrDefaultAsync();
    }

    #region Common methods

    protected async Task<List<CompanyAPIKey>> GetCompanyApiKeysAsync(string companyId)
    {
        return await _appDbContext.CompanyAPIKeys
            .Where(x => x.CompanyId == companyId && x.KeyType == ApiKeyType)
            .ToListAsync();
    }

    protected async Task DeleteAsync(CompanyAPIKey companyApiKey)
    {
        _logger.LogWarning(
            "Start delete company API key. {CompanyId} {APIKey} {KeyType}",
            companyApiKey.CompanyId,
            companyApiKey.APIKey,
            companyApiKey.KeyType);

        var count = await _appDbContext.CompanyAPIKeys
            .Where(x => x.APIKey == companyApiKey.APIKey && x.CompanyId == companyApiKey.CompanyId)
            .ExecuteDeleteAsync();

        if (count != 1)
        {
            _logger.LogError(
                "Invalid API Key count deleted. {CompanyId} {APIKey} {KeyType} {count}",
                companyApiKey.CompanyId,
                companyApiKey.APIKey,
                companyApiKey.KeyType,
                count);
        }
    }

    /// <summary>
    /// Previous issued APIKeys are assigned permissions to all features during the acknowledge period.
    /// https://www.notion.so/sleekflow/Integration-Hub-ee7649d446e344a9a8e6a40836de2d20#8dd5449c41b44d739014242af6a50049
    /// </summary>
    /// <param name="apiKey">API Key.</param>
    /// <returns>If old CompanyAPIKey exists.</returns>
    private async Task<(bool IsSuccess, CompanyAPIKey CompanyApiKey)> TryGetOldCompanyApiKeyAsync(string apiKey)
    {
        var companyApiKey = await _appDbContext.CompanyAPIKeys
            .FirstOrDefaultAsync(x => x.APIKey == apiKey);

        var isSuccess = companyApiKey != null &&
                        companyApiKey.Permissions != null &&
                        companyApiKey.Permissions.Contains(ApiKeyType);

        return (isSuccess, companyApiKey);
    }

    private static string RemoveSpecialCharacters(string str)
    {
        return Regex.Replace(str, "[^a-zA-Z0-9_.]+", string.Empty, RegexOptions.Compiled);
    }

    #endregion
}