﻿using System.ComponentModel.DataAnnotations;
using Travis_backend.CampaignAnalyticsDomain.Models;

namespace Travis_backend.CampaignAnalyticsDomain.ViewModels;

public class GetCampaignCommonMetricsByBroadcastRequest
{
    [Required]
    public string BroadcastCampaignId { get; set; }

    [Required]
    public ReplyWindow ReplyWindow { get; set; }
}

public class GetCampaignCommonMetricsByAnalyticTagRequest
{
    [Required]
    [MinLength(1)]
    public string AnalyticTag { get; set; }

    [Required]
    public ReplyWindow ReplyWindow { get; set; }
}