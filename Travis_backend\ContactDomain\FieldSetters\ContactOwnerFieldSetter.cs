﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.Database;
using Travis_backend.SignalR;

namespace Travis_backend.ContactDomain.FieldSetters;

public sealed class ContactOwnerFieldSetter : FieldSetterBase
{
    private readonly ISignalRService _signalRService;

    protected override string FieldName => "ContactOwner";

    private Conversation _conversation;

    public ContactOwnerFieldSetter(
        ApplicationDbContext appDbContext,
        IServiceProvider serviceProvider,
        ISignalRService signalRService)
        : base(
            appDbContext,
            serviceProvider)
    {
        _signalRService = signalRService;
    }

    protected override async Task<SetFieldRequest> FormatRequestAsync(SetFieldRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.FieldValue))
        {
            return request;
        }

        var staffIdentityId = await _appDbContext.UserRoleStaffs
            .Where(
                x =>
                    x.CompanyId == Request.CompanyId
                    && (x.IdentityId == Request.FieldValue
                        || x.Identity.DisplayName == Request.FieldValue
                        || x.Identity.UserName == Request.FieldValue))
            .Select(x => x.IdentityId)
            .FirstOrDefaultAsync();

        if (!string.IsNullOrEmpty(staffIdentityId))
        {
            request = request with
            {
                FieldValue = staffIdentityId
            };
        }

        return request;
    }

    protected override async Task<bool> HandleChangesAsync()
    {
        TargetUserProfile.ContactOwnerId = Request.FieldValue;

        _conversation = await _appDbContext.Conversations
            .Include(x => x.Assignee)
            .FirstOrDefaultAsync(c => c.UserProfileId == Request.UserProfileId);

        if (_conversation is null)
        {
            return false;
        }

        if (!_conversation.AssigneeId.HasValue
            || _conversation.Assignee.IdentityId != Request.FieldValue)
        {
            var newContactOwner = await _appDbContext.UserRoleStaffs
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == Request.CompanyId
                        && x.IdentityId == Request.FieldValue);

            _conversation.Assignee = newContactOwner;
            _conversation.AssigneeId = newContactOwner?.Id;

            // Avoid staff becoming contact owner and collaborator simultaneously
            if (newContactOwner is not null)
            {
                // Find if the new contact owner is already an additional assignee for the conversation.
                var existingCollaborator = await _appDbContext.ConversationAdditionalAssignees
                    .FirstOrDefaultAsync(col => col.ConversationId == _conversation.Id && col.AssigneeId == newContactOwner.Id);

                if (existingCollaborator is not null)
                {
                    _appDbContext.ConversationAdditionalAssignees.Remove(existingCollaborator);
                }
            }

            var newContactOwnerTeams = newContactOwner is not null ?
                await _appDbContext.CompanyStaffTeams
                    .Where(x => x.Members.Any(y => y.StaffId == newContactOwner.Id))
                    .ToListAsync() :
                new();

            if (newContactOwnerTeams.Count > 0
                && !newContactOwnerTeams
                    .Select(x => x.Id)
                    .Contains(_conversation.AssignedTeamId.GetValueOrDefault(0)))
            {
                await SetAsync(
                    "AssignedTeam",
                    newContactOwnerTeams.First().Id.ToString());
            }
        }

        return true;
    }

    protected override async Task ExecutePostUpdateActionsAsync()
    {
        if (_conversation is null)
        {
            return;
        }

        if (_conversation.Assignee != null)
        {
            await _signalRService.SignalROnConversationAssigneeChanged(_conversation);
        }
        else
        {
            await _signalRService.SignalROnConversationAssigneeDeleted(_conversation);
        }
    }
}