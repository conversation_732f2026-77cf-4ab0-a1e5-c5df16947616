namespace Travis_backend.Cache.Models.CacheKeyPatterns;

public class FeaturesCacheKeyPattern : ICacheKeyPattern
{
    public string? FeatureName { get; set; }

    public FeaturesCacheKeyPattern(string featureName)
    {
        FeatureName = featureName;
    }

    public string GenerateKeyPattern()
    {
        return CacheHelper.BuildKeyNameFromPattern(
            new object[]
            {
                FeatureName
            });
    }
}