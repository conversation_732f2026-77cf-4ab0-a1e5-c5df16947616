﻿namespace Travis_backend.BackgroundTaskServices.Models;

public abstract class BackgroundTaskResultPayload : object
{
    public abstract string ResultPayloadType { get; set; }
}

public class EmptyBackgroundTaskResultPayload : BackgroundTaskResultPayload
{
    public override string ResultPayloadType { get; set; } = nameof(EmptyBackgroundTaskResultPayload);
}

public abstract class FileBackgroundTaskResultPayload : BackgroundTaskResultPayload
{
    public string FileName { get; set; }

    public string FilePath { get; set; }

    public string MIMEType { get; set; }

    public string Url { get; set; }

    public long FileSize { get; set; }
}


public class ImportedUserProfileBackgroundTaskResultPayload
{
    public long Id { get; set; }

    public long ImportContactHistoryId { get; set; }

    public string UserProfileId { get; set; }
}

public class ExportContactsListToCsvBackgroundTaskResultPayload : FileBackgroundTaskResultPayload
{
    public override string ResultPayloadType { get; set; } =
        nameof(ExportContactsListToCsvBackgroundTaskResultPayload);
}

public class ExportBroadcastStatusListToCsvBackgroundTaskResultPayload : FileBackgroundTaskResultPayload
{
    public override string ResultPayloadType { get; set; } =
        nameof(ExportContactsListToCsvBackgroundTaskResultPayload);
}

public class ExportAnalyticListToCsvBackgroundTaskResultPayload : FileBackgroundTaskResultPayload
{
    public override string ResultPayloadType { get; set; } =
        nameof(ExportAnalyticListToCsvBackgroundTaskResultPayload);
}

public class ExportFlowHubWorkflowExecutionUsagesTaskResultPayload : FileBackgroundTaskResultPayload
{
    public override string ResultPayloadType { get; set; } =
        nameof(ExportFlowHubWorkflowExecutionUsagesTaskResultPayload);
}


public class ExportTicketsCsvTaskResultPayload : FileBackgroundTaskResultPayload
{
    public override string ResultPayloadType { get; set; } =
        nameof(ExportTicketsCsvTaskResultPayload);
}

public class ExportConversationAnalyticListToCsvBackgroundTaskResultPayload : FileBackgroundTaskResultPayload
{
    public override string ResultPayloadType { get; set; } =
        nameof(ExportConversationAnalyticListToCsvBackgroundTaskResultPayload);
}

public class ExportCampaignAnalyticMessageOverviewsToCsvBackgroundTaskResultPayload : FileBackgroundTaskResultPayload
{
    public override string ResultPayloadType { get; set; } =
        nameof(ExportCampaignAnalyticMessageOverviewsToCsvBackgroundTaskResultPayload);
}