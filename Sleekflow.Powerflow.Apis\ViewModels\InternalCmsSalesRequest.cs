﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Travis_backend.InternalDomain.Models;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class SetCompanyContactOwnerRequest
{
    [Required]
    public string CompanyId { get; set; }

    public string SalesContactOwnerId { get; set; }

    public string CustomerSuccessContactOwnerId { get; set; }

    public string CompanyOwnerId { get; set; }

    public string ActivationOwnerId { get; set; }

    public string CsOwnerId { get; set; }
}

public class GetSalesPaymentRecordFileUrlRequest
{
    [Required]
    public long CmsSalesPaymentRecordId { get; set; }

    [Required]
    public long FileId { get; set; }
}

public class CreateSalesPaymentRecordRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public long BillRecordId { get; set; }

    [Required]
    public decimal SubscriptionFee { get; set; }

    [Required]
    public decimal OneTimeSetupFee { get; set; }

    [Required]
    public decimal WhatsappCreditAmount { get; set; }

    [Required]
    public string Currency { get; set; }

    public decimal? Discount { get; set; }

    public int? PeriodInMonths { get; set; }

    [Required]
    [JsonConverter(typeof(StringEnumConverter))]
    public PaymentMethod PaymentMethod { get; set; }

    public int? PaymentTerm { get; set; }

    public DateTime? PaidAt { get; set; }

    public string InvoiceId { get; set; }

    public string Remark { get; set; }

    public IList<IFormFile> Files { get; set; }
}

public class UpdateSalesPaymentRecordRequest
{
    [Required]
    public long CmsSalesPaymentRecordId { get; set; }

    [Required]
    public decimal SubscriptionFee { get; set; }

    [Required]
    public decimal OneTimeSetupFee { get; set; }

    [Required]
    public decimal WhatsappCreditAmount { get; set; }

    [Required]
    public string Currency { get; set; }

    [Required]
    [JsonConverter(typeof(StringEnumConverter))]
    public PaymentMethod PaymentMethod { get; set; }

    public int? PaymentTerm { get; set; }

    public DateTime? PaidAt { get; set; }

    public decimal? Discount { get; set; }

    public int? PeriodInMonths { get; set; }

    public string InvoiceId { get; set; }

    public string Remark { get; set; }

    public IList<long> RemoveFileIds { get; set; }

    public IList<IFormFile> AdditionalFiles { get; set; }
}

public class DeleteSalesPaymentRecordRequest
{
    [Required]
    public long CmsSalesPaymentRecordId { get; set; }

    [Required]
    public long BillRecordId { get; set; }
}