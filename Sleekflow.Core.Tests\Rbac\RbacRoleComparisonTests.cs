using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using Moq;
using NUnit.Framework;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Enums;
using Travis_backend.Extensions;

namespace Sleekflow.Core.Tests.Rbac
{
    [TestFixture]
    public class RbacRoleComparisonTests
    {
        private Mock<HttpContext> _mockHttpContext = null!;
        private Dictionary<object, object> _httpContextItems = null!;
        private readonly Staff _superAdmin = new Staff { RoleType = StaffUserRole.SuperAdmin };
        private readonly Staff _admin = new Staff { RoleType = StaffUserRole.Admin };
        private readonly Staff _teamAdmin = new Staff { RoleType = StaffUserRole.TeamAdmin };
        private readonly Staff _staff = new Staff { RoleType = StaffUserRole.Staff };

        [SetUp]
        public void Setup()
        {
            _mockHttpContext = new Mock<HttpContext>();
            _httpContextItems = new Dictionary<object, object>();
            _mockHttpContext.Setup(x => x.Items).Returns(_httpContextItems);
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
        }

        [Test]
        public void StaffHasRole_WhenRbacIsEnabled_ShouldReturnTrue()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = true;
            var companyUser = new Staff { RoleType = StaffUserRole.Staff };

            // Act
            bool conditionResult = companyUser.RbacRole == StaffUserRole.Staff;
            bool hasRoleMethodResult = companyUser.HasRole(StaffUserRole.Staff);

            // Assert
            Assert.That(conditionResult, Is.True, "With RBAC enabled, any role should match any role");
            Assert.That(hasRoleMethodResult, Is.True, "HasRole() should return true with RBAC enabled");
        }

        [Test]
        public void StaffHasRole_WhenRbacIsDisabled_ShouldCheckRolesExactly()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;
            var companyUser = new Staff { RoleType = StaffUserRole.Staff };

            // Act
            bool conditionResult = companyUser.RbacRole == StaffUserRole.Staff;
            bool hasRoleMethodResultForSameRole = companyUser.HasRole(StaffUserRole.Staff);
            bool hasRoleMethodResultForDifferentRole = companyUser.HasRole(StaffUserRole.Admin);

            // Assert
            Assert.That(conditionResult, Is.True, "With RBAC disabled, roles should match exactly");
            Assert.That(hasRoleMethodResultForSameRole, Is.True, "HasRole() should return true for the same role");
            Assert.That(hasRoleMethodResultForDifferentRole, Is.False, "HasRole() should return false for different roles");
        }

        [Test]
        public void RoleEquality_WhenUsingOperator_ShouldCompareCorrectly()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;

            // Act & Assert
            Assert.That(StaffUserRole.Staff == StaffUserRole.Staff, Is.True, "Same roles should be equal");
            Assert.That(StaffUserRole.Staff == StaffUserRole.Admin, Is.False, "Different roles should not be equal");
        }

        [Test]
        public void RoleEquality_WhenUsingEquals_ShouldCompareCorrectly()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;

            // Act & Assert
            Assert.That(StaffUserRole.Staff.Equals(StaffUserRole.Staff), Is.True, "Same roles should be equal");
            Assert.That(StaffUserRole.Staff.Equals(StaffUserRole.Admin), Is.False, "Different roles should not be equal");
        }

        [Test]
        public void RbacAwareRole_EqualityOperator_SuperAdminShouldHaveAdminPermissions_WhenRbacIsDisabled()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;

            // Act & Assert
            Assert.That(_superAdmin.RbacRole == StaffUserRole.Admin, Is.True, "SuperAdmin.RbacRole should equal Admin");
            Assert.That(_admin.RbacRole == StaffUserRole.SuperAdmin, Is.False, "Admin.RbacRole should not equal SuperAdmin");
        }

        [Test]
        public void StaffUserRole_EqualToRbacAwareRole_WhenRbacIsEnabled_ShouldReturnTrue()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = true;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            var rbacAwareRole = StaffUserRole.Admin.AsRbacAware();

            // Act & Assert
            Assert.That(StaffUserRole.Staff == rbacAwareRole, Is.True, "With RBAC enabled, any role should equal any RbacAwareRole");
            Assert.That(StaffUserRole.Admin == rbacAwareRole, Is.True, "With RBAC enabled, any role should equal any RbacAwareRole");
        }

        [Test]
        public void StaffUserRole_EqualToRbacAwareRole_WhenRbacIsDisabled_ShouldCompareNormally()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            var rbacAwareRole = StaffUserRole.Admin.AsRbacAware();

            // Act & Assert
            Assert.That(StaffUserRole.Staff == rbacAwareRole, Is.False, "With RBAC disabled, roles should compare normally");
            Assert.That(StaffUserRole.Admin == rbacAwareRole, Is.True, "With RBAC disabled, roles should compare normally");
        }

        [Test]
        public void SuperAdmin_Should_Have_Admin_Permissions_When_RbacIsDisabled()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            
            // Act & Assert - Testing HasRole method
            Assert.That(_superAdmin.HasRole(StaffUserRole.Admin), Is.True, "SuperAdmin should have Admin permissions");
            Assert.That(_admin.HasRole(StaffUserRole.SuperAdmin), Is.False, "Admin should not have SuperAdmin permissions");
        }

        [Test]
        public void All_Roles_Should_Have_All_Permissions_When_RbacIsEnabled()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = true;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            
            // Act & Assert - Testing HasRole method
            Assert.That(_superAdmin.RbacRole == StaffUserRole.Admin, Is.True, "With RBAC enabled, SuperAdmin.RbacRole should equal Admin");
            Assert.That(_admin.RbacRole == StaffUserRole.SuperAdmin, Is.True, "With RBAC enabled, Admin.RbacRole should equal SuperAdmin");
        }

        [Test]
        public void Staff_Role_Should_Not_Have_Other_Permissions_When_RbacIsDisabled()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            
            // Act & Assert - Testing HasRole method for Staff role
            Assert.That(_staff.HasRole(StaffUserRole.Staff), Is.True, "Staff should have Staff permissions");
            Assert.That(_staff.HasRole(StaffUserRole.TeamAdmin), Is.False, "Staff should not have TeamAdmin permissions");
            Assert.That(_staff.HasRole(StaffUserRole.Admin), Is.False, "Staff should not have Admin permissions");
            Assert.That(_staff.HasRole(StaffUserRole.SuperAdmin), Is.False, "Staff should not have SuperAdmin permissions");
            
            // Testing RbacRole equality for Staff role
            Assert.That(_staff.RbacRole == StaffUserRole.Staff, Is.True, "Staff.RbacRole should equal Staff");
            Assert.That(_staff.RbacRole == StaffUserRole.TeamAdmin, Is.False, "Staff.RbacRole should not equal TeamAdmin");
            Assert.That(_staff.RbacRole == StaffUserRole.Admin, Is.False, "Staff.RbacRole should not equal Admin");
            Assert.That(_staff.RbacRole == StaffUserRole.SuperAdmin, Is.False, "Staff.RbacRole should not equal SuperAdmin");
        }

        [Test]
        public void TeamAdmin_Role_Should_Not_Have_Other_Permissions_When_RbacIsDisabled()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = false;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            
            // Act & Assert - Testing HasRole method for TeamAdmin role
            Assert.That(_teamAdmin.HasRole(StaffUserRole.TeamAdmin), Is.True, "TeamAdmin should have TeamAdmin permissions");
            Assert.That(_teamAdmin.HasRole(StaffUserRole.Staff), Is.False, "TeamAdmin should not have Staff permissions");
            Assert.That(_teamAdmin.HasRole(StaffUserRole.Admin), Is.False, "TeamAdmin should not have Admin permissions");
            Assert.That(_teamAdmin.HasRole(StaffUserRole.SuperAdmin), Is.False, "TeamAdmin should not have SuperAdmin permissions");
            
            // Testing RbacRole equality for TeamAdmin role
            Assert.That(_teamAdmin.RbacRole == StaffUserRole.TeamAdmin, Is.True, "TeamAdmin.RbacRole should equal TeamAdmin");
            Assert.That(_teamAdmin.RbacRole == StaffUserRole.Staff, Is.False, "TeamAdmin.RbacRole should not equal Staff");
            Assert.That(_teamAdmin.RbacRole == StaffUserRole.Admin, Is.False, "TeamAdmin.RbacRole should not equal Admin");
            Assert.That(_teamAdmin.RbacRole == StaffUserRole.SuperAdmin, Is.False, "TeamAdmin.RbacRole should not equal SuperAdmin");
        }

        [Test]
        public void Staff_And_TeamAdmin_Should_Have_All_Permissions_When_RbacIsEnabled()
        {
            // Arrange
            _httpContextItems["IsRbacEnabled"] = true;
            StaffUserRoleExtensions.SetCurrentHttpContext(_mockHttpContext.Object);
            
            // Act & Assert - Testing HasRole method for Staff and TeamAdmin roles with RBAC enabled
            Assert.That(_staff.HasRole(StaffUserRole.Staff), Is.True, "Staff should have Staff permissions with RBAC enabled");
            Assert.That(_staff.HasRole(StaffUserRole.TeamAdmin), Is.True, "Staff should have TeamAdmin permissions with RBAC enabled");
            Assert.That(_staff.HasRole(StaffUserRole.Admin), Is.True, "Staff should have Admin permissions with RBAC enabled");
            Assert.That(_staff.HasRole(StaffUserRole.SuperAdmin), Is.True, "Staff should have SuperAdmin permissions with RBAC enabled");
            
            Assert.That(_teamAdmin.HasRole(StaffUserRole.Staff), Is.True, "TeamAdmin should have Staff permissions with RBAC enabled");
            Assert.That(_teamAdmin.HasRole(StaffUserRole.TeamAdmin), Is.True, "TeamAdmin should have TeamAdmin permissions with RBAC enabled");
            Assert.That(_teamAdmin.HasRole(StaffUserRole.Admin), Is.True, "TeamAdmin should have Admin permissions with RBAC enabled");
            Assert.That(_teamAdmin.HasRole(StaffUserRole.SuperAdmin), Is.True, "TeamAdmin should have SuperAdmin permissions with RBAC enabled");
            
            // Testing RbacRole equality for Staff and TeamAdmin roles with RBAC enabled
            Assert.That(_staff.RbacRole == StaffUserRole.Staff, Is.True, "Staff.RbacRole should equal Staff with RBAC enabled");
            Assert.That(_staff.RbacRole == StaffUserRole.TeamAdmin, Is.True, "Staff.RbacRole should equal TeamAdmin with RBAC enabled");
            Assert.That(_staff.RbacRole == StaffUserRole.Admin, Is.True, "Staff.RbacRole should equal Admin with RBAC enabled");
            Assert.That(_staff.RbacRole == StaffUserRole.SuperAdmin, Is.True, "Staff.RbacRole should equal SuperAdmin with RBAC enabled");
            
            Assert.That(_teamAdmin.RbacRole == StaffUserRole.Staff, Is.True, "TeamAdmin.RbacRole should equal Staff with RBAC enabled");
            Assert.That(_teamAdmin.RbacRole == StaffUserRole.TeamAdmin, Is.True, "TeamAdmin.RbacRole should equal TeamAdmin with RBAC enabled");
            Assert.That(_teamAdmin.RbacRole == StaffUserRole.Admin, Is.True, "TeamAdmin.RbacRole should equal Admin with RBAC enabled");
            Assert.That(_teamAdmin.RbacRole == StaffUserRole.SuperAdmin, Is.True, "TeamAdmin.RbacRole should equal SuperAdmin with RBAC enabled");
        }
    }
}
