using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Sleekflow.SleekPay.SQLDatabase;
using Sleekflow.SleekPay.SQLDatabase.Entity;
using Stripe;

namespace Sleekflow.SleekPay.Service;

public interface IStripeOnBoardingService
{
    Task<AccountLink> GenerateStripeOnBoardingLink(string companyId, string platformCountry);
    Task<LoginLink> GenerateDashboardLink(string companyId, string platformCountry);
    Task<Balance> GetAccountBalance(string companyId, string platformCountry);
}

public class StripeOnBoardingService : IStripeOnBoardingService
{
    private readonly ApplicationDbContext _appDbContext;

    public StripeOnBoardingService(ApplicationDbContext appDbContext)
    {
        _appDbContext = appDbContext;
    }

    public async Task<AccountLink> GenerateStripeOnBoardingLink(string companyId, string platformCountry)
    {
        return null;

        SetStripeApiKeyByPlatformCountry(platformCountry);

        var stripePaymentConfig = await _appDbContext.ConfigStripePaymentConfigs.FirstOrDefaultAsync(x => x.CompanyId == companyId && x.Country == platformCountry);

        if (stripePaymentConfig == null)
        {
            stripePaymentConfig = new StripePaymentConfig() {CompanyId = companyId};

            var options = new AccountCreateOptions
            {
                Type = platformCountry == "MY" ? "standard" : "express",
                Settings = new AccountSettingsOptions
                {
                    Branding = new AccountSettingsBrandingOptions
                    {
                        PrimaryColor = "#FFFFFF",
                        SecondaryColor = "#0D122C"
                    }
                },
                DefaultCurrency = GetDefaultCurrencyByPlatformCountry(platformCountry),
                Country = platformCountry,
            };
            var service = new AccountService();
            var accountService = service.Create(options);

            stripePaymentConfig.AccountId = accountService.Id;
            stripePaymentConfig.AccountInfo = accountService;
            stripePaymentConfig.Country = accountService.Country;
            stripePaymentConfig.DefaultCurrency = accountService.DefaultCurrency;
            stripePaymentConfig.PaymentLinkExpirationOption = new PaymentLinkExpirationOption
            {
                PaymentLinkExpirationType = PaymentLinkExpirationType.ExpireSometimeAfterCreation,
                ExpireNumberOfDaysAfter = 1
            };
            stripePaymentConfig.SupportedCurrencies = new List<string> { accountService.DefaultCurrency };

            await _appDbContext.ConfigStripePaymentConfigs.AddAsync(stripePaymentConfig);
            await _appDbContext.SaveChangesAsync();
        }

        if (stripePaymentConfig.Status == StripePaymentRegistrationStatus.Registered)
        {
            throw new Exception($"Registered");
        }

        var accountLinkCreateOptions = new AccountLinkCreateOptions
        {
            Account = stripePaymentConfig.AccountId,
            RefreshUrl = $"{Environment.GetEnvironmentVariable("AzureFunctionDomain")}/refresh?accountId={stripePaymentConfig.AccountId}",
            ReturnUrl = $"{Environment.GetEnvironmentVariable("RedirectDomain")}/settings/paymentlink",
            Type = "account_onboarding",
        };
        var accountLinkService = new AccountLinkService();
        var accountLink = accountLinkService.Create(accountLinkCreateOptions);

        return accountLink;
    }

    public async Task<LoginLink> GenerateDashboardLink(string companyId, string platformCountry)
    {
        SetStripeApiKeyByPlatformCountry(platformCountry);

        var stripePaymentConfig = await _appDbContext.ConfigStripePaymentConfigs.FirstOrDefaultAsync(x => x.CompanyId == companyId && x.Country == platformCountry);

        if (stripePaymentConfig == null)
        {
            throw new Exception($"Not found");
        }

        var service = new LoginLinkService();
        var link = service.Create(stripePaymentConfig.AccountId);

        return link;
    }

    public async Task<Balance> GetAccountBalance(string companyId, string platformCountry)
    {
        SetStripeApiKeyByPlatformCountry(platformCountry);

        var stripePaymentConfig = await _appDbContext.ConfigStripePaymentConfigs.FirstOrDefaultAsync(x => x.CompanyId == companyId && x.Country == platformCountry);

        if (stripePaymentConfig == null)
        {
            throw new Exception($"Not found");
        }

        var requestOptions = new RequestOptions();
        requestOptions.StripeAccount = stripePaymentConfig.AccountId;
        var service = new BalanceService();
        Balance balance = service.Get(requestOptions);

        return balance;
    }

    private static void SetStripeApiKeyByPlatformCountry(string platformCountry)
    {
        switch (platformCountry.ToLower())
        {
            case "hk":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_HK");
                break;
            case "sg":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_SG");
                break;
            case "my":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_MY");
                break;
            case "gb":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_GB");
                break;
        }
    }

    private static string GetDefaultCurrencyByPlatformCountry(string platformCountry)
    {
        switch (platformCountry)
        {
            case "HK":
                return "HKD";
            case "SG":
                return "SGD";
            case "MY":
                return "MYR";
            case "GB":
                return "GBP";
            default:
                return null;
        }
    }
}