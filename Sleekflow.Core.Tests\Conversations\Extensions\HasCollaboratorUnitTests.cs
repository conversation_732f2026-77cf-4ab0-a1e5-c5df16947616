using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.Extensions;

[TestFixture]
public class HasCollaboratorUnitTests
{
    [Test]
    public void Conversation_Is_Null_Return_False()
    {
        Conversation? conversation = null;
        var staff = new StaffAccessControlAggregate { StaffId = 1 };

        bool result = conversation.HasCollaborator(staff);

        Assert.IsFalse(result);
    }

    [Test]
    public void Staff_Is_Null_Return_False()
    {
        var conversation = new Conversation();
        StaffAccessControlAggregate? staff = null;

        bool result = conversation.HasCollaborator(staff);

        Assert.IsFalse(result);
    }

    [Test]
    public void Staff_Is_Conversation_Collaborator_Return_True()
    {
        var conversation = new Conversation
        {
            AdditionalAssignees = new List<AdditionalAssignee>
            {
                new AdditionalAssignee
                {
                    AssigneeId = 1
                }
            }
        };
        var staff = new StaffAccessControlAggregate { StaffId = 1 };

        bool result = conversation.HasCollaborator(staff);

        Assert.IsTrue(result);
    }

    [Test]
    public void Staff_Is_Not_Conversation_Collaborator_Return_False()
    {
        var conversation = new Conversation
        {
            AdditionalAssignees = new List<AdditionalAssignee>
            {
                new AdditionalAssignee
                {
                    AssigneeId = 1
                }
            }
        };
        var staff = new StaffAccessControlAggregate { StaffId = 2 };

        bool result = conversation.HasCollaborator(staff);

        Assert.IsFalse(result);
    }

}