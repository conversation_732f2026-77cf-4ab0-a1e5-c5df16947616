using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Constants;

namespace Sleekflow.Core.Tests.Conversations.Modals
{
    [TestFixture]
    public class TeamDefaultChannelsTests
    {
        [Test]
        public void HasDefaultChannel_ShouldReturnFalse_WhenAllChannelsAreNull()
        {
            // Arrange
            var teamDefaultChannels = new TeamDefaultChannels
            {
                ChannelTypeToChannelIdentityIds = new Dictionary<string, List<string>>()
            };

            // Act
            var result = teamDefaultChannels.HasDefaultChannel;

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public void HasDefaultChannel_ShouldReturnTrue_WhenAtLeastOneChannelIsNotEmpty()
        {
            // Arrange
            var teamDefaultChannels = new TeamDefaultChannels
            {
                ChannelTypeToChannelIdentityIds = new Dictionary<string, List<string>>
                {
                    { "WhatsappTwilio", new List<string> { "channel1" } }
                }
            };

            // Act
            var result = teamDefaultChannels.HasDefaultChannel;

            // Assert
            Assert.IsTrue(result);
        }

        [Test]
        public void AllTeamDefaultChannelIds_ShouldReturnEmptyList_WhenAllChannelsAreNull()
        {
            // Arrange
            var teamDefaultChannels = new TeamDefaultChannels
            {
                ChannelTypeToChannelIdentityIds = new Dictionary<string, List<string>>()
            };

            // Act
            var result = teamDefaultChannels.AllTeamDefaultChannelIds;

            // Assert
            Assert.IsEmpty(result);
        }

        [Test]
        public void AllTeamDefaultChannelIds_ShouldReturnCombinedList_WhenChannelsAreNotNull()
        {
            // Arrange
            var teamDefaultChannels = new TeamDefaultChannels
            {
                ChannelTypeToChannelIdentityIds = new Dictionary<string, List<string>>
                {
                    { ChannelTypes.WhatsappTwilio, new List<string> { "channel1" } },
                    { ChannelTypes.Whatsapp360Dialog, new List<string> { "channel2" } },
                    { ChannelTypes.WhatsappCloudApi, new List<string> { "channel3" } },
                    { ChannelTypes.Instagram, new List<string> { "channel4" } },
                    { ChannelTypes.Facebook, new List<string> { "channel5" } }
                }
            };

            // Act
            var result = teamDefaultChannels.AllTeamDefaultChannelIds;

            // Assert
            Assert.That(result.Count, Is.EqualTo(5));
            Assert.That(result, Is.EquivalentTo(new List<string> { "channel1", "channel2", "channel3", "channel4", "channel5" }));
        }
    }
}