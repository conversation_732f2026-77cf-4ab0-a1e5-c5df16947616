﻿using System;
using System.Collections.Generic;
using Hangfire.States;
using Hangfire.Storage;
using Sleekflow.Apis.AuditHub.Api;
using Sleekflow.Apis.FlowHub.Api;
using Travis_backend.IntegrationServices;

namespace Travis_backend.BackgroundTaskServices.Attributes;

public class CustomJobExpirationFilter : IApplyStateFilter
{
    private readonly Dictionary<string, TimeSpan> _expirationMap = new ()
    {
        {
            nameof(IAuditLogsApi), TimeSpan.FromMinutes(1)
        },
        {
            nameof(ISystemAuditLogsApi), TimeSpan.FromMinutes(1)
        },
        {
            nameof(IEventsApi), TimeSpan.FromMinutes(1)
        },
        {
            nameof(IShopifyService), TimeSpan.FromMinutes(1)
        },
    };

    public void OnStateUnapplied(
        ApplyStateContext context,
        IWriteOnlyTransaction transaction)
    {
        var jobType = context.BackgroundJob.Job.Type;

        if (_expirationMap.TryGetValue(jobType.Name, out var expiration))
        {
            context.JobExpirationTimeout = expiration;
        }
    }

    public void OnStateApplied(
        ApplyStateContext context,
        IWriteOnlyTransaction transaction)
    {
        var jobType = context.BackgroundJob.Job.Type;

        if (_expirationMap.TryGetValue(jobType.Name, out var expiration))
        {
            context.JobExpirationTimeout = expiration;
        }
    }
}