namespace Travis_backend.Cache.Models.CacheKeyPatterns;

public class WebClientGetStaffAllInfoCacheKeyPattern : ICacheKeyPattern
{
    public string CompanyId { get; set; }

    public WebClientGetStaffAllInfoCacheKeyPattern(string companyId)
    {
        CompanyId = companyId;
    }

    public string GenerateKeyPattern()
    {
        var keyName = CacheHelper.BuildKeyNameFromPattern(
            new object[]
            {
                CompanyId
            });
        return keyName;
    }
} 