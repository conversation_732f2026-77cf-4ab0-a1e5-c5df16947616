﻿using System;
using System.Collections.Generic;
using System.Linq;
using LinqKit;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.Services.ConversationAnalyticsAdvanceFilters;

namespace Travis_backend.AnalyticsDomain.Utils;

public static class ConversationAnalyticsMetricUtils
{
    /// <summary>
    /// There may exist more than 1 record for the same dimension data per day,
    /// Recognized by <see cref="ConversationAnalyticsMetric.ToBeDeleted"/>.
    /// We assume there are at most 1 record with ToBeDeleted = 0, which has the highest priority for this dimension data.
    /// </summary>
    /// <param name="metrics">Metrics.</param>
    public static void Sanitize(ref List<ConversationAnalyticsMetric> metrics)
    {
        var metricDictionary = new Dictionary<string, ConversationAnalyticsMetric>(metrics.Count);
        foreach (var metric in metrics)
        {
            var key = metric.DimensionDataJson;

            if (metric.ToBeDeleted == 0)
            {
                metricDictionary[key] = metric;
            }
            else
            {
                metricDictionary.TryAdd(key, metric);
            }
        }

        metrics = metricDictionary.Values.ToList();
    }

    public static void ApplyAdvancedFilter(ref List<ConversationAnalyticsMetric> metrics, AdvancedFilter advancedFilter)
    {
        if (metrics is { Count: 0 }
            || advancedFilter.Filters.Count == 0)
        {
            return;
        }

        var logicalOperator = advancedFilter.LogicalOperator.ToUpper();

        var predicate = PredicateBuilder.New<ConversationAnalyticsMetric>(logicalOperator == "AND");

        foreach (var filter in advancedFilter.Filters)
        {
            var fieldName = filter.FieldName;
            var @operator = filter.Operator;

            var filterStrategy = FilterStrategyFactory.GetFilterStrategy(@operator);
            var thisPredicate = filterStrategy.BuildPredicate(fieldName, filter.Values);

            predicate = logicalOperator == "AND"
                ? predicate.And(thisPredicate)
                : predicate.Or(thisPredicate);
        }

        metrics = metrics.Where(predicate).ToList();
    }

    public static ConversationAnalyticsMetric Aggregate(IEnumerable<ConversationAnalyticsMetric> metrics)
    {
        return metrics.Aggregate(
            ConversationAnalyticsMetric.Empty(),
            (acc, metric) =>
            {
                acc.TotalConversations += metric.TotalConversations ?? 0;
                acc.TotalActiveConversations += metric.TotalActiveConversations ?? 0;
                acc.MessagesSent += metric.MessagesSent ?? 0;
                acc.MessagesReceived += metric.MessagesReceived ?? 0;
                acc.MessagesSendFailed += metric.MessagesSendFailed ?? 0;
                acc.ResolutionTimeInSecondsNumerator += metric.ResolutionTimeInSecondsNumerator ?? 0;
                acc.ResolutionTimeInSecondsDenominator += metric.ResolutionTimeInSecondsDenominator ?? 0;
                acc.FirstResponseTimeInSecondsNumerator += metric.FirstResponseTimeInSecondsNumerator ?? 0;
                acc.FirstResponseTimeInSecondsDenominator += metric.FirstResponseTimeInSecondsDenominator ?? 0;
                acc.ResponseTimeInSecondsNumerator += metric.ResponseTimeInSecondsNumerator ?? 0;
                acc.ResponseTimeInSecondsDenominator += metric.ResponseTimeInSecondsDenominator ?? 0;
                acc.NewEnquiries += metric.NewEnquiries ?? 0;
                acc.NewContacts += metric.NewContacts ?? 0;

                return acc;
            });
    }

    public static ConversationAnalyticsMetricDto Aggregate(
        DateOnly? date,
        IEnumerable<ConversationAnalyticsMetricDto> metrics)
    {
        return metrics.Aggregate(
            ConversationAnalyticsMetricDto.Default(date),
            (acc, metric) =>
            {
                acc.TotalConversations += metric.TotalConversations;
                acc.TotalActiveConversations += metric.TotalActiveConversations;
                acc.MessagesSent += metric.MessagesSent;
                acc.MessagesReceived += metric.MessagesReceived;
                acc.MessagesSendFailed += metric.MessagesSendFailed;
                acc.ResolutionTimeInSecondsNumerator += metric.ResolutionTimeInSecondsNumerator;
                acc.ResolutionTimeInSecondsDenominator += metric.ResolutionTimeInSecondsDenominator;
                acc.FirstResponseTimeInSecondsNumerator += metric.FirstResponseTimeInSecondsNumerator;
                acc.FirstResponseTimeInSecondsDenominator += metric.FirstResponseTimeInSecondsDenominator;
                acc.ResponseTimeInSecondsNumerator += metric.ResponseTimeInSecondsNumerator;
                acc.ResponseTimeInSecondsDenominator += metric.ResponseTimeInSecondsDenominator;
                acc.NewEnquiries += metric.NewEnquiries;
                acc.NewContacts += metric.NewContacts;

                return acc;
            });
    }

    public static ConversationAnalyticsMetricDto AggregateToDto(
        DateOnly? date,
        IEnumerable<ConversationAnalyticsMetric> metrics)
    {
        return metrics.Aggregate(
            ConversationAnalyticsMetricDto.Default(date),
            (acc, metric) =>
            {
                acc.TotalConversations += metric.TotalConversations ?? 0;
                acc.TotalActiveConversations += metric.TotalActiveConversations ?? 0;
                acc.MessagesSent += metric.MessagesSent ?? 0;
                acc.MessagesReceived += metric.MessagesReceived ?? 0;
                acc.MessagesSendFailed += metric.MessagesSendFailed ?? 0;
                acc.ResolutionTimeInSecondsNumerator += metric.ResolutionTimeInSecondsNumerator ?? 0;
                acc.ResolutionTimeInSecondsDenominator += metric.ResolutionTimeInSecondsDenominator ?? 0;
                acc.FirstResponseTimeInSecondsNumerator += metric.FirstResponseTimeInSecondsNumerator ?? 0;
                acc.FirstResponseTimeInSecondsDenominator += metric.FirstResponseTimeInSecondsDenominator ?? 0;
                acc.ResponseTimeInSecondsNumerator += metric.ResponseTimeInSecondsNumerator ?? 0;
                acc.ResponseTimeInSecondsDenominator += metric.ResponseTimeInSecondsDenominator ?? 0;
                acc.NewEnquiries += metric.NewEnquiries ?? 0;
                acc.NewContacts += metric.NewContacts ?? 0;

                return acc;
            });
    }
}