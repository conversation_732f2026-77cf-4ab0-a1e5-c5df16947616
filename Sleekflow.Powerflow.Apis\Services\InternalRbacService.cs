using Microsoft.EntityFrameworkCore;
using Sleekflow.Apis.TenantHub.Model;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.ConversationSettingsConstants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.TenantHubDomain.Services;

namespace Sleekflow.Powerflow.Apis.Services;

public interface IInternalRbacService
{
    Task InitiateRbacInboxSettingMigration(List<string> companyIds);
}

public class InternalRbacService : IInternalRbacService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IRbacService _rbacService;

    public InternalRbacService(ApplicationDbContext appDbContext, IRbacService rbacService)
    {
        _appDbContext = appDbContext;
        _rbacService = rbacService;
    }


    public async Task InitiateRbacInboxSettingMigration(List<string> companyIds)
    {
        // Define role mapping once (could be moved to a constant/static field)
        var roleMapping = new Dictionary<StaffUserRole, string>
        {
            {
                StaffUserRole.Admin, nameof(StaffUserRole.Admin)
            },
            {
                StaffUserRole.TeamAdmin, nameof(StaffUserRole.TeamAdmin)
            },
            {
                StaffUserRole.Staff, nameof(StaffUserRole.Staff)
            }
        };

        // Fetch data sequentially but with optimized queries
        var validRoleTypes = new[]
        {
            StaffUserRole.Admin,
            StaffUserRole.TeamAdmin,
            StaffUserRole.Staff
        };

        var companyRolePermissions = await _appDbContext.CompanyRolePermissions
            .Where(x => companyIds.Contains(x.CompanyId))
            .ToListAsync();

        var staffIdMappings = await _appDbContext.UserRoleStaffs
            .Where(x => companyIds.Contains(x.CompanyId) && validRoleTypes.Contains(x.RoleType))
            .GroupBy(
                x => new
                {
                    x.CompanyId, x.RoleType
                })
            .Select(
                g => new
                {
                    g.Key.CompanyId, g.Key.RoleType, StaffId = g.First().Id
                })
            .ToListAsync();

        // Organize staff data by company for faster lookup
        var staffIdsByCompany = staffIdMappings
            .GroupBy(x => x.CompanyId)
            .ToDictionary(
                g => g.Key,
                g => new Dictionary<StaffUserRole, long>(
                    g.Select(x => new KeyValuePair<StaffUserRole, long>(x.RoleType, x.StaffId))
                )
            );

        // Process each company
        foreach (var companyId in companyIds)
        {
            if (!staffIdsByCompany.TryGetValue(companyId, out var staffIdByRole))
            {
                continue;
            }

            // Convert to strings once
            var staffIdStrings = staffIdByRole.Values.Select(id => id.ToString()).ToList();

            // Get relevant data for this company
            var companyPermissions = companyRolePermissions
                .Where(x => x.CompanyId == companyId)
                .ToList();

            var rbacRoles = await GetRbacRoles(companyId, staffIdStrings);

            // Map to dictionary for faster lookup
            var rbacRolesByName = rbacRoles.ToDictionary(r => r.RoleName);

            // Create required permission modifications
            var permissionUpdates = BuildPermissionModifications(
                companyPermissions,
                rbacRolesByName,
                roleMapping);

            // Apply updates if any exist
            if (permissionUpdates.Count > 0)
            {
                await _rbacService.BulkUpdatePermission(companyId, permissionUpdates);
            }
        }
    }

    // Extract method to build the permission modifications
    private List<ManagementPermissionModification> BuildPermissionModifications(
        List<RolePermission> companyPermissions,
        Dictionary<string, (string RoleId, string RoleName, List<string> RolePermissions)> rbacRolesByName,
        Dictionary<StaffUserRole, string> roleMapping)
    {
        // Using Dictionary<string, HashSet<string>> improves performance over List operations
        var permissionToRoleIds = new Dictionary<string, HashSet<string>>();

        foreach (var permission in companyPermissions)
        {
            // Skip if no matching role
            if (!roleMapping.TryGetValue(permission.StaffUserRole, out var roleName) ||
                !rbacRolesByName.TryGetValue(roleName, out var rbacRole))
                continue;

            // Map permissions
            var rbacPermissions = MappingOriginalInboxSettingsToRbacInboxSettings(permission.Permission);

            // Add missing permissions to our dictionary
            foreach (var rbacPermission in rbacPermissions)
            {
                if (rbacPermission == null || rbacRole.RolePermissions.Contains(rbacPermission))
                    continue;

                if (!permissionToRoleIds.TryGetValue(rbacPermission, out var roleIds))
                {
                    roleIds = new HashSet<string>
                    {
                        rbacRole.RoleId
                    };
                    permissionToRoleIds[rbacPermission] = roleIds;
                }
                else
                {
                    roleIds.Add(rbacRole.RoleId);
                }
            }
        }

        // Convert to required output format
        return permissionToRoleIds
            .Select(
                kvp => new ManagementPermissionModification(
                    kvp.Key,
                    kvp.Value.ToList(),
                    new List<string>()))
            .ToList();
    }
    private async Task<List<(string RoleId, string RoleName, List<string> RolePermissions)>> GetRbacRoles(string companyId, List<string> staffIds)
    {
        if(staffIds is null || staffIds.Count == 0)
        {
            return [];
        }

        var rbacUserRolePermissions = await _rbacService.GetRolesWithPermissionsByStaffIds(companyId, staffIds);
        return rbacUserRolePermissions.SelectMany(
            x => x.Roles.Select(
                rbacRolePermissionsItem => (
                    rbacRolePermissionsItem.RoleId,
                    rbacRolePermissionsItem.RoleName,
                    rbacRolePermissionsItem.Permissions.ToList()
                )
            )
        ).ToList();
    }

    private static List<string> MappingOriginalInboxSettingsToRbacInboxSettings(Permission permission)
    {
        var rbacPermissionKeys = new List<string>();

        if (permission.ReceiveUnassignedNotifications)
        {
            rbacPermissionKeys.Add(RbacInboxSettings.UnassignedConversationNotifications);
        }

        if (permission.AddAsCollaboratorWhenAssignedToOthers)
        {
            rbacPermissionKeys.Add(RbacInboxSettings.RemainAsCollaboratorWhenReassigned);
        }

        if (permission.AddAsCollaboratorWhenReply)
        {
            rbacPermissionKeys.Add(RbacInboxSettings.BecomeCollaboratorWhenReply);
        }

        if (permission.IsShowDefaultChannelMessagesOnly)
        {
            rbacPermissionKeys.Add(RbacInboxSettings.ViewDefaultChannelMessagesOnly);
        }

        return rbacPermissionKeys;
    }
}