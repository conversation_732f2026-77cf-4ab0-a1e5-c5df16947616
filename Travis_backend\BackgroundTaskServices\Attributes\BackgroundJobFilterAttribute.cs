﻿using Hangfire.Client;
using Hangfire.Common;
using Hangfire.Logging;
using Hangfire.Server;
using Hangfire.States;
using Hangfire.Storage;
using Serilog.Context;
using Travis_backend.Enums;
using Travis_backend.OpenTelemetry.Meters;

namespace Travis_backend.BackgroundTaskServices.Attributes;

public class BackgroundJobFilterAttribute : JobFilterAttribute, IServerFilter, IClientFilter, IApplyStateFilter
{
    private static readonly ILog Logger = LogProvider.GetCurrentClassLogger();
    private readonly IHangfireMeters _hangfireMeters;

    public BackgroundJobFilterAttribute(IHangfireMeters hangfireMeters)
    {
        _hangfireMeters = hangfireMeters;
    }

    public void OnCreating(CreatingContext context)
    {
    }

    public void OnCreated(CreatedContext context)
    {
        _hangfireMeters.IncrementCounter(HangfireStateTypes.Scheduled);
    }

    public void OnPerforming(PerformingContext context)
    {
        var backgroundJob = context.BackgroundJob;
        LogContext.PushProperty("SleekflowBackgroundJobId", backgroundJob.Id);
        LogContext.PushProperty("SleekflowBackgroundJobName", backgroundJob.Job.Method.Name);

        _hangfireMeters.IncrementCounter(HangfireStateTypes.Processing);
    }

    public void OnPerformed(PerformedContext context)
    {
    }

    public void OnStateUnapplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
    {
    }

    public void OnStateApplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
    {
        var backgroundJob = context.BackgroundJob;

        switch (context.NewState)
        {
            case SucceededState succeededState:
                _hangfireMeters.IncrementCounter(HangfireStateTypes.Succeeded);
                break;

            case FailedState:
                _hangfireMeters.IncrementCounter(HangfireStateTypes.Failed);
                break;

            case DeletedState:
                _hangfireMeters.IncrementCounter(HangfireStateTypes.Deleted);
                break;
        }
    }
}