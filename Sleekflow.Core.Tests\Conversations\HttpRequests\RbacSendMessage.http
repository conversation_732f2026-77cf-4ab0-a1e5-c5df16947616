### Login

POST https://localhost:5000/auth0/account/getusertoken
content-type: application/json-patch+json

{
  "username": "",
  "password": ""
}

> {% client.global.set("token", response.body.accessToken); %}

### Send Note Message
POST https://{{host}}/Conversation/Note/{{conversation_Id}}
content-type: application/x-www-form-urlencoded
Authorization: Bearer {{token}}

MessageChecksum={{$random.uuid}}&Channel=note&MessageGroupName=d1d947a2aac94307a678dec233a865fa&MessageType=text&MessageContent=Hello