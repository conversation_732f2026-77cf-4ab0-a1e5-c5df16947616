﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.Database;
using Travis_backend.InternalDomain.Models;
using CmsCompanyStaffDto = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyStaffDto;
using CmsLoginAsHelper = Sleekflow.Powerflow.Apis.Helpers.CmsLoginAsHelper;
using DivingUserInfoResponse = Sleekflow.Powerflow.Apis.ViewModels.DivingUserInfoResponse;
using GetLoginAsSecretRequest = Sleekflow.Powerflow.Apis.ViewModels.GetLoginAsSecretRequest;
using GetLoginAsSecretResponse = Sleekflow.Powerflow.Apis.ViewModels.GetLoginAsSecretResponse;
using InvestigateCompanyRequest = Sleekflow.Powerflow.Apis.ViewModels.InvestigateCompanyRequest;
using InvestigateCompanyStatusResponse = Sleekflow.Powerflow.Apis.ViewModels.InvestigateCompanyStatusResponse;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Managing the Whatsapp Application.
/// </summary>
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)] // Basic Role Requirement
[Route("/internal/dive/[action]")]
public class InternalCmsDiveController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IDistributedCache _cache;

    public InternalCmsDiveController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<InternalCmsDiveController> logger,
        IDistributedCache cache)
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _cache = cache;
    }

    /// <summary>
    ///     Get All Dive Activity Log.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetDiveActivityLogsResponse>> GetDiveActivityLogs(
        [FromBody]
        GetDiveActivityLogsRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            [ApplicationUserRole.InternalCmsSuperUser]);
        if (user == null)
        {
            return Unauthorized();
        }

        var count = await _appDbContext.CmsLoginAsHistories.Where(x => x.CreatedAt != null).CountAsync();

        var diveActivityLogs = await (from dive in _appDbContext.CmsLoginAsHistories
            join company in _appDbContext.CompanyCompanies on dive.CompanyId equals company.Id
            join createdBy in _appDbContext.Users on dive.CreatedByUserId equals createdBy.Id into createdByJoin
            from createdBy in createdByJoin.DefaultIfEmpty()
            join loginAs in _appDbContext.Users on dive.LoginToStaffIdentityId equals loginAs.Id into loginAsJoin
            from loginAs in loginAsJoin.DefaultIfEmpty()
            where dive.CreatedAt != null
            select new DiveActivityLogs
            {
                UserName = createdBy != null ? createdBy.UserName : dive.CreatedByUserId,
                UserEmail = createdBy != null ? createdBy.Email : null,
                CompanyName = company.CompanyName,
                LoginAsName = loginAs != null ? loginAs.UserName : dive.LoginToStaffIdentityId,
                LoginAsEmail = loginAs != null ? loginAs.Email : null,
                CreatedAt = dive.CreatedAt,
                IsAdminDive = dive.IsAdminDive
            })
            .OrderByDescending(x => x.CreatedAt)
            .Skip(request.Limit * request.Page)
            .Take(request.Limit)
            .ToListAsync();

        var res = new GetDiveActivityLogsResponse
        {
            DiveActivityLogs = diveActivityLogs, Count = count
        };
        return Ok(res);
    }

    #region Login As Dive

    /// <summary>
    /// Generate Login As Secret.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Obsolete("Use /auth0/core/cms/{action} instead")]
    public async Task<ActionResult<GetLoginAsSecretResponse>> GetLoginAsSecret(
        [FromBody]
        GetLoginAsSecretRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        var staff = await _appDbContext.UserRoleStaffs
            .Include(x => x.Identity)
            .Where(
                x => x.Id == request.StaffId && x.CompanyId == request.CompanyId &&
                     x.IdentityId == request.StaffIdentityId)
            .Select(
                x => new CmsCompanyStaffDto
                {
                    StaffId = x.Id,
                    UserId = x.Identity.Id,
                    FirstName = x.Identity.FirstName,
                    LastName = x.Identity.LastName,
                    DisplayName = x.Identity.DisplayName,
                    UserName = x.Identity.UserName,
                    Email = x.Identity.Email,
                    EmailConfirmed = x.Identity.EmailConfirmed,
                    PhoneNumber = x.Identity.PhoneNumber,
                    Status = x.Status,
                    CreatedAt = x.Identity.CreatedAt,
                    LastLoginAt = x.Identity.LastLoginAt,
                    RoleType = x.RoleType,
                    TimeZoneInfoId = x.TimeZoneInfoId,
                    Position = x.Position,
                    Order = x.Order
                })
            .FirstOrDefaultAsync();

        if (staff == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "User Not Found."
                });
        }

        var loginAsSecret = CmsLoginAsHelper.GenerateLoginAsSecret(
            request.CompanyId,
            request.StaffIdentityId,
            request.StaffId,
            request.ValidDurationInMinute);

        var response = new GetLoginAsSecretResponse()
        {
            LoginAsStaff = staff, LoginAsSecret = loginAsSecret.LoginAsSecret, ExpireAt = loginAsSecret.ExpireAt
        };

        _appDbContext.CmsLoginAsHistories.Add(
            new CmsLoginAsHistory()
            {
                CompanyId = request.CompanyId,
                LoginToStaffId = request.StaffId,
                LoginToStaffIdentityId = request.StaffIdentityId,
                CreatedByUserId = user.Id,
            });

        await _appDbContext.SaveChangesAsync();

        return Ok(response);
    }

    #endregion

    #region Admin Dive

    /// <summary>
    /// Get Investigate Company / Dive Status.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<InvestigateCompanyStatusResponse>> GetInvestigateCompanyStatus()
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var diveStatus = await _appDbContext.UserRoleStaffs
            .Include(x => x.Company)
            .Where(x => x.Id == 1)
            .Select(
                x => new InvestigateCompanyStatusResponse
                {
                    CompanyId = x.CompanyId,
                    CompanyName = x.Company.CompanyName,
                    IsDiving = x.CompanyId != _configuration.GetValue<String>("Values:SleekFlowCompanyId")
                })
            .FirstOrDefaultAsync();

        if (diveStatus != null && diveStatus.IsDiving)
        {
            var userInfoCache = await _cache.GetStringAsync(GetDiveUserCacheKey());

            diveStatus.User = userInfoCache != null
                ? JsonConvert.DeserializeObject<DivingUserInfoResponse>(userInfoCache)
                : null;
        }

        return Ok(diveStatus);
    }

    /// <summary>
    /// Investigate Company / Dive.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<InvestigateCompanyStatusResponse>> InvestigateCompany(
        [FromBody]
        InvestigateCompanyRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        if (request.CompanyId == _configuration.GetValue<String>("Values:SleekFlowCompanyId"))
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "You cannot dive into this company."
                });
        }

        var company = await _appDbContext.CompanyCompanies
            .Select(
                x => new
                {
                    CompanyId = x.Id, CompanyName = x.CompanyName
                })
            .FirstOrDefaultAsync(x => x.CompanyId == request.CompanyId);

        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found"
                });
        }

        var adminAccount = await _appDbContext.UserRoleStaffs.Where(x => x.Id == 1).FirstOrDefaultAsync();
        adminAccount.CompanyId = request.CompanyId;

        _appDbContext.CmsLoginAsHistories.Add(
            new CmsLoginAsHistory
            {
                CompanyId = request.CompanyId,
                LoginToStaffId = adminAccount.Id,
                LoginToStaffIdentityId = adminAccount.IdentityId,
                CreatedByUserId = user.Id,
                IsAdminDive = true
            });

        await _appDbContext.SaveChangesAsync();

        var currentUser = _mapper.Map<DivingUserInfoResponse>(await _userManager.GetUserAsync(User));
        currentUser.DiveAt = DateTime.UtcNow;

        await _cache.SetStringAsync(GetDiveUserCacheKey(), JsonConvert.SerializeObject(currentUser));

        return Ok(
            new InvestigateCompanyStatusResponse
            {
                CompanyName = company.CompanyName,
                CompanyId = company.CompanyId,
                IsDiving = adminAccount.CompanyId != _configuration.GetValue<String>("Values:SleekFlowCompanyId"),
                User = currentUser
            });
    }

    /// <summary>
    /// Reset Investigate Company / Dive.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> ResetInvestigateCompany()
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        var adminAccount = await _appDbContext.UserRoleStaffs.Where(x => x.Id == 1).FirstOrDefaultAsync();

        adminAccount.CompanyId = _configuration.GetValue<String>("Values:SleekFlowCompanyId");

        await _appDbContext.SaveChangesAsync();

        await _cache.RemoveAsync(GetDiveUserCacheKey());

        return Ok(
            new InvestigateCompanyStatusResponse
            {
                IsDiving = false
            });
    }

    private string GetDiveUserCacheKey()
    {
        return "dive_user";
    }

    #endregion
}