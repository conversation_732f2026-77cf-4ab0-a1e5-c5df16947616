using Pulumi.AzureNative.Resources;
using Pulumi.AzureNative.Web.Inputs;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Components.Models;
using Sleekflow.Core.Infra.Constants;
using Docker = Pulumi.Docker;
using Web = Pulumi.AzureNative.Web;
using Cache = Pulumi.AzureNative.Cache;
using Storage = Pulumi.AzureNative.Storage;
using Insights = Pulumi.AzureNative.Insights;
using SignalRService = Pulumi.AzureNative.SignalRService;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;

namespace Sleekflow.Core.Infra.Utils;

public static class AppSettingUtils
{
    public static List<Web.Inputs.NameValuePairArgs> GetWebAppSettings(
        string webAppName,
        Dictionary<string, Cache.Redis> redis,
        Docker.Image image,
        MyConfig myConfig,
        string sfRegion,
        ResourceGroup resourceGroup,
        Insights.Component appInsights,
        SignalRService.SignalR signalR,
        SleekflowCoreConfig sleekflowCoreConfig,
        string defaultSleekflowCoreFrontDoorDomain,
        ContainerRegistryOutput containerRegistryOutput,
        OperationalInsights.Workspace logAnalyticsWorkspace)
    {
        var appSettings = new List<Web.Inputs.NameValuePairArgs>();
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WEBSITE_HEALTHCHECK_MAXPINGFAILURES", Value = "2",
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WEBSITES_ENABLE_APP_SERVICE_STORAGE", Value = "false",
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WEBSITE_WARMUP_PATH", Value = "/__health",
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WEBSITE_SWAP_WARMUP_PING_PATH", Value = "/__health",
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WEBSITES_CONTAINER_STOP_TIME_LIMIT", Value = "120",
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "DOCKER_REGISTRY_SERVER_URL",
                Value = containerRegistryOutput.Registry.LoginServer.Apply(s => $"https://{s}")
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "DOCKER_REGISTRY_SERVER_USERNAME", Value = containerRegistryOutput.AdminUsername
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "DOCKER_REGISTRY_SERVER_PASSWORD", Value = containerRegistryOutput.AdminPassword
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "DOCKER_CUSTOM_IMAGE_NAME", Value = image.ImageName.Apply(i => i)
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "APPINSIGHTS_INSTRUMENTATIONKEY", Value = appInsights.InstrumentationKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "APPINSIGHTS_PROFILERFEATURE_VERSION", Value = "1.0.0"
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "APPINSIGHTS_SNAPSHOTFEATURE_VERSION", Value = "1.0.0"
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "APPLICATIONINSIGHTS_CONNECTION_STRING", Value = appInsights.ConnectionString
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "ApplicationInsightsAgent_EXTENSION_VERSION", Value = "~2"
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "ASPNETCORE_ENVIRONMENT", Value = sleekflowCoreConfig.AspnetcoreEnvironment
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "ASPNETCORE_URLS", Value = "http://+:80",
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "AuditHub__Endpoint", Value = sleekflowCoreConfig.AuditHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "AuditHub__Key", Value = sleekflowCoreConfig.AuditHub.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__ActionAudience", Value = sleekflowCoreConfig.Auth0.ActionAudience
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__ActionIssuer", Value = sleekflowCoreConfig.Auth0.ActionIssuer
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__Audience", Value = sleekflowCoreConfig.Auth0.Audience
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__ClientId", Value = sleekflowCoreConfig.Auth0.ClientId
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__ClientSecret", Value = sleekflowCoreConfig.Auth0.ClientSecret
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__DatabaseConnectionName", Value = sleekflowCoreConfig.Auth0.DatabaseConnectionName
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__Domain", Value = sleekflowCoreConfig.Auth0.Domain
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__HttpRetries", Value = sleekflowCoreConfig.Auth0.HttpRetries
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__Namespace", Value = sleekflowCoreConfig.Auth0.Namespace
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__RoleClaimType", Value = sleekflowCoreConfig.Auth0.RoleClaimType
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__UserEmailClaimType", Value = sleekflowCoreConfig.Auth0.UserEmailClaimType
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__UserIdClaimType", Value = sleekflowCoreConfig.Auth0.UserIdClaimType
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__UserNameClaimType", Value = sleekflowCoreConfig.Auth0.UsernameClaimType
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__TenantHub__SecretKey", Value = sleekflowCoreConfig.Auth0.TenantHubSecretKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__HealthCheck__IsEnabled", Value = sleekflowCoreConfig.Auth0.HealthCheckConfig.IsEnabled
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__HealthCheck__ClientId", Value = sleekflowCoreConfig.Auth0.HealthCheckConfig.ClientId
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__HealthCheck__ClientSecret",
                Value = sleekflowCoreConfig.Auth0.HealthCheckConfig.ClientSecret
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__HealthCheck__Username", Value = sleekflowCoreConfig.Auth0.HealthCheckConfig.Username
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Auth0__HealthCheck__Password", Value = sleekflowCoreConfig.Auth0.HealthCheckConfig.Password
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Azure__MediaService__AccountName", Value = sleekflowCoreConfig.Azure.MediaService.AccountName
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Azure__MediaService__ClientId", Value = sleekflowCoreConfig.Azure.MediaService.ClientId
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Azure__MediaService__ClientSecret", Value = sleekflowCoreConfig.Azure.MediaService.ClientSecret
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Azure__MediaService__ResourceGroup",
                Value = sleekflowCoreConfig.Azure.MediaService.ResourceGroup
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Azure__MediaService__SubscriptionId",
                Value = sleekflowCoreConfig.Azure.MediaService.SubscriptionId
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Azure__MediaService__TenantId", Value = sleekflowCoreConfig.Azure.MediaService.TenantId
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Azure__SignalR__ConnectionString",
                Value = ConnectionStringUtils.GetSignalRConnStr(resourceGroup.Name, signalR)
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Azure__TextAnalyticsCredentials", Value = sleekflowCoreConfig.Azure.TextAnalyticsCredentials
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Azure__TextAnalyticsUrl", Value = sleekflowCoreConfig.Azure.TextAnalyticsUrl
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = sleekflowCoreConfig.ApplicationInsightsConfig.IsTelemetryTracerEnabled,
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = sleekflowCoreConfig.ApplicationInsightsConfig.IsSamplingDisabled,
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Beamer__APIKey", Value = sleekflowCoreConfig.Beamer.ApiKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Beamer__APIUrl", Value = sleekflowCoreConfig.Beamer.ApiUrl
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Beamer__WebhookVerifyKey", Value = sleekflowCoreConfig.Beamer.WebhookVerifyKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "ChatAPI__APIKey", Value = sleekflowCoreConfig.ChatApi.ApiKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "ChatAPI__APIUrl", Value = sleekflowCoreConfig.ChatApi.ApiUrl
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "CommerceHub__Endpoint", Value = sleekflowCoreConfig.CommerceHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "CommerceHub__Key", Value = sleekflowCoreConfig.CommerceHub.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "CrmHub__Endpoint", Value = sleekflowCoreConfig.CrmHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "CrmHub__Key", Value = sleekflowCoreConfig.CrmHub.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "DataSnapshot__IsEnable", Value = sleekflowCoreConfig.DataSnapshot.IsEnable
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Development__RedirectFBWebhock", Value = sleekflowCoreConfig.Development.RedirectFbWebhook
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "EnvironmentFeatures__IsRecurringJobEnabled",
                Value = sleekflowCoreConfig.EnvironmentFeatures.IsRecurringJobEnabled
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "EPPlus__ExcelPackage__LicenseContext",
                Value = sleekflowCoreConfig.EpPlus.ExcelPackage.LicenseContext
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Facebook__ClientId", Value = sleekflowCoreConfig.Facebook.ClientId
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Facebook__ClientSecret", Value = sleekflowCoreConfig.Facebook.ClientSecret
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "ffmpeg__FFMpegExeName", Value = sleekflowCoreConfig.FFMpeg.FFMpegExeName
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "FlowHub__Endpoint", Value = sleekflowCoreConfig.FlowHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "FlowHub__Key", Value = sleekflowCoreConfig.FlowHub.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "HubSpot__InternalHubSpotApiKey", Value = sleekflowCoreConfig.HubSpot.InternalHubSpotApiKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "HubSpot__IsEnable", Value = sleekflowCoreConfig.HubSpot.IsEnable
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "InstrumentationEngine_EXTENSION_VERSION",
                Value = sleekflowCoreConfig.InstrumentationEngineExtensionVersion
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "InternalGoogleCloud__Credential", Value = sleekflowCoreConfig.InternalGoogleCloud.Credential
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "GeneralGoogleCloud__Credential", Value = sleekflowCoreConfig.GeneralGoogleCloud.Credential
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "GeneralGoogleCloud__ProjectId", Value = sleekflowCoreConfig.GeneralGoogleCloud.ProjectId
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "GeneralGoogleCloud__ServerLocation",
                Value = sleekflowCoreConfig.GeneralGoogleCloud.ServerLocation
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "GeneralGoogleCloud__GoogleStorageBucketName",
                Value = sleekflowCoreConfig.GeneralGoogleCloud.GoogleStorageBucketName
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "IpLookUp__Key", Value = sleekflowCoreConfig.IpLookUp.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "IntelligentHub__Endpoint", Value = sleekflowCoreConfig.IntelligentHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "IntelligentHub__Key", Value = sleekflowCoreConfig.IntelligentHub.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WebhookHub__Endpoint", Value = sleekflowCoreConfig.WebhookHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WebhookHub__Key", Value = sleekflowCoreConfig.WebhookHub.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WebhookHub__AuthSecretKey", Value = sleekflowCoreConfig.WebhookHub.AuthSecretKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "LOGGER_AUTHENTICATION_ID",
                Value = ConnectionStringUtils.GetWorkspaceKey(
                    resourceGroup.Name,
                    logAnalyticsWorkspace.Name)
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE"
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED",
                Value = sleekflowCoreConfig.Logger.IsGoogleCloudLoggingEnabled
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = sleekflowCoreConfig.Logger.GoogleCloudProjectId
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON",
                Value = sleekflowCoreConfig.Logger.GoogleCloudCredentialJson
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "MessagingHub__Endpoint", Value = sleekflowCoreConfig.MessagingHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "MessagingHub__Key", Value = sleekflowCoreConfig.MessagingHub.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Mixpanel__Token", Value = sleekflowCoreConfig.Mixpanel.Token
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "MobileAppsManagement_EXTENSION_VERSION",
                Value = sleekflowCoreConfig.MobileAppsManagementExtensionVersion
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "NotificationHub__ConnectionString", Value = sleekflowCoreConfig.NotificationHub.ConnectionString
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "NotificationHub__HubName", Value = sleekflowCoreConfig.NotificationHub.HubName
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "PublicApiGateway__Endpoint", Value = sleekflowCoreConfig.PublicApiGateway.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "PublicApiGateway__Key", Value = sleekflowCoreConfig.PublicApiGateway.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "redis__connectionString",
                Value = sleekflowCoreConfig.Redis is not null
                    ? sleekflowCoreConfig.Redis.ConnectionString
                    : ConnectionStringUtils.GetRedisConnStr(resourceGroup.Name, redis[RedisInstances.Default])
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "redis__caching__connectionString",
                Value = ConnectionStringUtils.GetRedisConnStr(resourceGroup.Name, redis[RedisInstances.Caching])
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Reseller__DomainName", Value = sleekflowCoreConfig.Reseller.DomainName
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Rewardful__APISecret", Value = sleekflowCoreConfig.Rewardful.ApiSecret
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Salesforce__CustomActivityWebApp", Value = sleekflowCoreConfig.Salesforce.CustomActivityWebApp
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "ShareHub__Endpoint", Value = sleekflowCoreConfig.ShareHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "ShareHub__Key", Value = sleekflowCoreConfig.ShareHub.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Shopify__ShopifyApiKey", Value = sleekflowCoreConfig.Shopify.ShopifyApiKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Shopify__ShopifySecretKey", Value = sleekflowCoreConfig.Shopify.ShopifySecretKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SnapshotDebugger_EXTENSION_VERSION",
                Value = sleekflowCoreConfig.SnapshotDebuggerExtensionVersion
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SqlPerformance__FromRawSql", Value = sleekflowCoreConfig.SqlPerformance.FromRawSql
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SqlPerformance__IsAndConditionEnabled",
                Value = sleekflowCoreConfig.SqlPerformance.IsAndConditionEnabled
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SqlPerformance__IsOrConditionEnabled",
                Value = sleekflowCoreConfig.SqlPerformance.IsOrConditionEnabled
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SqlPerformance__IsConversationAnalyticsConditionEnabled",
                Value = sleekflowCoreConfig.SqlPerformance.IsConversationAnalyticsConditionEnabled ?? "false"
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SqlPerformance__IsShopifyOrderStatisticsEnabled",
                Value = sleekflowCoreConfig.SqlPerformance.IsShopifyOrderStatisticsEnabled
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SqlPerformance__IsSalesPerformanceEnabled",
                Value = sleekflowCoreConfig.SqlPerformance.IsSalesPerformanceEnabled
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SqlPerformance__IsPublicApiNewUpsertEnabled",
                Value = sleekflowCoreConfig.SqlPerformance.IsPublicApiNewUpsertEnabled
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SqlPerformance__IsZapierNewUpsertEnabled",
                Value = sleekflowCoreConfig.SqlPerformance.IsZapierNewUpsertEnabled
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe__Stripe_Public_Key", Value = sleekflowCoreConfig.Stripe.StripePublicKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe__Stripe_Report_Key", Value = sleekflowCoreConfig.Stripe.StripeReportKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe__Stripe_Secret_Key", Value = sleekflowCoreConfig.Stripe.StripeSecretKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe__Stripe_Webhook_Secret", Value = sleekflowCoreConfig.Stripe.StripeWebhookSecret
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripePayment__Stripe_Payment_Secret_Key_GB",
                Value = sleekflowCoreConfig.StripePayment.StripePaymentSecretKeyGb
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripePayment__Stripe_Payment_Secret_Key_HK",
                Value = sleekflowCoreConfig.StripePayment.StripePaymentSecretKeyHk
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripePayment__Stripe_Payment_Secret_Key_MY",
                Value = sleekflowCoreConfig.StripePayment.StripePaymentSecretKeyMy
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripePayment__Stripe_Payment_Secret_Key_SG",
                Value = sleekflowCoreConfig.StripePayment.StripePaymentSecretKeySg
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeReport__Stripe_Report_Webhook_Secret_GB",
                Value = sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretGb
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeReport__Stripe_Report_Webhook_Secret_HK",
                Value = sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretHk
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeReport__Stripe_Report_Webhook_Secret_MY",
                Value = sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretMy
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeReport__Stripe_Report_Webhook_Secret_SG",
                Value = sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretSg
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "TenantHub__Endpoint", Value = sleekflowCoreConfig.TenantHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "TenantHub__Key", Value = sleekflowCoreConfig.TenantHub.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "TenantHub__IsEnableTenantLogic", Value = sleekflowCoreConfig.TenantHub.IsEnableTenantLogic
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "TicketingHub__Endpoint", Value = sleekflowCoreConfig.TicketingHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "TicketingHub__Key", Value = sleekflowCoreConfig.TicketingHub.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "TicketingHub__IsEnableTicketingLogic",
                Value = sleekflowCoreConfig.TicketingHub.IsEnableTicketingLogic
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "testswaping", Value = sleekflowCoreConfig.TestSwaping
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Token__Audience", Value = sleekflowCoreConfig.Token.Audience
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Tokens__Audience", Value = sleekflowCoreConfig.Tokens.Audience
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Tokens__Issuer", Value = sleekflowCoreConfig.Tokens.Issuer
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Tokens__Key", Value = sleekflowCoreConfig.Tokens.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Tokens__Lifetime", Value = sleekflowCoreConfig.Tokens.Lifetime
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "UserEventHub__Endpoint", Value = sleekflowCoreConfig.UserEventHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "UserEventHub__Key", Value = sleekflowCoreConfig.UserEventHub.Key
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Values__AppDomainName", Value = sleekflowCoreConfig.Values.AppDomainName
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Values__AppDomainNameV1", Value = sleekflowCoreConfig.Values.AppDomainNameV1
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Values__DomainName", Value = $"https://{webAppName}.azurewebsites.net"
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Values__ShareLinkFunction", Value = sleekflowCoreConfig.Values.ShareLinkFunction
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Values__SleekflowApiGateway", Value = sleekflowCoreConfig.Values.SleekflowApiGateway
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Values__SleekFlowCompanyId", Value = sleekflowCoreConfig.Values.SleekflowCompanyId
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Values__SleekFlowCompanyShouldUsePublicApi", Value = sleekflowCoreConfig.Values.SleekflowCompanyShouldUsePublicApi
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Values__SleekFlowPublicApiKey", Value =  sleekflowCoreConfig.Values.SleekflowPublicApiKey
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Values__SleekFlowPublicApiUrl", Value =  sleekflowCoreConfig.Values.SleekflowPublicApiUrl
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Values__SleekPayFunction", Value = sleekflowCoreConfig.SleekPayConfig.DomainName
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Values__SleekflowCoreAzureFrontDoorDomain", Value = defaultSleekflowCoreFrontDoorDomain
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WEBSITE_HTTPLOGGING_RETENTION_DAYS", Value = sleekflowCoreConfig.WebsiteHttpLoggingRetentionDays
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WEBSITE_NODE_DEFAULT_VERSION", Value = sleekflowCoreConfig.WebsiteNodeDefaultVersion
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WhatsAppCloudApiTemplate__DefaultImageBlobId",
                Value = sleekflowCoreConfig.WhatsAppCloudApiTemplate.DefaultImageBlobId
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "XDT_MicrosoftApplicationInsights_BaseExtensions",
                Value = sleekflowCoreConfig.XdtMicrosoftApplicationInsightsBaseExtensions
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "XDT_MicrosoftApplicationInsights_Mode",
                Value = sleekflowCoreConfig.XdtMicrosoftApplicationInsightsMode
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SF_ENVIRONMENT", Value = myConfig.Name
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SF_REGION", Value = sfRegion
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "GlobalPricing__IsFeatureEnabled",
                Value = sleekflowCoreConfig.GlobalPricingConfig.IsFeatureEnabled
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "GlobalPricing__PlanMigrationIncentivesStartDate",
                Value = sleekflowCoreConfig.GlobalPricingConfig.PlanMigrationIncentivesStartDate
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "GlobalPricing__PlanMigrationIncentivesEndDate",
                Value = sleekflowCoreConfig.GlobalPricingConfig.PlanMigrationIncentivesEndDate
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "ContactSafeDeletion__IsFeatureEnabled",
                Value = sleekflowCoreConfig.ContactSafeDeletionConfig.IsFeatureEnabled
            });

        appSettings.AddRange(
            sleekflowCoreConfig.Auth0.Issuers.Select(
                (auth0Issuer, i) => new Web.Inputs.NameValuePairArgs
                {
                    Name = "Auth0__Issuers__" + i, Value = auth0Issuer
                }));

        appSettings.AddRange(
            sleekflowCoreConfig.FeatureFlags.Select(
                (feature) => new Web.Inputs.NameValuePairArgs
                {
                    Name = $"FeatureFlags__{feature.FeatureName}__IsEnabled", Value = feature.IsEnabled
                }));

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "PartnerStack__PublicKey", Value = sleekflowCoreConfig.PartnerStackConfig.PublicKey
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "PartnerStack__SecretKey", Value = sleekflowCoreConfig.PartnerStackConfig.SecretKey
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "InternalIntegrationHub__Endpoint", Value = sleekflowCoreConfig.InternalIntegrationHub.Endpoint
            });
        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "InternalIntegrationHub__Key", Value = sleekflowCoreConfig.InternalIntegrationHub.Key
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "IntegrationAlert__Endpoint",
                Value = sleekflowCoreConfig.IntegrationAlertConfig.Endpoint
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "IntegrationAlert__ApiKey",
                Value = sleekflowCoreConfig.IntegrationAlertConfig.ApiKey
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "IntegrationAlert__HostCompanyId",
                Value = sleekflowCoreConfig.IntegrationAlertConfig.HostCompanyId
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "IntegrationAlert__FromPhoneNumber",
                Value = sleekflowCoreConfig.IntegrationAlertConfig.FromPhoneNumber
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "IntegrationAlert__TemplateName",
                Value = sleekflowCoreConfig.IntegrationAlertConfig.TemplateName
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "IntegrationAlert__FacebookLeadAdsHelpCenterUrl",
                Value = sleekflowCoreConfig.IntegrationAlertConfig.FacebookLeadAdsHelpCenterUrl
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Rbac__IsMiddlewareVerificationEnabled",
                Value = sleekflowCoreConfig.RbacConfig.IsMiddlewareVerificationEnabled
            });

        appSettings.Add(
            new NameValuePairArgs
            {
                Name = "FlowBuilderFlowEnrollmentsIncentives__PeriodStart",
                Value = sleekflowCoreConfig.FlowBuilderFlowEnrollmentsIncentivesConfig.PeriodStart
            });

        appSettings.Add(
            new NameValuePairArgs
            {
                Name = "FlowBuilderFlowEnrollmentsIncentives__PeriodEnd",
                Value = sleekflowCoreConfig.FlowBuilderFlowEnrollmentsIncentivesConfig.PeriodEnd
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "HubSpotSmtp__Username", Value = sleekflowCoreConfig.HubSpotSmtp.Username
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "HubSpotSmtp__Password", Value = sleekflowCoreConfig.HubSpotSmtp.Password
            });

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "HubSpotSmtp__SendExecutionUsageReachedThresholdEmail__Username",
                Value = sleekflowCoreConfig.HubSpotSmtp.SendExecutionUsageReachedThresholdEmail.Username
            }
        );

        appSettings.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "HubSpotSmtp__SendExecutionUsageReachedThresholdEmail__Password",
                Value = sleekflowCoreConfig.HubSpotSmtp.SendExecutionUsageReachedThresholdEmail.Password
            }
        );

        appSettings.Add(
            new NameValuePairArgs
            {
                Name = "LegacyPremiumOptInUpgradeIncentives__PeriodStart",
                Value = sleekflowCoreConfig.LegacyPremiumOptInUpgradeIncentivesConfig.PeriodStart
            });

        appSettings.Add(
            new NameValuePairArgs
            {
                Name = "LegacyPremiumOptInUpgradeIncentives__PeriodEnd",
                Value = sleekflowCoreConfig.LegacyPremiumOptInUpgradeIncentivesConfig.PeriodEnd
            });

        appSettings.Add(
            new NameValuePairArgs
            {
                Name = "Values__AppDomainNameV2", Value = sleekflowCoreConfig.Values.AppDomainNameV2
            });

        return appSettings;
    }

    public static List<Web.Inputs.NameValuePairArgs> GetSleekPaySetting(
        string webAppName,
        Storage.Blob blob,
        Dictionary<string, Cache.Redis> redis,
        Web.WebApp sleekflowCore,
        ResourceGroup resourceGroup,
        Insights.Component appInsights,
        Storage.BlobContainer blobContainer,
        Storage.StorageAccount storageAccount,
        SleekflowCoreConfig sleekflowCoreConfig,
        SqlServerProperties sqlServerProperties,
        OperationalInsights.Workspace logAnalyticsWorkspace)
    {
        var sleekPayConfig = sleekflowCoreConfig.SleekPayConfig;
        var publicKeys = sleekPayConfig.StripeConfig.PublicKeys;
        var secretKeys = sleekPayConfig.StripeConfig.SecretKeys;
        var connectWebhookSecrets = sleekPayConfig.StripeConfig.ConnectWebhookSecrets;
        var reportWebhookSecrets = sleekPayConfig.StripeConfig.ReportWebhookSecrets;
        var webhookSecrets = sleekPayConfig.StripeConfig.WebhookSecrets;
        var list = new List<Web.Inputs.NameValuePairArgs>();
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "FUNCTIONS_EXTENSION_VERSION", Value = "~4",
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "FUNCTIONS_WORKER_RUNTIME", Value = "dotnet",
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "FUNCTIONS_INPROC_NET8_ENABLED", Value = "1",
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "APPINSIGHTS_INSTRUMENTATIONKEY", Value = appInsights.InstrumentationKey
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "APPLICATIONINSIGHTS_CONNECTION_STRING", Value = appInsights.ConnectionString,
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = sleekflowCoreConfig.ApplicationInsightsConfig.IsTelemetryTracerEnabled,
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = sleekflowCoreConfig.ApplicationInsightsConfig.IsSamplingDisabled,
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "AzureWebJobsSecretStorageType", Value = "Blob"
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "AzureWebJobsStorage",
                Value = ConnectionStringUtils.GetStorageConnStr(
                    resourceGroup.Name,
                    storageAccount.Name),
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WEBSITE_CONTENTAZUREFILECONNECTIONSTRING",
                Value = ConnectionStringUtils.GetStorageConnStr(
                    resourceGroup.Name,
                    storageAccount.Name),
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WEBSITE_CONTENTSHARE", Value = webAppName + "96ed",
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "WEBSITE_RUN_FROM_PACKAGE",
                Value = ConnectionStringUtils.SignedBlobReadUrl(
                    blob,
                    blobContainer,
                    storageAccount,
                    resourceGroup),
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "TRUE",
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "LOGGER_AUTHENTICATION_ID",
                Value = ConnectionStringUtils.GetWorkspaceKey(
                    resourceGroup.Name,
                    logAnalyticsWorkspace.Name)
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "AzureFunctionDomain", Value = sleekPayConfig.DomainName
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "RedirectDomain", Value = sleekflowCoreConfig.Values.AppDomainNameV1
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "redisCacheConnectionString",
                Value = ConnectionStringUtils.GetRedisConnStr(resourceGroup.Name, redis[RedisInstances.Caching])
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SleekFlowAPIDomain", Value = sleekflowCore.Name.Apply(n => $"https://{n}.azurewebsites.net")
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SqlConnectionString",
                Value = ConnectionStringUtils.GetSqlServerConnStr(
                    resourceGroup.Name,
                    sqlServerProperties,
                    sleekflowCoreConfig.GeoSqlDb)
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "SqlReadConnectionString",
                Value = ConnectionStringUtils.GetSqlServerConnStr(
                    resourceGroup.Name,
                    sqlServerProperties,
                    sleekflowCoreConfig.GeoSqlDb,
                    isReadOnly: true)
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "ShopifyGraphQlAdminApiVersion", Value = "2022-10"
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe_Public_Key", Value = publicKeys.Default!
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe_Public_Key_GB", Value = publicKeys.Gb
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe_Public_Key_HK", Value = publicKeys.Hk
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe_Public_Key_MY", Value = publicKeys.My
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe_Public_Key_SG", Value = publicKeys.Sg
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe_Secret_Key", Value = secretKeys.Default!
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe_Secret_Key_GB", Value = secretKeys.Gb
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe_Secret_Key_HK", Value = secretKeys.Hk
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe_Secret_Key_MY", Value = secretKeys.My
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "Stripe_Secret_Key_SG", Value = secretKeys.Sg
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeConnectWebhookSecret_GB", Value = connectWebhookSecrets.Gb
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeConnectWebhookSecret_HK", Value = connectWebhookSecrets.Hk
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeConnectWebhookSecret_MY", Value = connectWebhookSecrets.My
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeConnectWebhookSecret_SG", Value = connectWebhookSecrets.Sg
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeReportWebhookSecret_GB", Value = reportWebhookSecrets.Gb
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeReportWebhookSecret_HK", Value = reportWebhookSecrets.Hk
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeReportWebhookSecret_MY", Value = reportWebhookSecrets.My
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeReportWebhookSecret_SG", Value = reportWebhookSecrets.Sg
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeWebhookSecret", Value = webhookSecrets.Default!
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeWebhookSecret_GB", Value = webhookSecrets.Gb
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeWebhookSecret_HK", Value = webhookSecrets.Hk
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeWebhookSecret_MY", Value = webhookSecrets.My
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "StripeWebhookSecret_SG", Value = webhookSecrets.Sg
            });
        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "HubSpotSmtp__Username", Value = sleekflowCoreConfig.HubSpotSmtp.Username
            });

        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "HubSpotSmtp__Password", Value = sleekflowCoreConfig.HubSpotSmtp.Password
            });

        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "HubSpotSmtp__SendExecutionUsageReachedThresholdEmail__Username",
                Value = sleekflowCoreConfig.HubSpotSmtp.SendExecutionUsageReachedThresholdEmail.Username
            }
        );

        list.Add(
            new Web.Inputs.NameValuePairArgs
            {
                Name = "HubSpotSmtp__SendExecutionUsageReachedThresholdEmail__Password",
                Value = sleekflowCoreConfig.HubSpotSmtp.SendExecutionUsageReachedThresholdEmail.Password
            }
        );

        return list;
    }
}