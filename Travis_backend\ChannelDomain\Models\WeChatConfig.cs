﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Travis_backend.ChannelDomain.Models.Interfaces;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;

namespace Travis_backend.ChannelDomain.Models
{
    public class WeChatConfig : IMessagingChannel
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public string WebChatId { get; set; }

        public string AppId { get; set; }

        public string AppSecret { get; set; }

        public string AccessToken { get; set; }

        public DateTime TokenExpireAt { get; set; }

        public string QRCodeURL { get; set; }

        public ProfilePictureFile QRCode { get; set; }

        public DateTime ConnectedDateTime { get; set; } = DateTime.UtcNow;

        public bool IsShowInWidget { get; set; } = true;

        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

        [NotMapped]
        public string ChannelType => ChannelTypes.Wechat;

        // [NotMapped]
        // public string ChannelIdentityId => AppId;
        [MaxLength(400)]
        public string ChannelIdentityId { get; set; }

        [NotMapped]
        public string ChannelDisplayName => Name;
    }
}