using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Core.Infra.Perf.Components;
using Sleekflow.Core.Infra.Perf.Components.Configs;
using Sleekflow.Core.Infra.Perf.Components.Models;
using Sleekflow.Core.Infra.Perf.Components.SleekflowCore;
using Sleekflow.Core.Infra.Perf.Constants;
using Sleekflow.Core.Infra.Perf.Utils;
using Web = Pulumi.AzureNative.Web;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using FrontDoor = Sleekflow.Core.Infra.Perf.Components.FrontDoor;

namespace Sleekflow.Core.Infra.Perf;

internal class MyStack : Stack
{
    [Output]
    public Output<string> FrontDoorEndpoint { get; set; }

    public MyStack()
    {
        var myConfig = new MyConfig();
        var serverConfig = new ServerConfig();

        // Init the central resource group for handling the common modules
        // i.e. Azure container registry - Handling Docker Image, Azure Front Dot or to route the traffic
        var resourceGroup = new ResourceGroup(
            ResourceUtils.GetName("sleekflow-core-rg", myConfig),
            new ResourceGroupArgs
            {
                Location = LocationNames.EastAsia
            });

        var containerRegistryOutput = InitContainerRegistry(myConfig, resourceGroup);
        var envGroups = InitEnvGroups(myConfig, serverConfig);

        InitSleekflowCore(myConfig, serverConfig, containerRegistryOutput, envGroups);

        // Init Global Azure Front Door to direct the traffic to different location base on the X-Sleekflow-Location
        // i.e. Default -> East-Asia Travis Backend, eastus -> East-Us Sleekflow Core
        var frontDoorEndpoint = new FrontDoor(myConfig, envGroups, serverConfig, resourceGroup).InitFrontDoor();

        FrontDoorEndpoint = frontDoorEndpoint.HostName;
    }

    private static List<EnvGroup> InitEnvGroups(MyConfig myConfig, ServerConfig serverConfig)
    {
        return myConfig.Name switch
        {
            EnvironmentNames.Performance => new List<EnvGroup>
            {
                InitEnvGroup(myConfig, serverConfig, LocationNames.EastAsia)
            },
            _ => throw new Exception("MyStack - InitEnvGroups")
        };
    }

    private static EnvGroup InitEnvGroup(MyConfig myConfig, ServerConfig serverConfig, string locationName)
    {
        var resourceGroup = new ResourceGroup(
            ResourceUtils.GetName($"sleekflow-core-rg-{LocationNames.GetShortName(locationName)}", myConfig),
            new ResourceGroupArgs
            {
                Location = LocationNames.GetAzureLocation(locationName)
            });

        return new EnvGroup(
            locationName,
            new Redis(myConfig, locationName, serverConfig, resourceGroup).InitRedis(),
            resourceGroup,
            new Dictionary<string, Web.WebApp>());
    }

    private static void InitSleekflowCore(
        MyConfig myConfig,
        ServerConfig serverConfig,
        ContainerRegistryOutput containerRegistryOutput,
        List<EnvGroup> envGroups)
    {
        new SleekflowCore(
                myConfig,
                envGroups,
                serverConfig,
                containerRegistryOutput)
            .InitSleekflowCore();
    }

    private static ContainerRegistryOutput InitContainerRegistry(MyConfig myConfig, ResourceGroup resourceGroup)
    {
        var registry = new ContainerRegistry.Registry(
            ResourceUtils.GetName("sleekflow-core-registry", myConfig),
            new ContainerRegistry.RegistryArgs
            {
                RegistryName = ResourceUtils.GetRegistryName("sleekflowcoreregistry", myConfig),
                ResourceGroupName = resourceGroup.Name,
                Sku = new ContainerRegistry.Inputs.SkuArgs
                {
                    Name = ContainerRegistry.SkuName.Basic
                },
                AdminUserEnabled = true
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var registryCredentials = ContainerRegistry.ListRegistryCredentials.Invoke(
            new ContainerRegistry.ListRegistryCredentialsInvokeArgs
            {
                ResourceGroupName = resourceGroup.Name, RegistryName = registry.Name
            });
        var adminUsername = registryCredentials
            .Apply(c => c.Username ?? string.Empty);
        var adminPassword = registryCredentials
            .Apply(c => Output.CreateSecret(c.Passwords.First().Value ?? string.Empty));

        return new ContainerRegistryOutput(
            registry,
            adminUsername,
            adminPassword);
    }
}