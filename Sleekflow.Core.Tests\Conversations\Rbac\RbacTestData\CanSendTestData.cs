using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.Conversations.Rbac.RbacTestData;

public class CanSendTestData
{
    private static readonly string CompanyId = "sleekflow";
    private static readonly long StaffId = 1;
    private static readonly TeamAccessControlAggregate TeamA = new TeamAccessControlAggregate
    {
        Id = 1,
        TeamMemberStaffIds = new List<long> { 1, 2, 3 }
    };

    private static StaffAccessControlAggregate CreateStaff(List<string>? permissions = null)
    {
        permissions ??= [];

        return new StaffAccessControlAggregate
        {
            StaffId = StaffId,
            CompanyId = CompanyId,
            AssociatedTeams = new List<TeamAccessControlAggregate> { TeamA },
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    SleekflowRoleName = "CustomRole",
                    SleekflowCompanyId = CompanyId,
                    RbacRolePermissions = permissions
                }
            }
        };
    }

    private static Conversation CreateConversation(
        string id,
        long? assigneeId = null,
        long? assignedTeamId = null,
        List<AdditionalAssignee>? additionalAssignees = null,
        List<Mention>? mentions = null)
    {
        return new Conversation
        {
            Id = id,
            CompanyId = CompanyId,
            AssigneeId = assigneeId,
            AssignedTeamId = assignedTeamId,
            AdditionalAssignees = additionalAssignees,
            Mentions = mentions
        };
    }

    public static IEnumerable<TestCaseData> GetNoSendMessagePermissionTestCases()
    {
        var staff = CreateStaff();

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), false).SetName(
            "assigned_to_me_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), false)
            .SetName("team_member_as_contact_owner_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("team_member_as_contact_owner_with_associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("team_member_as_contact_owner_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "assigned_to_non-associated_team_user_conversation_cannot_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), false).SetName(
                "assigned_to_other_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), false)
            .SetName("assigned_to_associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), false).SetName(
            "assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "unassigned_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "assignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            false).SetName("assigned_as_collaborator_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("team_member_as_collaborator_assigned_to_associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("team_member_as_collaborator_assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("team_member_as_collaborator_unassigned_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            false).SetName("mentioned_conversation_cannot_send_message");
    }

    public static IEnumerable<TestCaseData> GetAllConversationSendMessagePermissionTestCases()
    {
        var permissions = RbacSendMessagePermissions.GetAll.ToList();
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("team_member_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("team_member_as_contact_owner_with_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("team_member_as_contact_owner_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "assigned_to_non-associated_team_user_conversation_can_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), true).SetName(
                "assigned_to_other_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), true)
            .SetName("assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), true).SetName(
            "assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId = StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_send_message");
    }

    public static IEnumerable<TestCaseData> GetAssignedToMeSendMessagePermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacSendMessagePermissions.AssignedToMe
        };
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), false)
            .SetName("team_member_as_contact_owner_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("team_member_as_contact_owner_with_associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("team_member_as_contact_owner_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "assigned_to_non-associated_team_user_conversation_cannot_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), false).SetName(
                "assigned_to_other_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), false)
            .SetName("assigned_to_associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), false).SetName(
            "assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "unassigned_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("team_member_as_collaborator_assigned_to_associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("team_member_as_collaborator_assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("team_member_as_collaborator_unassigned_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId= StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("mentioned_conversation_can_send_message");
    }

    public static IEnumerable<TestCaseData> GetAssignedToMeAndAssignedToMyTeamSendMessagePermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacSendMessagePermissions.AssignedToMe,
            RbacSendMessagePermissions.AssignedToMyTeam
        };
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), true)
            .SetName("Assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "Unassigned_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId= StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_send_message");
    }

    public static IEnumerable<TestCaseData> GetAssignedToMeAndAllAssignedConversationsSendMessagePermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacSendMessagePermissions.AssignedToMe,
            RbacSendMessagePermissions.AllAssignedConversations
        };
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "Assigned_to_non-associated_team_user_conversation_can_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), true).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), true)
            .SetName("Assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), true).SetName(
            "Assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "Unassigned_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_unassigned_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId= StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_send_message");
    }

    public static IEnumerable<TestCaseData> GetAssignedToMeAndUnassignedConversationsUnderMyTeamSendMessagePermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacSendMessagePermissions.AssignedToMe,
            RbacSendMessagePermissions.UnassignedConversationsUnderMyTeam
        };
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), false)
            .SetName("Team_member_as_contact_owner_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), true)
            .SetName("Assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "Unassigned_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_unassigned_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId= StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_send_message");
    }

    public static IEnumerable<TestCaseData> GetAssignedToMeAndAllUnassignedConversationsSendMessagePermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacSendMessagePermissions.AssignedToMe,
            RbacSendMessagePermissions.AllUnassignedConversations
        };
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), false)
            .SetName("Team_member_as_contact_owner_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), false)
            .SetName("Assigned_to_associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "Unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId= StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_send_message");
    }


    public static IEnumerable<TestCaseData>
        GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsAndUnassignedConversationsUnderMyTeamSendMessagePermissionTestCases()

    {
        var permissions = new List<string>
        {
            RbacSendMessagePermissions.AssignedToMe,
            RbacSendMessagePermissions.AssignedToMyTeam,
            RbacSendMessagePermissions.AllAssignedConversations,
            RbacSendMessagePermissions.UnassignedConversationsUnderMyTeam
        };
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "Assigned_to_non-associated_team_user_conversation_can_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), true).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), true)
            .SetName("Assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), true).SetName(
            "Assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "Unassigned_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId= StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_send_message");
    }
    public static IEnumerable<TestCaseData> GetAssignedToMeAndAssignedToMyTeamAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsSendMessagePermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacSendMessagePermissions.AssignedToMe,
            RbacSendMessagePermissions.AssignedToMyTeam,
            RbacSendMessagePermissions.UnassignedConversationsUnderMyTeam,
            RbacSendMessagePermissions.AllUnassignedConversations

        };
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), true)
            .SetName("Assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "Unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId= StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_send_message");
    }

    public static IEnumerable<TestCaseData> GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsAndAllUnassignedConversationsSendMessagePermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacSendMessagePermissions.AssignedToMe,
            RbacSendMessagePermissions.AssignedToMyTeam,
            RbacSendMessagePermissions.AllAssignedConversations,
            RbacSendMessagePermissions.AllUnassignedConversations

        };
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "Assigned_to_non-associated_team_user_conversation_can_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), true).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), true)
            .SetName("Assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), true).SetName(
            "Assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "Unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId= StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_send_message");
    }

    public static IEnumerable<TestCaseData> GetAssignedToMeAndAllAssignedConversationsAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsSendMessagePermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacSendMessagePermissions.AssignedToMe,
            RbacSendMessagePermissions.AllAssignedConversations,
            RbacSendMessagePermissions.UnassignedConversationsUnderMyTeam,
            RbacSendMessagePermissions.AllUnassignedConversations

        };
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "Assigned_to_non-associated_team_user_conversation_can_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), true).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), true)
            .SetName("Assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), true).SetName(
            "Assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "Unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId= StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_send_message");
    }

    public static IEnumerable<TestCaseData> GetAssignedToMeAndAssignedToMyTeamAndAllAssignedConversationsSendMessagePermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacSendMessagePermissions.AssignedToMe,
            RbacSendMessagePermissions.AssignedToMyTeam,
            RbacSendMessagePermissions.AllAssignedConversations
        };
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), true)
            .SetName("Team_member_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            true).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), true).SetName(
            "Assigned_to_non-associated_team_user_conversation_can_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), true).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), true)
            .SetName("Assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), true).SetName(
            "Assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), false).SetName(
            "Unassigned_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId= StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_send_message");
    }

    public static IEnumerable<TestCaseData> GetAssignedToMeAndUnassignedConversationsUnderMyTeamAndAllUnassignedConversationsSendMessagePermissionTestCases()
    {
        var permissions = new List<string>
        {
            RbacSendMessagePermissions.AssignedToMe,
            RbacSendMessagePermissions.UnassignedConversationsUnderMyTeam,
            RbacSendMessagePermissions.AllUnassignedConversations
        };
        var staff = CreateStaff(permissions);

        yield return new TestCaseData(staff, CreateConversation("assignedToMeConversation", StaffId), true).SetName(
            "Assigned_to_me_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("teamMemberAsContactOwnerConversation", 2), false)
            .SetName("Team_member_as_contact_owner_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation("teamMemberAsContactOwnerWithAssociatedTeamConversation", 2, TeamA.Id),
            false).SetName("Team_member_as_contact_owner_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToOtherConversation", 100), false).SetName(
            "Assigned_to_non-associated_team_user_conversation_cannot_send_message");

        yield return
            new TestCaseData(staff, CreateConversation("assignedToOtherWithNonAssociatedTeamConversation", 100, 100), false).SetName(
                "Assigned_to_other_with_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToAssociatedTeamConversation", null, TeamA.Id), true)
            .SetName("Assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(staff, CreateConversation("assignedToNonAssociatedTeamConversation", null, 2), false).SetName(
            "Assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(staff, CreateConversation("unassignedConversation"), true).SetName(
            "Unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "AssignedAsCollaboratorConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = StaffId
                    }
                }),
            true).SetName("Assigned_as_collaborator_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToNonAssociatedTeamUserAsContactOwnerConversation",
                100,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_non-associated_team_user_as_contact_owner_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorAssignedToAssociatedTeamConversation",
                null,
                TeamA.Id,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_assigned_to_associated_team_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorConversation",
                null,
                2,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            false).SetName("Team_member_as_collaborator_assigned_to_non-associated_team_conversation_cannot_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "teamMemberAsCollaboratorUnassignedConversation",
                null,
                null,
                new List<AdditionalAssignee>
                {
                    new AdditionalAssignee
                    {
                        CompanyId = CompanyId, AssigneeId = 2
                    }
                }),
            true).SetName("Team_member_as_collaborator_unassigned_conversation_can_send_message");

        yield return new TestCaseData(
            staff,
            CreateConversation(
                "mentionedConversation",
                null,
                null,
                null,
                new List<Mention>
                {
                    new Mention()
                    {
                        MentionedStaffId= StaffId, UpdatedAt = DateTime.UtcNow
                    }
                }),
            true).SetName("Mentioned_conversation_can_send_message");
    }
}
