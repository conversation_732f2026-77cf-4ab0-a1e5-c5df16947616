using System;

namespace Travis_backend.AnalyticsDomain.Models;

public class TopicAnalyticsMetricDto
{
    public DateOnly? Date { get; set; }

    public int? TotalConversations { get; set; }

    public  double? PercentageConversations { get; set; }

    public TopicAnalyticsMetricDto(TopicAnalyticsMetric metric)
    {
        PercentageConversations = 0;
        Date = metric.Date;
        TotalConversations = metric.TotalConversations;
    }

    public TopicAnalyticsMetricDto(DateOnly? date, int? totalConversations, double? percentageConversations)
    {
        Date = date;
        TotalConversations = totalConversations;
        PercentageConversations = percentageConversations;
    }

    public static TopicAnalyticsMetricDto Default(DateOnly? date = null)
    {
        return new TopicAnalyticsMetricDto(
            date,
            0,
            0.0
        );
    }
}