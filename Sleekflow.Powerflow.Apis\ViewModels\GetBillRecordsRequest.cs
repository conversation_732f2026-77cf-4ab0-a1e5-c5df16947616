using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class GetBillRecordsRequest
{
    [JsonProperty("paymentMethod")]
    public string PaymentMethod { get; set; }

    [JsonProperty("offset")]
    public int Offset { get; set; } = 0;

    [JsonProperty("limit")]
    public int Limit { get; set; } = 50;
}

public class UpdateAutoRenewalRequest
{
    [Required]
    [JsonProperty("billRecordId")]
    public long BillRecordId { get; set; }

    [Required]
    [JsonProperty("enabled")]
    public bool Enabled { get; set; }
}