using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationDomain.Models; // Add this using directive


namespace Sleekflow.Core.Tests.Conversations.Extensions.Assignments;

[TestFixture]
public class IsContactOwnerUnchangedUnitTests
{
    [Test]
    public void IsContactOwnerUnchanged_ShouldReturnFalse_WhenConversationIsNull()
    {
        Conversation conversation = null;
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 1 };

        bool result = conversation.IsContactOwnerUnchanged(newContactOwner);

        Assert.IsFalse(result);
    }

    [Test]
    public void IsContactOwnerUnchanged_ShouldReturnTrue_WhenAssigneeIdEqualsNewContactOwnerStaffId()
    {
        var conversation = new Travis_backend.ConversationDomain.Models.Conversation { AssigneeId = 1 };
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 1 };

        bool result = conversation.IsContactOwnerUnchanged(newContactOwner);

        Assert.IsTrue(result);
    }

    [Test]
    public void IsContactOwnerUnchanged_ShouldReturnFalse_WhenAssigneeIdNotEqualsNewContactOwnerStaffId()
    {
        var conversation = new Conversation { AssigneeId = 1 };
        var newContactOwner = new StaffAccessControlAggregate { StaffId = 2 };

        bool result = conversation.IsContactOwnerUnchanged(newContactOwner);

        Assert.IsFalse(result);
    }

    [Test]
    public void IsContactOwnerUnchanged_ShouldReturnFalse_WhenAssigneeIdIsNullAndNewContactOwnerIsNull()
    {
        var conversation = new Conversation { AssigneeId = null };
        StaffAccessControlAggregate? newContactOwner = null;

        bool result = conversation.IsContactOwnerUnchanged(newContactOwner);

        Assert.IsTrue(result);
    }

    [Test]
    public void IsContactOwnerUnchanged_ShouldReturnFalse_WhenAssigneeIdHasValueAndNewContactOwnerIsNull()
    {
        var conversation = new Conversation { AssigneeId = 1 };
        StaffAccessControlAggregate? newContactOwner = null;

        bool result = conversation.IsContactOwnerUnchanged(newContactOwner);

        Assert.IsFalse(result);
    }
}