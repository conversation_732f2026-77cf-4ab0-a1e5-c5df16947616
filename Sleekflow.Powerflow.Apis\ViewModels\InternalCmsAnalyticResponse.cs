﻿using Travis_backend.InternalDomain.ViewModels;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class GetCmsAnalyticResponse
{
    public List<CmsDailyAnalyticDto> DailyAnalytics { get; set; } = new ();
}

public class GetCmsDailyMonthlyRecurringRevenueAnalyticResponse
{
    public List<CmsDailyMonthlyRecurringRevenueAnalyticDto> DailyMonthlyRecurringRevenueAnalytics { get; set; } =
        new ();
}

public class GetCmsDailyRevenueAnalyticResponse
{
    public List<CmsDailyRevenueAnalyticDto> DailyRevenueAnalytics { get; set; } = new ();
}

public class GetCmsPlanDistributionAnalyticResponse
{
    public List<CmsDailyPlanDistributionAnalyticDto> DailyPlanDistributionAnalytics { get; set; } = new ();
}

public class GetCmsAccruedAnalyticResponse
{
    public List<CmsDailyAccruedRevenueAnalyticDto> DailyAccruedAnalytics { get; set; } = new ();
}

public class GetCmsAnalyticByOwnersResponse
{
    public CmsDailyAnalyticByContactOwnersDto ByOwnersDailyAnalytic { get; set; } = new ();
}

public class GetCmsDailyMonthlyRecurringRevenueAnalyticByOwnersResponse
{
    public CmsDailyMonthlyRecurringRevenueAnalyticByContactOwnersDto ByOwnersDailyMonthlyRecurringRevenueAnalytics
    {
        get;
        set;
    } = new ();
}

public class GetCmsDailyRevenueAnalyticByOwnersResponse
{
    public CmsDailyRevenueAnalyticByContactOwnersDto ByOwnersDailyRevenueAnalytic { get; set; } = new ();
}

public class GetCmsPlanDistributionAnalyticByOwnersResponse
{
    public CmsDailyPlanDistributionAnalyticByContactOwnersDto ByOwnersDailyPlanDistributionAnalytic { get; set; } =
        new ();
}

public class GetCmsAnalyticByPartnerStackGroupNamesResponse
{
    public CmsDailyAnalyticByPartnerStackGroupNamesDto ByPartnerStackGroupNamesDailyAnalytic { get; set; } = new ();
}

public class GetCmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesResponse
{
    public CmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesDto
        ByPartnerStackGroupNamesDailyMonthlyRecurringRevenueAnalytics { get; set; } = new ();
}

public class GetCmsDailyRevenueAnalyticByPartnerStackGroupNamesResponse
{
    public CmsDailyRevenueAnalyticByPartnerStackGroupNamesDto
        ByPartnerStackGroupNamesDailyRevenueAnalytic { get; set; } = new ();
}

public class GetCmsPlanDistributionAnalyticByPartnerStackGroupNamesResponse
{
    public CmsDailyPlanDistributionAnalyticByPartnerStackGroupNamesDto
        ByPartnerStackGroupNamesDailyPlanDistributionAnalytic { get; set; } = new ();
}

public class GetCmsDistributionAnalyticResponse
{
    public List<CmsDailyDistributionAnalyticDto> DailyDistributionAnalytics { get; set; } = new ();
}