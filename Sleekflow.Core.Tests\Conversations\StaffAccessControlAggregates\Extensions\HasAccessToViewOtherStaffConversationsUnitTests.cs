using Moq;
using Travis_backend.ConversationDomain.ConversationPermissionConstants;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.ViewModels;

namespace Sleekflow.Core.Tests.Conversations.StaffAccessControlAggregates.Extensions;

[TestFixture]
public class HasAccessToViewOtherStaffOrSelfConversationsUnitTests
{
    [Test]
    public void TryingToAccessStaff_Is_Null_Return_False()
    {
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>()
        };
        StaffAccessControlAggregate tryingToAccessStaff = null;

        bool result = staff.HasAccessToViewOtherStaffOrSelfConversations(tryingToAccessStaff);

        Assert.IsFalse(result);
    }

    [Test]
    public void RbacRoles_Is_Null_Return_False()
    {
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = null
        };
        var tryingToAccessStaff = new StaffAccessControlAggregate
        {
            StaffId = 1
        };

        bool result = staff.HasAccessToViewOtherStaffOrSelfConversations(tryingToAccessStaff);

        Assert.IsFalse(result);
    }

    [Test]
    public void HasViewConversationsFullAccess_Return_True()
    {
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = RbacViewConversationsPermissions.GetAll.ToList()
                }
            }
        };
        var tryingToAccessStaff = new StaffAccessControlAggregate
        {
            StaffId = 1
        };

        bool result = staff.HasAccessToViewOtherStaffOrSelfConversations(tryingToAccessStaff);

        Assert.IsTrue(result);
    }

    [Test]
    public void HasAllAssignedConversationsPermission_Return_True()
    {
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacViewConversationsPermissions.AllAssignedConversations
                    }
                }
            }
        };
        var tryingToAccessStaff = new StaffAccessControlAggregate
        {
            StaffId = 1
        };

        bool result = staff.HasAccessToViewOtherStaffOrSelfConversations(tryingToAccessStaff);

        Assert.IsTrue(result);
    }

    [Test]
    public void HasAssignedToMyTeamPermission_And_Staff_Is_Team_Member_Return_True()
    {
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                new TeamAccessControlAggregate
                {
                    Id = 1
                }
            },
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacViewConversationsPermissions.AssignedToMyTeam
                    }
                }
            }
        };
        var tryingToAccessStaff = new StaffAccessControlAggregate
        {
            StaffId = 2,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                new TeamAccessControlAggregate
                {
                    Id = 1
                }
            }
        };

        bool result = staff.HasAccessToViewOtherStaffOrSelfConversations(tryingToAccessStaff);

        Assert.IsTrue(result);
    }

    [Test]
    public void HasAssignedToMePermission_And_StaffId_Is_Self_Return_True()
    {
        var staff = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        RbacViewConversationsPermissions.AssignedToMe
                    }
                }
            }
        };
        var tryingToAccessStaff = new StaffAccessControlAggregate
        {
            StaffId = 1
        };

        bool result = staff.HasAccessToViewOtherStaffOrSelfConversations(tryingToAccessStaff);

        Assert.IsTrue(result);
    }

    [Test]
    public void No_Relevant_Permissions_Return_False()
    {
        var staff = new StaffAccessControlAggregate
        {
            RbacRoles = new List<RbacRole>
            {
                new RbacRole
                {
                    RbacRolePermissions = new List<string>
                    {
                        "SomeOtherPermission"
                    }
                }
            }
        };
        var tryingToAccessStaff = new StaffAccessControlAggregate
        {
            StaffId = 1
        };

        bool result = staff.HasAccessToViewOtherStaffOrSelfConversations(tryingToAccessStaff);

        Assert.IsFalse(result);
    }
}