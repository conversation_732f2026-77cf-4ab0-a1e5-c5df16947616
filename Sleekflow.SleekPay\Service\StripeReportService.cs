﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Mail;
using System.Net.Mime;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Sleekflow.SleekPay.Model;
using Sleekflow.SleekPay.SQLDatabase;
using Sleekflow.SleekPay.SQLDatabase.Entity;
using Stripe;
using File = Stripe.File;

namespace Sleekflow.SleekPay.Service;

public interface IStripeReportService
{
    Task ProcessStripeReportRunSucceededEvent(
        long stripePaymentReportExportRecordId,
        string companyId,
        File reportFile,
        string reportReceiverEmailAddress,
        DateTime reportDataDateRangeStartAt,
        DateTime reportDataDateRangeEndAt,
        string platformCountry);

    Task ProcessStripeReportRunFailedEvent(
        long stripePaymentReportExportRecordId);
}

public class StripeReportService : IStripeReportService
{
    private readonly ApplicationDbContext _appDbContext;

    public StripeReportService(
        ApplicationDbContext appDbContext)
    {
        _appDbContext = appDbContext;
    }

    public async Task ProcessStripeReportRunSucceededEvent(
        long stripePaymentReportExportRecordId,
        string companyId,
        File reportFile,
        string reportReceiverEmailAddress,
        DateTime reportDataDateRangeStartAt,
        DateTime reportDataDateRangeEndAt,
        string platformCountry)
    {
        if (reportFile == null)
        {
            return;
        }

        var stripePaymentReportExportRecord =
            await _appDbContext.StripePaymentReportExportRecords.FirstOrDefaultAsync(r =>
                r.Id == stripePaymentReportExportRecordId);

        if (stripePaymentReportExportRecord == null
            || await IsStripePaymentReportRunProcessed(stripePaymentReportExportRecord.Id))
        {
            return;
        }

        var stripeActivityReport =
            GetStripeActivityReportFromCsv(await DownloadStripeReportAsCsv(reportFile.Id, platformCountry));

        if (stripeActivityReport?.Activities == null || stripeActivityReport.Activities.Count == 0)
        {
            return;
        }

        var stripePaymentIntentActivitiesGroupings =
            GetPaymentIntentGroupingsFromStripeActivities(stripeActivityReport.Activities);

        var stripeCustomizedReport = new StripeCustomizedReport
        {
            Items = new List<StripeCustomizedReportItem>()
        };

        foreach (var stripePaymentIntentActivitiesGrouping in stripePaymentIntentActivitiesGroupings)
        {
            if (string.IsNullOrEmpty(stripePaymentIntentActivitiesGrouping.Key))
            {
                continue;
            }

            var stripePaymentRecord = await _appDbContext.StripePaymentRecords.FirstOrDefaultAsync(r =>
                r.StripePaymentIntentId == stripePaymentIntentActivitiesGrouping.Key &&
                r.CompanyId == companyId);

            if (stripePaymentRecord == null)
            {
                continue;
            }

            var (feeItem, paymentItem, refundItem) = await GetStripeCustomizedReportItems(
                stripePaymentRecord, stripePaymentIntentActivitiesGrouping.ToList());

            stripeCustomizedReport.Items.AddToListIfNotNull(feeItem);
            stripeCustomizedReport.Items.AddToListIfNotNull(paymentItem);
            stripeCustomizedReport.Items.AddToListIfNotNull(refundItem);
        }

        var stripeCustomizedReportFileContents = await WriteStripeCustomizedReportToFileAndGetContents(stripeCustomizedReport);

        await SendPaymentReportEmail(
            stripePaymentReportExportRecordId,
            reportReceiverEmailAddress,
            reportDataDateRangeStartAt,
            reportDataDateRangeEndAt,
            stripeCustomizedReportFileContents);
    }

    public async Task ProcessStripeReportRunFailedEvent(
        long stripePaymentReportExportRecordId)
    {
        var emailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
        if (emailConfig == null)
        {
            throw new Exception("No email address found to send this file with");
        }

        var stripePaymentReportExportRecord =
            await _appDbContext.StripePaymentReportExportRecords.FirstOrDefaultAsync(r =>
                r.Id == stripePaymentReportExportRecordId);

        if (stripePaymentReportExportRecord == null
            || await IsStripePaymentReportRunProcessed(stripePaymentReportExportRecord.Id))
        {
            return;
        }

        var client = ConfigureHubSpotSmtpClient();

        await client.SendMailAsync(await PrepareReportFailureEmailMessage(
            emailConfig.Email,
            new List<string>{ stripePaymentReportExportRecord.ReportReceiverEmailAddress },
            "SleekFlow",
            null));

        stripePaymentReportExportRecord.ReportFailedEmailSentAt = DateTime.UtcNow;

        await _appDbContext.SaveChangesAsync();
    }

    private static async Task<string> DownloadStripeReportAsCsv(
        string fileId,
        string platformCountry)
    {
        SetStripeApiKeyByPlatformCountry(platformCountry);

        var baseAddress = $"https://files.stripe.com/v1/files/{fileId}/contents";

        using var client = new HttpClient();

        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic",
            Convert.ToBase64String(Encoding.UTF8.GetBytes(StripeConfiguration.ApiKey)));

        using var response = await client.GetAsync(baseAddress);

        return await response.Content.ReadAsStringAsync();
    }

    private static void SetStripeApiKeyByPlatformCountry(string platformCountry)
    {
        switch (platformCountry.ToLower())
        {
            case "hk":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_HK");
                break;
            case "sg":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_SG");
                break;
            case "my":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_MY");
                break;
            case "gb":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_GB");
                break;
        }
    }

    private static StripeActivityReport GetStripeActivityReportFromCsv(string csv)
    {
        var csvLines = csv?.Split("\n");

        return csvLines == null || csvLines.Length <= 1
            ? null
            : StripeActivityReport.InitializeFromCsvLines(csv);
    }

    private static List<IGrouping<string, StripeActivity>> GetPaymentIntentGroupingsFromStripeActivities(
        IEnumerable<StripeActivity> activities)
    {
        return activities.GroupBy(i => i.PaymentIntentId).ToList();
    }

    private async Task<(StripeCustomizedReportItem feeItem,
                        StripeCustomizedReportItem paymentItem,
                        StripeCustomizedReportItem refundItem)>
            GetStripeCustomizedReportItems(StripePaymentRecord stripePaymentRecord, List<StripeActivity> stripePaymentIntentActivities)
    {
        var shopifyOrderId = stripePaymentRecord.ShopifyOrderId ?? stripePaymentRecord.ShopifyDraftOrderId;

        var staffName = string.Empty;

        if (stripePaymentRecord.StaffId is null)
        {
            staffName = "system";
        }
        else
        {
            var staff = await _appDbContext.UserRoleStaffs.Include(s => s.Identity)
                .FirstOrDefaultAsync(s => s.Id == stripePaymentRecord.StaffId);

            if (!string.IsNullOrEmpty(staff?.Identity?.DisplayName))
            {
                staffName = staff.Identity.DisplayName;
            }
        }

        var feeAmount = 0m;
        var latestFeeActivityDateTime = new DateTime(1000, 1, 1);
        var feeActivityInvoiceId = string.Empty;
        var feeActivityPaymentMethodType = string.Empty;
        var feeActivityCardBrand = string.Empty;
        var feeActivityCardFunding = string.Empty;
        var feeActivityCardCountry = string.Empty;
        var feeActivityStatementDescriptor = string.Empty;
        var feeActivityDisputeId = string.Empty;
        var feeActivityConnectedAccountDirectChargeId = string.Empty;

        var latestPaymentActivityDateTime = new DateTime(1000, 1, 1);
        var paymentActivityInvoiceId = string.Empty;
        var paymentActivityPaymentMethodType = string.Empty;
        var paymentActivityCardBrand = string.Empty;
        var paymentActivityCardFunding = string.Empty;
        var paymentActivityCardCountry = string.Empty;
        var paymentActivityStatementDescriptor = string.Empty;
        var paymentActivityDisputeId = string.Empty;
        var paymentActivityTransferId = string.Empty;

        var latestRefundActivityDateTime = new DateTime(1000, 1, 1);
        var refundActivityInvoiceId = string.Empty;
        var refundActivityPaymentMethodType = string.Empty;
        var refundActivityCardBrand = string.Empty;
        var refundActivityCardFunding = string.Empty;
        var refundActivityCardCountry = string.Empty;
        var refundActivityStatementDescriptor = string.Empty;
        var refundActivityDisputeId = string.Empty;
        var refundActivityTransferId = string.Empty;
        var refundActivityRefundId = string.Empty;
        var refundedAmount = 0m;

        var shippingAddressLine1 = string.Empty;
        var shippingAddressLine2 = string.Empty;
        var shippingAddressCity = string.Empty;
        var shippingAddressState = string.Empty;
        var shippingAddressCountry = string.Empty;

        var chargeId = string.Empty;
        var connectedAccountId = string.Empty;
        var connectedAccountName = string.Empty;
        var connectedAccountCountry = string.Empty;

        foreach (var stripePaymentIntentActivity in stripePaymentIntentActivities)
        {
            if (stripePaymentIntentActivity.BalanceTransactionReportingCategory == "platform_earning")
            {
                feeAmount += Math.Abs(stripePaymentIntentActivity.Amount);

                if (stripePaymentIntentActivity.ActivityAt > latestFeeActivityDateTime)
                {
                    latestFeeActivityDateTime = stripePaymentIntentActivity.ActivityAt;
                }

                if (string.IsNullOrEmpty(feeActivityInvoiceId) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.InvoiceId))
                {
                    feeActivityInvoiceId = stripePaymentIntentActivity.InvoiceId;
                }
                if (string.IsNullOrEmpty(feeActivityPaymentMethodType) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.PaymentMethodType))
                {
                    feeActivityPaymentMethodType = stripePaymentIntentActivity.PaymentMethodType;
                }
                if (string.IsNullOrEmpty(feeActivityCardBrand) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.CardBrand))
                {
                    feeActivityCardBrand = stripePaymentIntentActivity.CardBrand;
                }
                if (string.IsNullOrEmpty(feeActivityCardFunding) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.CardFunding))
                {
                    feeActivityCardFunding = stripePaymentIntentActivity.CardFunding;
                }
                if (string.IsNullOrEmpty(feeActivityCardCountry) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.CardCountry))
                {
                    feeActivityCardCountry = stripePaymentIntentActivity.CardCountry;
                }
                if (string.IsNullOrEmpty(feeActivityStatementDescriptor) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.StatementDescriptor))
                {
                    feeActivityStatementDescriptor = stripePaymentIntentActivity.StatementDescriptor;
                }
                if (string.IsNullOrEmpty(feeActivityDisputeId) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.DisputeId))
                {
                    feeActivityDisputeId = stripePaymentIntentActivity.DisputeId;
                }
                if (string.IsNullOrEmpty(feeActivityConnectedAccountDirectChargeId) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.ConnectedAccountDirectChargeId))
                {
                    feeActivityConnectedAccountDirectChargeId = stripePaymentIntentActivity.ConnectedAccountDirectChargeId;
                }
            }

            if (stripePaymentIntentActivity.BalanceTransactionReportingCategory == "charge")
            {
                if (string.IsNullOrEmpty(paymentActivityInvoiceId) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.InvoiceId))
                {
                    paymentActivityInvoiceId = stripePaymentIntentActivity.InvoiceId;
                }
                if (string.IsNullOrEmpty(paymentActivityPaymentMethodType) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.PaymentMethodType))
                {
                    paymentActivityPaymentMethodType = stripePaymentIntentActivity.PaymentMethodType;
                }
                if (string.IsNullOrEmpty(paymentActivityCardBrand) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.CardBrand))
                {
                    paymentActivityCardBrand = stripePaymentIntentActivity.CardBrand;
                }
                if (string.IsNullOrEmpty(paymentActivityCardFunding) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.CardFunding))
                {
                    paymentActivityCardFunding = stripePaymentIntentActivity.CardFunding;
                }
                if (string.IsNullOrEmpty(paymentActivityCardCountry) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.CardCountry))
                {
                    paymentActivityCardCountry = stripePaymentIntentActivity.CardCountry;
                }
                if (string.IsNullOrEmpty(paymentActivityStatementDescriptor) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.StatementDescriptor))
                {
                    paymentActivityStatementDescriptor = stripePaymentIntentActivity.StatementDescriptor;
                }
                if (string.IsNullOrEmpty(paymentActivityDisputeId) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.DisputeId))
                {
                    paymentActivityDisputeId = stripePaymentIntentActivity.DisputeId;
                }
            }

            if (stripePaymentIntentActivity.BalanceTransactionReportingCategory == "transfer")
            {
                if (stripePaymentIntentActivity.ActivityAt > latestPaymentActivityDateTime)
                {
                    latestPaymentActivityDateTime = stripePaymentIntentActivity.ActivityAt;
                }

                if (string.IsNullOrEmpty(paymentActivityTransferId) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.TransferId))
                {
                    paymentActivityTransferId = stripePaymentIntentActivity.TransferId;
                }
            }

            if (stripePaymentIntentActivity.BalanceTransactionReportingCategory == "refund")
            {
                if (stripePaymentIntentActivity.ActivityAt > latestRefundActivityDateTime)
                {
                    latestRefundActivityDateTime = stripePaymentIntentActivity.ActivityAt;
                }

                if (string.IsNullOrEmpty(refundActivityInvoiceId) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.InvoiceId))
                {
                    refundActivityInvoiceId = stripePaymentIntentActivity.InvoiceId;
                }
                if (string.IsNullOrEmpty(refundActivityPaymentMethodType) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.PaymentMethodType))
                {
                    refundActivityPaymentMethodType = stripePaymentIntentActivity.PaymentMethodType;
                }
                if (string.IsNullOrEmpty(refundActivityCardBrand) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.CardBrand))
                {
                    refundActivityCardBrand = stripePaymentIntentActivity.CardBrand;
                }
                if (string.IsNullOrEmpty(refundActivityCardFunding) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.CardFunding))
                {
                    refundActivityCardFunding = stripePaymentIntentActivity.CardFunding;
                }
                if (string.IsNullOrEmpty(refundActivityCardCountry) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.CardCountry))
                {
                    refundActivityCardCountry = stripePaymentIntentActivity.CardCountry;
                }
                if (string.IsNullOrEmpty(refundActivityStatementDescriptor) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.StatementDescriptor))
                {
                    refundActivityStatementDescriptor = stripePaymentIntentActivity.StatementDescriptor;
                }
                if (string.IsNullOrEmpty(refundActivityDisputeId) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.DisputeId))
                {
                    refundActivityDisputeId = stripePaymentIntentActivity.DisputeId;
                }
                if (string.IsNullOrEmpty(refundActivityRefundId) &&
                    !string.IsNullOrEmpty(stripePaymentIntentActivity.RefundId))
                {
                    refundActivityRefundId = stripePaymentIntentActivity.RefundId;
                }

                refundedAmount += Math.Abs(stripePaymentIntentActivity.Amount);
            }

            if (stripePaymentIntentActivity.BalanceTransactionReportingCategory == "transfer_reversal")
            {
                refundActivityTransferId = stripePaymentIntentActivity.TransferId;
            }

            if (string.IsNullOrEmpty(shippingAddressLine1) &&
                !string.IsNullOrEmpty(stripePaymentIntentActivity.ShippingAddressLine1))
            {
                shippingAddressLine1 = stripePaymentIntentActivity.ShippingAddressLine1;
            }
            if (string.IsNullOrEmpty(shippingAddressLine2) &&
                !string.IsNullOrEmpty(stripePaymentIntentActivity.ShippingAddressLine2))
            {
                shippingAddressLine2 = stripePaymentIntentActivity.ShippingAddressLine2;
            }
            if (string.IsNullOrEmpty(shippingAddressCity) &&
                !string.IsNullOrEmpty(stripePaymentIntentActivity.ShippingAddressCity))
            {
                shippingAddressCity = stripePaymentIntentActivity.ShippingAddressCity;
            }
            if (string.IsNullOrEmpty(shippingAddressState) &&
                !string.IsNullOrEmpty(stripePaymentIntentActivity.ShippingAddressState))
            {
                shippingAddressState = stripePaymentIntentActivity.ShippingAddressState;
            }
            if (string.IsNullOrEmpty(shippingAddressCountry) &&
                !string.IsNullOrEmpty(stripePaymentIntentActivity.ShippingAddressCountry))
            {
                shippingAddressCountry = stripePaymentIntentActivity.ShippingAddressCountry;
            }

            if (string.IsNullOrEmpty(chargeId) &&
                !string.IsNullOrEmpty(stripePaymentIntentActivity.ChargeId))
            {
                chargeId = stripePaymentIntentActivity.ChargeId;
            }
            if (string.IsNullOrEmpty(connectedAccountId) &&
                !string.IsNullOrEmpty(stripePaymentIntentActivity.ConnectedAccountId))
            {
                connectedAccountId = stripePaymentIntentActivity.ConnectedAccountId;
            }
            if (string.IsNullOrEmpty(connectedAccountName) &&
                !string.IsNullOrEmpty(stripePaymentIntentActivity.ConnectedAccountName))
            {
                connectedAccountName = stripePaymentIntentActivity.ConnectedAccountName;
            }
            if (string.IsNullOrEmpty(connectedAccountCountry) &&
                !string.IsNullOrEmpty(stripePaymentIntentActivity.ConnectedAccountCountry))
            {
                connectedAccountCountry = stripePaymentIntentActivity.ConnectedAccountCountry;
            }
        }

        var lineItemsAmount = stripePaymentRecord.LineItems.Sum(l => (l.Amount - l.TotalDiscount) * l.Quantity);
        var shippingFeeAmount = stripePaymentRecord.PayAmount - lineItemsAmount;
        var lineItemDetail = FormatStripePaymentLineItemForReport(stripePaymentRecord.LineItems);

        return (
            feeItem: latestFeeActivityDateTime > new DateTime(1000, 1, 1)
                ? new StripeCustomizedReportItem
                {
                    ShopifyId = shopifyOrderId?.ToString(),
                    PaymentLinkCreatedBy = staffName,
                    BalanceTransactionCreatedAt = latestFeeActivityDateTime,
                    BalanceTransactionReportingCategory = "SleekFlow Fee",
                    ActivityAt = latestFeeActivityDateTime,
                    Currency = stripePaymentRecord.Currency,
                    OrderAmount = -feeAmount,
                    ShippingAmount = 0,
                    CustomerEmail = stripePaymentRecord.CustomerEmail,
                    CustomerId = stripePaymentRecord.CustomerId,
                    CustomerName = stripePaymentIntentActivities[0].CustomerName,
                    ShippingAddressLine1 = shippingAddressLine1,
                    ShippingAddressLine2 = shippingAddressLine2,
                    ShippingAddressCity = shippingAddressCity,
                    ShippingAddressState = shippingAddressState,
                    ShippingAddressCountry = shippingAddressCountry,
                    ChargeId = chargeId,
                    PaymentIntentId = stripePaymentRecord.StripePaymentIntentId,
                    InvoiceId = feeActivityInvoiceId,
                    PaymentMethodType = feeActivityPaymentMethodType,
                    CardBrand = feeActivityCardBrand,
                    CardCountry = feeActivityCardCountry,
                    CardFunding = feeActivityCardFunding,
                    StatementDescriptor = feeActivityStatementDescriptor,
                    DisputeId = feeActivityDisputeId,
                    ConnectedAccountId = connectedAccountId,
                    ConnectedAccountName = connectedAccountName,
                    ConnectedAccountCountry = connectedAccountCountry,
                    ConnectedAccountDirectChargeId = feeActivityConnectedAccountDirectChargeId,
                    OrderLineItems = lineItemDetail
                }
                : null,
            paymentItem: latestPaymentActivityDateTime > new DateTime(1000, 1, 1)
                ? new StripeCustomizedReportItem
                {
                    ShopifyId = shopifyOrderId?.ToString(),
                    PaymentLinkCreatedBy = staffName,
                    BalanceTransactionCreatedAt = latestPaymentActivityDateTime,
                    BalanceTransactionReportingCategory = "Order Amount",
                    ActivityAt = latestPaymentActivityDateTime,
                    Currency = stripePaymentRecord.Currency,
                    OrderAmount = lineItemsAmount,
                    ShippingAmount = shippingFeeAmount,
                    CustomerEmail = stripePaymentRecord.CustomerEmail,
                    CustomerId = stripePaymentRecord.CustomerId,
                    CustomerName = stripePaymentIntentActivities[0].CustomerName,
                    ShippingAddressLine1 = shippingAddressLine1,
                    ShippingAddressLine2 = shippingAddressLine2,
                    ShippingAddressCity = shippingAddressCity,
                    ShippingAddressState = shippingAddressState,
                    ShippingAddressCountry = shippingAddressCountry,
                    ChargeId = chargeId,
                    PaymentIntentId = stripePaymentRecord.StripePaymentIntentId,
                    InvoiceId = paymentActivityInvoiceId,
                    PaymentMethodType = paymentActivityPaymentMethodType,
                    CardBrand = paymentActivityCardBrand,
                    CardCountry = paymentActivityCardCountry,
                    CardFunding = paymentActivityCardFunding,
                    StatementDescriptor = paymentActivityStatementDescriptor,
                    DisputeId = paymentActivityDisputeId,
                    TransferId = paymentActivityTransferId,
                    ConnectedAccountId = connectedAccountId,
                    ConnectedAccountName = connectedAccountName,
                    ConnectedAccountCountry = connectedAccountCountry,
                    OrderLineItems = lineItemDetail
                }
                : null,
            refundItem: latestRefundActivityDateTime > new DateTime(1000, 1, 1)
                ? new StripeCustomizedReportItem
                {
                    ShopifyId = shopifyOrderId?.ToString(),
                    PaymentLinkCreatedBy = staffName,
                    BalanceTransactionCreatedAt = latestRefundActivityDateTime,
                    BalanceTransactionReportingCategory = "Refund",
                    ActivityAt = latestRefundActivityDateTime,
                    Currency = stripePaymentRecord.Currency,
                    OrderAmount = refundedAmount > lineItemsAmount ? -lineItemsAmount : -refundedAmount,
                    ShippingAmount = refundedAmount > lineItemsAmount ? -(refundedAmount - lineItemsAmount) : 0,
                    CustomerEmail = stripePaymentRecord.CustomerEmail,
                    CustomerId = stripePaymentRecord.CustomerId,
                    CustomerName = stripePaymentIntentActivities[0].CustomerName,
                    ShippingAddressLine1 = shippingAddressLine1,
                    ShippingAddressLine2 = shippingAddressLine2,
                    ShippingAddressCity = shippingAddressCity,
                    ShippingAddressState = shippingAddressState,
                    ShippingAddressCountry = shippingAddressCountry,
                    ChargeId = chargeId,
                    PaymentIntentId = stripePaymentRecord.StripePaymentIntentId,
                    InvoiceId = refundActivityInvoiceId,
                    PaymentMethodType = refundActivityPaymentMethodType,
                    CardBrand = refundActivityCardBrand,
                    CardCountry = refundActivityCardCountry,
                    CardFunding = refundActivityCardFunding,
                    StatementDescriptor = refundActivityStatementDescriptor,
                    DisputeId = refundActivityDisputeId,
                    TransferId = refundActivityTransferId,
                    RefundId = refundActivityRefundId,
                    ConnectedAccountId = connectedAccountId,
                    ConnectedAccountName = connectedAccountName,
                    ConnectedAccountCountry = connectedAccountCountry,
                    OrderLineItems = lineItemDetail
                }
                : null);
    }

    private static async Task<byte[]> WriteStripeCustomizedReportToFileAndGetContents(
        StripeCustomizedReport stripeCustomizedReport)
    {
        var stripeCustomizedReportCsv = new StringBuilder();

        var stripeCustomizedReportItemProperties = typeof(StripeCustomizedReportItem).GetProperties();

        var headerLine = string.Empty;
        foreach (var stripeCustomizedReportItemProperty in stripeCustomizedReportItemProperties)
        {
            headerLine += CamelCaseToSnakeCase(stripeCustomizedReportItemProperty.Name);

            if (stripeCustomizedReportItemProperty != stripeCustomizedReportItemProperties.Last())
            {
                headerLine += ",";
            }
        }

        stripeCustomizedReportCsv.AppendLine(headerLine);

        var stripeCustomizedReportItems = stripeCustomizedReport.Items;
        foreach (var stripeCustomizedReportItem in stripeCustomizedReportItems)
        {
            var itemLine = string.Empty;

            foreach (var stripeCustomizedReportItemProperty in stripeCustomizedReportItemProperties)
            {
                if (stripeCustomizedReportItemProperty.Name is "ShippingAddressLine1" or "ShippingAddressLine2")
                {
                    itemLine += "\"";
                }

                itemLine += (stripeCustomizedReportItemProperty.Name == "ShopifyId" ? "=\"" : string.Empty) +
                            stripeCustomizedReportItemProperty.GetValue(stripeCustomizedReportItem) +
                            (stripeCustomizedReportItemProperty.Name == "ShopifyId" ? "\"" : string.Empty);

                if (stripeCustomizedReportItemProperty.Name is "ShippingAddressLine1" or "ShippingAddressLine2")
                {
                    itemLine += "\"";
                }

                if (stripeCustomizedReportItemProperty != stripeCustomizedReportItemProperties.Last())
                {
                    itemLine += ",";
                }
            }

            stripeCustomizedReportCsv.AppendLine(itemLine);
        }

        var ms = new MemoryStream();

        var sw = new StreamWriter(ms);

        await sw.WriteAsync(stripeCustomizedReportCsv.ToString());
        await sw.FlushAsync();

        return ms.ToArray();
    }

    private async Task SendPaymentReportEmail(
        long stripePaymentReportExportRecordId,
        string receiverEmailAddress,
        DateTime reportDataDateRangeStartAt,
        DateTime reportDataDateRangeEndAt,
        byte[] fileContents)
    {
        var emailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
        if (emailConfig == null)
        {
            throw new Exception("No email address found to send this file with");
        }

        var client = ConfigureHubSpotSmtpClient();

        var message = await PrepareReportEmailMessage(
            emailConfig.Email,
            new List<string>{ receiverEmailAddress },
            "SleekFlow",
            reportDataDateRangeStartAt,
            reportDataDateRangeEndAt,
            new Dictionary<string, byte[]> {{ $"payment_report_{reportDataDateRangeStartAt.ToString("ddMMyyyy")}_{reportDataDateRangeEndAt.ToString("ddMMyyyy")}.csv", fileContents }});

        var stripePaymentReportExportRecord =
            await _appDbContext.StripePaymentReportExportRecords.FirstOrDefaultAsync(r =>
                r.Id == stripePaymentReportExportRecordId);

        try
        {
            await client.SendMailAsync(message);
        }
        catch (Exception e)
        {
            await client.SendMailAsync(await PrepareReportFailureEmailMessage(
                emailConfig.Email,
                new List<string>{ receiverEmailAddress },
                "SleekFlow",
                null));

            stripePaymentReportExportRecord.ReportFailedEmailSentAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            return;
        }

        stripePaymentReportExportRecord.ReportEmailSentAt = DateTime.UtcNow;

        await _appDbContext.SaveChangesAsync();
    }

    private async Task<MailMessage> PrepareReportEmailMessage(
        string fromEmailAddress,
        List<string> toEmailAddresses,
        string senderName,
        DateTime reportDataDateRangeStartAt,
        DateTime reportDataDateRangeEndAt,
        Dictionary<string, byte[]> attachments)
    {
        var reportEmailTemplate =
            await _appDbContext.CoreEmailNotificationTemplates.FirstOrDefaultAsync(t =>
                t.NotificationType == NotificationType.StripePaymentReport);

        return PrepareEmailMessage(
            fromEmailAddress,
            toEmailAddresses,
            senderName,
            reportEmailTemplate.EmailSubject,
            reportEmailTemplate.EmailSubject,
            reportEmailTemplate.EmailTemplate
                .Replace("{StartDate}", reportDataDateRangeStartAt.ToString("dd-MM-yyyy"))
                .Replace("{EndDate}", reportDataDateRangeEndAt.ToString("dd-MM-yyyy")),
            attachments);
    }

    private async Task<MailMessage> PrepareReportFailureEmailMessage(
        string fromEmailAddress,
        List<string> toEmailAddresses,
        string senderName,
        Dictionary<string, byte[]> attachments)
    {
        var reportEmailTemplate =
            await _appDbContext.CoreEmailNotificationTemplates.FirstOrDefaultAsync(t =>
                t.NotificationType == NotificationType.StripePaymentReportFailed);

        return PrepareEmailMessage(
            fromEmailAddress,
            toEmailAddresses,
            senderName,
            reportEmailTemplate.EmailSubject,
            reportEmailTemplate.EmailSubject,
            reportEmailTemplate.EmailTemplate,
            attachments);
    }

    private static MailMessage PrepareEmailMessage(
        string fromEmailAddress,
        List<string> toEmailAddresses,
        string senderName,
        string subject,
        string plainTextContent,
        string htmlContent,
        Dictionary<string, byte[]> attachments)
    {
        var from = new MailAddress(fromEmailAddress, senderName);

        var to = new List<MailAddress>();
        foreach (var toEmailAddress in toEmailAddresses)
        {
            to.Add(item: new MailAddress(toEmailAddress));
        }

        var message = CreateSingleEmailToMultipleRecipients(from, to, subject, plainTextContent, htmlContent);

        long attachmentSizeCount = 0;

        if (attachments == null)
        {
            return message;
        }

        foreach (var file in attachments)
        {
            attachmentSizeCount += file.Value.LongLength;

            if (attachmentSizeCount >= 23000000)
            {
                attachmentSizeCount -= file.Value.LongLength;
            }
            else
            {
                var fileStream = new MemoryStream(buffer: file.Value);

                message.Attachments.Add(new Attachment(fileStream, file.Key));
            }
        }

        return message;
    }

    private static string CamelCaseToSnakeCase(string textInCamelCase)
    {
        if (textInCamelCase == null)
        {
            throw new ArgumentNullException(nameof(textInCamelCase));
        }

        if(textInCamelCase.Length < 2) {
            return textInCamelCase;
        }

        var sb = new StringBuilder();
        sb.Append(char.ToLowerInvariant(textInCamelCase[0]));
        for (var i = 1; i < textInCamelCase.Length; ++i) {
            var c = textInCamelCase[i];
            if(char.IsUpper(c))
            {
                sb.Append('_');
                sb.Append(char.ToLowerInvariant(c));
            }
            else
            {
                sb.Append(c);
            }
        }

        return sb.ToString();
    }

    private async Task<bool> IsStripePaymentReportRunProcessed(long stripePaymentReportExportRecordId)
    {
        var stripePaymentReportExportRecord =
            await _appDbContext.StripePaymentReportExportRecords.FirstOrDefaultAsync(r =>
                r.Id == stripePaymentReportExportRecordId);

        return stripePaymentReportExportRecord.ReportFailedEmailSentAt != default ||
               stripePaymentReportExportRecord.ReportEmailSentAt != default;
    }

    private string FormatStripePaymentLineItemForReport(List<StripePaymentLineItem> lineItems)
    {
        var sb = new StringBuilder();

        foreach (var lineItem in lineItems)
        {
            sb.Append($"Name: {lineItem.Name} ");
            sb.Append($"Description: {lineItem.Description} ");
            sb.Append($"Amount: {lineItem.Amount:C} ({lineItem.Currency}) ");
            sb.Append($"Quantity: {lineItem.Quantity} ");

            if (lineItem.ImageUrls != null && lineItem.ImageUrls.Count > 0)
            {
                sb.Append("Image URLs: ");
                sb.Append(string.Join(" ", lineItem.ImageUrls));
                sb.Append(" ");
            }

            if (lineItem.TotalDiscount > 0)
            {
                sb.Append($"Total Discount: {lineItem.TotalDiscount:C} ");
            }

            if (lineItem.Metadata != null && lineItem.Metadata.Count > 0)
            {
                sb.Append("Metadata: ");
                foreach (var kvp in lineItem.Metadata)
                {
                    sb.Append($"'{kvp.Key}: {kvp.Value}' ");
                }
            }

            sb.Remove(sb.Length - 2, 2); // Remove the trailing comma and space
            sb.Append(" | "); // Add a separator between line items
        }

        if (sb.Length > 0)
        {
            sb.Remove(sb.Length - 3, 3); // Remove the trailing separator
        }

        return sb.ToString();
    }

    private static SmtpClient ConfigureHubSpotSmtpClient()
    {
        var username = Environment.GetEnvironmentVariable("HubSpotSmtp__Username");
        var password = Environment.GetEnvironmentVariable("HubSpotSmtp__Password");

        var smtpClient = new SmtpClient();

        smtpClient.Host = "smtp.hubapi.com";
        smtpClient.Port = 587;
        smtpClient.UseDefaultCredentials = false;
        smtpClient.Credentials = new NetworkCredential(
            username,
            password);
        smtpClient.EnableSsl = true;

        return smtpClient;
    }

    private static MailMessage CreateSingleEmailToMultipleRecipients(
        MailAddress from,
        List<MailAddress> recipients,
        string subject,
        string plainTextContent,
        string htmlContent)
    {
        var mailMessage = new MailMessage();

        mailMessage.From = from;
        foreach (var recipient in recipients)
        {
            mailMessage.To.Add(recipient);
        }

        mailMessage.Subject = subject;
        mailMessage.Body = plainTextContent;
        mailMessage.BodyEncoding = Encoding.UTF8;
        mailMessage.SubjectEncoding = Encoding.UTF8;

        var mimeType = new ContentType("text/html");
        var alternate = AlternateView.CreateAlternateViewFromString(htmlContent, mimeType);
        mailMessage.AlternateViews.Add(alternate);

        return mailMessage;
    }
}